<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="cache-control" content="no-cache" />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" /> -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <script
      src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_18222_53.c0a472279a7dca1113b2ec69c665c383.js"
      integrity="sha384-zkga8qRw6sqovbU7PI7A6lH10uqjccYUkr6V7O3BqZyg5qWy3JIJgw/4B69YLT57"
      crossorigin="anonymous"
    ></script>
    <title>远程诊断</title>
    <script src="<%= BASE_URL %>rcpConfig.js"></script>
    <!-- <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script> -->
    <!-- <script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script> -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/eruda/3.0.0/eruda.min.js"></script>
    <script>
      eruda.init();
    </script> -->
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <!-- 开启顶部安全区适配 -->
    <van-nav-bar safe-area-inset-top />
    <div id="app">
      <div class="loading">
        <div></div>
        <div></div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <!-- 开启底部安全区适配 -->
    <van-number-keyboard safe-area-inset-bottom />
  </body>
</html>
<style>
  /* html {
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
    filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1)
  } */

  .loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .loading > div {
    position: relative;
    box-sizing: border-box;
  }

  .loading {
    display: block;
    font-size: 0;
    color: #5d139c;
  }

  .loading.la-dark {
    color: #5d139c;
  }

  .loading > div {
    display: inline-block;
    float: none;
    background-color: currentColor;
    border: 0 solid currentColor;
  }

  .loading {
    width: 32px;
    height: 32px;
  }

  .loading > div {
    position: absolute;
    top: 50%;
    left: 50%;
    background: transparent;
    border-style: solid;
    border-width: 2px;
    border-radius: 100%;
    animation: ball-clip-rotate-multiple-rotate 1s ease-in-out infinite;
  }

  .loading > div:first-child {
    position: absolute;
    width: 32px;
    height: 32px;
    border-right-color: transparent;
    border-left-color: transparent;
  }

  .loading > div:last-child {
    width: 16px;
    height: 16px;
    border-top-color: transparent;
    border-bottom-color: transparent;
    animation-duration: 0.5s;
    animation-direction: reverse;
  }

  @keyframes ball-clip-rotate-multiple-rotate {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }

    50% {
      transform: translate(-50%, -50%) rotate(180deg);
    }

    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
</style>
