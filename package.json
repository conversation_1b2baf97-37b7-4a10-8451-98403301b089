{"name": "rcpc-app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode=dev", "build-dev": "vue-cli-service build --mode=dev", "build-prod": "vue-cli-service build --mode=production", "api": "rimraf ./src/services && pont list"}, "dependencies": {"@types/ali-oss": "^6.16.2", "@types/crypto-js": "^4.1.1", "@types/lodash.merge": "^4.6.7", "@types/openseadragon": "^2.4.5", "ali-oss": "^6.16.0", "aws-sdk": "^2.410.0", "axios": "^0.27.2", "browser-fs-access": "^0.23.0", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.5", "js-sha1": "^0.6.0", "konva": "^8.2.3", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "mitt": "^3.0.0", "nertc-web-sdk": "^4.6.25", "node-polyfill-webpack-plugin": "^2.0.1", "normalize.css": "^8.0.1", "openseadragon": "^2.4.2", "pdfh5": "^1.4.2", "pinia": "^2.0.18", "pinia-plugin-persist": "^1.0.0", "rimraf": "^3.0.2", "sdpc-web-ts": "git+http://**************/hugc/sdpc-web-ts.git", "uuid": "^3.4.0", "vant": "^3.6.3", "vue": "^3.2.13", "vue-class-component": "^8.0.0-0", "vue-clipboard3": "^2.0.0", "vue-router": "^4.0.3", "vue3-preview": "^1.0.5"}, "devDependencies": {"@types/lodash": "^4.14.184", "@types/uuid": "^8.3.3", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "pont-engine": "git+http://**************/hugc/pont.git", "postcss-px-to-viewport-8-plugin": "^1.1.5", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "~4.5.5", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^0.22.7"}}