const { VantResolver } = require("unplugin-vue-components/resolvers");
const ComponentsPlugin = require("unplugin-vue-components/webpack");
const { defineConfig } = require("@vue/cli-service");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const Timestamp = new Date().getTime()
module.exports = defineConfig({
  publicPath: "./",
  productionSourceMap: false,
  configureWebpack: {
    plugins: [
      ComponentsPlugin({
        resolvers: [VantResolver()],
      }),
      require("unplugin-auto-import/webpack")({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: ["vue", "vue-router", "pinia"],
        dts: "src/auto-import.d.ts",
        eslintrc: {
          enabled: true, // <-- this
        },
      }),
      new NodePolyfillPlugin(), //解决VUE3+webpack ＞5报错问题
    ],
    externals: {
      RcpWeb: "RcpWeb",
    },
    output: {
      filename: 'js/[name].' + Timestamp + '.js',
      chunkFilename: 'js/[name].' + Timestamp + '.js'
    }
  },
  chainWebpack: (config) => {
    config.module
      .rule("vue")
      .use("vue-loader")
      .tap((options) => {
        options.compilerOptions = options.compilerOptions || {};
        options.compilerOptions.isCustomElement = (tag) =>
          tag === "iconpark-icon";
        // modify the options...
        return options;
      });
  },
  transpileDependencies: true,
  lintOnSave: false,
  devServer: {
    port: 8889,
    //   // 配置代理
    //   proxy: {
    //     "/api": {
    //       // 以 “/api” 开头的 代理到 下边的 target 属性 的值 中
    //       target: "https://rcp.sqray.com:8889", // 代理的目标地址
    //       changeOrigin: true, // 是否改变域名：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
    //       ws: true, // 是否启用websockets
    //       pathRewrite: {
    //         // 路径重写
    //         "^/api": "", // 用'/api'代替target里面的地址,比如我要调用'http://40.00.100.100:3002/user/add'，直接写'http://127.0.0.1:8080/user/add'
    //       },
    //       headers: {
    //         referer: "https://rcp.sqray.com:8889", //这里后端做了拒绝策略限制，请求头必须携带referer，否则无法访问后台
    //       },
    //     },
    // },
  },
});
