// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AudioPlayer: typeof import('./src/components/audioPlayer/index.vue')['default']
    BottomBtn: typeof import('./src/components/bottomBtn/BottomBtn.vue')['default']
    CaseCheckBox: typeof import('./src/components/case-checkbox/CaseCheckBox.vue')['default']
    CaseFileContent: typeof import('./src/components/case-detail/CaseFileContent.vue')['default']
    CaseInfoBottomBtn: typeof import('./src/components/case-detail/CaseInfoBottomBtn.vue')['default']
    CaseInfoContent: typeof import('./src/components/case-detail/CaseInfoContent.vue')['default']
    CaseInfoTab: typeof import('./src/components/case-detail/CaseInfoTab.vue')['default']
    CaseOpinion: typeof import('./src/components/case-detail/CaseOpinion.vue')['default']
    CaseOverlay: typeof import('./src/components/case-overlay/CaseOverlay.vue')['default']
    CaseRadio: typeof import('./src/components/caseRadio/CaseRadio.vue')['default']
    CaseReplyLate: typeof import('./src/components/case-detail/CaseReplyLate.vue')['default']
    CaseSearch: typeof import('./src/components/case-list/CaseSearch.vue')['default']
    CaseSliceContent: typeof import('./src/components/case-detail/CaseSliceContent.vue')['default']
    Chatroom: typeof import('./src/components/Nim/src/Chatroom.vue')['default']
    Contactus: typeof import('./src/components/contactUs/Contactus.vue')['default']
    DoctorAdvice: typeof import('./src/components/case-detail/DoctorAdvice.vue')['default']
    DocxViewer: typeof import('./src/components/docxViewer/DocxViewer.vue')['default']
    ExpertFormation: typeof import('./src/components/fzCase-details/ExpertFormation.vue')['default']
    Feedback: typeof import('./src/components/contactUs/Feedback.vue')['default']
    FreezeTabBars: typeof import('./src/components/TabBars/FreezeTabBars.vue')['default']
    FrozenSearch: typeof import('./src/components/fzSearch/frozenSearch.vue')['default']
    FzCaseFileContents: typeof import('./src/components/fzCase-detail/FzCaseFileContents.vue')['default']
    FzCaseInfoBottomBtn: typeof import('./src/components/fzCase-detail/FzCaseInfoBottomBtn.vue')['default']
    FzCaseInfoBottomBtns: typeof import('./src/components/fzCase-details/FzCaseInfoBottomBtns.vue')['default']
    FzCaseInfoContent: typeof import('./src/components/fzCase-detail/FzCaseInfoContent.vue')['default']
    FzCaseInfoContents: typeof import('./src/components/fzCase-details/FzCaseInfoContents.vue')['default']
    FzCaseInfoTab: typeof import('./src/components/fzCase-detail/FzCaseInfoTab.vue')['default']
    FzCaseInfoTabs: typeof import('./src/components/fzCase-details/FzCaseInfoTabs.vue')['default']
    FzPatientInfo: typeof import('./src/components/fzCase-detail/FzPatientInfo.vue')['default']
    FzPatientInfos: typeof import('./src/components/fzCase-details/FzPatientInfos.vue')['default']
    FzSearch: typeof import('./src/components/fzSearch/index.vue')['default']
    FzTabBars: typeof import('./src/components/TabBars/FzTabBars.vue')['default']
    GeneralReport: typeof import('./src/components/report/general-report.vue')['default']
    ImageBrowser: typeof import('./src/components/image-viewer/components/imageBrowser/index.vue')['default']
    ImageEditor: typeof import('./src/components/image-viewer/components/imageEditor/index.vue')['default']
    ImageScales: typeof import('./src/components/image-viewer/components/imageScales/index.vue')['default']
    ImageViewer: typeof import('./src/components/image-viewer/ImageViewer.vue')['default']
    ImageViewerTop: typeof import('./src/components/image-viewer/ImageViewerTop.vue')['default']
    ImageZoom: typeof import('./src/components/image-viewer/components/imageZoom/index.vue')['default']
    Index: typeof import('./src/components/navBar/Index.vue')['default']
    Informatization: typeof import('./src/components/image-viewer/Informatization.vue')['default']
    InputMessage: typeof import('./src/components/inputMessage/InputMessage.vue')['default']
    Inspect: typeof import('./src/components/contactUs/Inspect.vue')['default']
    InvitingOpinions: typeof import('./src/components/case-detail/InvitingOpinions.vue')['default']
    KSlider: typeof import('./src/components/k-slider/KSlider.vue')['default']
    LeaveWord: typeof import('./src/components/case-detail/leaveWord.vue')['default']
    LiquidBasedReport: typeof import('./src/components/report/liquid-based-report.vue')['default']
    MedicalHistory: typeof import('./src/components/case-detail/MedicalHistory.vue')['default']
    MeetingBar: typeof import('./src/components/Nertc/src/MeetingBar.vue')['default']
    MeetingMainVideo: typeof import('./src/components/Nertc/src/MeetingMainVideo.vue')['default']
    MeetingVideoList: typeof import('./src/components/Nertc/src/MeetingVideoList.vue')['default']
    Member: typeof import('./src/components/Nim/src/member.vue')['default']
    MemberList: typeof import('./src/components/Nim/src/components/MemberList.vue')['default']
    MemberProvider: typeof import('./src/components/Nertc/src/components/MemberProvider.vue')['default']
    MessageBubble: typeof import('./src/components/Nim/src/components/MessageBubble.vue')['default']
    MessageBubbleAngleContainer: typeof import('./src/components/Nim/src/components/MessageBubbleAngleContainer.vue')['default']
    MessageList: typeof import('./src/components/Nim/src/components/MessageList.vue')['default']
    MessageNickname: typeof import('./src/components/Nim/src/components/MessageNickname.vue')['default']
    MessageProvider: typeof import('./src/components/Nim/src/components/MessageProvider.vue')['default']
    MessageSendStatus: typeof import('./src/components/Nim/src/components/MessageSendStatus.vue')['default']
    MessageSendTimeController: typeof import('./src/components/Nim/src/components/MessageSendTimeController.vue')['default']
    MessageTextBubble: typeof import('./src/components/Nim/src/components/MessageTextBubble.vue')['default']
    PatientInfo: typeof import('./src/components/case-detail/PatientInfo.vue')['default']
    PdfViewerH5: typeof import('./src/components/pdfViewer/PdfViewerH5.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SendMessage: typeof import('./src/components/Nim/src/components/SendMessage.vue')['default']
    SliceImage: typeof import('./src/components/case-list/SliceImage.vue')['default']
    TabBars: typeof import('./src/components/TabBars/TabBars.vue')['default']
    UpdateLog: typeof import('./src/components/contactUs/updateLog.vue')['default']
    UploadReport: typeof import('./src/components/uploadReport/UploadReport.vue')['default']
    VanArea: typeof import('vant/es')['Area']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanCol: typeof import('vant/es')['Col']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDatetimePicker: typeof import('vant/es')['DatetimePicker']
    VanDropdownItem: typeof import('vant/es')['DropdownItem']
    VanDropdownMenu: typeof import('vant/es')['DropdownMenu']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanRow: typeof import('vant/es')['Row']
    VanSearch: typeof import('vant/es')['Search']
    VanSlider: typeof import('vant/es')['Slider']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VersionsShow: typeof import('./src/components/versionsShow/versionsShow.vue')['default']
    VideoCard: typeof import('./src/components/Nertc/src/components/VideoCard.vue')['default']
    VideoCardBar: typeof import('./src/components/Nertc/src/components/VideoCardBar.vue')['default']
    YearToDate: typeof import('./src/components/yearToDate/YearToDate.vue')['default']
    YunXin: typeof import('./src/components/YunXin/src/YunXin.vue')['default']
  }
}
