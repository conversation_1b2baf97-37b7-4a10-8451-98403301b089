.flex {
  display: flex;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
/* 页面切换效果 slide-fade */
.slide-fade-enter-active {
  transition: all 0.1s ease-out;
}
.slide-fade-leave-active {
  transition: all 0.2s ease-out;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(50px);
  opacity: 0;
}

.g-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.g-ellipsis1 {
  overflow: hidden;
  display: -webkit-box;
  webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

.case-info-content {
  box-sizing: border-box;
  background: white;
  margin: 12px 14px 0 14px;
  border-radius: 4px;
}
.form-title {
  display: flex;
  align-items: center;
  line-height: 30px;
  font-size: 14px;
  font-weight: bold;
  padding: 0 14px;
  color: #343840;
  border-bottom: 1px solid #e6ecfb;
}
.form-title :nth-child(1) {
  margin-right: 6px;
  width: 6px;
  height: 6px;
  background: #650faa;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

/* 占位 */
.occupied {
  height:99px ;
}
