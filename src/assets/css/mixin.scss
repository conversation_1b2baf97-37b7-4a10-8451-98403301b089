// 颜色定义规范
$color-background : #FFFFFF;
$color-background-d : rgba(0, 0, 0, 0.3);
$color-highlight-background : #333;
$color-dialog-background : #666;
$color-theme : #ffcd32;
$color-theme-d : rgba(255, 205, 49, 0.5);
$color-sub-theme : #d93f30;
$color-text-d : rgba(255, 255, 255, 0.3);
$color-text-l : rgba(255, 255, 255, 0.5);
$color-text-ll : rgba(255, 255, 255, 0.8);

$font-gray : #999;

//字体定义规范
$font-size-small-s : 10px;
$font-size-small : 12px;
$font-size-medium : 14px;
$font-size-medium-x : 16px;
$font-size-large : 18px;
$font-size-large-x : 22px;

$font-weight : 600;

body,html{
  //background: rgb(239, 242, 249);
}

//背景图片 100%
@mixin bkgMaxSize($url) {
  background-image: url($url);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

@mixin font-setting-group($font-size,$font-family,$font-weight,$color,$line-height){
  font-size: $font-size;
  font-family: $font-family;
  font-weight: $font-weight;
  color: $color;
  line-height: $line-height;
}

//边框圆角
@mixin borderRadius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  -o-border-radius: $radius;
  border-radius: $radius;
}

//定位上下左右居中
@mixin positionCenter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

//定位上下居中
@mixin ct {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

//定位左右居中
@mixin cl {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

//定位全屏
@mixin allcover {
  position: absolute;
  top: 0;
  right: 0;
}

//相对定位
@mixin my-absolute($left, $top,$z) {
  position: absolute;
  z-index: $z;
  margin-left: $left;
  margin-top: $top;
}

//宽高-不同
@mixin widthHeightN($width, $height){
  width: $width;
  height: $height;
}
//宽高-相同
@mixin widthHeightY($number){
  width: $number;
  height: $number;
}

//字体大小，颜色
@mixin sizeColor($size, $color){
  font-size: $size;
  color: $color;
}


//flex布局
@mixin center_none{
  display: flex;
  justify-content: center;
  align-items: center;
}


@mixin center_center{
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-start_center{
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

@mixin space-between_center{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@mixin space-around_center{
  display: flex;
  justify-content: space-around;
  align-items: center;
}



@mixin flex-end_center{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

@mixin wrap_flex-start{
  display: flex;
  flex-wrap:wrap;
  align-content:flex-start;
}


@mixin flex-start_column{
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
}
@mixin none_center_column{
  display: flex;
  align-items: center;
  flex-direction: column;
}

@mixin center_center_column{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
} 
