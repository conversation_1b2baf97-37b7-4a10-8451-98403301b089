declare namespace appEntity {
  /**
   * 用户信息
   */
  export class UserConfig {
    /** Account */
    Account?: string;
    /** AreaCode */
    AreaCode?: string;
    /** AreaName */
    AreaName?: string;
    /** Avatar */
    Avatar?: string;
    /** HeadPic */
    HeadPic?: string;
    /** Id */
    Id?: number;
    /** IsSiteAdmin */
    IsSiteAdmin?: number;
    /** Level */
    Level?: string;
    /** PlatformId */
    PlatformId?: number;
    /** RealName */
    RealName?: string;
    /** RoleId */
    RoleId?: number;
    /** RoleName */
    RoleName?: string;
    /** Signature */
    Signature?: string;
    /** SiteId */
    SiteId?: number;
    /** SiteModel */
    SiteModel?: defs.RcpSite;
    /** SiteName */
    SiteName?: string;
    /** Status */
    Status?: number;
    /** SubCenterId */
    SubCenterId?: number;
  }
  /**
   *
   */
  export class RcpSite {
    /** 管理员账号 */
    Account: string;

    /** 地址 */
    Address?: string;

    /** 所属地区编码 */
    AreaCode?: string;

    /** 所属地区名称 */
    AreaName?: string;

    /** 站点缩写-会诊编号前缀 */
    Code?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 自增Id */
    Id?: number;

    /** 会诊模式-是否直接提交专家 */
    IsDiagnosticMode?: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 站点名称 */
    Name?: string;

    /** 所属机构 */
    OrganizationId?: number;

    /** 所属平台 */
    ParentPlatformId: number;

    /** 电话 */
    Phone?: string;

    /** 所属分中心-平台表分中心的Id */
    PlatformId: number;

    /** 启用状态 */
    Status?: number;

    /** 编辑时间 */
    UpdateTime?: string;

    /** 编辑人 */
    UpdateUser?: string;

    /** 编辑人名称 */
    UpdateUserName?: string;
  }

  /**
   *
   */
  export class UserConfigApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.UserConfig;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export interface ReportInfoResult {
    /** 地址 */
    Address?: string;

    /** 条形码 */
    BarCode?: string;

    /** 床号 */
    BedNumber?: string;

    /** 上传时间 */
    CaseCreateTime?: string;

    /** 送检医生 */
    CaseDoctorName?: string;

    /** 送检类型 */
    CaseType?: string;

    /** 送检类型名称 */
    CaseTypeName?: string;

    /** 送检项目 */
    CheckItems?: string;

    /** 送检项目名称 */
    CheckItemsName?: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 报告生成时间 */
    CreateTime?: string;

    /** 报告医生级别 */
    DoctorLevel?: string;

    /** 报告医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 报告医生签名 */
    DoctorSignature?: string;

    /** 初审医生签名 */
    DoctorSignatureUrl?: string;

    /** 机构英文名称 */
    EName?: string;

    /** 机构 */
    Hospital?: string;

    /** 机构英文 */
    HospitalEName?: string;

    /** 送检医院 */
    HospitalName?: string;

    /** 住院号 */
    HospitalizedId?: string;

    /** 科别 */
    InspectionDept?: string;

    /** 报告模板 Txt Pdf */
    ModuleType?: string;

    /** 报告名称 */
    Name?: string;

    /** 机构联系电话 */
    OrganizePhone?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 患者年龄 */
    PatientAge?: string;

    /** 患者姓名 */
    PatientName?: string;

    /** 患者电话 */
    PatientPhone?: string;

    /** 性别code */
    PatientSex?: number;

    /** 平台Id */
    PlatformId?: number;

    /** 报告内容Json */
    ReportJsonData?: string;

    /** 复诊医生级别 */
    ReviewDoctorLevel?: string;

    /** 复诊医生名称 */
    ReviewDoctorName?: string;

    /** 复诊医生签名 */
    ReviewDoctorSignature?: string;

    /** 复审医生签名 */
    ReviewDoctorSignatureUrl?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别名称 */
    SexName?: string;

    /** 预约时间 */
    SubscribeTime?: string;

    /** 大体描述 */
    Thus?: string;

    /** 订单流水号 */
    TurnoverId?: string;
  }

  export interface ReportJsonResult<T> {
    GeneralDescription: string;
    Images: T;
    DiagnosisOpinion: string;
  }

  export interface ReportImageResult {
    Sort: number;
    //图像路径
    ImageUrl: string;
    //图像描述
    Remark: string;
  }
}
