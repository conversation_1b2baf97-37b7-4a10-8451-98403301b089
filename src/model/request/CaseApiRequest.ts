import { BasePageRequest } from "./BasePageRequest";
/**
 * 获取切片列表
 */
export interface GetExpertRequest {
  caseType: number;
}
/**
 * 查看病例列表
 */
export interface GetCaseListRequest extends BasePageRequest {
  StartingTime?: string;
  EndTime?: string;
}

/**
 * 预签名请求
 */
export interface GetPreSignerRequest {
  BucketName: string;
  ObjectName: string;
  MethodType: 1;
  TimeExpert: 3600;
}

/**
 * 查看病例详情/
 */
export interface CaseIdRequest {
  id: number;
}

/*
 * 获取切片列表
 */
export interface CaseListRequest {
  id: number;
  pageIndex: number;
  pageSize: number;
}
