export interface GetCaseInfoResponse {
  Age: string;
  BarCode?: string;
  BedNumber: string;
  CaseType: string;
  CaseTypeName: string;
  CheckItems: string;
  CheckItemsName: string;
  ClinicalData: string;
  ClinicalDiagnosis: string;
  DiagnosisContent: string;
  DoctorName: string;
  DoctorPhone: string;
  EthnicId: number;
  EthnicName: string;
  ExpertModel: string;
  HospitalizedId: string;
  Id: string;
  IdNumber: string;
  Immunohistochemical: string;
  InpatientWard: string;
  Inspection: string;
  InspectionDept: string;
  IsRefractoryDisease: string;
  Job: string;
  MaritalStatus: number;
  MaritalStatusName: string;
  MenstruationDate: string;
  Name: string;
  PathologyNumber: string;
  PatientId: number;
  Phone: string;
  Remark: string;
  SampleLocationCode: string;
  SampleLocationName: string;
  SamplingTime: string;
  Sex: number;
  SexName: string;
  SubProfessional: string;
  SubProfessionalName: string;
  SubscribeTime: string;
  Thus: string;
  VisitingNumber: string;
}
