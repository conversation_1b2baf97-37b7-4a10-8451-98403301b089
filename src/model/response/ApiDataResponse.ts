/**
 * 通用返回泛型
 */
export interface CommonResponse<T> {
  count: number;
  message: string;
  success: boolean;
  code: number;
  data: T;
}

/**
 * 用户登录
 */
export interface UserLoginResponse {
  Account: string;
  Password: string;
  VerifyCode: string;
}
export interface GetSliceListResponse {
  Id: number;
  IsPrint?: number;
  PatientId: number;
  PathologyNumber?: string;
  Sex?: number;
  SexName?: string;
  Age: number;
  CaseTypeCode: string;
  CaseTypeName: string;
  CheckItemsCode: string;
  CheckItemsName: string;
  Inspection: string;
  SiteId: number;
  SampleLocationCode: string;
  SampleLocationName: string;
  TurnoverId: string;
  Name: string;
  Time: string;
  Status: number;
  StatusName: string;
  SliceThumbnail: string;
}
