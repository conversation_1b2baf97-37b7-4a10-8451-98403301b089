export class AccountExistModel {
  /** 字符串是否存在 */
  ExistStr = '';

  /** id */
  Id = undefined;
}

export class AddCaseDto {
  /** AdviceInfolist */
  AdviceInfolist = [];

  /** AdviceType */
  AdviceType = '';

  /** CardType */
  CardType = '';

  /** DiagnoseInfolist */
  DiagnoseInfolist = [];

  /** PatHisNo */
  PatHisNo = '';

  /** PatVisitNumber */
  PatVisitNumber = '';

  /** PatientCard */
  PatientCard = '';
}

export class AddCaseIdReqModel {
  /** 病例id */
  CaseId = undefined;

  /** 是否为冰冻 */
  Frozen = false;

  /** 姓名 */
  Name = '';

  /** 医嘱Id */
  OrderId = '';

  /** 卡号 */
  PatCardNo = '';

  /** 登记号 */
  PatHisNo = '';

  /** 就诊号 */
  PatVisitNumber = '';
}

export class AddDocAdviceResDto {
  /** AdviceOrderVoList */
  AdviceOrderVoList = [];

  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class AddDocAdviceResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new AddDocAdviceResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AddTenantUserRelationModel {
  /** 关联租户及角色 */
  TenantRoles = [];

  /** 用户Id */
  UserId = undefined;
}

export class AdminDto {
  /** 患者年龄 */
  Age = '';

  /** 病理类型 */
  CaseType = '';

  /** 病理子类型 */
  CheckItems = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 提交时间 */
  CreateTime = '';

  /** 初审专家 */
  ExpertName = '';

  /** 病例id */
  Id = undefined;

  /** 患者姓名 */
  Name = '';

  /** 专家诊断 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 切片质量 */
  Quality = '';

  /** 复核专家 */
  ReviewExpertName = '';

  /** 患者性别 */
  Sex = '';

  /** 申请单位 */
  SiteName = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class AdminDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminExpert {
  /** 账号 */
  Account = '';

  /** 市 */
  CityCode = undefined;

  /** 市 */
  CityName = '';

  /** 专家名称 */
  ExpertName = '';

  /** 昵称 */
  ExpertTypeName = '';

  /** 职务 */
  ExpertleveName = '';

  /** 头像 */
  HeadPic = '';

  /** 头像 */
  HeadPicUrl = '';

  /** Id */
  Id = undefined;

  /** 电话 */
  Phone = '';

  /** 省 */
  ProvinceCode = undefined;

  /** 省 */
  ProvinceName = '';

  /** 状态 */
  Status = undefined;

  /** 分中心id */
  SubCenterId = undefined;

  /** false不存在 / ture存在于表中   用户是否存在 */
  UserExist = false;
}

export class AdminExpertPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminListDto {
  /** 账号 */
  Account = '';

  /** id */
  Id = undefined;

  /** 是否为工程师 */
  IsEngineer = false;

  /** 名称 */
  Name = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** false不存在 / ture存在于表中   用户是否存在 */
  UserExist = false;
}

export class AdminListDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminQualityControlStatisticsDto {
  /** 年龄 */
  Age = '';

  /** 病例质量评价 */
  CaseQualityAssessmentRemarks = '';

  /** 病例质量评价 */
  CaseQualityAssessments = '';

  /** 送检项目 */
  CheckItems = '';

  /** 发布时间 */
  CompleteTime = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 提交时间 */
  CreateTime = '';

  /** 诊断符合率备注 */
  DiagnosticCoincidenceRateRemarks = '';

  /** 诊断符合率 */
  DiagnosticCoincidenceRates = '';

  /** 诊断专家 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** 姓名 */
  Name = '';

  /** 专家诊断 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 切片质量评价 */
  Quality = '';

  /** 评价专家 */
  QualityExpertName = '';

  /** 复核专家 */
  ReviewExpertName = '';

  /** 性别 */
  Sex = '';

  /** 诊断中心 */
  SiteName = '';

  /** 切片质量评价备注 */
  SliceQualityAssessmentsRateRemarks = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class AdminQualityControlStatisticsDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminRemoteManagementDto {
  /** 年龄 */
  Age = '';

  /** 病例类型 */
  CaseTypeCode = '';

  /** 病例类型名称 */
  CaseTypeName = '';

  /** 测试项目 */
  CheckItemsCode = '';

  /** 测试项目名称 */
  CheckItemsName = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 创建时间 */
  CreateTime = '';

  /** 复审专家 */
  ExpertId = undefined;

  /** 诊断专家 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 送检医院/单位 */
  Inspection = '';

  /** 打印 */
  IsPrint = undefined;

  /** 名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 切片质量 */
  Quality = undefined;

  /** 切片质量 */
  QualityName = '';

  /** 复审专家 */
  ReviewExpertId = undefined;

  /** 复审专家 */
  ReviewExpertName = '';

  /** 年龄 */
  Sex = undefined;

  /** 年龄名称 */
  SexName = '';

  /** 站点 */
  SiteId = undefined;

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科CODE */
  SubProfessionalCode = '';

  /** 亚专科 */
  SubProfessionalName = '';

  /** 是否超时 */
  TimeOutName = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class AdminRemoteManagementDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminUpdateDto {
  /** 机构id */
  DepartmentId = '';

  /** 机构名称 */
  DepartmentName = '';

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 头像Url */
  HeadPicUrl = '';

  /** id */
  Id = undefined;

  /** 是否为工程师 */
  IsEngineer = false;

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 名称 */
  TrueName = '';
}

export class AdminUpdateDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new AdminUpdateDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdminWordDto {
  /** 内容 */
  Concent = '';

  /** 留言时间 */
  CreateTime = '';

  /** id */
  Id = undefined;

  /** 留言人名称 */
  Name = '';

  /** 被留言方 */
  ParentUserName = '';

  /** 病例号 */
  PathologyNumber = '';
}

export class AdminWordDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AdviceInfolistItem {
  /** AdviceCode */
  AdviceCode = '';

  /** AdviceEnterDeptCode */
  AdviceEnterDeptCode = '';

  /** AdviceEnterDocCode */
  AdviceEnterDocCode = '';

  /** AdviceEnterExecCode */
  AdviceEnterExecCode = '';

  /** AdvicePriorityCode */
  AdvicePriorityCode = '';

  /** AdviceQty */
  AdviceQty = '';

  /** ThirdOrderId */
  ThirdOrderId = '';
}

export class AdviceOrderVoListItem {
  /** HisAdviceId */
  HisAdviceId = '';

  /** RisAdviceId */
  RisAdviceId = '';

  /** ThirdOrderId */
  ThirdOrderId = '';
}

export class AmericaConfig {
  /** 网易云信是否开启 */
  NetEaseYunxin = false;

  /** 短信是否开启 */
  Sms = false;
}

export class AmericaConfigApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new AmericaConfig();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AmountDto {
  /** 平台金额 */
  Amount = '';

  /** 平台金额 */
  PlatformAmount = '';
}

export class AmountDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new AmountDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AnhuiReportStatusReModel {
  /** 医嘱id */
  OrderId = '';

  /** his订单号 */
  PatHisNo = '';
}

export class AnnexBaseReqModel {
  /** 文件磁盘路径 */
  DiskUrl = '';

  /** id */
  Id = undefined;

  /** 文件名称 */
  ImageName = '';

  /** 文件大小 */
  ImageSize = undefined;

  /** 文件类型 */
  ImageType = '';

  /** 状态 */
  Status = undefined;

  /** 文件Http路径 */
  Url = '';
}

export class AnnexDto {
  /** 附件url */
  AnnexUrl = '';

  /** 病例ID */
  CaseId = undefined;

  /** 上传时间 */
  CreateTime = '';

  /** 上传用户 */
  CreateUser = '';

  /** 上传用户名称 */
  CreateUserName = '';

  /** 文件磁盘路径 */
  DiskUrl = '';

  /** 图片id */
  Id = undefined;

  /** 图片名称 */
  ImageName = '';

  /** 文件大小 */
  ImageSize = undefined;

  /** 文件类型 */
  ImageType = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 附件类型name */
  TypeName = '';

  /** 文件Http路径 */
  Url = '';
}

export class AnnexDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AnnexReqModel {
  /** 文件磁盘路径 */
  DiskUrl = '';

  /** Id */
  Id = undefined;

  /** 文件名称 */
  ImageName = '';

  /** 文件大小 */
  ImageSize = undefined;

  /** 文件类型 */
  ImageType = '';

  /** 状态 */
  Status = undefined;

  /** 文件Http路径 */
  Url = '';
}

export class ApiResult {
  /** 状态码 */
  Code = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ApplicationManagementDto {
  /** 年龄 */
  Age = '';

  /** 病例类型 */
  CaseType = '';

  /** 病例类型名称 */
  CaseTypeName = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 病例id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 诊断中心 */
  SiteName = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class ApplicationManagementDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class AssignExpertReqModel {
  /** 专家ID */
  ExpertId = undefined;

  /** 专家名称 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 是否为冰冻预约 */
  IsFrozen = false;
}

export class Assigned {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** 专家名称 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 意向专家 */
  IntentPersonName = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 复核专家 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** SliceThumbnail */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class AssignedPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class BasePatientByIdNoReqDto {
  /** IdNo */
  IdNo = '';

  /** IdType */
  IdType = '';

  /** Name */
  Name = '';

  /** Phone */
  Phone = '';
}

export class BasePatientByIdNoResDto {
  /** PatientCardDetailVoList */
  PatientCardDetailVoList = [];

  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class BasePatientByIdNoResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new BasePatientByIdNoResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class BoolDto {
  /** IsFalse */
  IsFalse = false;
}

export class BoolDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new BoolDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class BooleanApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = false;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class BulkAllocationReqModel {
  /** 专家ID */
  ExpertId = undefined;

  /** 专家名称 */
  ExpertName = '';

  /** 病例id集合 */
  Id = [];

  /** 是否为冰冻预约 */
  IsFrozen = false;
}

export class BusinessTypeChargeParam {
  /** 业务类型id */
  Id = undefined;

  /** 金额 */
  Money = '';

  /** 站点id */
  SiteId = undefined;
}

export class CancelDocAdviceItem {
  /** HisAdviceId */
  HisAdviceId = '';
}

export class CancelDocAdviceReqDto {
  /** AdviceInfolist */
  AdviceInfolist = [];

  /** PatHisNo */
  PatHisNo = '';

  /** PatVisitNumber */
  PatVisitNumber = '';

  /** UpdateUserCode */
  UpdateUserCode = '';

  /** UpdateUserDesc */
  UpdateUserDesc = '';
}

export class CancelDocAdviceResDto {
  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class CancelDocAdviceResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CancelDocAdviceResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CancelRegisterReqDto {
  /** AdmNo */
  AdmNo = '';
}

export class CancelRegisterResDto {
  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class CancelRegisterResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CancelRegisterResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseBusTypeChargeDto {
  /** CaseBusTypeCharge */
  CaseBusTypeCharge = [];

  /** Id */
  Id = undefined;

  /** 金额 */
  Money = '';

  /** 名称 */
  Name = '';

  /** 父级id */
  ParentId = undefined;
}

export class CaseBusTypeChargeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseChargesDto {
  /** CaseChargesModels */
  CaseChargesModels = new CaseChargesModel();

  /** 缴费状态 */
  CaseChargingStatus = undefined;

  /** 收费时间 */
  ChargingTime = '';

  /** 会诊专家名称 */
  ExpertName = '';

  /** 病例id */
  Id = undefined;

  /** MedicalAdviceFees */
  MedicalAdviceFees = new MedicalAdviceFees();

  /** 姓名 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 关联病史 */
  PathologyNumberList = [];

  /** 复核专家名称 */
  ReviewExpertName = '';

  /** 站点名称 */
  SiteName = '';

  /** 提交时间 */
  Time = '';
}

export class CaseChargesDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseChargesModel {
  /** 实际支付金额 */
  ActualPaymentAmount = '';

  /** 安徽省立金额 */
  AnhuiProvincialGovernmentAmount = '';

  /** 收费状态 */
  CaseChargingStatus = undefined;

  /** 收费时间 */
  CaseChargingTime = '';

  /** 收费状态 */
  ChargingStatus = '';

  /** 诊断时间 */
  DiagnosisTime = '';

  /** 诊断状态 */
  DiagnosticStatus = '';

  /** 诊断专家团队所属医院金额 */
  HospitalAmount = '';

  /** 平台金额 */
  PlatformAmount = '';

  /** 项目类型 */
  ProjectType = '';

  /** 病例类型 */
  SettlementType = '';

  /** 站点金额 */
  SiteAmount = '';
}

export class CaseConfig {
  /** 配置项编号 */
  Code = '';

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 自增ID */
  Id = undefined;

  /** 配置项名称 */
  Name = '';

  /** 平台Id */
  PlatformId = '';

  /** 备注 */
  Remark = '';

  /** 站点Id */
  SiteId = '';

  /** 站点名称 */
  SiteName = '';

  /** 启用状态 */
  Status = undefined;

  /** 配置值 */
  Value = '';

  /** 配置值2 */
  Value2 = '';
}

export class CaseConfigPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseCreateReqModel {
  /** 年龄 */
  Age = '';

  /** 附件 */
  AnnexList = [];

  /** 会诊号 */
  BarCode = '';

  /** 床号 */
  BedNumber = '';

  /** 评价 */
  CaseSiteEvaluateList = [];

  /** 病例类型 */
  CaseType = '';

  /** 送检项目 */
  CheckItems = '';

  /** 临床资料 */
  ClinicalData = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 留言类容 */
  Content = '';

  /** 原病理诊断 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 民族 */
  EthnicId = undefined;

  /** 住院号 */
  HospitalizedId = '';

  /** 身份证号 */
  IdNumber = '';

  /** 免疫组化 */
  Immunohistochemical = '';

  /** 病房 */
  InpatientWard = '';

  /** 送检单位 */
  Inspection = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 意向专家Id */
  IntentPerson = undefined;

  /** 意向专家名称 */
  IntentPersonName = '';

  /** 是否为疑难病 */
  IsRefractoryDisease = undefined;

  /** 工作 */
  Job = '';

  /** 婚姻状况 */
  MaritalStatus = undefined;

  /** 女性月经时间 */
  MenstruationDate = '';

  /** 患者名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 联系电话 */
  Phone = '';

  /** 备注信息 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 采样日期 */
  SamplingTime = '';

  /** 性别 */
  Sex = undefined;

  /** 切片 */
  SliceList = [];

  /** 亚专科 */
  SubProfessional = '';

  /** 预约日期 */
  SubscribeTime = '';

  /** 大体可见 */
  Thus = '';

  /** 会诊编号 */
  TurnoverId = '';

  /** 就诊号 */
  VisitingNumber = '';
}

export class CaseDataWithQrDto {
  /** AnnexList */
  AnnexList = new AnnexDtoPageResult();

  /** CaseCollection */
  CaseCollection = new BoolDto();

  /** 病例报告单 */
  Report = '';

  /** SliceList */
  SliceList = new CaseSliceDtoPageResult();

  /** WriteDiagnosticsDto */
  WriteDiagnosticsDto = new WriteDiagnosticsDto();
}

export class CaseDataWithQrDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseDataWithQrDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseDto {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** 缩略图 */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class CaseDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseFrozen {
  /** 年龄 */
  Age = '';

  /** 预约日期 */
  AppointmentTime = '';

  /** 送检类型 */
  CaseType = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 临床病历 */
  ClinicalMedicalRecords = '';

  /** 完成时间 */
  CompleteTime = '';

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 医生名称 */
  Doctor = '';

  /** 自增id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 预约号码 */
  Number = '';

  /** 平台id */
  PlatformId = undefined;

  /** 备注 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 性别 */
  Sex = undefined;

  /** 站点id */
  SiteId = undefined;

  /** 冰冻状态 */
  Status = undefined;

  /** 分中心id */
  SubCenterId = undefined;

  /** 手术所见 */
  SurgicalFindings = '';

  /** 订单编号 */
  TurnoverId = '';

  /** 病人id */
  UserId = undefined;

  /** 医生号码 */
  UserPhone = '';
}

export class CaseFrozenApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseFrozen();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseHistoricalDiagnosticRecordsDto {
  /** 申请站点 */
  ApplicationSite = '';

  /** CheckItemsCode */
  CheckItemsCode = '';

  /** 送检项目名称 */
  CheckItemsName = '';

  /** 会诊专家 */
  ConsultationExpert = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 诊断时间 */
  DiagnosisTime = '';

  /** Id */
  Id = undefined;

  /** 身份证 */
  IdNumber = '';

  /** 名称 */
  Name = '';

  /** 原病理号 */
  OriginalPathologicalNumber = '';

  /** 发布时间 */
  ReleaseTime = '';

  /** 复核专家 */
  ReviewExpert = '';
}

export class CaseHistoricalDiagnosticRecordsDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseInforDto {
  /** 年龄 */
  Age = '';

  /** 病理类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 是否选中 */
  Check = false;

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 诊断专家 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 复核专家 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 缩略图 */
  SliceThumbnail = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class CaseInforDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseLabelParam {
  /** CaseId */
  CaseId = undefined;

  /** 标签id */
  LabelId = undefined;
}

export class CaseLeavemsgDto {
  /** 内容 */
  Content = '';

  /** 留言时间 */
  CreateTime = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 留言方 */
  ParentUserName = '';

  /** 病例号 */
  PathologyNumber = '';
}

export class CaseLeavemsgDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseMedicalAdviceFee {
  /** 病例id */
  CaseId = undefined;

  /** Id */
  Id = undefined;

  /** 医嘱id */
  JsonId = '';

  /** 医嘱金额 */
  OrderAmount = '';
}

export class CaseMedicalAdviceFeeApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseMedicalAdviceFee();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseMedicalHistoryModel {
  /** 关联病例 CaseId */
  AssociatedCaseId = undefined;

  /** 当前病例 Id */
  CurrentCaseId = undefined;
}

export class CaseMessageDto {
  /** 内容 */
  Content = '';

  /** Id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 时间 */
  Time = '';
}

export class CaseMessageDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseOperationRecordsReqModel {
  /** 入参 */
  Parameter = '';

  /** 路径名称 */
  PathName = '';

  /** 返回参数 */
  ReturnData = '';

  /** 路径 */
  Url = '';
}

export class CaseQualityAssessment {
  /** CaseInformationId */
  CaseInformationId = undefined;

  /** 病例质量评价备注 */
  CaseQualityAssessmentRemarks = '';

  /** 病例质量评价 */
  CaseQualityAssessments = undefined;

  /** 诊断符合率备注 */
  DiagnosticCoincidenceRateRemarks = '';

  /** 诊断符合率 */
  DiagnosticCoincidenceRates = undefined;

  /** 自增Id */
  Id = undefined;

  /** 切片质量评价备注 */
  SliceQualityAssessmentsRateRemarks = '';

  /** 指控评价用户ID */
  UserId = undefined;
}

export class CaseQualityAssessmentApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseQualityAssessment();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseQualityAssessmentDto {
  /** 年龄 */
  Age = '';

  /** 质控评价ID */
  CaseQualityAssessmentId = undefined;

  /** 病例质量评价 */
  CaseQualityAssessmentsRateText = '';

  /** CaseQualityAssessmentsRates */
  CaseQualityAssessmentsRates = undefined;

  /** 病例类型 */
  CaseType = '';

  /** 病例类型 */
  CaseTypeText = '';

  /** 发布时间 */
  CompleteTime = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 诊断时间 */
  DiagnosisTime = '';

  /** 诊断符合率文本 */
  DiagnosticCoincidenceRateText = '';

  /** 诊断符合率 */
  DiagnosticCoincidenceRates = undefined;

  /** 专家名称 */
  ExpertName = '';

  /** 病例ID */
  Id = undefined;

  /** 患者名称 */
  Name = '';

  /** 意见 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人表id */
  PatientId = undefined;

  /** 质控专家 */
  QualityControlExpert = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexText = '';

  /** 站点id */
  SiteId = undefined;

  /** 诊断中心 */
  SiteName = '';

  /** 切片质量评价 */
  SliceEvaluation = undefined;

  /** 切片质量评价文本 */
  SliceEvaluationText = '';

  /** 提交时间 */
  SubmissionTime = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class CaseQualityAssessmentDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseQualityAssessmentLogDto {
  /** 病例质量评价备注 */
  CaseQualityAssessmentRemarks = '';

  /** 病例质量评价文本 */
  CaseQualityAssessmentText = undefined;

  /** Code */
  Code = '';

  /** 诊断符合率备注 */
  DiagnosticCoincidenceRateRemarks = '';

  /** 诊断符合率文本 */
  DiagnosticCoincidenceRateText = undefined;

  /** 记录的操作名称 */
  LogMethod = '';

  /** 记录简讯 */
  LogMsg = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 切片质量评价备注 */
  SliceQualityAssessmentsRateRemarks = '';

  /** 切片质量评价List */
  SliceQualityAssessmentsRateTexts = [];
}

export class CaseQualityAssessmentSingleDto {
  /** CaseInformationId */
  CaseInformationId = undefined;

  /** 病例质量评价备注 */
  CaseQualityAssessmentRemarks = '';

  /** 病例质量评价 */
  CaseQualityAssessments = undefined;

  /** 切片评价List */
  CaseUploadslices = [];

  /** 诊断符合率备注 */
  DiagnosticCoincidenceRateRemarks = '';

  /** 诊断符合率 */
  DiagnosticCoincidenceRates = undefined;

  /** 自增Id */
  Id = undefined;

  /** 切片质量评价备注 */
  SliceQualityAssessmentsRateRemarks = '';

  /** 指控评价用户ID */
  UserId = undefined;

  /** 指控评价用户名称 */
  UserName = '';
}

export class CaseQualityAssessmentSingleDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseQualityAssessmentSingleDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseRenderingDto {
  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 床号 */
  BedNumber = '';

  /** 评价 */
  CaseSiteEvaluateList = [];

  /** 病例类型 */
  CaseType = '';

  /** 病例类型名称 */
  CaseTypeName = '';

  /** 送检项目 */
  CheckItems = '';

  /** 送检项目名称 */
  CheckItemsName = '';

  /** 临床资料 */
  ClinicalData = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 提交时间 */
  CreateTime = '';

  /** 原病理诊断 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 民族 */
  EthnicId = undefined;

  /** 民族 */
  EthnicName = '';

  /** 初审专家 */
  ExpertName = '';

  /** 住院号 */
  HospitalizedId = '';

  /** 病例id */
  Id = undefined;

  /** 身份证号 */
  IdNumber = '';

  /** 免疫组化 */
  Immunohistochemical = '';

  /** 病房 */
  InpatientWard = '';

  /** 送检单位 */
  Inspection = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 是否为疑难病 */
  IsRefractoryDisease = undefined;

  /** 工作 */
  Job = '';

  /** 婚姻状况 */
  MaritalStatus = undefined;

  /** 婚姻状况名称 */
  MaritalStatusName = '';

  /** 女性月经时间 */
  MenstruationDate = '';

  /** 患者名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 病人id */
  PatientId = undefined;

  /** 联系电话 */
  Phone = '';

  /** 备注信息 */
  Remark = '';

  /** 复核专家 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位 */
  SampleLocationName = '';

  /** 采样日期 */
  SamplingTime = '';

  /** 性别 */
  Sex = undefined;

  /** 性别name */
  SexName = '';

  /** 状态 */
  Status = undefined;

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 预约日期 */
  SubscribeTime = '';

  /** 大体可见 */
  Thus = '';

  /** 就诊号 */
  VisitingNumber = '';
}

export class CaseRenderingDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CaseRenderingDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseRenderingDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseReportReqModel {
  /** 细胞量 */
  CellMass = '';

  /** 镜下描述 */
  Description = '';

  /** 电镜描述 */
  ElectronMicroscopyDescription = '';

  /** 免疫组化 */
  Fluorescence = '';

  /** id */
  Id = undefined;

  /** 炎症细胞 */
  InflammatoryCells = '';

  /** json数据 */
  JsonData = '';

  /** 光镜所见 */
  LightMicroScope = '';

  /** 意见 */
  Opinion = '';

  /** 切片质量评价 */
  Quality = undefined;

  /** 附注意见 */
  Suggest = '';

  /** TBS标准诊断 */
  Tbs = '';

  /** 阴阳 1为阴2为阳 */
  YinPositive = undefined;
}

export class CaseReturnedDto {
  /** 账号 */
  Account = '';

  /** 退回时间 */
  ApplyTime = '';

  /** 属性 */
  Attribute = '';

  /** id */
  Id = undefined;

  /** 名字 */
  Name = '';

  /** 退回原因类型 */
  ReasonType = '';

  /** 审批备注 */
  Remark = '';
}

export class CaseReturnedDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseSiteEvaluate {
  /** 类型 */
  BusType = '';

  /** 病例id */
  CaseId = undefined;

  /** 值 */
  Code = undefined;

  /** CreateTime */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** 站点id */
  SiteId = undefined;
}

export class CaseSliceDto {
  /** 病例Id */
  CaseId = undefined;

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 磁盘路径 */
  DiskPath = '';

  /** 染色 */
  Dyeing = '';

  /** 染色名称 */
  DyeingName = '';

  /** 评价值 */
  Evaluation = undefined;

  /** FileName */
  FileName = '';

  /** FileSize */
  FileSize = undefined;

  /** FileType */
  FileType = '';

  /** Id */
  Id = undefined;

  /** 标签 */
  Label = '';

  /** 宏观图 */
  MacroImageUrl = '';

  /** 备注 */
  Remark = '';

  /** 标注文件 */
  Sdpl = '';

  /** 切片评价 */
  SliceDescription = '';

  /** 磁盘路径 */
  SliceDiskPath = '';

  /** 标签 */
  SliceLabel = '';

  /** 宏观图 */
  SliceMacroImageUrl = '';

  /** 缩略图 */
  SliceThumbnail = '';

  /** 切片类型 */
  SliceType = undefined;

  /** 切片路径 */
  SliceUrl = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 缩略图 */
  Thumbnail = '';

  /** 切片路径 */
  Url = '';
}

export class CaseSliceDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CaseTimeoutMessage {
  /** CaseId */
  CaseId = undefined;

  /** 超时理由 */
  CaseOutTimeMessage = '';

  /** CreateTime */
  CreateTime = '';

  /** CreateUser */
  CreateUser = '';

  /** CreateUserName */
  CreateUserName = '';

  /** Id */
  Id = undefined;

  /** Status */
  Status = undefined;
}

export class CaseTimeoutRecord {
  /** 即将超时 */
  AboutToTimeOut = undefined;

  /** 病例Id */
  CaseId = undefined;

  /** CreateTime */
  CreateTime = '';

  /** CreateUser */
  CreateUser = '';

  /** CreateUserName */
  CreateUserName = '';

  /** 诊断超时 */
  DiagnosticTimeOut = undefined;

  /** Id */
  Id = undefined;
}

export class CaseTypeShowEnum {}

export class CaseUploadslice {
  /** 病例Id */
  CaseId = undefined;

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 磁盘路径 */
  DiskPath = '';

  /** 染色 */
  Dyeing = '';

  /** 评价值 */
  Evaluation = undefined;

  /** 评价时间 */
  EvaluationTime = '';

  /** FileName */
  FileName = '';

  /** FileSize */
  FileSize = undefined;

  /** FileType */
  FileType = '';

  /** 自增Id */
  Id = undefined;

  /** 标签 */
  Label = '';

  /** 宏观图 */
  MacroImageUrl = '';

  /** 备注 */
  Remark = '';

  /** 标注文件 */
  Sdpl = '';

  /** 切片描述 */
  SliceDescription = '';

  /** 切片描述时间 */
  SliceDescriptionTime = '';

  /** 切片类型 */
  SliceType = undefined;

  /** 状态 */
  Status = undefined;

  /** 缩略图 */
  Thumbnail = '';

  /** 切片路径 */
  Url = '';
}

export class CasehistoryReqModel {
  /** 证件号 */
  IdNo = '';

  /** 证件号类型 */
  IdType = '';

  /** Name */
  Name = '';

  /** 操作类型 【值自己定义入：1创建 2检索】 */
  OperateType = undefined;

  /** 卡号 */
  PatCardNo = '';

  /** 卡类型 */
  PatCardType = '';

  /** 卡类型备注 */
  PatCardTypeDesc = '';

  /** 登记号 */
  PatHisNo = '';

  /** 费用类型 */
  PatType = '';

  /** Phone */
  Phone = '';
}

export class ChannelStatusEnum {}

export class ChannelStatusParam {
  /** 频道id */
  Cid = '';

  /** StatusEnum */
  StatusEnum = new ChannelStatusEnum();
}

export class ChannelUr {
  /** HlsPullUrl */
  HlsPullUrl = '';

  /** HttpPullUrl */
  HttpPullUrl = '';

  /** Name */
  Name = '';

  /** PushUrl */
  PushUrl = '';

  /** RtmpPullUrl */
  RtmpPullUrl = '';
}

export class ChannelUrApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ChannelUr();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ChargingItemConfigurationListDto {
  /** id */
  Id = undefined;

  /** 站点名称 */
  Name = '';

  /** 医院级别 */
  SiteLevel = '';

  /** 是否启用收费 */
  Status = undefined;
}

export class ChargingItemConfigurationListDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CommLoginParam {
  /** Id */
  Id = undefined;

  /** 密码 */
  LoginPwd = '';
}

export class CommonDto {
  /** 值 */
  Id = '';

  /** 名称 */
  Name = '';
}

export class CommonDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CommonDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CommonDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CompleteAdminReqModel {
  /** 机构id */
  DepartmentId = '';

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 名称 */
  TrueName = '';
}

export class CompleteCreateExpertModel {
  /** 简介 */
  BriefIntroduction = '';

  /** 关联业务 */
  Business = '';

  /** 收费标准 */
  Charges = undefined;

  /** 市code 440300 */
  CityCode = undefined;

  /** 工作单位 */
  Company = '';

  /** 邮箱 */
  Email = '';

  /** 专家类型 */
  ExpertType = '';

  /** 专家级别 */
  ExpertlevelCode = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 医生职称 */
  NickName = '';

  /** 病例数量 */
  NumberOfCases = '';

  /** 省code 440000 */
  ProvinceCode = undefined;

  /** 质控权限 */
  QualityControl = '';

  /** 签名 */
  Signature = '';

  /** 分中心和站点 */
  SiteSubCenter = [];

  /** 专家专长 */
  SpecialistExpertise = '';

  /** 名称 */
  TrueName = '';
}

export class CompleteDistributorReqModel {
  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 站点分中心list */
  SiteSubCenterList = [];

  /** 名称 */
  TrueName = '';
}

export class CompleteSiteManagerReqModel {
  /** 市code */
  CityCode = undefined;

  /** 站点缩写 */
  Code = '';

  /** 所属机构id */
  DepartmentId = undefined;

  /** 邮箱 */
  Email = '';

  /** 专家 */
  ExpertJson = [];

  /** 头像 */
  HeadPic = '';

  /** 是否提交给专家 */
  IsDiagnosticMode = undefined;

  /** 是否报告工作单位 */
  IsShowCompany = undefined;

  /** 账号 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 站点名称 */
  Name = '';

  /** 省份code */
  ProvinceCode = undefined;

  /** 分中心 */
  SubCenterId = undefined;

  /** 后缀 */
  Suffix = '';
}

export class ConfigReqModel {
  /** Code */
  Id = undefined;

  /** 值 */
  Value = '';
}

export class ConsultationExpertResultDto {
  /** 4月 */
  April = undefined;

  /** 8月 */
  August = undefined;

  /** 12月 */
  December = undefined;

  /** 医生名称 */
  DoctorName = '';

  /** 2月 */
  February = undefined;

  /** 1月 */
  January = undefined;

  /** 7月 */
  July = undefined;

  /** 6月 */
  June = undefined;

  /** 3月 */
  March = undefined;

  /** 5月 */
  May = undefined;

  /** 11月 */
  November = undefined;

  /** 10月 */
  October = undefined;

  /** 9月 */
  September = undefined;
}

export class ConsultationExpertResultDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CorrelationParam {
  /** 当前病例id */
  CaseId = undefined;

  /** 关联病例id */
  OriginalCaseId = undefined;
}

export class CreateAdminReqModel {
  /** 机构id */
  DepartmentId = '';

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 是否为工程师 */
  IsEngineer = false;

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 电话 */
  Mobile = '';

  /** 名称 */
  TrueName = '';
}

export class CreateDistributorReqModel {
  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 电话 */
  Mobile = '';

  /** 站点分中心list */
  SiteSubCenterList = [];

  /** 名称 */
  TrueName = '';
}

export class CreateExpertParam {
  /** 简介 */
  BriefIntroduction = '';

  /** 关联业务 */
  Business = '';

  /** 收费标准 */
  Charges = undefined;

  /** 市code 440300 */
  CityCode = undefined;

  /** 工作单位 */
  Company = undefined;

  /** 邮箱 */
  Email = '';

  /** 专家类型 */
  ExpertType = '';

  /** 专家级别 */
  ExpertlevelCode = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 电话号码 */
  Mobile = '';

  /** 医生职称 */
  NickName = '';

  /** 病例数量 */
  NumberOfCases = '';

  /** 省code 440000 */
  ProvinceCode = undefined;

  /** 质控权限 */
  QualityControl = '';

  /** 签名 */
  Signature = '';

  /** 分中心和站点 */
  SiteSubCenter = [];

  /** 专家专长 */
  SpecialistExpertise = '';

  /** 名称 */
  TrueName = '';
}

export class CreateParam {
  /** 名称 */
  ChannelName = '';
}

export class CreatePatientCardReqDto {
  /** Address */
  Address = '';

  /** IdNo */
  IdNo = '';

  /** IdType */
  IdType = '';

  /** Name */
  Name = '';

  /** Phone */
  Phone = '';
}

export class CreatePatientCardResDto {
  /** PatCardNo */
  PatCardNo = '';

  /** PatCardType */
  PatCardType = '';

  /** PatCardTypeDesc */
  PatCardTypeDesc = '';

  /** PatHisNo */
  PatHisNo = '';

  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class CreatePatientCardResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CreatePatientCardResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CreateRecordTaskDto {
  /** Code */
  Code = '';

  /** Record */
  Record = new CreateRecordTaskRecordDto();
}

export class CreateRecordTaskDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new CreateRecordTaskDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class CreateRecordTaskRecordDto {
  /** 房间id(停止录制和查询云端录制文件时需要) */
  Cid = '';

  /** 任务id */
  TaskId = '';
}

export class CreateRoomParam {
  /** 病例id */
  CaseId = undefined;

  /** 房间名称 */
  ChannelName = '';
}

export class CreateSubCenterReqModel {
  /** 预约号前缀 */
  ConsultationNumber = '';

  /** 默认专家 */
  ExpertId = undefined;

  /** 医院权限 */
  HospitalAuthority = undefined;

  /** 名称 */
  Name = '';

  /** 手抄手机号 */
  Phone = '';

  /** 分诊号前缀/会诊号 */
  PrefixForTriageNumber = '';
}

export class CreateSuggesReqModel {
  /** 联系方式 */
  ContactDetails = '';

  /** 内容 */
  Content = '';
}

export class CreateTeamParam {
  /** 病例id */
  CaseId = undefined;

  /** 邀请发送的文字，最大长度150字符 */
  Msg = '';

  /** 群名称 */
  Tname = '';
}

export class DataFlowDto {
  /** 病例id */
  CaseId = undefined;

  /** 数据来源 */
  DataSources = '';

  /** 标签 */
  Label = '';

  /** 亚专科 */
  SampleLocationName = '';

  /** 性别 */
  SexName = '';

  /** 年龄 */
  StrAge = '';

  /** 姓名 */
  StrName = '';

  /** 缩略图 */
  Thumbnail = [];

  /** 登记时间 */
  Time = '';
}

export class DataFlowDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowEditDto {
  /** 年龄 */
  Age = '';

  /** id */
  Id = undefined;

  /** 身份证 */
  IdNumber = '';

  /** 标签 */
  Label = '';

  /** 名称 */
  Name = '';

  /** 电话号码 */
  Phone = '';

  /** 性别 */
  SexName = '';
}

export class DataFlowEditDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new DataFlowEditDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowEditDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowLogDto {
  /** 主键 */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 项目 */
  Project = undefined;

  /** 病理号 */
  Time = '';
}

export class DataFlowLogDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowReportDto {
  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 诊断意见 */
  Opinion = '';

  /** 大体所见 */
  Thus = '';
}

export class DataFlowReportDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new DataFlowReportDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowReqModel {
  /** 病例id */
  CaseId = undefined;

  /** 年龄 */
  EncryptionAge = undefined;

  /** 身份证 */
  EncryptionIdCard = undefined;

  /** 名称 */
  EncryptionName = undefined;

  /** 电话号码 */
  EncryptionPhone = undefined;

  /** 性别 */
  EncryptionSex = undefined;

  /** 标签 */
  EncryptionSliceLabel = undefined;

  /** 项目 1科研 2规培 3读片会 TODO 使用枚举类型 */
  Project = undefined;
}

export class DataFlowSliceDto {
  /** 病例Id */
  CaseId = undefined;

  /** 磁盘路径 */
  DiskPath = '';

  /** 切片名称 */
  FileName = '';

  /** 切片大小 */
  FileSize = undefined;

  /** 自增Id */
  Id = undefined;

  /** 标签 */
  Label = '';

  /** 宏观图 */
  MacroImageUrl = '';

  /** 缩略图 */
  Thumbnail = '';

  /** 切片路径 */
  Url = '';
}

export class DataFlowSliceDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DataFlowWithdrawReqModel {
  /** id */
  Id = undefined;

  /** 项目 1科研 */
  Project = undefined;
}

export class DateTimeApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = '';

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DeleteCaseReqModel {
  /** id */
  Id = undefined;

  /** 是否删除 */
  IsFrozen = false;
}

export class DiagnoseInfolistItem {
  /** DiagnoseCode */
  DiagnoseCode = '';

  /** DiagnoseDescription */
  DiagnoseDescription = '';

  /** DiagnoseEnterDocCode */
  DiagnoseEnterDocCode = '';

  /** DiagnoseRemark */
  DiagnoseRemark = '';
}

export class DiagnosticComplianceRateDto {
  /** 符合 */
  AccordWith = undefined;

  /** CaseType */
  CaseType = '';

  /** CaseTypeCount */
  CaseTypeCount = undefined;

  /** CaseTypeName */
  CaseTypeName = '';

  /** 符合率 */
  ComplianceRate = undefined;

  /** 不符合 */
  NonCompliant = undefined;

  /** 抽查数 */
  NumberOfSpotChecks = undefined;

  /** 抽查率 */
  SpotCheckRate = undefined;

  /** 是否达标 */
  WhetherItMeetsTheStandards = false;
}

export class DiagnosticComplianceRateDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DiagnosticVocabularySimpleTypesDto {
  /** DiagnosticVocabularyTypeCode */
  DiagnosticVocabularyTypeCode = '';

  /** DiagnosticVocabularyTypeName */
  DiagnosticVocabularyTypeName = '';

  /** Id */
  Id = undefined;

  /** InverseDiagnosticVocabularyType */
  InverseDiagnosticVocabularyType = [];

  /** OwnerUserId */
  OwnerUserId = undefined;
}

export class DiagnosticVocabularySimpleTypesDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DiagnosticVocabularyTextDto {
  /** CreatedTime */
  CreatedTime = '';

  /** DiagnosticVocabularyEnglishText */
  DiagnosticVocabularyEnglishText = '';

  /** DiagnosticVocabularySimpleText */
  DiagnosticVocabularySimpleText = '';

  /** DiagnosticVocabularyText */
  DiagnosticVocabularyText = '';

  /** DiagnosticVocabularyTypeId */
  DiagnosticVocabularyTypeId = undefined;

  /** Id */
  Id = undefined;

  /** IsDeleted */
  IsDeleted = undefined;

  /** Remarks */
  Remarks = '';

  /** UpdatedTime */
  UpdatedTime = '';
}

export class DiagnosticVocabularyTextDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new DiagnosticVocabularyTextDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DiagnosticVocabularyTextDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DiagnosticVocabularyTypeDto {
  /** DiagnosticVocabularyTypeCode */
  DiagnosticVocabularyTypeCode = '';

  /** DiagnosticVocabularyTypeId */
  DiagnosticVocabularyTypeId = undefined;

  /** DiagnosticVocabularyTypeName */
  DiagnosticVocabularyTypeName = '';

  /** Id */
  Id = undefined;

  /** IsPrivate */
  IsPrivate = false;

  /** OwnerUserId */
  OwnerUserId = undefined;
}

export class DiagnosticVocabularyTypeDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new DiagnosticVocabularyTypeDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DistributionDto {
  /** 患者年龄 */
  Age = '';

  /** 病理类型 */
  CaseType = '';

  /** 送检项目 */
  CheckItems = '';

  /** 发布时间 */
  CompleteTime = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 提交时间 */
  CreateTime = '';

  /** 诊断专家 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** 患者姓名 */
  Name = '';

  /** 专家诊断 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 复核专家 */
  ReviewExpertName = '';

  /** 患者性别 */
  Sex = '';

  /** Status */
  Status = undefined;

  /** StatusName */
  StatusName = '';

  /** 诊断中心 */
  SubCentersName = '';

  /** 诊断编号 */
  TurnoverId = '';
}

export class DistributionDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DistributorDto {
  /** 账号 */
  Account = '';

  /** 邮箱 */
  Email = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 电话 */
  Phone = '';

  /** 状态 */
  Status = undefined;

  /** false不存在 / ture存在于表中   用户是否存在 */
  UserExist = false;
}

export class DistributorDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DistributorFrozenDto {
  /** 年龄 */
  Age = '';

  /** 预约时间 */
  AppointmentTime = '';

  /** 会诊号 */
  BarCode = '';

  /** 送检类型 */
  CaseTypeCode = '';

  /** 送检类型名称 */
  CaseTypeName = '';

  /** 临床医生 */
  Clinicians = '';

  /** 专家名称 */
  ExpertName = '';

  /** 冰冻id */
  Id = undefined;

  /** 送检单位名称 */
  Inspection = '';

  /** 意向专家 */
  IntentPersonName = '';

  /** 病人名称 */
  Name = '';

  /** 预约编号 */
  Number = '';

  /** 年龄 */
  PatientAge = '';

  /** 手机号码 */
  Phone = '';

  /** 备注 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  SexCode = undefined;

  /** 性别 */
  SexName = '';

  /** SiteId */
  SiteId = undefined;

  /** 切片缩略图 */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class DistributorFrozenDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DistributorSiteDto {
  /** 站点 */
  SiteId = undefined;

  /** 分中心 */
  SubCenterId = undefined;
}

export class DistributorUpdateDto {
  /** 机构id */
  DepartmentId = '';

  /** 机构名称 */
  DepartmentName = '';

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 头像Url */
  HeadPicUrl = '';

  /** id */
  Id = undefined;

  /** 是否为工程师 */
  IsEngineer = false;

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 站点分配员关联表 */
  SiteSubCenterList = [];

  /** 名称 */
  TrueName = '';
}

export class DistributorUpdateDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new DistributorUpdateDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class DistributorWithdrawReqModel {
  /** id */
  Id = undefined;

  /** 是否为冰冻预约 */
  IsFrozen = false;
}

export class EditHistoryDto {
  /** 申请状态 */
  ApplyStatusName = '';

  /** 修改历史 */
  Content = '';

  /** 申请原因 */
  Reason = '';

  /** 拒绝理由 */
  ReasonForRejection = '';

  /** 时间 */
  Time = '';
}

export class EditHistoryDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class EngineerDto {
  /** 邮箱 */
  Email = '';

  /** Id */
  Id = undefined;

  /** 登录账号 */
  LoginName = '';

  /** 登录密码 */
  LoginPwd = '';

  /** 手机号码 */
  Mobile = '';

  /** Status */
  Status = undefined;

  /** 真实姓名 */
  TrueName = '';

  /** UserExist */
  UserExist = false;
}

export class EngineerDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class EngineerReqModel {
  /** 邮箱 */
  Email = '';

  /** Id */
  Id = undefined;

  /** 登录账号 */
  LoginName = '';

  /** 登录密码 */
  LoginPwd = '';

  /** 手机号码 */
  Mobile = '';

  /** 真实姓名 */
  TrueName = '';
}

export class EvaluationUpdateReqModel {
  /** 切片ID */
  CaseUploadsliceId = undefined;

  /** 评价值 */
  Evaluation = undefined;
}

export class ExcellentRateOfSliceDto {
  /** 病例类型 */
  CaseType = '';

  /** 病例类型名称 */
  CaseTypeName = '';

  /** 优秀 */
  ClassExcellent = undefined;

  /** 良好 */
  ClassGood = undefined;

  /** 合格 */
  ClassQualified = undefined;

  /** 不合格 */
  ClassUnQualified = undefined;

  /** 总数 */
  Count = undefined;

  /** 优良率 */
  ExcellentRate = undefined;

  /** 是否达标 */
  Qualified = false;
}

export class ExcellentRateOfSliceDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertAdminDto {
  /** id */
  Id = undefined;

  /** 电话号码 */
  Phone = '';
}

export class ExpertAdminDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertCaseDto {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** 医嘱状态 这里和申请端的一样 */
  DoctorAdvice = undefined;

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** 缩略图 */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';

  /** 是否补充 0为不补充 比0大的为补充 */
  WhetherToSupplement = undefined;
}

export class ExpertCaseDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertDoctorsAdviceDto {
  /** 病例id */
  CaseId = undefined;

  /** 内容 */
  Content = '';

  /** 下发时间 */
  CreateTime = '';

  /** 下发账号 */
  CreateUser = '';

  /** 下发人名称 */
  CreateUserName = '';

  /** 专家id */
  ExpertId = undefined;

  /** 快递地址 */
  Express = '';

  /** Id */
  Id = undefined;

  /** 状态 */
  Status = undefined;

  /** 0的时候可补充或提交 有值的时候为隐藏按钮 */
  SubmitOrSupplement = undefined;

  /** WhetherToSupplement */
  WhetherToSupplement = undefined;
}

export class ExpertDoctorsAdviceDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ExpertDoctorsAdviceDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertHomeCountDto {
  /** 冰冻 */
  Frozen = undefined;

  /** 受邀诊断 */
  InvitedDiagnosis = undefined;

  /** 已发布 */
  Published = undefined;

  /** 退回 */
  Returned = undefined;

  /** 待诊断 */
  ToBeDiagnosed = undefined;

  /** 待复核 */
  ToBeReviewed = undefined;
}

export class ExpertHomeCountDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ExpertHomeCountDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertInviteeDto {
  /** 专家名称 */
  ExpertName = '';

  /** 留言时间 */
  MsfContentsDate = '';

  /** 消息回复 */
  MsgContents = '';

  /** 邀请理由 */
  Remark = '';
}

export class ExpertInviteeDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ExpertInviteeDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertInviteeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertInviteeReqModel {
  /** CaseId */
  CaseId = undefined;

  /** id */
  Id = undefined;

  /** 字符串 */
  Str = '';
}

export class ExpertLevel {
  /** 收费标准 */
  Charges = undefined;

  /** 级别代码 */
  Code = '';

  /** 专家级别ID */
  Id = undefined;

  /** 级别名称 */
  Name = '';

  /** 收费标准说明 */
  Remark = '';

  /** 状态 */
  Status = undefined;
}

export class ExpertLevelApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ExpertLevel();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertLevelPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertLevelReqModel {
  /** 收费标准 */
  Charges = '';

  /** 级别名称 */
  Name = '';

  /** 收费标准说明 */
  Remark = '';
}

export class ExpertOrderTypeDto {
  /** 项目编号 */
  Code = '';

  /** Id */
  Id = undefined;

  /** 数组 */
  List = [];

  /** 项目名称 */
  Name = '';

  /** 医嘱类型 */
  OrderType = '';

  /** 父级 */
  ParentId = undefined;
}

export class ExpertOrderTypeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertQualityControlEvaluationDto {
  /** 年龄 */
  Age = '';

  /** 病例类型 */
  CaseType = '';

  /** 诊断符合率评价 */
  CoincidenceRateEvaluation = '';

  /** 评论备注 */
  CoincidenceRateRemark = '';

  /** 提交时间 */
  CreateTime = '';

  /** 初审专家 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** id */
  Id = '';

  /** 申请医院 */
  Inspection = '';

  /** 姓名 */
  Name = '';

  /** 专家评价 */
  Opinion = '';

  /** 病例号 */
  PathologyNumber = '';

  /** 性别 */
  Sex = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class ExpertQualityControlEvaluationDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertRenderingDto {
  /** 简介 */
  BriefIntroduction = '';

  /** 关联业务 */
  Business = '';

  /** 收费标准 */
  Charges = undefined;

  /** 市code 440300 */
  CityCode = undefined;

  /** 工作单位 */
  Company = undefined;

  /** 工作单位名称 */
  CompanyName = '';

  /** 邮箱 */
  Email = '';

  /** 专家类型 */
  ExpertType = '';

  /** 专家类型名称 */
  ExpertTypeName = '';

  /** 专家级别 */
  ExpertlevelCode = '';

  /** 头像 */
  HeadPic = '';

  /** 头像url */
  HeadPicUrl = '';

  /** Id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 医生职称 */
  NickName = '';

  /** 病例数量 */
  NumberOfCases = '';

  /** 省code 440000 */
  ProvinceCode = undefined;

  /** 质控权限 */
  QualityControl = '';

  /** 签名 */
  Signature = '';

  /** 签名url */
  SignatureUrl = '';

  /** 关联集合 */
  SiteSubCenter = [];

  /** 专家专长 */
  SpecialistExpertise = '';

  /** 分中心 */
  SubCenterId = undefined;

  /** 名称 */
  TrueName = '';
}

export class ExpertRenderingDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ExpertRenderingDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertSiteDto {
  /** 站点id */
  SiteId = undefined;

  /** 分中心 */
  SubCenterId = undefined;
}

export class ExpertStatisticsDto {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 发布时间 */
  CompleteTime = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** 初审名称 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 标签 */
  LabelList = [];

  /** 病人名称 */
  Name = '';

  /** 意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 复审专家 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** 缩略图 */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class ExpertStatisticsDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertVocabularysDto {
  /** 英文模板 */
  EnglishTemplate = '';

  /** Id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 描述 */
  Remark = '';

  /** 中文模板 */
  Template = '';
}

export class ExpertVocabularysDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ExpertVocabularysReqModel {
  /** 类型 */
  CaseTypeCode = '';

  /** 送检项 */
  CheckItemsCode = '';

  /** 英文模板 */
  EnglishTemplate = '';

  /** 名称 */
  Name = '';

  /** 描述 */
  Remark = '';

  /** 中文模板 */
  Template = '';
}

export class FeeStatisticsDto {
  /** CaseId */
  CaseId = undefined;

  /** 支付时间 */
  CreateTime = '';

  /** FeeStatisticsInformation */
  FeeStatisticsInformation = new FeeStatisticsInformation();

  /** Id */
  Id = undefined;

  /** 名称集合 */
  Name = [];

  /** 预约号 */
  Number = '';

  /** 订单编号 */
  PaymentOrderNumber = '';

  /** SiteId */
  SiteId = undefined;

  /** 支付状态 */
  Status = undefined;

  /** 支付状态 */
  StatusName = '';

  /** 分中心 */
  SubCenterId = undefined;

  /** 支付总金额 */
  TotalPrice = '';

  /** 冰冻预约和普通病例唯一标识 */
  TurnoverId = '';
}

export class FeeStatisticsDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class FeeStatisticsInformation {
  /** 实际支付 */
  ActualPayment = '';

  /** 年龄 */
  Age = '';

  /** 申请站点金额 */
  ApplicationSiteAmount = '';

  /** CaseId */
  CaseId = undefined;

  /** 病理类型 */
  CaseType = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 送检项目 */
  CheckItem = '';

  /** 送检项目 */
  CheckItemName = '';

  /** 会诊中心金额 */
  ConsultationCenterAmount = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 诊断医院金额 */
  DiagnosisHospitalAmount = '';

  /** 专家所属医院 */
  ExpertHospital = '';

  /** 专家名称 */
  ExpertName = '';

  /** 复诊专家 */
  FollowUpSpecialist = '';

  /** 复诊专家所属医院 */
  FollowUpSpecialistHospital = '';

  /** 身份证 */
  IdNumber = '';

  /** 医嘱id */
  MedicalOrderId = '';

  /** 名称 */
  Name = '';

  /** 医嘱金额 */
  OrderAmount = '';

  /** 原病理号 */
  OriginalPathologicalNumber = '';

  /** 业务类型 */
  SettlementType = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** SiteId */
  SiteId = undefined;
}

export class FrozenCaseReqModel {
  /** 年龄 */
  Age = '';

  /** 附件 */
  AnnexList = [];

  /** 预约日期 */
  AppointmentTime = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 临床病历 */
  ClinicalMedicalRecords = '';

  /** 医生名称 */
  Doctor = '';

  /** 意向专家Id */
  IntentPerson = undefined;

  /** 意向专家名称 */
  IntentPersonName = '';

  /** 名称 */
  Name = '';

  /** 预约号码 */
  Number = '';

  /** 备注 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 性别 */
  Sex = undefined;

  /** 手术所见 */
  SurgicalFindings = '';

  /** 号码 */
  UserPhone = '';
}

export class FrozenDto {
  /** 预约时间 */
  AppointmentTime = '';

  /** 会诊号 */
  BarCode = '';

  /** 临床医生 */
  Clinicians = '';

  /** 专家名称 */
  ExpertName = '';

  /** 冰冻id */
  Id = undefined;

  /** 病人名称 */
  Name = '';

  /** 预约编号 */
  Number = '';

  /** 手机号码 */
  Phone = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** SiteId */
  SiteId = undefined;

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class FrozenDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class FrozenInformationDto {
  /** 年龄 */
  Age = '';

  /** 预约日期 */
  AppointmentTime = '';

  /** 病理类型 */
  CaseType = '';

  /** 病例类型名称 */
  CaseTypeName = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 临床病历 */
  ClinicalMedicalRecords = '';

  /** 医生名称 */
  Doctor = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 预约号码 */
  Number = '';

  /** 备注 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位code */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别中文 */
  SexName = '';

  /** 手术所见 */
  SurgicalFindings = '';

  /** 会诊编号 */
  TurnoverId = '';

  /** 号码 */
  UserPhone = '';
}

export class FrozenInformationDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new FrozenInformationDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class FrozenInitialDiagnosisAndReviewCoincidenceRateDto {
  /** 符合率 */
  CoincidenceRate = '';

  /** 总数 */
  FrozenCount = '';

  /** 不合格 */
  Incompatible = undefined;

  /** 是否达标 */
  IsItUpToStandard = '';

  /** 优 */
  MeetsThe = undefined;
}

export class FrozenInitialDiagnosisAndReviewCoincidenceRateDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new FrozenInitialDiagnosisAndReviewCoincidenceRateDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class FrozenRemoteManagementDto {
  /** 年龄 */
  Age = '';

  /** 创建时间 */
  CreateTime = '';

  /** 专家 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 申请单位 */
  Inspection = '';

  /** 名字 */
  Name = '';

  /** 预约编号 */
  Number = '';

  /** 年龄 */
  PatientAge = '';

  /** 手机 */
  Phone = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = '';

  /** 站点id */
  SiteId = undefined;

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class FrozenRemoteManagementDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class GateWayAddParam {
  /** 账号 */
  Account = '';

  /** 邮箱 */
  Email = '';

  /** Id */
  Id = undefined;

  /** 昵称 */
  NickName = '';

  /** 密码 */
  Password = '';

  /** 电话号码 */
  Phone = '';

  /** 角色code */
  RoleCode = '';

  /** 角色Id 不要用 */
  RoleId = undefined;

  /** 角色名称 */
  RoleName = '';

  /** 关联租户Id */
  TenantId = undefined;

  /** 验证码 */
  ValidCode = '';
}

export class GetOpenTenantResponseDto {
  /** Checked */
  Checked = false;

  /** EnName */
  EnName = '';

  /** Id */
  Id = undefined;

  /** IdString */
  IdString = '';

  /** Name */
  Name = '';

  /** Roles */
  Roles = [];
}

export class GetOpenTenantResponseDtoPageResponse {
  /** 响应码 200为成功 401为未授权 500内部报错等 对应httpcode */
  Code = undefined;

  /** Data */
  Data = [];

  /** 响应消息 */
  Message = '';

  /** PageIndex */
  PageIndex = undefined;

  /** PageSize */
  PageSize = undefined;

  /** 是否成功 */
  Success = false;

  /** TotalCount */
  TotalCount = undefined;
}

export class GetQualityDto {
  /** 病例id */
  CaseId = '';

  /** 诊断符合率 */
  CoincidenceRate = '';

  /** 诊断符合率具体评价 */
  CoincidenceRateEvaluation = '';

  /** 诊断符合率备注 */
  CoincidenceRateRemark = '';

  /** 切片质量评价 */
  Quality = undefined;

  /** 切片质量评价备注 */
  QualityRemark = '';

  /** 切片质量具体评价 */
  QualityRemarkEvaluation = '';
}

export class GetQualityDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new GetQualityDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class GetRecordFileInfoDto {
  /** Code */
  Code = '';

  /** 文件列表 */
  FileInfos = [];
}

export class GetRecordFileInfoDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new GetRecordFileInfoDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class GetRecordFileInfoListDto {
  /** 创建时间(时间戳) */
  CreateTime = '';

  /** 文件名 */
  ObjectName = '';

  /** 文件路径 */
  Url = '';
}

export class GetUserResponseDto {
  /** Id */
  Id = undefined;

  /** Desc:邮箱
Default:
Nullable:False */
  Mail = '';

  /** Desc:手机号
Default:
Nullable:False */
  Phone = '';

  /** 备注信息 */
  Remark = '';

  /** Desc:用户账号
Default:
Nullable:False */
  UserAccount = '';

  /** Desc:用户名称
Default:
Nullable:False */
  UserName = '';
}

export class GetUserResponseDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new GetUserResponseDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class HISCreateCase {
  /** 年龄 */
  Age = '';

  /** 床号 */
  BedNumber = '';

  /** 临床资料 */
  ClinicalData = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 原病理诊断 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 民族 */
  EthnicId = undefined;

  /** 住院号 */
  HospitalizedId = '';

  /** 身份证号 */
  IdNumber = '';

  /** 免疫组化 */
  Immunohistochemical = '';

  /** 病房 */
  InpatientWard = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 工作 */
  Job = '';

  /** 婚姻状况 */
  MaritalStatus = undefined;

  /** 女性月经时间 */
  MenstruationDate = '';

  /** 患者名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 联系电话 */
  Phone = '';

  /** 性别 */
  Sex = undefined;

  /** 大体可见 */
  Thus = '';

  /** 就诊号 */
  VisitingNumber = '';
}

export class HisCaseinformationReqModel {
  /** CaseId */
  CaseId = undefined;

  /** his订单号 */
  HisAdviceId = '';

  /** 登记号 */
  PatHisNo = '';

  /** 就诊号 */
  PatVisitNumber = '';

  /** ris订单号 */
  RisAdviceId = '';

  /** 第三方订单号 */
  ThirdOrderId = '';
}

export class HisCaseinformationReqModelApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new HisCaseinformationReqModel();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class HsiCasehistoryDto {
  /** Id */
  Id = undefined;

  /** 证件号 */
  IdNo = '';

  /** 证件号类型 */
  IdType = '';

  /** Name */
  Name = '';

  /** 操作类型 */
  OperateType = undefined;

  /** 卡号 */
  PatCardNo = '';

  /** 卡类型 */
  PatCardType = '';

  /** 卡类型备注 */
  PatCardTypeDesc = '';

  /** 登记号 */
  PatHisNo = '';

  /** 费用类型 */
  PatType = '';

  /** Phone */
  Phone = '';
}

export class HsiCasehistoryDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new HsiCasehistoryDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class HsiCasehistoryDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class IdParam {
  /** id */
  Id = undefined;
}

export class ImmunohistochemicalPackage {
  /** 免疫组化id */
  Immunohistochemistry = undefined;

  /** 父级id */
  ParentId = undefined;
}

export class Information {
  /** 缴费金额 */
  AmountMoney = '';

  /** 姓名 */
  Name = '';

  /** 医嘱Id */
  OrderId = '';

  /** 卡号 */
  PatCardNo = '';

  /** 登记号 */
  PatHisNo = '';

  /** 就诊号 */
  PatVisitNumber = '';
}

export class InspectionItemsDto {
  /** 4月 */
  April = undefined;

  /** 8月 */
  August = undefined;

  /** 12月 */
  December = undefined;

  /** 2月 */
  February = undefined;

  /** 1月 */
  January = undefined;

  /** 7月 */
  July = undefined;

  /** 6月 */
  June = undefined;

  /** 3月 */
  March = undefined;

  /** 5月 */
  May = undefined;

  /** 11月 */
  November = undefined;

  /** 10月 */
  October = undefined;

  /** 9月 */
  September = undefined;
}

export class InspectionItemsDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new InspectionItemsDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class Int32ApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class Int64ApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class Int64NullableApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class InvoiceDto {
  /** 业务类型 */
  BusinessType = '';

  /** 业务类型金额 */
  BusinessTypeAmount = '';

  /** 时间 */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** 发票状态 */
  InvoiceStatus = '';

  /** 发票类型 */
  InvoiceType = '';

  /** 名称 */
  Name = '';

  /** 医嘱类型 */
  OrderType = '';

  /** 医嘱类型金额 */
  OrderTypeAmount = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 总计金额 */
  TotalAmount = '';
}

export class InvoiceDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class Item {
  /** List */
  List = [];
}

export class ItemPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class LabelDto {
  /** Id */
  Id = undefined;

  /** 标签名称 */
  Name = '';
}

export class LabelDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new LabelDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class LabelDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ListItem {
  /** CId */
  CId = '';

  /** Ctime */
  Ctime = undefined;

  /** Duration */
  Duration = '';

  /** Filename */
  Filename = '';

  /** Format */
  Format = undefined;

  /** Name */
  Name = '';

  /** NeedRecord */
  NeedRecord = undefined;

  /** Status */
  Status = undefined;

  /** UId */
  UId = undefined;
}

export class ListOranizenDto {
  /** 自增id */
  Id = undefined;

  /** LimsName */
  LimsName = '';

  /** 名称 */
  Name = '';
}

export class ListOranizenDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class LogDto {
  /** 账号 */
  Account = '';

  /** 行为 */
  Behavior = '';

  /** 业务类型 */
  BusinessType = undefined;

  /** 病例id */
  CaseId = undefined;

  /** 操作时间 */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** Ip */
  Ip = '';

  /** 对象 */
  Object = '';

  /** 操作人 */
  Operator = '';

  /** 路径 */
  Path = '';

  /** 类型 */
  Type = '';
}

export class LogDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class LogParam {
  /** 账号 */
  Account = '';

  /** 行为 */
  Behavior = '';

  /** 业务类型 1诊断类型 2操作类型 */
  BusinessType = undefined;

  /** 病例id */
  CaseId = undefined;

  /** Ip */
  Ip = '';

  /** 对象 */
  Object = '';

  /** 操作人 */
  Operator = '';

  /** 路径 */
  Path = '';

  /** 类型 */
  Type = '';
}

export class LoginReqModel {
  /** 账户 */
  Account = '';

  /** 密码 */
  PassWord = '';

  /** 验证码 */
  VerificationCode = '';
}

export class MedicalAdviceFees {
  /** 实际支付金额 */
  ActualPaymentAmount = '';

  /** 安徽省立金额 */
  AnhuiProvincialGovernmentAmount = '';

  /** 收费状态 */
  ChargingStatus = '';

  /** 诊断时间 */
  DiagnosisTime = '';

  /** 诊断状态 */
  DiagnosticStatus = '';

  /** 诊断专家团队所属医院金额 */
  HospitalAmount = '';

  /** 平台金额 */
  PlatformAmount = '';

  /** 项目类型 */
  ProjectType = '';

  /** 医嘱类型 */
  SettlementType = '';

  /** 站点金额 */
  SiteAmount = '';
}

export class MedicalOrderConfirmationDto {
  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病理类型 */
  CaseTypeCode = '';

  /** 病理类型名称 */
  CaseTypeName = '';

  /** 送检类型 */
  CheckItemsCode = '';

  /** 送检类型名称 */
  CheckItemsName = '';

  /** ConsultationNumber */
  ConsultationNumber = '';

  /** 医嘱内容 */
  Content = '';

  /** 提交时间 */
  CreateTime = '';

  /** 按钮是否禁用 2启用3禁用 */
  Disabled = undefined;

  /** 下发专家 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 姓名 */
  Name = '';

  /** 医嘱下发时间 */
  OrderCreatetime = '';

  /** 下发状态 */
  OrderStatus = undefined;

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 诊断状态 */
  Status = undefined;

  /** 诊断状态名称 */
  StatusName = '';

  /** 是否已超时 */
  TimeOutName = '';

  /** 诊断流水号 */
  TurnoverId = '';
}

export class MedicalOrderConfirmationDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class MedicalOrderFeesDto {
  /** 编码 */
  Code = '';

  /** Id */
  Id = undefined;

  /** 子集合（免疫组化套餐） */
  List = [];

  /** 金额 */
  Money = '';

  /** Name */
  Name = '';

  /** ParentId */
  ParentId = undefined;
}

export class MedicalOrderFeesDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class NeteaseYunXin {
  /** AppKey */
  AppKey = '';

  /** AppSecret */
  AppSecret = '';
}

export class NeteaseYunXinApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new NeteaseYunXin();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class NeteaseyunxinUser {
  /** 创建时间 */
  AddTime = '';

  /** Id */
  Id = undefined;

  /** NeteaseImaccId */
  NeteaseImaccId = '';

  /** NeteaseImnickName */
  NeteaseImnickName = '';

  /** 网易云信token */
  NeteaseImtoken = '';

  /** 子系统 1远程2科研3读片会4规培 */
  Subsystem = undefined;

  /** 用户id */
  UserId = undefined;
}

export class NeteaseyunxinUserApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new NeteaseyunxinUser();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class NonGynecologicalDto {
  /** 阴 */
  Negative = '';

  /** 总数 */
  NonGynecologicalCount = '';

  /** 阳 */
  Positive = '';

  /** 阳性率 */
  PositiveRate = '';
}

export class NonGynecologicalDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new NonGynecologicalDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class NoticeTypeEnum {}

export class ObjectApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class OpenTenantRoleDto {
  /** Checked */
  Checked = false;

  /** Code */
  Code = '';

  /** Id */
  Id = undefined;

  /** IdString */
  IdString = '';

  /** Name */
  Name = '';
}

export class OperateReqModel {
  /** id */
  Id = undefined;

  /** 操作 true为同意 */
  Operate = false;

  /** 理由 */
  Reason = '';
}

export class OperationRecordDto {
  /** 专家账号 */
  Account = '';

  /** 诊断时间 */
  ApplyTime = '';

  /** 诊断专家 */
  ApplyUserName = '';

  /** 节点名称 */
  NodeName = '';
}

export class OperationRecordDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class OrderToCreateCaseModel {
  /** 年龄 */
  Age = '';

  /** 新添加的附件 */
  AnnexList = [];

  /** 会诊号 */
  BarCode = '';

  /** 床号 */
  BedNumber = '';

  /** 病例类型 */
  CaseType = '';

  /** 送检项目 */
  CheckItems = '';

  /** 临床资料 */
  ClinicalData = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 留言类容 */
  Content = '';

  /** 原病理诊断 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 民族 */
  EthnicId = undefined;

  /** 住院号 */
  HospitalizedId = '';

  /** 身份证号 */
  IdNumber = '';

  /** 免疫组化 */
  Immunohistochemical = '';

  /** 病房 */
  InpatientWard = '';

  /** 送检单位 */
  Inspection = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 意向专家Id */
  IntentPerson = undefined;

  /** 意向专家名称 */
  IntentPersonName = '';

  /** 是否为疑难病 */
  IsRefractoryDisease = undefined;

  /** 工作 */
  Job = '';

  /** 婚姻状况 */
  MaritalStatus = undefined;

  /** 女性月经时间 */
  MenstruationDate = '';

  /** 患者名称 */
  Name = '';

  /** 原病例id */
  OriginalCaseId = undefined;

  /** 病理号 */
  PathologyNumber = '';

  /** 联系电话 */
  Phone = '';

  /** 备注信息 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 采样日期 */
  SamplingTime = '';

  /** 性别 */
  Sex = undefined;

  /** 新添加的切片 */
  SliceList = [];

  /** 亚专科 */
  SubProfessional = '';

  /** 预约日期 */
  SubscribeTime = '';

  /** 大体可见 */
  Thus = '';

  /** 会诊编号 */
  TurnoverId = '';

  /** 旧附件 */
  UsedAnnexList = [];

  /** 旧切片 */
  UsedSliceList = [];

  /** 就诊号 */
  VisitingNumber = '';
}

export class OrderTypeDto {
  /** 编码 */
  Code = '';

  /** Id */
  Id = undefined;

  /** 子集合（免疫组化套餐） */
  List = [];

  /** Name */
  Name = '';

  /** ParentId */
  ParentId = undefined;
}

export class OrderTypeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class OrderTypeParam {
  /** 编码 */
  Code = '';

  /** 子集合（免疫组化套餐） */
  ImmunohistochemicalPackage = [];

  /** Name */
  Name = '';

  /** ParentId */
  ParentId = undefined;
}

export class OrdinaryCaseAssignmentParam {
  /** 专家id */
  ExpertId = undefined;

  /** id */
  Id = undefined;
}

export class OrganizenReqModel {
  /** 地址 */
  Address = '';

  /** 英文 */
  EName = '';

  /** LimsName */
  LimsName = '';

  /** 名称 */
  Name = '';

  /** 电话 */
  Phone = '';
}

export class OtherChargesDto {
  /** 安徽省立医院收费 */
  AnhuiProvincialHospital = '';

  /** 会诊专家团队所属医院收费 */
  HospitalCharges = '';

  /** 站点收费 */
  SiteCharges = '';

  /** 所属站点 */
  SiteId = undefined;
}

export class OtherChargesDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new OtherChargesDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class OtherChargesParam {
  /** 安徽省立医院收费 */
  AnhuiProvincialHospital = '';

  /** 会诊专家团队所属医院收费 */
  HospitalCharges = '';

  /** 站点收费 */
  SiteCharges = '';

  /** 所属站点 */
  SiteId = undefined;
}

export class PageParam {
  /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
  AdvancedSearch = '';

  /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
  OrderByType = '';

  /** 当前页 */
  PageIndex = undefined;

  /** 每页总条数 */
  PageSize = undefined;
}

export class PathologicalSectionMarkDto {
  /** 标记信息 */
  Marks = '';

  /** 病例切片ID */
  PathologicalSectionId = undefined;

  /** 具体路径 */
  Url = '';
}

export class PathologySliceReturnedRateDto {
  /** Count */
  Count = undefined;

  /** ReturnCount */
  ReturnCount = undefined;

  /** ReturnRate */
  ReturnRate = undefined;
}

export class PathologySliceReturnedRateDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new PathologySliceReturnedRateDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PathologySliceTimelinessOfDiagnosisDto {
  /** CaseType */
  CaseType = '';

  /** CaseTypeName */
  CaseTypeName = '';

  /** Count */
  Count = undefined;

  /** InTimeCount */
  InTimeCount = undefined;

  /** InTimeRate */
  InTimeRate = undefined;

  /** OutTimeCount */
  OutTimeCount = undefined;

  /** OverTimeRate */
  OverTimeRate = undefined;

  /** Qualified */
  Qualified = false;
}

export class PathologySliceTimelinessOfDiagnosisDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PatientCardDetailVoListItem {
  /** Name */
  Name = '';

  /** PatCardNo */
  PatCardNo = '';

  /** PatCardType */
  PatCardType = '';

  /** PatCardTypeDesc */
  PatCardTypeDesc = '';

  /** PatHisNo */
  PatHisNo = '';

  /** PatType */
  PatType = '';
}

export class PaymentCompletedReqModel {
  /** 基本信息 */
  InformationList = [];

  /** 缴费订单号 */
  PaymentOrderNumber = '';

  /** 缴费总价 */
  TotalPrice = '';
}

export class PlatformRenderingDto {
  /** 管理员账号 */
  Administrator = '';

  /** 预约号前缀 */
  ConsultationNumber = '';

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 英文名称 */
  EName = '';

  /** 默认专家 */
  ExpertId = undefined;

  /** 专家名称 */
  ExpertName = '';

  /** 全称 */
  FullName = '';

  /** 医院端权限 1选择专家，2不选择专家 */
  HospitalAuthority = undefined;

  /** 自增Id */
  Id = undefined;

  /** 简称 */
  Name = '';

  /** 父节点Id */
  ParentId = undefined;

  /** 抄送手机号 */
  Phone = '';

  /** 分诊号前缀 */
  PrefixForTriageNumber = '';

  /** 审核状态 */
  Status = undefined;

  /** 类型 1 分诊平台2 分中心 */
  Type = undefined;
}

export class PlatformRenderingDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new PlatformRenderingDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PlatformTreeDto {
  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 子集 */
  Tree = [];
}

export class PlatformTreeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PositiveDto {
  /** Adenocarcinoma */
  Adenocarcinoma = undefined;

  /** Adenocarcinoma率 */
  AdenocarcinomaRate = '';

  /** AGCN */
  Agcn = undefined;

  /** AGCN率 */
  AgcnRate = '';

  /** AGCNOS */
  Agcnos = undefined;

  /** AGCNOS率 */
  AgcnosRate = '';

  /** AIS */
  Ais = undefined;

  /** AIS率 */
  AisRate = '';

  /** ASCH */
  Asch = undefined;

  /** ASCH率 */
  AschRate = '';

  /** ASCUS */
  Ascus = undefined;

  /** ASCUS率 */
  AscusRate = '';

  /** 不满意 */
  Dissatisfied = undefined;

  /** 不满意率 */
  DissatisfiedRate = '';

  /** HSIL */
  Hsil = undefined;

  /** HSIL率 */
  HsilRate = '';

  /** LSIH */
  Lsih = undefined;

  /** LSIH率 */
  LsihRate = '';

  /** LSILASCH */
  Lsilasch = undefined;

  /** LSILASCH率 */
  LsilaschRate = '';

  /** NILM */
  Nilm = undefined;

  /** NILM率 */
  NilmRate = '';

  /** 阳性总数 */
  PositiveCount = undefined;

  /** 阳性百分比 */
  PositivePercentage = '';

  /** SCC */
  Scc = undefined;

  /** SCC率 */
  SccRate = '';

  /** 总数 */
  Total = undefined;
}

export class PositiveDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new PositiveDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PuBaseCodeDto {
  /** 即将超时 */
  AboutToTimeOut = '';

  /** 即将超时单位 */
  AboutToTimeOutUnit = undefined;

  /** 类型 */
  BusTye = '';

  /** 编码 */
  Code = '';

  /** 诊断超时 */
  DiagnosticTimeOut = '';

  /** 诊断超时单位 */
  DiagnosticTimeOutUnit = undefined;

  /** 扩展名 */
  ExtName = '';

  /** Id */
  Id = undefined;

  /** Json数据 */
  JsonData = '';

  /** 业务线属性 */
  LineOfBusinessProperties = undefined;

  /** 名称 */
  Name = '';

  /** 父级 */
  ParentId = undefined;

  /** 备注 */
  Remark = '';
}

export class PuBaseCodeDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new PuBaseCodeDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PubBaseCodeReqModel {
  /** 即将超时 */
  AboutToTimeOut = '';

  /** 即将超时单位 */
  AboutToTimeOutUnit = undefined;

  /** 业务类型 */
  BusType = '';

  /** 编码或英文 */
  Code = '';

  /** 诊断超时 */
  DiagnosticTimeOut = '';

  /** 诊断超时单位 */
  DiagnosticTimeOutUnit = undefined;

  /** 扩展名 */
  ExtName = '';

  /** his编码 */
  HisCode = '';

  /** json绑定 */
  JsonData = undefined;

  /** 业务线属性 */
  LineOfBusinessProperties = undefined;

  /** 名称 */
  Name = '';

  /** 父节点 */
  ParentId = undefined;

  /** 备注 */
  Remark = '';
}

export class PubBaseCodeTreeDto {
  /** 即将超时 */
  AboutToTimeOut = '';

  /** 即将超时单位 */
  AboutToTimeOutUnit = undefined;

  /** 子集 */
  Children = [];

  /** 代码 */
  Code = '';

  /** 诊断超时 */
  DiagnosticTimeOut = '';

  /** 诊断超时单位 */
  DiagnosticTimeOutUnit = undefined;

  /** 扩展名称 */
  ExtName = '';

  /** his编码 */
  HisCode = '';

  /** 自增Id */
  Id = undefined;

  /** json数据 */
  JsonData = undefined;

  /** 业务线属性 */
  LineOfBusinessProperties = undefined;

  /** 名称 */
  Name = '';

  /** 上级Id */
  ParentId = undefined;

  /** 备注 */
  Remark = '';

  /** 排序 */
  Sort = undefined;

  /** 启动状态 */
  StatusName = '';
}

export class PubBaseCodeTreeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PublishedStatisticsDto {
  /** 条形码/会诊编号 */
  BarCode = '';

  /** 病例总数 */
  CaseCount = undefined;

  /** 切片列表 */
  CaseSliceList = [];

  /** 病例超时留言 */
  CaseTimeoutMessages = [];

  /** CaseTimeoutRecordList */
  CaseTimeoutRecordList = new CaseTimeoutRecord();

  /** 病理类型 */
  CaseType = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 送检项目 */
  CheckItem = '';

  /** 送检项目 */
  CheckItemName = '';

  /** 病例提交时间 */
  CreateTime = '';

  /** 诊断符合率 */
  DiagnosticComplianceRate = undefined;

  /** 诊断符合率备注/不符合原因 */
  DiagnosticComplianceRateRemark = '';

  /** 诊断符合率时间 */
  DiagnosticComplianceRateTime = '';

  /** 专家名称 */
  ExpertName = '';

  /** Id */
  Id = undefined;

  /** 送检医院 */
  Inspection = '';

  /** 患者名称 */
  Name = '';

  /** 病例号 */
  PathologyNumber = '';

  /** 病例发布时间 */
  ReleaseTime = '';

  /** 复核专家名称 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位 */
  SampleLocationName = '';

  /** 亚专科 */
  SubProfessional = '';
}

export class PublishedStatisticsDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class PullGroupChatParam {
  /** 受邀id    参数示例 166976861799424 */
  Members = '';

  /** 邀请人id */
  NeteaseImaccId = '';

  /** 群聊id */
  TId = '';
}

export class PullGroupChatUrlParam {
  /** 邀请人id */
  NeteaseImaccId = '';

  /** 群聊id */
  TId = '';
}

export class QualityControlDto {
  /** 质控权限 */
  QualityControl = '';
}

export class QualityControlDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new QualityControlDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class QualityControlEvaluationUpdateParam {
  /** 诊断符合率 */
  DiagnosticComplianceRate = undefined;

  /** 诊断符合率备注 */
  DiagnosticComplianceRateRemark = '';

  /** id */
  Id = undefined;
}

export class QualityControlStatisticsDto {
  /** CaseId */
  CaseId = undefined;

  /** 诊断符合率 */
  DiagnosticComplianceRate = undefined;

  /** 诊断符合率备注/不符合原因 */
  DiagnosticComplianceRateRemark = '';

  /** 诊断符合率时间 */
  DiagnosticComplianceRateTime = '';
}

export class QualityControlStatisticsDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class QueryPatientReqDto {
  /** Name */
  Name = '';

  /** ParCardType */
  ParCardType = '';

  /** PatCardNo */
  PatCardNo = '';

  /** PatHisNo */
  PatHisNo = '';
}

export class QueryPatientResDto {
  /** Age */
  Age = '';

  /** Dob */
  Dob = '';

  /** IdNo */
  IdNo = '';

  /** IdType */
  IdType = '';

  /** Mobile */
  Mobile = '';

  /** Name */
  Name = '';

  /** PatCardNo */
  PatCardNo = '';

  /** PatCardType */
  PatCardType = '';

  /** PatHisNo */
  PatHisNo = '';

  /** Sex */
  Sex = '';
}

export class QueryPatientResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new QueryPatientResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class QueryRegSourceReqDto {
  /** DeptCode */
  DeptCode = '';

  /** DoctorCode */
  DoctorCode = '';

  /** EndDate */
  EndDate = '';

  /** HisId */
  HisId = '';

  /** StartDate */
  StartDate = '';
}

export class QueryRegSourceResDto {
  /** RegSources */
  RegSources = [];

  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';
}

export class QueryRegSourceResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new QueryRegSourceResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RcpNotify {
  /** 关联id */
  CaseId = undefined;

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 英文消息 */
  EnMessage = '';

  /** 主键 */
  Id = undefined;

  /** 是否全部可见 */
  IsAllVisible = undefined;

  /** 消息 */
  Message = '';

  /** 具体看枚举值 */
  MessageType = undefined;

  /** 接收人 */
  Receiver = undefined;

  /** 状态 1为已读 2正常 */
  Status = undefined;
}

export class RcpNotifyPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RcpPathologyTemplate {
  /** 病例类型Code */
  CaseTypeCode = '';

  /** 送检项目Code */
  CheckItemCode = '';

  /** 创建时间 */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** 是否启用 */
  IsDisable = undefined;

  /** 语言Code */
  LanguageCode = '';

  /** 应用范围 */
  RangeOfApplication = '';

  /** 备注 */
  Remark = '';

  /** 截图数量 */
  ShotNum = undefined;

  /** 病理模板Code */
  TemplateCode = '';

  /** 病理模板文件Code */
  TemplateFileCode = '';

  /** 1、分中心 2、站点 */
  TypeEnum = undefined;

  /** 分中心或者站点ID */
  TypePointId = undefined;
}

export class RcpPathologyTemplateApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RcpPathologyTemplate();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RcpPathologyTemplateDto {
  /** 病例类型Code */
  CaseTypeCode = '';

  /** 病例类型CodeName */
  CaseTypeCodeName = '';

  /** 送检项目Code */
  CheckItemCode = '';

  /** 创建时间 */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** 是否启用 */
  IsDisable = undefined;

  /** 语言Code */
  LanguageCode = '';

  /** 语言CodeName */
  LanguageCodeName = '';

  /** 应用范围 */
  RangeOfApplication = '';

  /** 备注 */
  Remark = '';

  /** 截图数量 */
  ShotNum = undefined;

  /** 病理模板Code */
  TemplateCode = '';

  /** 病理模板文件Code */
  TemplateFileCode = '';

  /** 1、分中心 2、站点 */
  TypeEnum = undefined;

  /** 分中心或者站点ID */
  TypePointId = undefined;

  /** 分中心或者站点Name */
  TypePointName = '';
}

export class RcpPathologyTemplateDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RcpPubaseCodeList {
  /** 即将超时 */
  AboutToTimeOut = '';

  /** 即将超时单位 */
  AboutToTimeOutUnit = undefined;

  /** 类型 */
  BusTye = '';

  /** 编码 */
  Code = '';

  /** 诊断超时 */
  DiagnosticTimeOut = '';

  /** 诊断超时单位 */
  DiagnosticTimeOutUnit = undefined;

  /** 扩展名称 */
  ExtName = '';

  /** his编码 */
  HisCode = '';

  /** Id */
  Id = undefined;

  /** Json数据 */
  JsonData = undefined;

  /** 业务线属性 */
  LineOfBusinessProperties = undefined;

  /** 名称 */
  Name = '';

  /** 父级 */
  ParentId = undefined;

  /** 备注 */
  Remark = '';

  /** 所属站点 */
  Site = undefined;

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';
}

export class RcpPubaseCodeListPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RcpSiteData {
  /** 管理员账号 */
  Account = '';

  /** 地址 */
  Address = '';

  /** 所属地区编码 */
  AreaCode = '';

  /** 所属地区名称 */
  AreaName = '';

  /** 站点缩写-会诊编号前缀 */
  Code = '';

  /** 创建时间 */
  CreateTime = '';

  /** 创建人 */
  CreateUser = '';

  /** 创建人名称 */
  CreateUserName = '';

  /** 自增Id */
  Id = undefined;

  /** 会诊模式-是否直接提交专家 */
  IsDiagnosticMode = undefined;

  /** 是否报告工作单位 */
  IsShowCompany = undefined;

  /** 站点名称 */
  Name = '';

  /** 所属机构 */
  OrganizationId = undefined;

  /** 所属平台 */
  ParentPlatformId = undefined;

  /** 电话 */
  Phone = '';

  /** 所属分中心-平台表分中心的Id */
  PlatformId = undefined;

  /** 启用状态 */
  Status = undefined;

  /** 编辑时间 */
  UpdateTime = '';

  /** 编辑人 */
  UpdateUser = '';

  /** 编辑人名称 */
  UpdateUserName = '';
}

export class RealTimeRoughlyDto {
  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 电话号码 */
  Phone = '';

  /** 站点所关联的用户id */
  UserId = undefined;
}

export class RealTimeRoughlyDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ReasonForReturnDto {
  /** 类容 */
  Content = '';

  /** id */
  Id = undefined;
}

export class ReasonForReturnDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RecommendationDetailsDto {
  /** /建议内容 */
  Content = '';

  /** id */
  Id = undefined;
}

export class RecommendationDetailsDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RecommendationDetailsDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ReconciliationChargeDto {
  /** 年龄 */
  Age = '';

  /** 金额 */
  AmountOfNoney = undefined;

  /** 病理类型 */
  CaseType = '';

  /** 收费项目 */
  CheckItems = '';

  /** 提交时间 */
  CreateTime = '';

  /** 专家名称 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** Id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 复审专家名称 */
  ReviewExpertName = '';

  /** 医院 */
  SiteName = '';

  /** 总金额 */
  TotalAmount = undefined;

  /** 会诊编号 */
  TurnoverId = '';
}

export class ReconciliationChargeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class Redistribute {
  /** 专家ID */
  ExpertId = undefined;

  /** 专家名称 */
  ExpertName = '';

  /** id */
  Id = undefined;
}

export class RefreshTokenResponse {
  /** Refresh Token */
  RefreshToken = '';

  /** token */
  Token = '';
}

export class RefreshTokenResponseApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RefreshTokenResponse();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RegSourcesItem {
  /** ClinicRoomCode */
  ClinicRoomCode = '';

  /** ClinicRoomName */
  ClinicRoomName = '';

  /** DeptName */
  DeptName = '';

  /** DeptNo */
  DeptNo = '';

  /** DoctorName */
  DoctorName = '';

  /** DoctorNo */
  DoctorNo = '';

  /** DoctorTitle */
  DoctorTitle = '';

  /** EndTime */
  EndTime = '';

  /** HasDetailTime */
  HasDetailTime = '';

  /** RegDate */
  RegDate = '';

  /** RegFee */
  RegFee = '';

  /** ScheduleItemCode */
  ScheduleItemCode = '';

  /** StartTime */
  StartTime = '';

  /** TimeCode */
  TimeCode = '';

  /** TimeFlag */
  TimeFlag = '';

  /** TotalNum */
  TotalNum = '';
}

export class RegisterNoticeResDto {
  /** AdmNo */
  AdmNo = '';

  /** DoctorName */
  DoctorName = '';

  /** HisOrdNum */
  HisOrdNum = '';

  /** ResultCode */
  ResultCode = '';

  /** ResultMessage */
  ResultMessage = '';

  /** SerialNum */
  SerialNum = '';

  /** TimeRange */
  TimeRange = '';

  /** VisitPosition */
  VisitPosition = '';
}

export class RegisterNoticeResDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RegisterNoticeResDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RegisteredDto {
  /** EndTime */
  EndTime = '';

  /** PatHisNo */
  PatHisNo = '';

  /** RegFee */
  RegFee = '';

  /** ScheduleItemCode */
  ScheduleItemCode = '';

  /** StartTime */
  StartTime = '';
}

export class RegistrationManagementDto {
  /** 就诊号 */
  AdmNo = '';

  /** 预约专家 */
  AppointmentExpert = '';

  /** 预约时间 */
  AppointmentTime = '';

  /** 卡类型 */
  CardType = '';

  /** 卡类型备注 */
  CardTypeDesc = '';

  /** 历史记录表Id */
  CaseHistoryId = undefined;

  /** Id */
  Id = undefined;

  /** 就诊人 */
  Name = '';

  /** 登记号 */
  PatHisNo = '';

  /** 状态 */
  Status = undefined;

  /** 挂号时间 */
  Time = '';
}

export class RegistrationManagementDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RegistrationManagementReqModel {
  /** 就诊号 */
  AdmNo = '';

  /** 预约专家 */
  AppointmentExpert = '';

  /** 预约时间 */
  AppointmentTime = '';

  /** 卡类型 */
  CarType = '';

  /** 卡类型备注 */
  CardTypeDesc = '';

  /** 历史记录表Id */
  CaseHistoryId = undefined;

  /** 排班接口返回的某个排班结束时间 */
  EndTime = '';

  /** 就诊人 */
  Name = '';

  /** 登记号 */
  PatHisNo = '';

  /** 挂号费 */
  RegFee = '';

  /** 排班Id */
  ScheduleItemCode = '';

  /** 排班接口返回的某个排班开始时间 */
  StartTime = '';

  /** 锁号返回的his流水号 */
  ThirdOrderId = '';
}

export class RenderExpertVocabularysDto {
  /** 类型 */
  CaseTypeCode = '';

  /** 送检项 */
  CheckItemsCode = '';

  /** 英文模板 */
  EnglishTemplate = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 描述 */
  Remark = '';

  /** 中文模板 */
  Template = '';
}

export class RenderExpertVocabularysDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RenderExpertVocabularysDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RenderTemplatePostModel {
  /** 模板文件Code */
  Code = '';

  /** 渲染数据 */
  JsonNode = '';
}

export class RetChannelStatus {
  /** Cid */
  Cid = '';

  /** Ctime */
  Ctime = undefined;

  /** Duration */
  Duration = undefined;

  /** 解刨学直播间001 */
  Filename = '';

  /** Format */
  Format = undefined;

  /** 解刨学直播间001 */
  Name = '';

  /** NeedRecord */
  NeedRecord = undefined;

  /** RecordStatus */
  RecordStatus = undefined;

  /** Status */
  Status = undefined;

  /** Type */
  Type = undefined;

  /** UId */
  UId = undefined;
}

export class RetChannelStatusApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new RetChannelStatus();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class RetrievePasswordReqModel {
  /** 验证码 */
  Code = '';

  /** 密码 */
  PassWord = '';

  /** 字符串 */
  Str = '';
}

export class ReviewOpinionDto {
  /** id */
  Id = undefined;

  /** 意见 */
  Opinion = '';

  /** 复核意见 */
  ReviewOpinion = '';
}

export class ReviewOpinionDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new ReviewOpinionDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ReviewReqModel {
  /** id */
  Id = undefined;

  /** 是否为复核退回/邀请复核 */
  IsReturn = false;

  /** 复核专家 */
  ReviewExpertId = undefined;

  /** 复核专家名称 */
  ReviewExpertName = '';
}

export class SendMessageParam {
  /** 发送者accid，用户帐号 */
  From = '';

  /** 发送消息内容 格式：如 {"msg":"test"} 表示只发文本 内容为test */
  Message = '';

  /** MessageType */
  MessageType = new NoticeTypeEnum();

  /** 接收对象 用户Id/群Id */
  To = '';
}

export class SendType {}

export class SendTypeEnum {}

export class SettlementDto {
  /** 卡类型 */
  CardType = '';

  /** 病例id */
  CaseId = undefined;

  /** 缴费时间 */
  CreateTime = '';

  /** id */
  Id = undefined;

  /** 诊断卡号 */
  PatCardNo = '';

  /** 登记号 */
  PatHisNo = '';

  /** 就诊号 */
  PatVisitNumber = '';

  /** 患者姓名 */
  PatientName = '';

  /** 缴费金额 */
  PaymentAmount = '';

  /** 项目 */
  Project = '';

  /** 会诊状态 */
  RcpStatusName = '';

  /** 缴费状态 */
  Status = undefined;
}

export class SettlementDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class ShortMessageBulkAllocationParam {
  /** 条数 */
  Count = '';

  /** 专家ID */
  ExpertId = undefined;

  /** id */
  Id = [];
}

export class SiteDropDto {
  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';
}

export class SiteDropDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteDto {
  /** 市 */
  CityName = '';

  /** 站点id */
  Id = undefined;

  /** 登录名称 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 机构 */
  OrganizeName = '';

  /** 省份 */
  ProvinceName = '';

  /** 站点名称 */
  SiteName = '';

  /** 状态 */
  Status = undefined;

  /** false不存在 / ture存在于表中 */
  UserExist = false;

  /** id */
  UserId = undefined;
}

export class SiteDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteExpertDto {
  /** 简介 */
  BriefIntroduction = '';

  /** 市 */
  CityCode = undefined;

  /** 市 */
  CityName = '';

  /** 专家名称 */
  ExpertName = '';

  /** 昵称 */
  ExpertTypeName = '';

  /** 职务 */
  ExpertleveName = '';

  /** 头像 */
  HeadPic = '';

  /** 头像 */
  HeadPicUrl = '';

  /** Id */
  Id = undefined;

  /** 专家类型（此处和职称互换位置） */
  NickName = '';

  /** 病例可提交数量 */
  NumberOfCases = '';

  /** 已提交病例数量 */
  NumberOfSubmittedCases = '';

  /** 电话号码 */
  Phone = '';

  /** 省 */
  ProvinceCode = undefined;

  /** 省 */
  ProvinceName = '';
}

export class SiteExpertDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SiteExpertDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteExpertDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteInformationDto {
  /** 市code */
  CityCode = undefined;

  /** 站点缩写 */
  Code = '';

  /** 所属机构id */
  DepartmentId = undefined;

  /** 机构 */
  DepartmentName = '';

  /** 邮箱 */
  Email = '';

  /** 专家 */
  Expert = [];

  /** 专家 */
  ExpertJson = [];

  /** 头像 */
  HeadPic = '';

  /** 头像url */
  HeadPicUrl = '';

  /** 自增id */
  Id = undefined;

  /** 是否提交给专家 */
  IsDiagnosticMode = undefined;

  /** 是否报告工作单位 */
  IsShowCompany = undefined;

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 电话号码 */
  Mobile = '';

  /** 站点名称 */
  Name = '';

  /** 省份code */
  ProvinceCode = undefined;

  /** 分中心 */
  SubCenterId = undefined;

  /** 分中心下拉列表 */
  SubCenterName = '';

  /** 后缀 */
  Suffix = '';

  /** 自增id */
  UserId = undefined;
}

export class SiteInformationDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SiteInformationDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteManagerReqModel {
  /** 市code */
  CityCode = undefined;

  /** 站点缩写 */
  Code = '';

  /** 所属机构id */
  DepartmentId = undefined;

  /** 邮箱 */
  Email = '';

  /** 专家 */
  ExpertJson = [];

  /** 头像 */
  HeadPic = '';

  /** 是否提交给专家 */
  IsDiagnosticMode = undefined;

  /** 是否报告工作单位 */
  IsShowCompany = undefined;

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 电话号码 */
  Mobile = '';

  /** 站点名称 */
  Name = '';

  /** 省份code */
  ProvinceCode = undefined;

  /** 分中心 */
  SubCenterId = undefined;

  /** 后缀 */
  Suffix = '';
}

export class SiteUserDto {
  /** id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 名称 */
  TrueName = '';
}

export class SiteUserDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteUserInforDto {
  /** 市 */
  CityCode = undefined;

  /** 邮箱 */
  Email = '';

  /** 用户头像 */
  HeadPic = '';

  /** 头像 */
  HeadPicUrl = '';

  /** 用户id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 手机号码 */
  Mobile = '';

  /** 省 */
  ProvinceCode = undefined;

  /** 名称 */
  TrueName = '';
}

export class SiteUserInforDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SiteUserInforDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SiteUserReqModel {
  /** 市 */
  CityCode = undefined;

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 账号 */
  LoginName = '';

  /** 密码 */
  LoginPwd = '';

  /** 手机号码 */
  Mobile = '';

  /** 省 */
  ProvinceCode = undefined;

  /** 名称 */
  TrueName = '';
}

export class SliceDescriptionUpdateReqModel {
  /** 切片ID */
  CaseUploadsliceId = undefined;

  /** 评价值 */
  SliceDescription = '';
}

export class SliceQualityAssessmentsRate {
  /** 切片质量评价 */
  RateText = '';

  /** 切片名称 */
  SliceName = '';
}

export class SliceQualityControlDto {
  /** 条形码/会诊编号 */
  BarCode = '';

  /** 病例id */
  CaseId = undefined;

  /** 病理类型 */
  CaseType = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 送检项目 */
  CheckItemName = '';

  /** 送检项目 */
  CheckItems = '';

  /** 切片上传时间 */
  CreateTime = '';

  /** 评价值 */
  Evaluation = undefined;

  /** 评价时间 */
  EvaluationTime = '';

  /** 专家名称 */
  ExpertName = '';

  /** 切片id */
  Id = undefined;

  /** 站点名称 */
  Inspection = '';

  /** 患者名称 */
  Name = '';

  /** 病例号 */
  PathologyNumber = '';

  /** 备注 */
  Remark = '';

  /** 报告发布时间 */
  ReportTime = '';

  /** 复合专家 */
  ReviewExpertName = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位 */
  SampleLocationName = '';

  /** 切片总数 */
  SliceCount = undefined;

  /** 切片描述 */
  SliceDescription = '';

  /** 切片描述时间 */
  SliceDescriptionTime = '';

  /** 切片名称 */
  SliceName = '';

  /** 亚专科 */
  SubProfessional = '';
}

export class SliceQualityControlDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SliceReqModel {
  /** 创建人 */
  CreateUser = '';

  /** 磁盘路径 */
  DiskPath = '';

  /** 染色 */
  Dyeing = '';

  /** 切片名称 */
  FileName = '';

  /** 切片大小 */
  FileSize = undefined;

  /** 切片类型 */
  FileType = '';

  /** 标签 */
  Label = '';

  /** 宏观图 */
  MacroImageUrl = '';

  /** 备注 */
  Remark = '';

  /** 切片类型 */
  SliceType = undefined;

  /** 状态 */
  Status = undefined;

  /** 缩略图 */
  Thumbnail = '';

  /** 切片路径 */
  Url = '';
}

export class SpotCheckCaseCountDto {
  /** Currently selected */
  CurrentlySelected = undefined;

  /** 未抽查病例 */
  UnsampledCase = undefined;

  /** 全部 */
  Whole = undefined;
}

export class SpotCheckCaseCountDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SpotCheckCaseCountDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SpotCheckCaseDto {
  /** 年龄 */
  Age = '';

  /** 诊断中心 */
  DiagnosticCenter = '';

  /** 诊断专家 */
  DiagnosticExpert = '';

  /** 诊断编号 */
  DiagnosticNumber = '';

  /** 专家诊断 */
  ExpertDiagnosis = '';

  /** Id */
  Id = undefined;

  /** 姓名 */
  Name = '';

  /** 原病理号 */
  OriginalPathologyNumber = '';

  /** 病理类型 */
  PathologicalType = '';

  /** 病理类型名称 */
  PathologicalTypeName = '';

  /** 性别 */
  Sex = undefined;

  /** 提交时间 */
  SubmissionTime = '';
}

export class SpotCheckCaseDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StatisticsShowDto {
  /** 年龄 */
  Age = '';

  /** 病理类型 */
  CaseType = '';

  /** 病理子类型 */
  CheckItems = '';

  /** 发布时间 */
  CompleteTime = '';

  /** 会诊号 */
  ConsultationNumber = '';

  /** 提交时间 */
  CreateTime = '';

  /** 初诊专家 */
  ExpertName = '';

  /** 诊断时间 */
  ExpertTime = '';

  /** 患者姓名 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 患者性别 */
  Sex = '';

  /** 会诊状态 */
  Status = '';

  /** 会诊状态名称 */
  StatusName = '';

  /** 分中心名称 */
  SubCentersName = '';

  /** 会诊编号 */
  TurnoverId = '';
}

export class StatisticsShowDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StatusDto {
  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';
}

export class StatusDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StopRecordTaskDto {
  /** Code */
  Code = '';

  /** Record */
  Record = new StopRecordTaskRecordDto();
}

export class StopRecordTaskDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new StopRecordTaskDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StopRecordTaskRecordDto {
  /** 房间id(停止录制和查询云端录制文件时需要) */
  Cid = '';

  /** State */
  State = undefined;

  /** 任务id */
  TaskId = '';
}

export class StorageConfigDto {
  /** AccessKeyId */
  AccessKeyId = '';

  /** AccessKeySecret */
  AccessKeySecret = '';

  /** 附件 */
  Annex = '';

  /** 存储桶 */
  BucketName = '';

  /** Url */
  Endpoint = '';

  /** 头像 */
  HeadPortrait = '';

  /** Region */
  Region = '';

  /** S3ForcePathStyle */
  S3ForcePathStyle = false;

  /** 标注 */
  Sdpl = '';

  /** 切片 */
  Slice = '';

  /** 切片截图 */
  SliceScreenshot = '';

  /** SslEnabled */
  SslEnabled = false;

  /** 对象存储名称 */
  StorageName = '';

  /** 对象存储类型 */
  StorageType = undefined;

  /** 缩略图 */
  Thumbnail = '';

  /** 令牌 */
  Token = '';
}

export class StorageConfigDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new StorageConfigDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StorageUserModel {
  /** Desc:邮箱
Default:
Nullable:False */
  Mail = '';

  /** Phone */
  Phone = '';

  /** Desc:备注
Default:
Nullable:True */
  Remark = '';

  /** Desc:用户名称
Default:
Nullable:False */
  UserName = '';
}

export class StrDto {
  /** Str */
  Str = '';
}

export class StrDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new StrDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StrParam {
  /** 字符串 */
  Str = '';
}

export class StrParamApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new StrParam();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StrParamListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StringApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = '';

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StringInt32DictionaryApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StringObjectDictionaryApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = undefined;

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StringParam {
  /** id */
  Id = undefined;

  /** 字符串 */
  Str = '';
}

export class StringParamApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new StringParam();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class StringStringValueTuple {}

export class SubCenterInformationDto {
  /** 条数 */
  ExpertCount = undefined;

  /** 专家下拉 */
  ExpertDrop = [];

  /** 医院权限下拉 */
  HospitalAuthority = [];
}

export class SubCenterInformationDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SubCenterInformationDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SubCenterListDto {
  /** 预约号前缀 */
  ConsultationNumber = '';

  /** 默认专家 */
  ExpertId = undefined;

  /** 医院权限 */
  HospitalAuthority = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 手抄手机号 */
  Phone = '';

  /** 分诊号前缀/会诊号 */
  PrefixForTriageNumber = '';
}

export class SubCenterListDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SubCenterListUpdateParam {
  /** 预约号前缀 */
  ConsultationNumber = '';

  /** 默认专家 */
  ExpertId = undefined;

  /** 医院权限 */
  HospitalAuthority = undefined;

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 手抄手机号 */
  Phone = '';

  /** 分诊号前缀/会诊号 */
  PrefixForTriageNumber = '';
}

export class SubaggregateDto {
  /** 编码 */
  Code = '';

  /** Id */
  Id = undefined;

  /** ImmunohistochemistryId */
  ImmunohistochemistryId = undefined;

  /** 子集合（免疫组化套餐） */
  List = [];

  /** Name */
  Name = '';

  /** ParentId */
  ParentId = undefined;
}

export class SubmitInspectionDto {
  /** 名称 */
  Name = '';

  /** 百分比 */
  Percentage = '';

  /** 备注 */
  Remark = '';
}

export class SubmitInspectionDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SubmitReturnReqModel {
  /** id */
  Id = undefined;

  /** 是否为冰冻 */
  IsFrozen = false;

  /** 退回原因 */
  Remark = '';

  /** 退回类型 */
  ReturnType = '';
}

export class Suffix {
  /** Id */
  Id = undefined;

  /** Name */
  Name = '';
}

export class SuffixListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SuggestDto {
  /** 联系方式 */
  ContactDetails = '';

  /** 内容 */
  Content = '';

  /** 提交时间 */
  CreateTime = '';

  /** 创建人姓名 */
  CreateUserName = '';

  /** id */
  Id = undefined;

  /** 组织名称 */
  OrganizeName = '';

  /** 平台名称 */
  PlatformName = '';

  /** 站点名称 */
  SiteName = '';
}

export class SuggestDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysCentralMessageLog {
  /** 添加时间 */
  CreateTime = '';

  /** Id */
  Id = undefined;

  /** 记录简讯 */
  LogMsg = '';

  /** 用户ID */
  UserId = undefined;
}

export class SysCentralMessageLogPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysOrganize {
  /** Address */
  Address = '';

  /** EName */
  EName = '';

  /** Id */
  Id = undefined;

  /** LimsName */
  LimsName = '';

  /** Name */
  Name = '';

  /** ParentIdList */
  ParentIdList = '';

  /** Phone */
  Phone = '';

  /** Status */
  Status = undefined;

  /** UpdateTime */
  UpdateTime = '';

  /** UpdateUser */
  UpdateUser = '';

  /** UpdateUserName */
  UpdateUserName = '';
}

export class SysOrganizeApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SysOrganize();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysUpdateLog {
  /** 更新时间 */
  CreateTime = '';

  /** 版本说明 */
  Description = '';

  /** 自增Id */
  Id = undefined;

  /** 版本简介 */
  Introduction = '';

  /** 脚本描述 */
  ScriptDescription = '';

  /** 脚本名称 */
  ScriptName = '';

  /** 脚本版本 */
  ScriptVersion = '';

  /** 更新状态 */
  Status = undefined;

  /** 版本代号 */
  Version = '';

  /** 版本名称 */
  VersionName = '';
}

export class SysUpdateLogPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysUserLog {
  /** 添加时间 */
  AddTime = '';

  /** Code */
  Code = '';

  /** Id */
  Id = undefined;

  /** 用户IP地址 */
  Ip = '';

  /** 记录详情 */
  LogDetails = '';

  /** 记录的操作名称 */
  LogMethod = '';

  /** 记录简讯 */
  LogMsg = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 请求端口 */
  Port = '';

  /** 用户ID */
  UserId = undefined;
}

export class SysUserLogApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new SysUserLog();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysUserLogDto {
  /** 添加时间 */
  AddTime = '';

  /** Id */
  Id = undefined;

  /** 用户IP地址 */
  Ip = '';

  /** 记录详情 */
  LogDetailObj = undefined;

  /** 记录详情 */
  LogDetails = '';

  /** 记录的操作名称 */
  LogMethod = '';

  /** 记录简讯 */
  LogMsg = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 请求端口 */
  Port = '';

  /** 用户ID */
  UserId = undefined;

  /** 用户Name */
  UserName = '';
}

export class SysUserLogDtoListApiResult {
  /** 状态码 */
  Code = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class SysUserLogDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class TemplateExistModel {
  /** 类型 */
  CaseTypeCode = '';

  /** 项目 */
  CheckItemCode = '';

  /** 分中心或站点 */
  Id = undefined;

  /** 截图个数 */
  ShotNum = undefined;

  /** 模板id */
  TemplateId = undefined;

  /** 1、分中心 2、站点 */
  TypeEnum = undefined;
}

export class TenantRolesDot {
  /** 角色Id */
  RoleId = undefined;

  /** 租户Id */
  TenantId = undefined;
}

export class ToBeAssignedDto {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 意向专家 */
  IntentPersonName = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** SliceThumbnail */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class ToBeAssignedDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class UpdateAdminParam {
  /** 机构id */
  DepartmentId = '';

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** Id */
  Id = undefined;

  /** 是否为工程师 */
  IsEngineer = false;

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 名称 */
  TrueName = '';
}

export class UpdateCaseReqModel {
  /** 年龄 */
  Age = '';

  /** 附件 */
  AnnexList = [];

  /** 会诊号 */
  BarCode = '';

  /** 床号 */
  BedNumber = '';

  /** 评价 */
  CaseSiteEvaluateList = [];

  /** 病例类型 */
  CaseType = '';

  /** 送检项目 */
  CheckItems = '';

  /** 临床资料 */
  ClinicalData = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 留言内容 */
  Content = '';

  /** 原病理诊断 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 民族 */
  EthnicId = undefined;

  /** 住院号 */
  HospitalizedId = '';

  /** 病例id */
  Id = undefined;

  /** 身份证号 */
  IdNumber = '';

  /** 免疫组化 */
  Immunohistochemical = '';

  /** 病房 */
  InpatientWard = '';

  /** 送检单位 */
  Inspection = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 意向专家Id */
  IntentPerson = undefined;

  /** 意向专家名称 */
  IntentPersonName = '';

  /** 是否为疑难病 */
  IsRefractoryDisease = undefined;

  /** 工作 */
  Job = '';

  /** 婚姻状况 */
  MaritalStatus = undefined;

  /** 女性月经时间 */
  MenstruationDate = '';

  /** 患者名称 */
  Name = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 病人id */
  PatientId = undefined;

  /** 联系电话 */
  Phone = '';

  /** 备注信息 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 采样日期 */
  SamplingTime = '';

  /** 性别 */
  Sex = undefined;

  /** 切片 */
  SliceList = [];

  /** 亚专科 */
  SubProfessional = '';

  /** 预约日期 */
  SubscribeTime = '';

  /** 大体可见 */
  Thus = '';

  /** 就诊号 */
  VisitingNumber = '';
}

export class UpdateDistributorReqModel {
  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 电话 */
  Mobile = '';

  /** 站点分中心list */
  SiteSubCenterList = [];

  /** 名称 */
  TrueName = '';
}

export class UpdateExpertLevelReqModel {
  /** 收费标准 */
  Charges = '';

  /** 专家级别ID */
  Id = undefined;

  /** 级别名称 */
  Name = '';

  /** 收费标准说明 */
  Remark = '';
}

export class UpdateExpertParam {
  /** 简介 */
  BriefIntroduction = '';

  /** 关联业务 */
  Business = '';

  /** 收费标准 */
  Charges = undefined;

  /** 市code 440300 */
  CityCode = undefined;

  /** 工作单位 */
  Company = undefined;

  /** 邮箱 */
  Email = '';

  /** 专家类型 */
  ExpertType = '';

  /** 专家级别 */
  ExpertlevelCode = '';

  /** 头像 */
  HeadPic = '';

  /** Id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 医生职称 */
  NickName = '';

  /** 病例数量 */
  NumberOfCases = '';

  /** 省code 440000 */
  ProvinceCode = undefined;

  /** 质控权限 */
  QualityControl = '';

  /** 签名 */
  Signature = '';

  /** 关联集合 */
  SiteSubCenter = [];

  /** 专家专长 */
  SpecialistExpertise = '';

  /** 名称 */
  TrueName = '';
}

export class UpdateExpertVocabularysReqModel {
  /** 类型 */
  CaseTypeCode = '';

  /** 送检项 */
  CheckItemsCode = '';

  /** 英文模板 */
  EnglishTemplate = '';

  /** id */
  Id = undefined;

  /** 名称 */
  Name = '';

  /** 描述 */
  Remark = '';

  /** 中文模板 */
  Template = '';
}

export class UpdateFrozenParam {
  /** 年龄 */
  Age = '';

  /** 附件 */
  AnnexList = [];

  /** 预约日期 */
  AppointmentTime = '';

  /** 临床诊断 */
  ClinicalDiagnosis = '';

  /** 临床病历 */
  ClinicalMedicalRecords = '';

  /** 医生名称 */
  Doctor = '';

  /** Id */
  Id = undefined;

  /** 意向专家Id */
  IntentPerson = undefined;

  /** 意向专家名称 */
  IntentPersonName = '';

  /** 名称 */
  Name = '';

  /** 预约号码 */
  Number = '';

  /** 备注 */
  Remark = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 性别 */
  Sex = undefined;

  /** 手术所见 */
  SurgicalFindings = '';

  /** 号码 */
  UserPhone = '';
}

export class UpdateNimidParam {
  /** 云信Id */
  AccId = '';

  /** 网易云信Token */
  Token = '';
}

export class UpdateOrderTypeParam {
  /** 编码 */
  Code = '';

  /** id */
  Id = undefined;

  /** Name */
  Name = '';
}

export class UpdateOrganizenReqModel {
  /** 地址 */
  Address = '';

  /** 英文 */
  EName = '';

  /** id */
  Id = undefined;

  /** LimsName */
  LimsName = '';

  /** 名称 */
  Name = '';

  /** 电话 */
  Phone = '';
}

export class UpdatePassWordReqModel {
  /** 旧密码 */
  OldPassWord = '';

  /** 密码 */
  PassWord = '';
}

export class UpdatePubBaseCodeParam {
  /** 即将超时 */
  AboutToTimeOut = '';

  /** 即将超时单位 */
  AboutToTimeOutUnit = undefined;

  /** 业务类型 */
  BusType = '';

  /** 编码或英文 */
  Code = '';

  /** 诊断超时 */
  DiagnosticTimeOut = '';

  /** 诊断超时单位 */
  DiagnosticTimeOutUnit = undefined;

  /** 扩展名 */
  ExtName = '';

  /** his编码 */
  HisCode = '';

  /** id */
  Id = undefined;

  /** json绑定 */
  JsonData = undefined;

  /** 业务线属性 */
  LineOfBusinessProperties = undefined;

  /** 名称 */
  Name = '';

  /** 父节点 */
  ParentId = undefined;

  /** 备注 */
  Remark = '';
}

export class UpdateQualityEvaluationListQueryParam {
  /** 诊断符合率 */
  CoincidenceRate = '';

  /** 诊断符合率具体评价 */
  CoincidenceRateEvaluation = '';

  /** 诊断符合率备注 */
  CoincidenceRateRemark = '';

  /** 病例id */
  Id = undefined;

  /** 切片质量评价 */
  Quality = undefined;

  /** 切片质量评价备注 */
  QualityRemark = '';

  /** 切片质量具体评价 */
  QualityRemarkEvaluation = '';
}

export class UpdateSiteParam {
  /** 市code */
  CityCode = undefined;

  /** 站点缩写 */
  Code = '';

  /** 所属机构id */
  DepartmentId = undefined;

  /** 邮箱 */
  Email = '';

  /** 专家 */
  ExpertJson = [];

  /** 头像 */
  HeadPic = '';

  /** id */
  Id = undefined;

  /** 是否提交给专家 */
  IsDiagnosticMode = undefined;

  /** 是否报告工作单位 */
  IsShowCompany = undefined;

  /** 账号 */
  LoginName = '';

  /** 电话号码 */
  Mobile = '';

  /** 站点名称 */
  Name = '';

  /** 省份code */
  ProvinceCode = undefined;

  /** 分中心 */
  SubCenterId = undefined;

  /** 后缀 */
  Suffix = '';
}

export class UpdateSiteUserReqModel {
  /** 市 */
  CityCode = undefined;

  /** 邮箱 */
  Email = '';

  /** 头像 */
  HeadPic = '';

  /** 用户id */
  Id = undefined;

  /** 账号 */
  LoginName = '';

  /** 手机号码 */
  Mobile = '';

  /** 省 */
  ProvinceCode = undefined;

  /** 名称 */
  TrueName = '';
}

export class UpdateSlice {
  /** 创建人 */
  CreateUser = '';

  /** 磁盘路径 */
  DiskPath = '';

  /** 染色 */
  Dyeing = '';

  /** 切片名称 */
  FileName = '';

  /** 切片大小 */
  FileSize = undefined;

  /** 切片类型 */
  FileType = '';

  /** Id */
  Id = undefined;

  /** 标签 */
  Label = '';

  /** 宏观图 */
  MacroImageUrl = '';

  /** 备注 */
  Remark = '';

  /** 切片类型 */
  SliceType = undefined;

  /** 状态 */
  Status = undefined;

  /** 缩略图 */
  Thumbnail = '';

  /** 切片路径 */
  Url = '';
}

export class UserCaseDto {
  /** 即将超时/分钟 */
  AboutToTimeOut = undefined;

  /** 年龄 */
  Age = '';

  /** 会诊号 */
  BarCode = '';

  /** 病例类型code */
  CaseTypeCode = '';

  /** 病理类型 */
  CaseTypeName = '';

  /** 项目code */
  CheckItemsCode = '';

  /** 项目 */
  CheckItemsName = '';

  /** 是否收藏 */
  Collect = false;

  /** 会诊号 */
  ConsultationNumber = '';

  /** 流转 */
  DataFlow = false;

  /** 诊断超时/分钟 */
  DiagnosticTimeOut = undefined;

  /** 病例显示时间 */
  DisplayTime = '';

  /** 专家名称 */
  ExpertName = '';

  /** id */
  Id = undefined;

  /** 站点 */
  Inspection = '';

  /** 是否可打印 */
  IsPrint = undefined;

  /** 病人名称 */
  Name = '';

  /** 诊断意见 */
  Opinion = '';

  /** 超时创建时间 */
  OutTimeCreateTime = '';

  /** 送检病例号 */
  PathologyNumber = '';

  /** 年龄 */
  PatientAge = '';

  /** 病人id */
  PatientId = undefined;

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = undefined;

  /** 性别 */
  SexName = '';

  /** 站点Id */
  SiteId = undefined;

  /** 缩略图 */
  SliceThumbnail = '';

  /** 状态 */
  Status = undefined;

  /** 状态名称 */
  StatusName = '';

  /** 亚专科 */
  SubProfessional = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 缩略图objname */
  ThumbnailObjName = '';

  /** 提交时间 */
  Time = '';

  /** 订单流水号/会诊编号 */
  TurnoverId = '';
}

export class UserCaseDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class UserConfig {
  /** 账号 */
  Account = '';

  /** 地区code */
  AreaCode = undefined;

  /** 地区 */
  Avatar = undefined;

  /** 头像 */
  HeadPic = '';

  /** id */
  Id = undefined;

  /** 是否为工程师账号 */
  IsEngineer = false;

  /** 级别 */
  Level = '';

  /** 平台 */
  PlatformId = undefined;

  /** RcpSiteData */
  RcpSiteData = new RcpSiteData();

  /** 名称 */
  RealName = '';

  /** 角色id */
  RoleId = undefined;

  /** 角色名称 */
  RoleName = '';

  /** 签名 */
  Signature = '';

  /** 签名 */
  SignatureObjectName = '';

  /** 站点 */
  SiteId = undefined;

  /** 站点名称 */
  SiteName = '';

  /** 启用状态 */
  Status = undefined;

  /** StorageType */
  StorageType = undefined;

  /** 分中心 */
  SubCenterId = undefined;
}

export class UserConfigApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new UserConfig();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class VerificationCodeReqModel {
  /** 参数类型 */
  PhoneOrMail = '';

  /** SendType */
  SendType = new SendTypeEnum();
}

export class VocabularysLikeDto {
  /** 字段 */
  Template = '';
}

export class VocabularysLikeDtoPageResponse {
  /** 响应码 200为成功 401为未授权 500内部报错等 对应httpcode */
  Code = undefined;

  /** Data */
  Data = [];

  /** 响应消息 */
  Message = '';

  /** PageIndex */
  PageIndex = undefined;

  /** PageSize */
  PageSize = undefined;

  /** 是否成功 */
  Success = false;

  /** TotalCount */
  TotalCount = undefined;
}

export class VocabularysLikeDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class WorkUnitDto {
  /** Id */
  Id = undefined;

  /** Name */
  Name = '';
}

export class WorkUnitDtoPageResult {
  /** 状态码 */
  Code = undefined;

  /** 总记录数 */
  Count = undefined;

  /** 数据集 */
  Data = [];

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}

export class WriteDiagnosticsDto {
  /** 年龄 */
  Age = '';

  /** 条形码 */
  BarCode = '';

  /** 床号 */
  BedNumber = '';

  /** 病例id */
  CaseId = undefined;

  /** CaseTypeCode */
  CaseTypeCode = '';

  /** CaseTypeName */
  CaseTypeName = '';

  /** 细胞量 */
  CellMass = '';

  /** CheckItemsCode */
  CheckItemsCode = '';

  /** CheckItemsName */
  CheckItemsName = '';

  /** 大体可见 */
  ClinicalDiagnosis = '';

  /** 上传时间 */
  CreateTime = '';

  /** 原病理号 */
  DiagnosisContent = '';

  /** 医生名称 */
  DoctorName = '';

  /** 医生电话 */
  DoctorPhone = '';

  /** 电镜描述 */
  ElectronMicroscopyDescription = '';

  /** 免疫荧光 */
  Fluorescence = '';

  /** 医院英文名称 */
  HospitalEnglishName = '';

  /** 住院号 */
  HospitalizedId = '';

  /** 病例Id */
  Id = undefined;

  /** 炎症细胞 */
  InflammatoryCells = '';

  /** 送检日期 */
  InspectionDate = '';

  /** 送检科室 */
  InspectionDept = '';

  /** 数据源 */
  JsonData = '';

  /** 光镜所见 */
  LightMicroScope = '';

  /** 月经 */
  Menstruation = '';

  /** 名称 */
  Name = '';

  /** 专家意见 */
  Opinion = '';

  /** 病理号 */
  PathologyNumber = '';

  /** 电话 */
  Phone = '';

  /** 质控 */
  Quality = undefined;

  /** 报告code */
  ReportCode = '';

  /** 报告名称 */
  ReportName = '';

  /** 报告类型 */
  ReportType = '';

  /** 复核专家光镜所见 */
  ReviewLightMicroScope = '';

  /** 复核专家意见 */
  ReviewOpinion = '';

  /** 取材部位 */
  SampleLocationCode = '';

  /** 取材部位名称 */
  SampleLocationName = '';

  /** 性别 */
  Sex = '';

  /** 站点名称 */
  SiteName = '';

  /** 亚专科 */
  SubProfessionalCode = '';

  /** 亚专科名称 */
  SubProfessionalName = '';

  /** 预约日期 */
  SubscribeTime = '';

  /** 附注建议 */
  Suggest = '';

  /** TBS标准诊断 */
  Tbs = '';

  /** 大体可见 */
  Thus = '';

  /** 就诊号 */
  VisitingNumber = '';

  /** 阴阳 1为阴2为阳 */
  YinPositive = undefined;
}

export class WriteDiagnosticsDtoApiResult {
  /** 状态码 */
  Code = undefined;

  /** Data */
  Data = new WriteDiagnosticsDto();

  /** 信息 */
  Message = '';

  /** 请求是否成功 */
  Success = false;
}
