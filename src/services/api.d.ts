type AxiosRequestConfig = any;
type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export class AccountExistModel {
    /** 字符串是否存在 */
    ExistStr?: string;

    /** id */
    Id?: number;
  }
  export class OssConfigDto {
    /** AccessKeyId */
    AccessKeyId?: string;

    /** AccessKeySecret */
    AccessKeySecret?: string;

    /** 附件 */
    Annex?: string;

    /** 存储桶 */
    BucketName?: string;

    /** Url */
    Endpoint?: string;

    /** 头像 */
    HeadPortrait?: string;

    /** 标注 */
    Sdpl?: string;

    /** 切片 */
    Slice?: string;

    /** 切片截图 */
    SliceScreenshot?: string;

    /** Thumbnail */
    Thumbnail?: string;

    /** 令牌 */
    Token?: string;
  }

  export class H3cConfigDto {
    /** AccessKeyId */
    AccessKeyId?: string;

    /** AccessKeySecret */
    AccessKeySecret?: string;

    /** 附件 */
    Annex?: string;

    /** 存储桶 */
    BucketName?: string;

    /** Url */
    Endpoint?: string;

    /** 头像 */
    HeadPortrait?: string;

    /** 标注 */
    Sdpl?: string;

    /** 切片 */
    Slice?: string;

    /** 切片截图 */
    SliceScreenshot?: string;

    /** Thumbnail */
    Thumbnail?: string;

    /** 令牌 */
    Token?: string;
  }

  export class AddCaseDto {
    /** AdviceInfolist */
    AdviceInfolist?: Array<defs.AdviceInfolistItem>;

    /** AdviceType */
    AdviceType?: string;

    /** CardType */
    CardType?: string;

    /** DiagnoseInfolist */
    DiagnoseInfolist?: Array<defs.DiagnoseInfolistItem>;

    /** PatHisNo */
    PatHisNo?: string;

    /** PatVisitNumber */
    PatVisitNumber?: string;

    /** PatientCard */
    PatientCard?: string;
  }

  export class AddCaseIdReqModel {
    /** 病例id */
    CaseId?: number;

    /** 是否为冰冻 */
    Frozen?: boolean;

    /** 姓名 */
    Name?: string;

    /** 医嘱Id */
    OrderId?: string;

    /** 卡号 */
    PatCardNo?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 就诊号 */
    PatVisitNumber?: string;
  }

  export class AddDocAdviceResDto {
    /** AdviceOrderVoList */
    AdviceOrderVoList?: Array<defs.AdviceOrderVoListItem>;

    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class AddDocAdviceResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.AddDocAdviceResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AddTenantUserRelationModel {
    /** 关联租户及角色 */
    TenantRoles?: Array<defs.TenantRolesDot>;

    /** 用户Id */
    UserId?: number;
  }

  export class AdminDto {
    /** 患者年龄 */
    Age?: string;

    /** 病理类型 */
    CaseType?: string;

    /** 病理子类型 */
    CheckItems?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 初审专家 */
    ExpertName?: string;

    /** 病例id */
    Id?: number;

    /** 患者姓名 */
    Name?: string;

    /** 专家诊断 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 切片质量 */
    Quality?: string;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 患者性别 */
    Sex?: string;

    /** 申请单位 */
    SiteName?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class AdminDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminExpert {
    /** 账号 */
    Account?: string;

    /** 市 */
    CityCode?: number;

    /** 市 */
    CityName?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 昵称 */
    ExpertTypeName?: string;

    /** 职务 */
    ExpertleveName?: string;

    /** 头像 */
    HeadPic?: string;

    /** 头像 */
    HeadPicUrl?: string;

    /** Id */
    Id?: number;

    /** 电话 */
    Phone?: string;

    /** 省 */
    ProvinceCode?: number;

    /** 省 */
    ProvinceName?: string;

    /** 状态 */
    Status?: number;

    /** 分中心id */
    SubCenterId?: number;

    /** false不存在 / ture存在于表中   用户是否存在 */
    UserExist?: boolean;
  }

  export class AdminExpertPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminExpert>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminListDto {
    /** 账号 */
    Account?: string;

    /** id */
    Id?: number;

    /** 是否为工程师 */
    IsEngineer?: boolean;

    /** 名称 */
    Name?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** false不存在 / ture存在于表中   用户是否存在 */
    UserExist?: boolean;
  }

  export class AdminListDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminListDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminQualityControlStatisticsDto {
    /** 年龄 */
    Age?: string;

    /** 病例质量评价 */
    CaseQualityAssessmentRemarks?: string;

    /** 病例质量评价 */
    CaseQualityAssessments?: string;

    /** 送检项目 */
    CheckItems?: string;

    /** 发布时间 */
    CompleteTime?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 诊断符合率备注 */
    DiagnosticCoincidenceRateRemarks?: string;

    /** 诊断符合率 */
    DiagnosticCoincidenceRates?: string;

    /** 诊断专家 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** 姓名 */
    Name?: string;

    /** 专家诊断 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 切片质量评价 */
    Quality?: string;

    /** 评价专家 */
    QualityExpertName?: string;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 性别 */
    Sex?: string;

    /** 诊断中心 */
    SiteName?: string;

    /** 切片质量评价备注 */
    SliceQualityAssessmentsRateRemarks?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class AdminQualityControlStatisticsDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminQualityControlStatisticsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminRemoteManagementDto {
    /** 年龄 */
    Age?: string;

    /** 病例类型 */
    CaseTypeCode?: string;

    /** 病例类型名称 */
    CaseTypeName?: string;

    /** 测试项目 */
    CheckItemsCode?: string;

    /** 测试项目名称 */
    CheckItemsName?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 复审专家 */
    ExpertId?: number;

    /** 诊断专家 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 送检医院/单位 */
    Inspection?: string;

    /** 打印 */
    IsPrint?: number;

    /** 名称 */
    Name?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 切片质量 */
    Quality?: number;

    /** 切片质量 */
    QualityName?: string;

    /** 复审专家 */
    ReviewExpertId?: number;

    /** 复审专家 */
    ReviewExpertName?: string;

    /** 年龄 */
    Sex?: number;

    /** 年龄名称 */
    SexName?: string;

    /** 站点 */
    SiteId?: number;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科CODE */
    SubProfessionalCode?: string;

    /** 亚专科 */
    SubProfessionalName?: string;

    /** 是否超时 */
    TimeOutName?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class AdminRemoteManagementDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminRemoteManagementDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminUpdateDto {
    /** 机构id */
    DepartmentId?: string;

    /** 机构名称 */
    DepartmentName?: string;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 头像Url */
    HeadPicUrl?: string;

    /** id */
    Id?: number;

    /** 是否为工程师 */
    IsEngineer?: boolean;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile?: string;

    /** 名称 */
    TrueName?: string;
  }

  export class AdminUpdateDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.AdminUpdateDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdminWordDto {
    /** 内容 */
    Concent?: string;

    /** 留言时间 */
    CreateTime?: string;

    /** id */
    Id?: number;

    /** 留言人名称 */
    Name?: string;

    /** 被留言方 */
    ParentUserName?: string;

    /** 病例号 */
    PathologyNumber?: string;
  }

  export class AdminWordDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AdminWordDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AdviceInfolistItem {
    /** AdviceCode */
    AdviceCode?: string;

    /** AdviceEnterDeptCode */
    AdviceEnterDeptCode?: string;

    /** AdviceEnterDocCode */
    AdviceEnterDocCode?: string;

    /** AdviceEnterExecCode */
    AdviceEnterExecCode?: string;

    /** AdvicePriorityCode */
    AdvicePriorityCode?: string;

    /** AdviceQty */
    AdviceQty?: string;

    /** ThirdOrderId */
    ThirdOrderId?: string;
  }

  export class AdviceOrderVoListItem {
    /** HisAdviceId */
    HisAdviceId?: string;

    /** RisAdviceId */
    RisAdviceId?: string;

    /** ThirdOrderId */
    ThirdOrderId?: string;
  }

  export class AmericaConfig {
    /** 网易云信是否开启 */
    NetEaseYunxin?: boolean;

    /** 短信是否开启 */
    Sms?: boolean;
  }

  export class AmericaConfigApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.AmericaConfig;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AmountDto {
    /** 平台金额 */
    Amount?: string;

    /** 平台金额 */
    PlatformAmount?: string;
  }

  export class AmountDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.AmountDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AnhuiReportStatusReModel {
    /** 医嘱id */
    OrderId?: string;

    /** his订单号 */
    PatHisNo?: string;
  }

  export class AnnexBaseReqModel {
    /** 文件磁盘路径 */
    DiskUrl?: string;

    /** id */
    Id?: number;

    /** 文件名称 */
    ImageName?: string;

    /** 文件大小 */
    ImageSize?: number;

    /** 文件类型 */
    ImageType?: string;

    /** 状态 */
    Status?: number;

    /** 文件Http路径 */
    Url?: string;
  }

  export class AnnexDto {
    /** 附件url */
    AnnexUrl?: string;

    /** 病例ID */
    CaseId?: number;

    /** 上传时间 */
    CreateTime?: string;

    /** 上传用户 */
    CreateUser?: string;

    /** 上传用户名称 */
    CreateUserName?: string;

    /** 文件磁盘路径 */
    DiskUrl?: string;

    /** 图片id */
    Id?: number;

    /** 图片名称 */
    ImageName?: string;

    /** 文件大小 */
    ImageSize?: number;

    /** 文件类型 */
    ImageType?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 附件类型name */
    TypeName?: string;

    /** 文件Http路径 */
    Url?: string;
  }

  export class AnnexDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.AnnexDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AnnexReqModel {
    /** 文件磁盘路径 */
    DiskUrl?: string;

    /** Id */
    Id?: number;

    /** 文件名称 */
    ImageName?: string;

    /** 文件大小 */
    ImageSize?: number;

    /** 文件类型 */
    ImageType?: string;

    /** 状态 */
    Status?: number;

    /** 文件Http路径 */
    Url?: string;
  }

  export class ApiResult {
    /** 状态码 */
    Code?: number;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ApplicationManagementDto {
    /** 年龄 */
    Age?: string;

    /** 病例类型 */
    CaseType?: string;

    /** 病例类型名称 */
    CaseTypeName?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 病例id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 诊断中心 */
    SiteName?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class ApplicationManagementDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ApplicationManagementDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class AssignExpertReqModel {
    /** 专家ID */
    ExpertId: number;

    /** 专家名称 */
    ExpertName: string;

    /** id */
    Id: number;

    /** 是否为冰冻预约 */
    IsFrozen?: boolean;
  }

  export class Assigned {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 意向专家 */
    IntentPersonName?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** SliceThumbnail */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class AssignedPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.Assigned>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class BasePatientByIdNoReqDto {
    /** IdNo */
    IdNo?: string;

    /** IdType */
    IdType?: string;

    /** Name */
    Name?: string;

    /** Phone */
    Phone?: string;
  }

  export class BasePatientByIdNoResDto {
    /** PatientCardDetailVoList */
    PatientCardDetailVoList?: Array<defs.PatientCardDetailVoListItem>;

    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class BasePatientByIdNoResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.BasePatientByIdNoResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class BoolDto {
    /** IsFalse */
    IsFalse?: boolean;
  }

  export class BoolDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.BoolDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class BooleanApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: boolean;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class BulkAllocationReqModel {
    /** 专家ID */
    ExpertId: number;

    /** 专家名称 */
    ExpertName: string;

    /** 病例id集合 */
    Id?: Array<defs.IdParam>;

    /** 是否为冰冻预约 */
    IsFrozen?: boolean;
  }

  export class BusinessTypeChargeParam {
    /** 业务类型id */
    Id: number;

    /** 金额 */
    Money: string;

    /** 站点id */
    SiteId: number;
  }

  export class CancelDocAdviceItem {
    /** HisAdviceId */
    HisAdviceId?: string;
  }

  export class CancelDocAdviceReqDto {
    /** AdviceInfolist */
    AdviceInfolist?: Array<defs.CancelDocAdviceItem>;

    /** PatHisNo */
    PatHisNo?: string;

    /** PatVisitNumber */
    PatVisitNumber?: string;

    /** UpdateUserCode */
    UpdateUserCode?: string;

    /** UpdateUserDesc */
    UpdateUserDesc?: string;
  }

  export class CancelDocAdviceResDto {
    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class CancelDocAdviceResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CancelDocAdviceResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CancelRegisterReqDto {
    /** AdmNo */
    AdmNo?: string;
  }

  export class CancelRegisterResDto {
    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class CancelRegisterResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CancelRegisterResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseBusTypeChargeDto {
    /** CaseBusTypeCharge */
    CaseBusTypeCharge?: Array<defs.CaseBusTypeChargeDto>;

    /** Id */
    Id?: number;

    /** 金额 */
    Money?: string;

    /** 名称 */
    Name?: string;

    /** 父级id */
    ParentId?: number;
  }

  export class CaseBusTypeChargeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseBusTypeChargeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseChargesDto {
    /** CaseChargesModels */
    CaseChargesModels?: defs.CaseChargesModel;

    /** 缴费状态 */
    CaseChargingStatus?: number;

    /** 收费时间 */
    ChargingTime?: string;

    /** 会诊专家名称 */
    ExpertName?: string;

    /** 病例id */
    Id?: number;

    /** MedicalAdviceFees */
    MedicalAdviceFees?: defs.MedicalAdviceFees;

    /** 姓名 */
    Name?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 关联病史 */
    PathologyNumberList?: Array<string>;

    /** 复核专家名称 */
    ReviewExpertName?: string;

    /** 站点名称 */
    SiteName?: string;

    /** 提交时间 */
    Time?: string;
  }

  export class CaseChargesDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseChargesDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseChargesModel {
    /** 实际支付金额 */
    ActualPaymentAmount?: string;

    /** 安徽省立金额 */
    AnhuiProvincialGovernmentAmount?: string;

    /** 收费状态 */
    CaseChargingStatus?: number;

    /** 收费时间 */
    CaseChargingTime?: string;

    /** 收费状态 */
    ChargingStatus?: string;

    /** 诊断时间 */
    DiagnosisTime?: string;

    /** 诊断状态 */
    DiagnosticStatus?: string;

    /** 诊断专家团队所属医院金额 */
    HospitalAmount?: string;

    /** 平台金额 */
    PlatformAmount?: string;

    /** 项目类型 */
    ProjectType?: string;

    /** 病例类型 */
    SettlementType?: string;

    /** 站点金额 */
    SiteAmount?: string;
  }

  export class CaseConfig {
    /** 配置项编号 */
    Code?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 自增ID */
    Id?: number;

    /** 配置项名称 */
    Name?: string;

    /** 平台Id */
    PlatformId?: string;

    /** 备注 */
    Remark?: string;

    /** 站点Id */
    SiteId?: string;

    /** 站点名称 */
    SiteName?: string;

    /** 启用状态 */
    Status?: number;

    /** 配置值 */
    Value?: string;

    /** 配置值2 */
    Value2?: string;
  }

  export class CaseConfigPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseConfig>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseCreateReqModel {
    /** 年龄 */
    Age: string;

    /** 附件 */
    AnnexList?: Array<defs.AnnexBaseReqModel>;

    /** 会诊号 */
    BarCode: string;

    /** 床号 */
    BedNumber?: string;

    /** 评价 */
    CaseSiteEvaluateList?: Array<defs.CaseSiteEvaluate>;

    /** 病例类型 */
    CaseType: string;

    /** 送检项目 */
    CheckItems: string;

    /** 临床资料 */
    ClinicalData: string;

    /** 临床诊断 */
    ClinicalDiagnosis: string;

    /** 留言类容 */
    Content?: string;

    /** 原病理诊断 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 民族 */
    EthnicId?: number;

    /** 住院号 */
    HospitalizedId?: string;

    /** 身份证号 */
    IdNumber?: string;

    /** 免疫组化 */
    Immunohistochemical?: string;

    /** 病房 */
    InpatientWard?: string;

    /** 送检单位 */
    Inspection: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 意向专家Id */
    IntentPerson?: number;

    /** 意向专家名称 */
    IntentPersonName?: string;

    /** 是否为疑难病 */
    IsRefractoryDisease?: number;

    /** 工作 */
    Job?: string;

    /** 婚姻状况 */
    MaritalStatus?: number;

    /** 女性月经时间 */
    MenstruationDate?: string;

    /** 患者名称 */
    Name: string;

    /** 病理号 */
    PathologyNumber: string;

    /** 联系电话 */
    Phone?: string;

    /** 备注信息 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 采样日期 */
    SamplingTime: string;

    /** 性别 */
    Sex: number;

    /** 切片 */
    SliceList?: Array<defs.SliceReqModel>;

    /** 亚专科 */
    SubProfessional: string;

    /** 预约日期 */
    SubscribeTime: string;

    /** 大体可见 */
    Thus?: string;

    /** 会诊编号 */
    TurnoverId?: string;

    /** 就诊号 */
    VisitingNumber?: string;
  }

  export class CaseDataWithQrDto {
    /** AnnexList */
    AnnexList?: defs.AnnexDtoPageResult;

    /** CaseCollection */
    CaseCollection?: defs.BoolDto;

    /** 病例报告单 */
    Report?: string;

    /** SliceList */
    SliceList?: defs.CaseSliceDtoPageResult;

    /** WriteDiagnosticsDto */
    WriteDiagnosticsDto?: defs.WriteDiagnosticsDto;
  }

  export class CaseDataWithQrDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseDataWithQrDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseDto {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class CaseDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseFrozen {
    /** 年龄 */
    Age?: string;

    /** 预约日期 */
    AppointmentTime: string;

    /** 送检类型 */
    CaseType?: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 临床病历 */
    ClinicalMedicalRecords?: string;

    /** 完成时间 */
    CompleteTime?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 医生名称 */
    Doctor: string;

    /** 自增id */
    Id: number;

    /** 名称 */
    Name?: string;

    /** 预约号码 */
    Number: string;

    /** 平台id */
    PlatformId?: number;

    /** 备注 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 性别 */
    Sex?: number;

    /** 站点id */
    SiteId?: number;

    /** 冰冻状态 */
    Status?: number;

    /** 分中心id */
    SubCenterId?: number;

    /** 手术所见 */
    SurgicalFindings?: string;

    /** 订单编号 */
    TurnoverId: string;

    /** 病人id */
    UserId: number;

    /** 医生号码 */
    UserPhone: string;
  }

  export class CaseFrozenApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseFrozen;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseHistoricalDiagnosticRecordsDto {
    /** 申请站点 */
    ApplicationSite?: string;

    /** CheckItemsCode */
    CheckItemsCode?: string;

    /** 送检项目名称 */
    CheckItemsName?: string;

    /** 会诊专家 */
    ConsultationExpert?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 诊断时间 */
    DiagnosisTime?: string;

    /** Id */
    Id?: number;

    /** 身份证 */
    IdNumber?: string;

    /** 名称 */
    Name?: string;

    /** 原病理号 */
    OriginalPathologicalNumber?: string;

    /** 发布时间 */
    ReleaseTime?: string;

    /** 复核专家 */
    ReviewExpert?: string;
  }

  export class CaseHistoricalDiagnosticRecordsDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseHistoricalDiagnosticRecordsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseInforDto {
    /** 年龄 */
    Age?: string;

    /** 病理类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 是否选中 */
    Check?: boolean;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 诊断专家 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class CaseInforDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseInforDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseLabelParam {
    /** CaseId */
    CaseId?: number;

    /** 标签id */
    LabelId?: number;
  }

  export class CaseLeavemsgDto {
    /** 内容 */
    Content?: string;

    /** 留言时间 */
    CreateTime?: string;

    /** id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 留言方 */
    ParentUserName?: string;

    /** 病例号 */
    PathologyNumber?: string;
  }

  export class CaseLeavemsgDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseLeavemsgDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseMedicalAdviceFee {
    /** 病例id */
    CaseId?: number;

    /** Id */
    Id?: number;

    /** 医嘱id */
    JsonId?: string;

    /** 医嘱金额 */
    OrderAmount?: string;
  }

  export class CaseMedicalAdviceFeeApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseMedicalAdviceFee;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseMedicalHistoryModel {
    /** 关联病例 CaseId */
    AssociatedCaseId?: number;

    /** 当前病例 Id */
    CurrentCaseId?: number;
  }

  export class CaseMessageDto {
    /** 内容 */
    Content?: string;

    /** Id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 时间 */
    Time?: string;
  }

  export class CaseMessageDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseMessageDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseOperationRecordsReqModel {
    /** 入参 */
    Parameter?: string;

    /** 路径名称 */
    PathName?: string;

    /** 返回参数 */
    ReturnData?: string;

    /** 路径 */
    Url?: string;
  }

  export class CaseQualityAssessment {
    /** CaseInformationId */
    CaseInformationId?: number;

    /** 病例质量评价备注 */
    CaseQualityAssessmentRemarks?: string;

    /** 病例质量评价 */
    CaseQualityAssessments?: number;

    /** 诊断符合率备注 */
    DiagnosticCoincidenceRateRemarks?: string;

    /** 诊断符合率 */
    DiagnosticCoincidenceRates?: number;

    /** 自增Id */
    Id?: number;

    /** 切片质量评价备注 */
    SliceQualityAssessmentsRateRemarks?: string;

    /** 指控评价用户ID */
    UserId?: number;
  }

  export class CaseQualityAssessmentApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseQualityAssessment;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseQualityAssessmentDto {
    /** 年龄 */
    Age?: string;

    /** 质控评价ID */
    CaseQualityAssessmentId?: number;

    /** 病例质量评价 */
    CaseQualityAssessmentsRateText?: string;

    /** CaseQualityAssessmentsRates */
    CaseQualityAssessmentsRates?: number;

    /** 病例类型 */
    CaseType?: string;

    /** 病例类型 */
    CaseTypeText?: string;

    /** 发布时间 */
    CompleteTime?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 诊断时间 */
    DiagnosisTime?: string;

    /** 诊断符合率文本 */
    DiagnosticCoincidenceRateText?: string;

    /** 诊断符合率 */
    DiagnosticCoincidenceRates?: number;

    /** 专家名称 */
    ExpertName?: string;

    /** 病例ID */
    Id?: number;

    /** 患者名称 */
    Name?: string;

    /** 意见 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人表id */
    PatientId?: number;

    /** 质控专家 */
    QualityControlExpert?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexText?: string;

    /** 站点id */
    SiteId?: number;

    /** 诊断中心 */
    SiteName?: string;

    /** 切片质量评价 */
    SliceEvaluation?: number;

    /** 切片质量评价文本 */
    SliceEvaluationText?: string;

    /** 提交时间 */
    SubmissionTime?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class CaseQualityAssessmentDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseQualityAssessmentDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseQualityAssessmentLogDto {
    /** 病例质量评价备注 */
    CaseQualityAssessmentRemarks?: string;

    /** 病例质量评价文本 */
    CaseQualityAssessmentText?: number;

    /** Code */
    Code?: string;

    /** 诊断符合率备注 */
    DiagnosticCoincidenceRateRemarks?: string;

    /** 诊断符合率文本 */
    DiagnosticCoincidenceRateText?: number;

    /** 记录的操作名称 */
    LogMethod?: string;

    /** 记录简讯 */
    LogMsg?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 切片质量评价备注 */
    SliceQualityAssessmentsRateRemarks?: string;

    /** 切片质量评价List */
    SliceQualityAssessmentsRateTexts?: Array<defs.SliceQualityAssessmentsRate>;
  }

  export class CaseQualityAssessmentSingleDto {
    /** CaseInformationId */
    CaseInformationId?: number;

    /** 病例质量评价备注 */
    CaseQualityAssessmentRemarks?: string;

    /** 病例质量评价 */
    CaseQualityAssessments?: number;

    /** 切片评价List */
    CaseUploadslices?: Array<defs.CaseUploadslice>;

    /** 诊断符合率备注 */
    DiagnosticCoincidenceRateRemarks?: string;

    /** 诊断符合率 */
    DiagnosticCoincidenceRates?: number;

    /** 自增Id */
    Id?: number;

    /** 切片质量评价备注 */
    SliceQualityAssessmentsRateRemarks?: string;

    /** 指控评价用户ID */
    UserId?: number;

    /** 指控评价用户名称 */
    UserName?: string;
  }

  export class CaseQualityAssessmentSingleDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseQualityAssessmentSingleDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseRenderingDto {
    /** 年龄 */
    Age: string;

    /** 会诊号 */
    BarCode: string;

    /** 床号 */
    BedNumber?: string;

    /** 评价 */
    CaseSiteEvaluateList?: Array<defs.CaseSiteEvaluate>;

    /** 病例类型 */
    CaseType: string;

    /** 病例类型名称 */
    CaseTypeName?: string;

    /** 送检项目 */
    CheckItems: string;

    /** 送检项目名称 */
    CheckItemsName?: string;

    /** 临床资料 */
    ClinicalData: string;

    /** 临床诊断 */
    ClinicalDiagnosis: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 原病理诊断 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 民族 */
    EthnicId?: number;

    /** 民族 */
    EthnicName?: string;

    /** 初审专家 */
    ExpertName?: string;

    /** 住院号 */
    HospitalizedId?: string;

    /** 病例id */
    Id?: number;

    /** 身份证号 */
    IdNumber?: string;

    /** 免疫组化 */
    Immunohistochemical?: string;

    /** 病房 */
    InpatientWard?: string;

    /** 送检单位 */
    Inspection: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 是否为疑难病 */
    IsRefractoryDisease?: number;

    /** 工作 */
    Job?: string;

    /** 婚姻状况 */
    MaritalStatus?: number;

    /** 婚姻状况名称 */
    MaritalStatusName?: string;

    /** 女性月经时间 */
    MenstruationDate?: string;

    /** 患者名称 */
    Name: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber: string;

    /** 病人id */
    PatientId?: number;

    /** 联系电话 */
    Phone?: string;

    /** 备注信息 */
    Remark?: string;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 取材部位 */
    SampleLocationName?: string;

    /** 采样日期 */
    SamplingTime: string;

    /** 性别 */
    Sex: number;

    /** 性别name */
    SexName?: string;

    /** 状态 */
    Status?: number;

    /** 亚专科 */
    SubProfessional: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 预约日期 */
    SubscribeTime: string;

    /** 大体可见 */
    Thus?: string;

    /** 就诊号 */
    VisitingNumber?: string;
  }

  export class CaseRenderingDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CaseRenderingDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseRenderingDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseRenderingDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseReportReqModel {
    /** 细胞量 */
    CellMass?: string;

    /** 镜下描述 */
    Description?: string;

    /** 电镜描述 */
    ElectronMicroscopyDescription?: string;

    /** 免疫组化 */
    Fluorescence?: string;

    /** id */
    Id: number;

    /** 炎症细胞 */
    InflammatoryCells?: string;

    /** json数据 */
    JsonData: string;

    /** 光镜所见 */
    LightMicroScope?: string;

    /** 意见 */
    Opinion: string;

    /** 切片质量评价 */
    Quality: number;

    /** 附注意见 */
    Suggest?: string;

    /** TBS标准诊断 */
    Tbs?: string;

    /** 阴阳 1为阴2为阳 */
    YinPositive?: number;
  }

  export class CaseReturnedDto {
    /** 账号 */
    Account?: string;

    /** 退回时间 */
    ApplyTime?: string;

    /** 属性 */
    Attribute?: string;

    /** id */
    Id?: number;

    /** 名字 */
    Name?: string;

    /** 退回原因类型 */
    ReasonType?: string;

    /** 审批备注 */
    Remark?: string;
  }

  export class CaseReturnedDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseReturnedDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseSiteEvaluate {
    /** 类型 */
    BusType?: string;

    /** 病例id */
    CaseId?: number;

    /** 值 */
    Code?: number;

    /** CreateTime */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** 站点id */
    SiteId?: number;
  }

  export class CaseSliceDto {
    /** 病例Id */
    CaseId?: number;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 磁盘路径 */
    DiskPath?: string;

    /** 染色 */
    Dyeing?: string;

    /** 染色名称 */
    DyeingName?: string;

    /** 评价值 */
    Evaluation?: number;

    /** FileName */
    FileName?: string;

    /** FileSize */
    FileSize?: number;

    /** FileType */
    FileType?: string;

    /** Id */
    Id?: number;

    /** 标签 */
    Label?: string;

    /** 宏观图 */
    MacroImageUrl?: string;

    /** 备注 */
    Remark?: string;

    /** 标注文件 */
    Sdpl?: string;

    /** 切片评价 */
    SliceDescription?: string;

    /** 磁盘路径 */
    SliceDiskPath?: string;

    /** 标签 */
    SliceLabel?: string;

    /** 宏观图 */
    SliceMacroImageUrl?: string;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 切片类型 */
    SliceType?: number;

    /** 切片路径 */
    SliceUrl?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 缩略图 */
    Thumbnail?: string;

    /** 切片路径 */
    Url?: string;
  }

  export class CaseSliceDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CaseSliceDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CaseTimeoutMessage {
    /** CaseId */
    CaseId?: number;

    /** 超时理由 */
    CaseOutTimeMessage?: string;

    /** CreateTime */
    CreateTime?: string;

    /** CreateUser */
    CreateUser?: string;

    /** CreateUserName */
    CreateUserName?: string;

    /** Id */
    Id?: number;

    /** Status */
    Status?: number;
  }

  export class CaseTimeoutRecord {
    /** 即将超时 */
    AboutToTimeOut?: number;

    /** 病例Id */
    CaseId?: number;

    /** CreateTime */
    CreateTime?: string;

    /** CreateUser */
    CreateUser?: string;

    /** CreateUserName */
    CreateUserName?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: number;

    /** Id */
    Id?: number;
  }

  export class CaseTypeShowEnum {}

  export class CaseUploadslice {
    /** 病例Id */
    CaseId?: number;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 磁盘路径 */
    DiskPath?: string;

    /** 染色 */
    Dyeing?: string;

    /** 评价值 */
    Evaluation?: number;

    /** 评价时间 */
    EvaluationTime?: string;

    /** FileName */
    FileName?: string;

    /** FileSize */
    FileSize?: number;

    /** FileType */
    FileType?: string;

    /** 自增Id */
    Id?: number;

    /** 标签 */
    Label?: string;

    /** 宏观图 */
    MacroImageUrl?: string;

    /** 备注 */
    Remark?: string;

    /** 标注文件 */
    Sdpl?: string;

    /** 切片描述 */
    SliceDescription?: string;

    /** 切片描述时间 */
    SliceDescriptionTime?: string;

    /** 切片类型 */
    SliceType?: number;

    /** 状态 */
    Status?: number;

    /** 缩略图 */
    Thumbnail?: string;

    /** 切片路径 */
    Url?: string;
  }

  export class CasehistoryReqModel {
    /** 证件号 */
    IdNo?: string;

    /** 证件号类型 */
    IdType?: string;

    /** Name */
    Name?: string;

    /** 操作类型 【值自己定义入：1创建 2检索】 */
    OperateType?: number;

    /** 卡号 */
    PatCardNo?: string;

    /** 卡类型 */
    PatCardType?: string;

    /** 卡类型备注 */
    PatCardTypeDesc?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 费用类型 */
    PatType?: string;

    /** Phone */
    Phone?: string;
  }

  export class ChannelStatusEnum {}

  export class ChannelStatusParam {
    /** 频道id */
    Cid?: string;

    /** StatusEnum */
    StatusEnum?: defs.ChannelStatusEnum;
  }

  export class ChannelUr {
    /** HlsPullUrl */
    HlsPullUrl?: string;

    /** HttpPullUrl */
    HttpPullUrl?: string;

    /** Name */
    Name?: string;

    /** PushUrl */
    PushUrl?: string;

    /** RtmpPullUrl */
    RtmpPullUrl?: string;
  }

  export class ChannelUrApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ChannelUr;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ChargingItemConfigurationListDto {
    /** id */
    Id?: number;

    /** 站点名称 */
    Name?: string;

    /** 医院级别 */
    SiteLevel?: string;

    /** 是否启用收费 */
    Status?: number;
  }

  export class ChargingItemConfigurationListDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ChargingItemConfigurationListDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CommLoginParam {
    /** Id */
    Id: number;

    /** 密码 */
    LoginPwd: string;
  }

  export class CommonDto {
    /** 值 */
    Id?: string;

    /** 名称 */
    Name?: string;
  }

  export class CommonDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CommonDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CommonDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.CommonDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CompleteAdminReqModel {
    /** 机构id */
    DepartmentId: string;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile: string;

    /** 名称 */
    TrueName: string;
  }

  export class CompleteCreateExpertModel {
    /** 简介 */
    BriefIntroduction: string;

    /** 关联业务 */
    Business?: string;

    /** 收费标准 */
    Charges: number;

    /** 市code 440300 */
    CityCode: number;

    /** 工作单位 */
    Company: string;

    /** 邮箱 */
    Email?: string;

    /** 专家类型 */
    ExpertType: string;

    /** 专家级别 */
    ExpertlevelCode: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 电话号码 */
    Mobile: string;

    /** 医生职称 */
    NickName: string;

    /** 病例数量 */
    NumberOfCases?: string;

    /** 省code 440000 */
    ProvinceCode: number;

    /** 质控权限 */
    QualityControl?: string;

    /** 签名 */
    Signature: string;

    /** 分中心和站点 */
    SiteSubCenter?: Array<defs.ExpertSiteDto>;

    /** 专家专长 */
    SpecialistExpertise: string;

    /** 名称 */
    TrueName: string;
  }

  export class CompleteDistributorReqModel {
    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile: string;

    /** 站点分中心list */
    SiteSubCenterList?: Array<defs.DistributorSiteDto>;

    /** 名称 */
    TrueName: string;
  }

  export class CompleteSiteManagerReqModel {
    /** 市code */
    CityCode: number;

    /** 站点缩写 */
    Code: string;

    /** 所属机构id */
    DepartmentId: number;

    /** 邮箱 */
    Email?: string;

    /** 专家 */
    ExpertJson?: Array<defs.IdParam>;

    /** 头像 */
    HeadPic?: string;

    /** 是否提交给专家 */
    IsDiagnosticMode?: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 账号 */
    LoginName: string;

    /** 电话号码 */
    Mobile: string;

    /** 站点名称 */
    Name: string;

    /** 省份code */
    ProvinceCode: number;

    /** 分中心 */
    SubCenterId: number;

    /** 后缀 */
    Suffix?: string;
  }

  export class ConfigReqModel {
    /** Code */
    Id?: number;

    /** 值 */
    Value?: string;
  }

  export class ConsultationExpertResultDto {
    /** 4月 */
    April?: number;

    /** 8月 */
    August?: number;

    /** 12月 */
    December?: number;

    /** 医生名称 */
    DoctorName?: string;

    /** 2月 */
    February?: number;

    /** 1月 */
    January?: number;

    /** 7月 */
    July?: number;

    /** 6月 */
    June?: number;

    /** 3月 */
    March?: number;

    /** 5月 */
    May?: number;

    /** 11月 */
    November?: number;

    /** 10月 */
    October?: number;

    /** 9月 */
    September?: number;
  }

  export class ConsultationExpertResultDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ConsultationExpertResultDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CorrelationParam {
    /** 当前病例id */
    CaseId?: number;

    /** 关联病例id */
    OriginalCaseId?: number;
  }

  export class CreateAdminReqModel {
    /** 机构id */
    DepartmentId: string;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 是否为工程师 */
    IsEngineer?: boolean;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd: string;

    /** 电话 */
    Mobile: string;

    /** 名称 */
    TrueName: string;
  }

  export class CreateDistributorReqModel {
    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd: string;

    /** 电话 */
    Mobile: string;

    /** 站点分中心list */
    SiteSubCenterList?: Array<defs.DistributorSiteDto>;

    /** 名称 */
    TrueName: string;
  }

  export class CreateExpertParam {
    /** 简介 */
    BriefIntroduction: string;

    /** 关联业务 */
    Business?: string;

    /** 收费标准 */
    Charges: number;

    /** 市code 440300 */
    CityCode: number;

    /** 工作单位 */
    Company: number;

    /** 邮箱 */
    Email?: string;

    /** 专家类型 */
    ExpertType: string;

    /** 专家级别 */
    ExpertlevelCode: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd: string;

    /** 电话号码 */
    Mobile: string;

    /** 医生职称 */
    NickName: string;

    /** 病例数量 */
    NumberOfCases?: string;

    /** 省code 440000 */
    ProvinceCode: number;

    /** 质控权限 */
    QualityControl?: string;

    /** 签名 */
    Signature: string;

    /** 分中心和站点 */
    SiteSubCenter?: Array<defs.ExpertSiteDto>;

    /** 专家专长 */
    SpecialistExpertise: string;

    /** 名称 */
    TrueName: string;
  }

  export class CreateParam {
    /** 名称 */
    ChannelName?: string;
  }

  export class CreatePatientCardReqDto {
    /** Address */
    Address?: string;

    /** IdNo */
    IdNo?: string;

    /** IdType */
    IdType?: string;

    /** Name */
    Name?: string;

    /** Phone */
    Phone?: string;
  }

  export class CreatePatientCardResDto {
    /** PatCardNo */
    PatCardNo?: string;

    /** PatCardType */
    PatCardType?: string;

    /** PatCardTypeDesc */
    PatCardTypeDesc?: string;

    /** PatHisNo */
    PatHisNo?: string;

    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class CreatePatientCardResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CreatePatientCardResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CreateRecordTaskDto {
    /** Code */
    Code?: string;

    /** Record */
    Record?: defs.CreateRecordTaskRecordDto;
  }

  export class CreateRecordTaskDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.CreateRecordTaskDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class CreateRecordTaskRecordDto {
    /** 房间id(停止录制和查询云端录制文件时需要) */
    Cid?: string;

    /** 任务id */
    TaskId?: string;
  }

  export class CreateRoomParam {
    /** 病例id */
    CaseId?: number;

    /** 房间名称 */
    ChannelName?: string;
  }

  export class CreateSubCenterReqModel {
    /** 预约号前缀 */
    ConsultationNumber?: string;

    /** 默认专家 */
    ExpertId?: number;

    /** 医院权限 */
    HospitalAuthority: number;

    /** 名称 */
    Name: string;

    /** 手抄手机号 */
    Phone: string;

    /** 分诊号前缀/会诊号 */
    PrefixForTriageNumber?: string;
  }

  export class CreateSuggesReqModel {
    /** 联系方式 */
    ContactDetails: string;

    /** 内容 */
    Content: string;
  }

  export class CreateTeamParam {
    /** 病例id */
    CaseId?: number;

    /** 邀请发送的文字，最大长度150字符 */
    Msg?: string;

    /** 群名称 */
    Tname?: string;
  }

  export class DataFlowDto {
    /** 病例id */
    CaseId?: number;

    /** 数据来源 */
    DataSources?: string;

    /** 标签 */
    Label?: string;

    /** 亚专科 */
    SampleLocationName?: string;

    /** 性别 */
    SexName?: string;

    /** 年龄 */
    StrAge?: string;

    /** 姓名 */
    StrName?: string;

    /** 缩略图 */
    Thumbnail?: Array<string>;

    /** 登记时间 */
    Time?: string;
  }

  export class DataFlowDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DataFlowDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowEditDto {
    /** 年龄 */
    Age?: string;

    /** id */
    Id?: number;

    /** 身份证 */
    IdNumber?: string;

    /** 标签 */
    Label?: string;

    /** 名称 */
    Name?: string;

    /** 电话号码 */
    Phone?: string;

    /** 性别 */
    SexName?: string;
  }

  export class DataFlowEditDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.DataFlowEditDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowEditDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DataFlowEditDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowLogDto {
    /** 主键 */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 项目 */
    Project?: number;

    /** 病理号 */
    Time?: string;
  }

  export class DataFlowLogDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DataFlowLogDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowReportDto {
    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 大体所见 */
    Thus?: string;
  }

  export class DataFlowReportDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.DataFlowReportDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowReqModel {
    /** 病例id */
    CaseId: number;

    /** 年龄 */
    EncryptionAge?: number;

    /** 身份证 */
    EncryptionIdCard?: number;

    /** 名称 */
    EncryptionName?: number;

    /** 电话号码 */
    EncryptionPhone?: number;

    /** 性别 */
    EncryptionSex?: number;

    /** 标签 */
    EncryptionSliceLabel?: number;

    /** 项目 1科研 2规培 3读片会 TODO 使用枚举类型 */
    Project?: number;
  }

  export class DataFlowSliceDto {
    /** 病例Id */
    CaseId?: number;

    /** 磁盘路径 */
    DiskPath?: string;

    /** 切片名称 */
    FileName?: string;

    /** 切片大小 */
    FileSize?: number;

    /** 自增Id */
    Id?: number;

    /** 标签 */
    Label?: string;

    /** 宏观图 */
    MacroImageUrl?: string;

    /** 缩略图 */
    Thumbnail?: string;

    /** 切片路径 */
    Url?: string;
  }

  export class DataFlowSliceDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DataFlowSliceDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DataFlowWithdrawReqModel {
    /** id */
    Id: number;

    /** 项目 1科研 */
    Project?: number;
  }

  export class DateTimeApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: string;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DeleteCaseReqModel {
    /** id */
    Id: number;

    /** 是否删除 */
    IsFrozen?: boolean;
  }

  export class DiagnoseInfolistItem {
    /** DiagnoseCode */
    DiagnoseCode?: string;

    /** DiagnoseDescription */
    DiagnoseDescription?: string;

    /** DiagnoseEnterDocCode */
    DiagnoseEnterDocCode?: string;

    /** DiagnoseRemark */
    DiagnoseRemark?: string;
  }

  export class DiagnosticComplianceRateDto {
    /** 符合 */
    AccordWith?: number;

    /** CaseType */
    CaseType?: string;

    /** CaseTypeCount */
    CaseTypeCount?: number;

    /** CaseTypeName */
    CaseTypeName?: string;

    /** 符合率 */
    ComplianceRate?: number;

    /** 不符合 */
    NonCompliant?: number;

    /** 抽查数 */
    NumberOfSpotChecks?: number;

    /** 抽查率 */
    SpotCheckRate?: number;

    /** 是否达标 */
    WhetherItMeetsTheStandards?: boolean;
  }

  export class DiagnosticComplianceRateDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.DiagnosticComplianceRateDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DiagnosticVocabularySimpleTypesDto {
    /** DiagnosticVocabularyTypeCode */
    DiagnosticVocabularyTypeCode?: string;

    /** DiagnosticVocabularyTypeName */
    DiagnosticVocabularyTypeName?: string;

    /** Id */
    Id?: number;

    /** InverseDiagnosticVocabularyType */
    InverseDiagnosticVocabularyType?: Array<
      defs.DiagnosticVocabularySimpleTypesDto
    >;

    /** OwnerUserId */
    OwnerUserId?: number;
  }

  export class DiagnosticVocabularySimpleTypesDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DiagnosticVocabularySimpleTypesDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DiagnosticVocabularyTextDto {
    /** CreatedTime */
    CreatedTime?: string;

    /** DiagnosticVocabularyEnglishText */
    DiagnosticVocabularyEnglishText?: string;

    /** DiagnosticVocabularySimpleText */
    DiagnosticVocabularySimpleText?: string;

    /** DiagnosticVocabularyText */
    DiagnosticVocabularyText?: string;

    /** DiagnosticVocabularyTypeId */
    DiagnosticVocabularyTypeId?: number;

    /** Id */
    Id?: number;

    /** IsDeleted */
    IsDeleted?: number;

    /** Remarks */
    Remarks?: string;

    /** UpdatedTime */
    UpdatedTime?: string;
  }

  export class DiagnosticVocabularyTextDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.DiagnosticVocabularyTextDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DiagnosticVocabularyTextDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DiagnosticVocabularyTextDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DiagnosticVocabularyTypeDto {
    /** DiagnosticVocabularyTypeCode */
    DiagnosticVocabularyTypeCode?: string;

    /** DiagnosticVocabularyTypeId */
    DiagnosticVocabularyTypeId?: number;

    /** DiagnosticVocabularyTypeName */
    DiagnosticVocabularyTypeName?: string;

    /** Id */
    Id?: number;

    /** IsPrivate */
    IsPrivate?: boolean;

    /** OwnerUserId */
    OwnerUserId?: number;
  }

  export class DiagnosticVocabularyTypeDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.DiagnosticVocabularyTypeDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DistributionDto {
    /** 患者年龄 */
    Age?: string;

    /** 病理类型 */
    CaseType?: string;

    /** 送检项目 */
    CheckItems?: string;

    /** 发布时间 */
    CompleteTime?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 诊断专家 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** 患者姓名 */
    Name?: string;

    /** 专家诊断 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 复核专家 */
    ReviewExpertName?: string;

    /** 患者性别 */
    Sex?: string;

    /** Status */
    Status?: number;

    /** StatusName */
    StatusName?: string;

    /** 诊断中心 */
    SubCentersName?: string;

    /** 诊断编号 */
    TurnoverId?: string;
  }

  export class DistributionDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DistributionDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DistributorDto {
    /** 账号 */
    Account?: string;

    /** 邮箱 */
    Email?: string;

    /** id */
    Id: number;

    /** 名称 */
    Name?: string;

    /** 电话 */
    Phone?: string;

    /** 状态 */
    Status?: number;

    /** false不存在 / ture存在于表中   用户是否存在 */
    UserExist?: boolean;
  }

  export class DistributorDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DistributorDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DistributorFrozenDto {
    /** 年龄 */
    Age?: string;

    /** 预约时间 */
    AppointmentTime?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 送检类型 */
    CaseTypeCode?: string;

    /** 送检类型名称 */
    CaseTypeName?: string;

    /** 临床医生 */
    Clinicians?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 冰冻id */
    Id?: number;

    /** 送检单位名称 */
    Inspection?: string;

    /** 意向专家 */
    IntentPersonName?: string;

    /** 病人名称 */
    Name?: string;

    /** 预约编号 */
    Number?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 手机号码 */
    Phone?: string;

    /** 备注 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    SexCode?: number;

    /** 性别 */
    SexName?: string;

    /** SiteId */
    SiteId?: number;

    /** 切片缩略图 */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class DistributorFrozenDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.DistributorFrozenDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DistributorSiteDto {
    /** 站点 */
    SiteId?: number;

    /** 分中心 */
    SubCenterId?: number;
  }

  export class DistributorUpdateDto {
    /** 机构id */
    DepartmentId: string;

    /** 机构名称 */
    DepartmentName?: string;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 头像Url */
    HeadPicUrl?: string;

    /** id */
    Id?: number;

    /** 是否为工程师 */
    IsEngineer?: boolean;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile: string;

    /** 站点分配员关联表 */
    SiteSubCenterList?: Array<defs.DistributorSiteDto>;

    /** 名称 */
    TrueName: string;
  }

  export class DistributorUpdateDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.DistributorUpdateDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class DistributorWithdrawReqModel {
    /** id */
    Id: number;

    /** 是否为冰冻预约 */
    IsFrozen?: boolean;
  }

  export class EditHistoryDto {
    /** 申请状态 */
    ApplyStatusName?: string;

    /** 修改历史 */
    Content?: string;

    /** 申请原因 */
    Reason?: string;

    /** 拒绝理由 */
    ReasonForRejection?: string;

    /** 时间 */
    Time?: string;
  }

  export class EditHistoryDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.EditHistoryDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class EngineerDto {
    /** 邮箱 */
    Email?: string;

    /** Id */
    Id?: number;

    /** 登录账号 */
    LoginName?: string;

    /** 登录密码 */
    LoginPwd?: string;

    /** 手机号码 */
    Mobile?: string;

    /** Status */
    Status?: number;

    /** 真实姓名 */
    TrueName?: string;

    /** UserExist */
    UserExist?: boolean;
  }

  export class EngineerDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.EngineerDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class EngineerReqModel {
    /** 邮箱 */
    Email: string;

    /** Id */
    Id?: number;

    /** 登录账号 */
    LoginName: string;

    /** 登录密码 */
    LoginPwd?: string;

    /** 手机号码 */
    Mobile: string;

    /** 真实姓名 */
    TrueName: string;
  }

  export class EvaluationUpdateReqModel {
    /** 切片ID */
    CaseUploadsliceId?: number;

    /** 评价值 */
    Evaluation?: number;
  }

  export class ExcellentRateOfSliceDto {
    /** 病例类型 */
    CaseType?: string;

    /** 病例类型名称 */
    CaseTypeName?: string;

    /** 优秀 */
    ClassExcellent?: number;

    /** 良好 */
    ClassGood?: number;

    /** 合格 */
    ClassQualified?: number;

    /** 不合格 */
    ClassUnQualified?: number;

    /** 总数 */
    Count?: number;

    /** 优良率 */
    ExcellentRate?: number;

    /** 是否达标 */
    Qualified?: boolean;
  }

  export class ExcellentRateOfSliceDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.ExcellentRateOfSliceDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertAdminDto {
    /** id */
    Id?: number;

    /** 电话号码 */
    Phone?: string;
  }

  export class ExpertAdminDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertAdminDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertCaseDto {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** 医嘱状态 这里和申请端的一样 */
    DoctorAdvice?: number;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;

    /** 是否补充 0为不补充 比0大的为补充 */
    WhetherToSupplement?: number;
  }

  export class ExpertCaseDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertCaseDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertDoctorsAdviceDto {
    /** 病例id */
    CaseId?: number;

    /** 内容 */
    Content?: string;

    /** 下发时间 */
    CreateTime?: string;

    /** 下发账号 */
    CreateUser?: string;

    /** 下发人名称 */
    CreateUserName?: string;

    /** 专家id */
    ExpertId?: number;

    /** 快递地址 */
    Express?: string;

    /** Id */
    Id?: number;

    /** 状态 */
    Status?: number;

    /** 0的时候可补充或提交 有值的时候为隐藏按钮 */
    SubmitOrSupplement?: number;

    /** WhetherToSupplement */
    WhetherToSupplement?: number;
  }

  export class ExpertDoctorsAdviceDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ExpertDoctorsAdviceDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertHomeCountDto {
    /** 冰冻 */
    Frozen?: number;

    /** 受邀诊断 */
    InvitedDiagnosis?: number;

    /** 已发布 */
    Published?: number;

    /** 退回 */
    Returned?: number;

    /** 待诊断 */
    ToBeDiagnosed?: number;

    /** 待复核 */
    ToBeReviewed?: number;
  }

  export class ExpertHomeCountDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ExpertHomeCountDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertInviteeDto {
    /** 专家名称 */
    ExpertName?: string;

    /** 留言时间 */
    MsfContentsDate?: string;

    /** 消息回复 */
    MsgContents?: string;

    /** 邀请理由 */
    Remark?: string;
  }

  export class ExpertInviteeDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ExpertInviteeDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertInviteeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertInviteeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertInviteeReqModel {
    /** CaseId */
    CaseId?: number;

    /** id */
    Id: number;

    /** 字符串 */
    Str?: string;
  }

  export class ExpertLevel {
    /** 收费标准 */
    Charges: number;

    /** 级别代码 */
    Code: string;

    /** 专家级别ID */
    Id?: number;

    /** 级别名称 */
    Name: string;

    /** 收费标准说明 */
    Remark: string;

    /** 状态 */
    Status?: number;
  }

  export class ExpertLevelApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ExpertLevel;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertLevelPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertLevel>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertLevelReqModel {
    /** 收费标准 */
    Charges: string;

    /** 级别名称 */
    Name: string;

    /** 收费标准说明 */
    Remark: string;
  }

  export class ExpertOrderTypeDto {
    /** 项目编号 */
    Code?: string;

    /** Id */
    Id?: number;

    /** 数组 */
    List?: Array<string>;

    /** 项目名称 */
    Name?: string;

    /** 医嘱类型 */
    OrderType?: string;

    /** 父级 */
    ParentId?: number;
  }

  export class ExpertOrderTypeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertOrderTypeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertQualityControlEvaluationDto {
    /** 年龄 */
    Age?: string;

    /** 病例类型 */
    CaseType?: string;

    /** 诊断符合率评价 */
    CoincidenceRateEvaluation?: string;

    /** 评论备注 */
    CoincidenceRateRemark?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 初审专家 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** id */
    Id?: string;

    /** 申请医院 */
    Inspection?: string;

    /** 姓名 */
    Name?: string;

    /** 专家评价 */
    Opinion?: string;

    /** 病例号 */
    PathologyNumber?: string;

    /** 性别 */
    Sex?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class ExpertQualityControlEvaluationDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertQualityControlEvaluationDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertRenderingDto {
    /** 简介 */
    BriefIntroduction: string;

    /** 关联业务 */
    Business?: string;

    /** 收费标准 */
    Charges: number;

    /** 市code 440300 */
    CityCode: number;

    /** 工作单位 */
    Company: number;

    /** 工作单位名称 */
    CompanyName?: string;

    /** 邮箱 */
    Email?: string;

    /** 专家类型 */
    ExpertType: string;

    /** 专家类型名称 */
    ExpertTypeName?: string;

    /** 专家级别 */
    ExpertlevelCode: string;

    /** 头像 */
    HeadPic?: string;

    /** 头像url */
    HeadPicUrl?: string;

    /** Id */
    Id: number;

    /** 账号 */
    LoginName: string;

    /** 电话号码 */
    Mobile: string;

    /** 医生职称 */
    NickName: string;

    /** 病例数量 */
    NumberOfCases?: string;

    /** 省code 440000 */
    ProvinceCode: number;

    /** 质控权限 */
    QualityControl?: string;

    /** 签名 */
    Signature: string;

    /** 签名url */
    SignatureUrl?: string;

    /** 关联集合 */
    SiteSubCenter?: Array<defs.ExpertSiteDto>;

    /** 专家专长 */
    SpecialistExpertise: string;

    /** 分中心 */
    SubCenterId?: number;

    /** 名称 */
    TrueName: string;
  }

  export class ExpertRenderingDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ExpertRenderingDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertSiteDto {
    /** 站点id */
    SiteId?: number;

    /** 分中心 */
    SubCenterId?: number;
  }

  export class ExpertStatisticsDto {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 发布时间 */
    CompleteTime?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** 初审名称 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 标签 */
    LabelList?: Array<defs.LabelDto>;

    /** 病人名称 */
    Name?: string;

    /** 意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 复审专家 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class ExpertStatisticsDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertStatisticsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertVocabularysDto {
    /** 英文模板 */
    EnglishTemplate?: string;

    /** Id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 描述 */
    Remark?: string;

    /** 中文模板 */
    Template?: string;
  }

  export class ExpertVocabularysDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ExpertVocabularysDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ExpertVocabularysReqModel {
    /** 类型 */
    CaseTypeCode: string;

    /** 送检项 */
    CheckItemsCode: string;

    /** 英文模板 */
    EnglishTemplate?: string;

    /** 名称 */
    Name: string;

    /** 描述 */
    Remark?: string;

    /** 中文模板 */
    Template: string;
  }

  export class FeeStatisticsDto {
    /** CaseId */
    CaseId?: number;

    /** 支付时间 */
    CreateTime?: string;

    /** FeeStatisticsInformation */
    FeeStatisticsInformation?: defs.FeeStatisticsInformation;

    /** Id */
    Id?: number;

    /** 名称集合 */
    Name?: Array<string>;

    /** 预约号 */
    Number?: string;

    /** 订单编号 */
    PaymentOrderNumber?: string;

    /** SiteId */
    SiteId?: number;

    /** 支付状态 */
    Status?: number;

    /** 支付状态 */
    StatusName?: string;

    /** 分中心 */
    SubCenterId?: number;

    /** 支付总金额 */
    TotalPrice?: string;

    /** 冰冻预约和普通病例唯一标识 */
    TurnoverId?: string;
  }

  export class FeeStatisticsDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.FeeStatisticsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class FeeStatisticsInformation {
    /** 实际支付 */
    ActualPayment?: string;

    /** 年龄 */
    Age?: string;

    /** 申请站点金额 */
    ApplicationSiteAmount?: string;

    /** CaseId */
    CaseId?: number;

    /** 病理类型 */
    CaseType?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 送检项目 */
    CheckItem?: string;

    /** 送检项目 */
    CheckItemName?: string;

    /** 会诊中心金额 */
    ConsultationCenterAmount?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 诊断医院金额 */
    DiagnosisHospitalAmount?: string;

    /** 专家所属医院 */
    ExpertHospital?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 复诊专家 */
    FollowUpSpecialist?: string;

    /** 复诊专家所属医院 */
    FollowUpSpecialistHospital?: string;

    /** 身份证 */
    IdNumber?: string;

    /** 医嘱id */
    MedicalOrderId?: string;

    /** 名称 */
    Name?: string;

    /** 医嘱金额 */
    OrderAmount?: string;

    /** 原病理号 */
    OriginalPathologicalNumber?: string;

    /** 业务类型 */
    SettlementType?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** SiteId */
    SiteId?: number;
  }

  export class FrozenCaseReqModel {
    /** 年龄 */
    Age: string;

    /** 附件 */
    AnnexList?: Array<defs.AnnexBaseReqModel>;

    /** 预约日期 */
    AppointmentTime: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 临床病历 */
    ClinicalMedicalRecords?: string;

    /** 医生名称 */
    Doctor?: string;

    /** 意向专家Id */
    IntentPerson: number;

    /** 意向专家名称 */
    IntentPersonName: string;

    /** 名称 */
    Name: string;

    /** 预约号码 */
    Number: string;

    /** 备注 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 性别 */
    Sex: number;

    /** 手术所见 */
    SurgicalFindings?: string;

    /** 号码 */
    UserPhone?: string;
  }

  export class FrozenDto {
    /** 预约时间 */
    AppointmentTime?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 临床医生 */
    Clinicians?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 冰冻id */
    Id?: number;

    /** 病人名称 */
    Name?: string;

    /** 预约编号 */
    Number?: string;

    /** 手机号码 */
    Phone?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** SiteId */
    SiteId?: number;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class FrozenDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.FrozenDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class FrozenInformationDto {
    /** 年龄 */
    Age?: string;

    /** 预约日期 */
    AppointmentTime: string;

    /** 病理类型 */
    CaseType?: string;

    /** 病例类型名称 */
    CaseTypeName?: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 临床病历 */
    ClinicalMedicalRecords?: string;

    /** 医生名称 */
    Doctor: string;

    /** id */
    Id?: number;

    /** 名称 */
    Name: string;

    /** 预约号码 */
    Number: string;

    /** 备注 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 取材部位code */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别中文 */
    SexName?: string;

    /** 手术所见 */
    SurgicalFindings?: string;

    /** 会诊编号 */
    TurnoverId?: string;

    /** 号码 */
    UserPhone: string;
  }

  export class FrozenInformationDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.FrozenInformationDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class FrozenInitialDiagnosisAndReviewCoincidenceRateDto {
    /** 符合率 */
    CoincidenceRate?: string;

    /** 总数 */
    FrozenCount?: string;

    /** 不合格 */
    Incompatible?: number;

    /** 是否达标 */
    IsItUpToStandard?: string;

    /** 优 */
    MeetsThe?: number;
  }

  export class FrozenInitialDiagnosisAndReviewCoincidenceRateDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.FrozenInitialDiagnosisAndReviewCoincidenceRateDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class FrozenRemoteManagementDto {
    /** 年龄 */
    Age?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 专家 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 申请单位 */
    Inspection?: string;

    /** 名字 */
    Name?: string;

    /** 预约编号 */
    Number?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 手机 */
    Phone?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: string;

    /** 站点id */
    SiteId?: number;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class FrozenRemoteManagementDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.FrozenRemoteManagementDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class GateWayAddParam {
    /** 账号 */
    Account?: string;

    /** 邮箱 */
    Email?: string;

    /** Id */
    Id?: number;

    /** 昵称 */
    NickName?: string;

    /** 密码 */
    Password?: string;

    /** 电话号码 */
    Phone?: string;

    /** 角色code */
    RoleCode?: string;

    /** 角色Id 不要用 */
    RoleId?: number;

    /** 角色名称 */
    RoleName?: string;

    /** 关联租户Id */
    TenantId?: number;

    /** 验证码 */
    ValidCode?: string;
  }

  export class GetOpenTenantResponseDto {
    /** Checked */
    Checked?: boolean;

    /** EnName */
    EnName?: string;

    /** Id */
    Id?: number;

    /** IdString */
    IdString?: string;

    /** Name */
    Name?: string;

    /** Roles */
    Roles?: Array<defs.OpenTenantRoleDto>;
  }

  export class GetOpenTenantResponseDtoPageResponse {
    /** 响应码 200为成功 401为未授权 500内部报错等 对应httpcode */
    Code?: number;

    /** Data */
    Data?: Array<defs.GetOpenTenantResponseDto>;

    /** 响应消息 */
    Message?: string;

    /** PageIndex */
    PageIndex?: number;

    /** PageSize */
    PageSize?: number;

    /** 是否成功 */
    Success?: boolean;

    /** TotalCount */
    TotalCount?: number;
  }

  export class GetQualityDto {
    /** 病例id */
    CaseId?: string;

    /** 诊断符合率 */
    CoincidenceRate?: string;

    /** 诊断符合率具体评价 */
    CoincidenceRateEvaluation?: string;

    /** 诊断符合率备注 */
    CoincidenceRateRemark?: string;

    /** 切片质量评价 */
    Quality?: number;

    /** 切片质量评价备注 */
    QualityRemark?: string;

    /** 切片质量具体评价 */
    QualityRemarkEvaluation?: string;
  }

  export class GetQualityDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.GetQualityDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class GetRecordFileInfoDto {
    /** Code */
    Code?: string;

    /** 文件列表 */
    FileInfos?: Array<defs.GetRecordFileInfoListDto>;
  }

  export class GetRecordFileInfoDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.GetRecordFileInfoDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class GetRecordFileInfoListDto {
    /** 创建时间(时间戳) */
    CreateTime?: string;

    /** 文件名 */
    ObjectName?: string;

    /** 文件路径 */
    Url?: string;
  }

  export class GetUserResponseDto {
    /** Id */
    Id?: number;

    /** Desc:邮箱
Default:
Nullable:False */
    Mail?: string;

    /** Desc:手机号
Default:
Nullable:False */
    Phone?: string;

    /** 备注信息 */
    Remark?: string;

    /** Desc:用户账号
Default:
Nullable:False */
    UserAccount?: string;

    /** Desc:用户名称
Default:
Nullable:False */
    UserName?: string;
  }

  export class GetUserResponseDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.GetUserResponseDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class HISCreateCase {
    /** 年龄 */
    Age: string;

    /** 床号 */
    BedNumber?: string;

    /** 临床资料 */
    ClinicalData?: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 原病理诊断 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 民族 */
    EthnicId?: number;

    /** 住院号 */
    HospitalizedId?: string;

    /** 身份证号 */
    IdNumber?: string;

    /** 免疫组化 */
    Immunohistochemical?: string;

    /** 病房 */
    InpatientWard?: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 工作 */
    Job?: string;

    /** 婚姻状况 */
    MaritalStatus?: number;

    /** 女性月经时间 */
    MenstruationDate?: string;

    /** 患者名称 */
    Name: string;

    /** 病理号 */
    PathologyNumber: string;

    /** 联系电话 */
    Phone?: string;

    /** 性别 */
    Sex: number;

    /** 大体可见 */
    Thus?: string;

    /** 就诊号 */
    VisitingNumber?: string;
  }

  export class HisCaseinformationReqModel {
    /** CaseId */
    CaseId?: number;

    /** his订单号 */
    HisAdviceId?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 就诊号 */
    PatVisitNumber?: string;

    /** ris订单号 */
    RisAdviceId?: string;

    /** 第三方订单号 */
    ThirdOrderId?: string;
  }

  export class HisCaseinformationReqModelApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.HisCaseinformationReqModel;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class HsiCasehistoryDto {
    /** Id */
    Id?: number;

    /** 证件号 */
    IdNo?: string;

    /** 证件号类型 */
    IdType?: string;

    /** Name */
    Name?: string;

    /** 操作类型 */
    OperateType?: number;

    /** 卡号 */
    PatCardNo?: string;

    /** 卡类型 */
    PatCardType?: string;

    /** 卡类型备注 */
    PatCardTypeDesc?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 费用类型 */
    PatType?: string;

    /** Phone */
    Phone?: string;
  }

  export class HsiCasehistoryDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.HsiCasehistoryDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class HsiCasehistoryDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.HsiCasehistoryDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class IdParam {
    /** id */
    Id: number;
  }

  export class ImmunohistochemicalPackage {
    /** 免疫组化id */
    Immunohistochemistry?: number;

    /** 父级id */
    ParentId?: number;
  }

  export class Information {
    /** 缴费金额 */
    AmountMoney?: string;

    /** 姓名 */
    Name?: string;

    /** 医嘱Id */
    OrderId?: string;

    /** 卡号 */
    PatCardNo?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 就诊号 */
    PatVisitNumber?: string;
  }

  export class InspectionItemsDto {
    /** 4月 */
    April?: number;

    /** 8月 */
    August?: number;

    /** 12月 */
    December?: number;

    /** 2月 */
    February?: number;

    /** 1月 */
    January?: number;

    /** 7月 */
    July?: number;

    /** 6月 */
    June?: number;

    /** 3月 */
    March?: number;

    /** 5月 */
    May?: number;

    /** 11月 */
    November?: number;

    /** 10月 */
    October?: number;

    /** 9月 */
    September?: number;
  }

  export class InspectionItemsDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.InspectionItemsDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class Int32ApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: number;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class Int64ApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: number;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class Int64NullableApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: number;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class InvoiceDto {
    /** 业务类型 */
    BusinessType?: string;

    /** 业务类型金额 */
    BusinessTypeAmount?: string;

    /** 时间 */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** 发票状态 */
    InvoiceStatus?: string;

    /** 发票类型 */
    InvoiceType?: string;

    /** 名称 */
    Name?: string;

    /** 医嘱类型 */
    OrderType?: string;

    /** 医嘱类型金额 */
    OrderTypeAmount?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 总计金额 */
    TotalAmount?: string;
  }

  export class InvoiceDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.InvoiceDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class Item {
    /** List */
    List?: Array<defs.ListItem>;
  }

  export class ItemPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.Item>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class LabelDto {
    /** Id */
    Id?: number;

    /** 标签名称 */
    Name?: string;
  }

  export class LabelDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.LabelDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class LabelDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.LabelDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ListItem {
    /** CId */
    CId?: string;

    /** Ctime */
    Ctime?: number;

    /** Duration */
    Duration?: string;

    /** Filename */
    Filename?: string;

    /** Format */
    Format?: number;

    /** Name */
    Name?: string;

    /** NeedRecord */
    NeedRecord?: number;

    /** Status */
    Status?: number;

    /** UId */
    UId?: number;
  }

  export class ListOranizenDto {
    /** 自增id */
    Id: number;

    /** LimsName */
    LimsName?: string;

    /** 名称 */
    Name: string;
  }

  export class ListOranizenDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ListOranizenDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class LogDto {
    /** 账号 */
    Account?: string;

    /** 行为 */
    Behavior?: string;

    /** 业务类型 */
    BusinessType?: number;

    /** 病例id */
    CaseId?: number;

    /** 操作时间 */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** Ip */
    Ip?: string;

    /** 对象 */
    Object?: string;

    /** 操作人 */
    Operator?: string;

    /** 路径 */
    Path?: string;

    /** 类型 */
    Type?: string;
  }

  export class LogDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.LogDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class LogParam {
    /** 账号 */
    Account?: string;

    /** 行为 */
    Behavior?: string;

    /** 业务类型 1诊断类型 2操作类型 */
    BusinessType?: number;

    /** 病例id */
    CaseId?: number;

    /** Ip */
    Ip?: string;

    /** 对象 */
    Object?: string;

    /** 操作人 */
    Operator?: string;

    /** 路径 */
    Path?: string;

    /** 类型 */
    Type?: string;
  }

  export class LoginReqModel {
    /** 账户 */
    Account: string;

    /** 密码 */
    PassWord: string;

    /** 验证码 */
    VerificationCode?: string;
  }

  export class MedicalAdviceFees {
    /** 实际支付金额 */
    ActualPaymentAmount?: string;

    /** 安徽省立金额 */
    AnhuiProvincialGovernmentAmount?: string;

    /** 收费状态 */
    ChargingStatus?: string;

    /** 诊断时间 */
    DiagnosisTime?: string;

    /** 诊断状态 */
    DiagnosticStatus?: string;

    /** 诊断专家团队所属医院金额 */
    HospitalAmount?: string;

    /** 平台金额 */
    PlatformAmount?: string;

    /** 项目类型 */
    ProjectType?: string;

    /** 医嘱类型 */
    SettlementType?: string;

    /** 站点金额 */
    SiteAmount?: string;
  }

  export class MedicalOrderConfirmationDto {
    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病理类型 */
    CaseTypeCode?: string;

    /** 病理类型名称 */
    CaseTypeName?: string;

    /** 送检类型 */
    CheckItemsCode?: string;

    /** 送检类型名称 */
    CheckItemsName?: string;

    /** ConsultationNumber */
    ConsultationNumber?: string;

    /** 医嘱内容 */
    Content?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 按钮是否禁用 2启用3禁用 */
    Disabled?: number;

    /** 下发专家 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 姓名 */
    Name?: string;

    /** 医嘱下发时间 */
    OrderCreatetime?: string;

    /** 下发状态 */
    OrderStatus?: number;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 诊断状态 */
    Status?: number;

    /** 诊断状态名称 */
    StatusName?: string;

    /** 是否已超时 */
    TimeOutName?: string;

    /** 诊断流水号 */
    TurnoverId?: string;
  }

  export class MedicalOrderConfirmationDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.MedicalOrderConfirmationDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class MedicalOrderFeesDto {
    /** 编码 */
    Code?: string;

    /** Id */
    Id?: number;

    /** 子集合（免疫组化套餐） */
    List?: Array<defs.MedicalOrderFeesDto>;

    /** 金额 */
    Money?: string;

    /** Name */
    Name?: string;

    /** ParentId */
    ParentId?: number;
  }

  export class MedicalOrderFeesDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.MedicalOrderFeesDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class NeteaseYunXin {
    /** AppKey */
    AppKey?: string;

    /** AppSecret */
    AppSecret?: string;
  }

  export class NeteaseYunXinApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.NeteaseYunXin;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class NeteaseyunxinUser {
    /** 创建时间 */
    AddTime?: string;

    /** Id */
    Id?: number;

    /** NeteaseImaccId */
    NeteaseImaccId: string;

    /** NeteaseImnickName */
    NeteaseImnickName: string;

    /** 网易云信token */
    NeteaseImtoken: string;

    /** 子系统 1远程2科研3读片会4规培 */
    Subsystem?: number;

    /** 用户id */
    UserId?: number;
  }

  export class NeteaseyunxinUserApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.NeteaseyunxinUser;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class NonGynecologicalDto {
    /** 阴 */
    Negative?: string;

    /** 总数 */
    NonGynecologicalCount?: string;

    /** 阳 */
    Positive?: string;

    /** 阳性率 */
    PositiveRate?: string;
  }

  export class NonGynecologicalDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.NonGynecologicalDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class NoticeTypeEnum {}

  export class ObjectApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: any;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class OpenTenantRoleDto {
    /** Checked */
    Checked?: boolean;

    /** Code */
    Code?: string;

    /** Id */
    Id?: number;

    /** IdString */
    IdString?: string;

    /** Name */
    Name?: string;
  }

  export class OperateReqModel {
    /** id */
    Id?: number;

    /** 操作 true为同意 */
    Operate?: boolean;

    /** 理由 */
    Reason?: string;
  }

  export class OperationRecordDto {
    /** 专家账号 */
    Account?: string;

    /** 诊断时间 */
    ApplyTime?: string;

    /** 诊断专家 */
    ApplyUserName?: string;

    /** 节点名称 */
    NodeName?: string;
  }

  export class OperationRecordDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.OperationRecordDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class OrderToCreateCaseModel {
    /** 年龄 */
    Age: string;

    /** 新添加的附件 */
    AnnexList?: Array<defs.AnnexBaseReqModel>;

    /** 会诊号 */
    BarCode: string;

    /** 床号 */
    BedNumber?: string;

    /** 病例类型 */
    CaseType: string;

    /** 送检项目 */
    CheckItems: string;

    /** 临床资料 */
    ClinicalData: string;

    /** 临床诊断 */
    ClinicalDiagnosis: string;

    /** 留言类容 */
    Content?: string;

    /** 原病理诊断 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 民族 */
    EthnicId?: number;

    /** 住院号 */
    HospitalizedId?: string;

    /** 身份证号 */
    IdNumber?: string;

    /** 免疫组化 */
    Immunohistochemical?: string;

    /** 病房 */
    InpatientWard?: string;

    /** 送检单位 */
    Inspection: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 意向专家Id */
    IntentPerson?: number;

    /** 意向专家名称 */
    IntentPersonName?: string;

    /** 是否为疑难病 */
    IsRefractoryDisease?: number;

    /** 工作 */
    Job?: string;

    /** 婚姻状况 */
    MaritalStatus?: number;

    /** 女性月经时间 */
    MenstruationDate?: string;

    /** 患者名称 */
    Name: string;

    /** 原病例id */
    OriginalCaseId: number;

    /** 病理号 */
    PathologyNumber: string;

    /** 联系电话 */
    Phone?: string;

    /** 备注信息 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 采样日期 */
    SamplingTime: string;

    /** 性别 */
    Sex: number;

    /** 新添加的切片 */
    SliceList?: Array<defs.SliceReqModel>;

    /** 亚专科 */
    SubProfessional: string;

    /** 预约日期 */
    SubscribeTime: string;

    /** 大体可见 */
    Thus?: string;

    /** 会诊编号 */
    TurnoverId?: string;

    /** 旧附件 */
    UsedAnnexList?: Array<defs.AnnexBaseReqModel>;

    /** 旧切片 */
    UsedSliceList?: Array<defs.SliceReqModel>;

    /** 就诊号 */
    VisitingNumber?: string;
  }

  export class OrderTypeDto {
    /** 编码 */
    Code?: string;

    /** Id */
    Id?: number;

    /** 子集合（免疫组化套餐） */
    List?: Array<defs.SubaggregateDto>;

    /** Name */
    Name?: string;

    /** ParentId */
    ParentId?: number;
  }

  export class OrderTypeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.OrderTypeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class OrderTypeParam {
    /** 编码 */
    Code?: string;

    /** 子集合（免疫组化套餐） */
    ImmunohistochemicalPackage?: Array<defs.ImmunohistochemicalPackage>;

    /** Name */
    Name?: string;

    /** ParentId */
    ParentId?: number;
  }

  export class OrdinaryCaseAssignmentParam {
    /** 专家id */
    ExpertId: number;

    /** id */
    Id: number;
  }

  export class OrganizenReqModel {
    /** 地址 */
    Address: string;

    /** 英文 */
    EName: string;

    /** LimsName */
    LimsName?: string;

    /** 名称 */
    Name: string;

    /** 电话 */
    Phone: string;
  }

  export class OtherChargesDto {
    /** 安徽省立医院收费 */
    AnhuiProvincialHospital?: string;

    /** 会诊专家团队所属医院收费 */
    HospitalCharges?: string;

    /** 站点收费 */
    SiteCharges?: string;

    /** 所属站点 */
    SiteId?: number;
  }

  export class OtherChargesDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.OtherChargesDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class OtherChargesParam {
    /** 安徽省立医院收费 */
    AnhuiProvincialHospital?: string;

    /** 会诊专家团队所属医院收费 */
    HospitalCharges?: string;

    /** 站点收费 */
    SiteCharges?: string;

    /** 所属站点 */
    SiteId: number;
  }

  export class PageParam {
    /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
    AdvancedSearch?: string;

    /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
    OrderByType?: string;

    /** 当前页 */
    PageIndex: number;

    /** 每页总条数 */
    PageSize: number;
  }

  export class PathologicalSectionMarkDto {
    /** 标记信息 */
    Marks: string;

    /** 病例切片ID */
    PathologicalSectionId: number;

    /** 具体路径 */
    Url?: string;
  }

  export class PathologySliceReturnedRateDto {
    /** Count */
    Count?: number;

    /** ReturnCount */
    ReturnCount?: number;

    /** ReturnRate */
    ReturnRate?: number;
  }

  export class PathologySliceReturnedRateDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.PathologySliceReturnedRateDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PathologySliceTimelinessOfDiagnosisDto {
    /** CaseType */
    CaseType?: string;

    /** CaseTypeName */
    CaseTypeName?: string;

    /** Count */
    Count?: number;

    /** InTimeCount */
    InTimeCount?: number;

    /** InTimeRate */
    InTimeRate?: number;

    /** OutTimeCount */
    OutTimeCount?: number;

    /** OverTimeRate */
    OverTimeRate?: number;

    /** Qualified */
    Qualified?: boolean;
  }

  export class PathologySliceTimelinessOfDiagnosisDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.PathologySliceTimelinessOfDiagnosisDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PatientCardDetailVoListItem {
    /** Name */
    Name?: string;

    /** PatCardNo */
    PatCardNo?: string;

    /** PatCardType */
    PatCardType?: string;

    /** PatCardTypeDesc */
    PatCardTypeDesc?: string;

    /** PatHisNo */
    PatHisNo?: string;

    /** PatType */
    PatType?: string;
  }

  export class PaymentCompletedReqModel {
    /** 基本信息 */
    InformationList?: Array<defs.Information>;

    /** 缴费订单号 */
    PaymentOrderNumber?: string;

    /** 缴费总价 */
    TotalPrice?: string;
  }

  export class PlatformRenderingDto {
    /** 管理员账号 */
    Administrator?: string;

    /** 预约号前缀 */
    ConsultationNumber?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 英文名称 */
    EName?: string;

    /** 默认专家 */
    ExpertId?: number;

    /** 专家名称 */
    ExpertName?: string;

    /** 全称 */
    FullName?: string;

    /** 医院端权限 1选择专家，2不选择专家 */
    HospitalAuthority?: number;

    /** 自增Id */
    Id?: number;

    /** 简称 */
    Name?: string;

    /** 父节点Id */
    ParentId?: number;

    /** 抄送手机号 */
    Phone?: string;

    /** 分诊号前缀 */
    PrefixForTriageNumber?: string;

    /** 审核状态 */
    Status?: number;

    /** 类型 1 分诊平台2 分中心 */
    Type?: number;
  }

  export class PlatformRenderingDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.PlatformRenderingDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PlatformTreeDto {
    /** id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 子集 */
    Tree?: Array<defs.PlatformTreeDto>;
  }

  export class PlatformTreeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.PlatformTreeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PositiveDto {
    /** Adenocarcinoma */
    Adenocarcinoma?: number;

    /** Adenocarcinoma率 */
    AdenocarcinomaRate?: string;

    /** AGCN */
    Agcn?: number;

    /** AGCN率 */
    AgcnRate?: string;

    /** AGCNOS */
    Agcnos?: number;

    /** AGCNOS率 */
    AgcnosRate?: string;

    /** AIS */
    Ais?: number;

    /** AIS率 */
    AisRate?: string;

    /** ASCH */
    Asch?: number;

    /** ASCH率 */
    AschRate?: string;

    /** ASCUS */
    Ascus?: number;

    /** ASCUS率 */
    AscusRate?: string;

    /** 不满意 */
    Dissatisfied?: number;

    /** 不满意率 */
    DissatisfiedRate?: string;

    /** HSIL */
    Hsil?: number;

    /** HSIL率 */
    HsilRate?: string;

    /** LSIH */
    Lsih?: number;

    /** LSIH率 */
    LsihRate?: string;

    /** LSILASCH */
    Lsilasch?: number;

    /** LSILASCH率 */
    LsilaschRate?: string;

    /** NILM */
    Nilm?: number;

    /** NILM率 */
    NilmRate?: string;

    /** 阳性总数 */
    PositiveCount?: number;

    /** 阳性百分比 */
    PositivePercentage?: string;

    /** SCC */
    Scc?: number;

    /** SCC率 */
    SccRate?: string;

    /** 总数 */
    Total?: number;
  }

  export class PositiveDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.PositiveDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PuBaseCodeDto {
    /** 即将超时 */
    AboutToTimeOut?: string;

    /** 即将超时单位 */
    AboutToTimeOutUnit?: number;

    /** 类型 */
    BusTye?: string;

    /** 编码 */
    Code?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: string;

    /** 诊断超时单位 */
    DiagnosticTimeOutUnit?: number;

    /** 扩展名 */
    ExtName?: string;

    /** Id */
    Id?: number;

    /** Json数据 */
    JsonData?: string;

    /** 业务线属性 */
    LineOfBusinessProperties?: number;

    /** 名称 */
    Name?: string;

    /** 父级 */
    ParentId?: number;

    /** 备注 */
    Remark?: string;
  }

  export class PuBaseCodeDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.PuBaseCodeDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PubBaseCodeReqModel {
    /** 即将超时 */
    AboutToTimeOut?: string;

    /** 即将超时单位 */
    AboutToTimeOutUnit?: number;

    /** 业务类型 */
    BusType?: string;

    /** 编码或英文 */
    Code?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: string;

    /** 诊断超时单位 */
    DiagnosticTimeOutUnit?: number;

    /** 扩展名 */
    ExtName?: string;

    /** his编码 */
    HisCode?: string;

    /** json绑定 */
    JsonData?: number;

    /** 业务线属性 */
    LineOfBusinessProperties?: number;

    /** 名称 */
    Name: string;

    /** 父节点 */
    ParentId?: number;

    /** 备注 */
    Remark?: string;
  }

  export class PubBaseCodeTreeDto {
    /** 即将超时 */
    AboutToTimeOut?: string;

    /** 即将超时单位 */
    AboutToTimeOutUnit?: number;

    /** 子集 */
    Children?: Array<defs.PubBaseCodeTreeDto>;

    /** 代码 */
    Code?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: string;

    /** 诊断超时单位 */
    DiagnosticTimeOutUnit?: number;

    /** 扩展名称 */
    ExtName?: string;

    /** his编码 */
    HisCode?: string;

    /** 自增Id */
    Id?: number;

    /** json数据 */
    JsonData?: number;

    /** 业务线属性 */
    LineOfBusinessProperties?: number;

    /** 名称 */
    Name?: string;

    /** 上级Id */
    ParentId?: number;

    /** 备注 */
    Remark?: string;

    /** 排序 */
    Sort?: number;

    /** 启动状态 */
    StatusName?: string;
  }

  export class PubBaseCodeTreeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.PubBaseCodeTreeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PublishedStatisticsDto {
    /** 条形码/会诊编号 */
    BarCode?: string;

    /** 病例总数 */
    CaseCount?: number;

    /** 切片列表 */
    CaseSliceList?: Array<defs.CaseUploadslice>;

    /** 病例超时留言 */
    CaseTimeoutMessages?: Array<defs.CaseTimeoutMessage>;

    /** CaseTimeoutRecordList */
    CaseTimeoutRecordList?: defs.CaseTimeoutRecord;

    /** 病理类型 */
    CaseType?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 送检项目 */
    CheckItem?: string;

    /** 送检项目 */
    CheckItemName?: string;

    /** 病例提交时间 */
    CreateTime?: string;

    /** 诊断符合率 */
    DiagnosticComplianceRate?: number;

    /** 诊断符合率备注/不符合原因 */
    DiagnosticComplianceRateRemark?: string;

    /** 诊断符合率时间 */
    DiagnosticComplianceRateTime?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** Id */
    Id?: number;

    /** 送检医院 */
    Inspection?: string;

    /** 患者名称 */
    Name?: string;

    /** 病例号 */
    PathologyNumber?: string;

    /** 病例发布时间 */
    ReleaseTime?: string;

    /** 复核专家名称 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位 */
    SampleLocationName?: string;

    /** 亚专科 */
    SubProfessional?: string;
  }

  export class PublishedStatisticsDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.PublishedStatisticsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class PullGroupChatParam {
    /** 受邀id    参数示例 166976861799424 */
    Members: string;

    /** 邀请人id */
    NeteaseImaccId: string;

    /** 群聊id */
    TId: string;
  }

  export class PullGroupChatUrlParam {
    /** 邀请人id */
    NeteaseImaccId: string;

    /** 群聊id */
    TId: string;
  }

  export class QualityControlDto {
    /** 质控权限 */
    QualityControl?: string;
  }

  export class QualityControlDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.QualityControlDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class QualityControlEvaluationUpdateParam {
    /** 诊断符合率 */
    DiagnosticComplianceRate?: number;

    /** 诊断符合率备注 */
    DiagnosticComplianceRateRemark?: string;

    /** id */
    Id: number;
  }

  export class QualityControlStatisticsDto {
    /** CaseId */
    CaseId?: number;

    /** 诊断符合率 */
    DiagnosticComplianceRate?: number;

    /** 诊断符合率备注/不符合原因 */
    DiagnosticComplianceRateRemark?: string;

    /** 诊断符合率时间 */
    DiagnosticComplianceRateTime?: string;
  }

  export class QualityControlStatisticsDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.QualityControlStatisticsDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class QueryPatientReqDto {
    /** Name */
    Name?: string;

    /** ParCardType */
    ParCardType?: string;

    /** PatCardNo */
    PatCardNo?: string;

    /** PatHisNo */
    PatHisNo?: string;
  }

  export class QueryPatientResDto {
    /** Age */
    Age?: string;

    /** Dob */
    Dob?: string;

    /** IdNo */
    IdNo?: string;

    /** IdType */
    IdType?: string;

    /** Mobile */
    Mobile?: string;

    /** Name */
    Name?: string;

    /** PatCardNo */
    PatCardNo?: string;

    /** PatCardType */
    PatCardType?: string;

    /** PatHisNo */
    PatHisNo?: string;

    /** Sex */
    Sex?: string;
  }

  export class QueryPatientResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.QueryPatientResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class QueryRegSourceReqDto {
    /** DeptCode */
    DeptCode?: string;

    /** DoctorCode */
    DoctorCode?: string;

    /** EndDate */
    EndDate?: string;

    /** HisId */
    HisId?: string;

    /** StartDate */
    StartDate?: string;
  }

  export class QueryRegSourceResDto {
    /** RegSources */
    RegSources?: Array<defs.RegSourcesItem>;

    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;
  }

  export class QueryRegSourceResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.QueryRegSourceResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RcpNotify {
    /** 关联id */
    CaseId?: number;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 英文消息 */
    EnMessage?: string;

    /** 主键 */
    Id?: number;

    /** 是否全部可见 */
    IsAllVisible?: number;

    /** 消息 */
    Message?: string;

    /** 具体看枚举值 */
    MessageType?: number;

    /** 接收人 */
    Receiver?: number;

    /** 状态 1为已读 2正常 */
    Status?: number;
  }

  export class RcpNotifyPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.RcpNotify>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RcpPathologyTemplate {
    /** 病例类型Code */
    CaseTypeCode?: string;

    /** 送检项目Code */
    CheckItemCode?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** 是否启用 */
    IsDisable?: number;

    /** 语言Code */
    LanguageCode?: string;

    /** 应用范围 */
    RangeOfApplication?: string;

    /** 备注 */
    Remark?: string;

    /** 截图数量 */
    ShotNum?: number;

    /** 病理模板Code */
    TemplateCode?: string;

    /** 病理模板文件Code */
    TemplateFileCode?: string;

    /** 1、分中心 2、站点 */
    TypeEnum?: number;

    /** 分中心或者站点ID */
    TypePointId?: number;
  }

  export class RcpPathologyTemplateApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RcpPathologyTemplate;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RcpPathologyTemplateDto {
    /** 病例类型Code */
    CaseTypeCode?: string;

    /** 病例类型CodeName */
    CaseTypeCodeName?: string;

    /** 送检项目Code */
    CheckItemCode?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** 是否启用 */
    IsDisable?: number;

    /** 语言Code */
    LanguageCode?: string;

    /** 语言CodeName */
    LanguageCodeName?: string;

    /** 应用范围 */
    RangeOfApplication?: string;

    /** 备注 */
    Remark?: string;

    /** 截图数量 */
    ShotNum?: number;

    /** 病理模板Code */
    TemplateCode?: string;

    /** 病理模板文件Code */
    TemplateFileCode?: string;

    /** 1、分中心 2、站点 */
    TypeEnum?: number;

    /** 分中心或者站点ID */
    TypePointId?: number;

    /** 分中心或者站点Name */
    TypePointName?: string;
  }

  export class RcpPathologyTemplateDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.RcpPathologyTemplateDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RcpPubaseCodeList {
    /** 即将超时 */
    AboutToTimeOut?: string;

    /** 即将超时单位 */
    AboutToTimeOutUnit?: number;

    /** 类型 */
    BusTye?: string;

    /** 编码 */
    Code?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: string;

    /** 诊断超时单位 */
    DiagnosticTimeOutUnit?: number;

    /** 扩展名称 */
    ExtName?: string;

    /** his编码 */
    HisCode?: string;

    /** Id */
    Id?: number;

    /** Json数据 */
    JsonData?: number;

    /** 业务线属性 */
    LineOfBusinessProperties?: number;

    /** 名称 */
    Name?: string;

    /** 父级 */
    ParentId?: number;

    /** 备注 */
    Remark?: string;

    /** 所属站点 */
    Site?: number;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;
  }

  export class RcpPubaseCodeListPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.RcpPubaseCodeList>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RcpSiteData {
    /** 管理员账号 */
    Account?: string;

    /** 地址 */
    Address?: string;

    /** 所属地区编码 */
    AreaCode?: string;

    /** 所属地区名称 */
    AreaName?: string;

    /** 站点缩写-会诊编号前缀 */
    Code?: string;

    /** 创建时间 */
    CreateTime?: string;

    /** 创建人 */
    CreateUser?: string;

    /** 创建人名称 */
    CreateUserName?: string;

    /** 自增Id */
    Id?: number;

    /** 会诊模式-是否直接提交专家 */
    IsDiagnosticMode?: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 站点名称 */
    Name?: string;

    /** 所属机构 */
    OrganizationId?: number;

    /** 所属平台 */
    ParentPlatformId?: number;

    /** 电话 */
    Phone?: string;

    /** 所属分中心-平台表分中心的Id */
    PlatformId?: number;

    /** 启用状态 */
    Status?: number;

    /** 编辑时间 */
    UpdateTime?: string;

    /** 编辑人 */
    UpdateUser?: string;

    /** 编辑人名称 */
    UpdateUserName?: string;
  }

  export class RealTimeRoughlyDto {
    /** id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 电话号码 */
    Phone?: string;

    /** 站点所关联的用户id */
    UserId?: number;
  }

  export class RealTimeRoughlyDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.RealTimeRoughlyDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ReasonForReturnDto {
    /** 类容 */
    Content?: string;

    /** id */
    Id?: number;
  }

  export class ReasonForReturnDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ReasonForReturnDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RecommendationDetailsDto {
    /** /建议内容 */
    Content?: string;

    /** id */
    Id?: number;
  }

  export class RecommendationDetailsDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RecommendationDetailsDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ReconciliationChargeDto {
    /** 年龄 */
    Age?: string;

    /** 金额 */
    AmountOfNoney?: number;

    /** 病理类型 */
    CaseType?: string;

    /** 收费项目 */
    CheckItems?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** Id */
    Id?: number;

    /** 名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 复审专家名称 */
    ReviewExpertName?: string;

    /** 医院 */
    SiteName?: string;

    /** 总金额 */
    TotalAmount?: number;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class ReconciliationChargeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ReconciliationChargeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class Redistribute {
    /** 专家ID */
    ExpertId: number;

    /** 专家名称 */
    ExpertName: string;

    /** id */
    Id: number;
  }

  export class RefreshTokenResponse {
    /** Refresh Token */
    RefreshToken?: string;

    /** token */
    Token?: string;
  }

  export class RefreshTokenResponseApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RefreshTokenResponse;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RegSourcesItem {
    /** ClinicRoomCode */
    ClinicRoomCode?: string;

    /** ClinicRoomName */
    ClinicRoomName?: string;

    /** DeptName */
    DeptName?: string;

    /** DeptNo */
    DeptNo?: string;

    /** DoctorName */
    DoctorName?: string;

    /** DoctorNo */
    DoctorNo?: string;

    /** DoctorTitle */
    DoctorTitle?: string;

    /** EndTime */
    EndTime?: string;

    /** HasDetailTime */
    HasDetailTime?: string;

    /** RegDate */
    RegDate?: string;

    /** RegFee */
    RegFee?: string;

    /** ScheduleItemCode */
    ScheduleItemCode?: string;

    /** StartTime */
    StartTime?: string;

    /** TimeCode */
    TimeCode?: string;

    /** TimeFlag */
    TimeFlag?: string;

    /** TotalNum */
    TotalNum?: string;
  }

  export class RegisterNoticeResDto {
    /** AdmNo */
    AdmNo?: string;

    /** DoctorName */
    DoctorName?: string;

    /** HisOrdNum */
    HisOrdNum?: string;

    /** ResultCode */
    ResultCode?: string;

    /** ResultMessage */
    ResultMessage?: string;

    /** SerialNum */
    SerialNum?: string;

    /** TimeRange */
    TimeRange?: string;

    /** VisitPosition */
    VisitPosition?: string;
  }

  export class RegisterNoticeResDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RegisterNoticeResDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RegisteredDto {
    /** EndTime */
    EndTime?: string;

    /** PatHisNo */
    PatHisNo?: string;

    /** RegFee */
    RegFee?: string;

    /** ScheduleItemCode */
    ScheduleItemCode?: string;

    /** StartTime */
    StartTime?: string;
  }

  export class RegistrationManagementDto {
    /** 就诊号 */
    AdmNo?: string;

    /** 预约专家 */
    AppointmentExpert?: string;

    /** 预约时间 */
    AppointmentTime?: string;

    /** 卡类型 */
    CardType?: string;

    /** 卡类型备注 */
    CardTypeDesc?: string;

    /** 历史记录表Id */
    CaseHistoryId?: number;

    /** Id */
    Id?: number;

    /** 就诊人 */
    Name?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 状态 */
    Status?: number;

    /** 挂号时间 */
    Time?: string;
  }

  export class RegistrationManagementDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.RegistrationManagementDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RegistrationManagementReqModel {
    /** 就诊号 */
    AdmNo?: string;

    /** 预约专家 */
    AppointmentExpert?: string;

    /** 预约时间 */
    AppointmentTime?: string;

    /** 卡类型 */
    CarType?: string;

    /** 卡类型备注 */
    CardTypeDesc?: string;

    /** 历史记录表Id */
    CaseHistoryId?: number;

    /** 排班接口返回的某个排班结束时间 */
    EndTime?: string;

    /** 就诊人 */
    Name?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 挂号费 */
    RegFee?: string;

    /** 排班Id */
    ScheduleItemCode?: string;

    /** 排班接口返回的某个排班开始时间 */
    StartTime?: string;

    /** 锁号返回的his流水号 */
    ThirdOrderId?: string;
  }

  export class RenderExpertVocabularysDto {
    /** 类型 */
    CaseTypeCode: string;

    /** 送检项 */
    CheckItemsCode: string;

    /** 英文模板 */
    EnglishTemplate?: string;

    /** id */
    Id: number;

    /** 名称 */
    Name: string;

    /** 描述 */
    Remark?: string;

    /** 中文模板 */
    Template: string;
  }

  export class RenderExpertVocabularysDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RenderExpertVocabularysDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RenderTemplatePostModel {
    /** 模板文件Code */
    Code?: string;

    /** 渲染数据 */
    JsonNode?: string;
  }

  export class RetChannelStatus {
    /** Cid */
    Cid?: string;

    /** Ctime */
    Ctime?: number;

    /** Duration */
    Duration?: number;

    /** 解刨学直播间001 */
    Filename?: string;

    /** Format */
    Format?: number;

    /** 解刨学直播间001 */
    Name?: string;

    /** NeedRecord */
    NeedRecord?: number;

    /** RecordStatus */
    RecordStatus?: number;

    /** Status */
    Status?: number;

    /** Type */
    Type?: number;

    /** UId */
    UId?: number;
  }

  export class RetChannelStatusApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.RetChannelStatus;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class RetrievePasswordReqModel {
    /** 验证码 */
    Code?: string;

    /** 密码 */
    PassWord?: string;

    /** 字符串 */
    Str?: string;
  }

  export class ReviewOpinionDto {
    /** id */
    Id: number;

    /** 意见 */
    Opinion?: string;

    /** 复核意见 */
    ReviewOpinion?: string;
  }

  export class ReviewOpinionDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.ReviewOpinionDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ReviewReqModel {
    /** id */
    Id: number;

    /** 是否为复核退回/邀请复核 */
    IsReturn?: boolean;

    /** 复核专家 */
    ReviewExpertId?: number;

    /** 复核专家名称 */
    ReviewExpertName?: string;
  }

  export class SendMessageParam {
    /** 发送者accid，用户帐号 */
    From?: string;

    /** 发送消息内容 格式：如 {"msg":"test"} 表示只发文本 内容为test */
    Message?: string;

    /** MessageType */
    MessageType?: defs.NoticeTypeEnum;

    /** 接收对象 用户Id/群Id */
    To?: string;
  }

  export class SendType {}

  export class SendTypeEnum {}

  export class SettlementDto {
    /** 卡类型 */
    CardType?: string;

    /** 病例id */
    CaseId?: number;

    /** 缴费时间 */
    CreateTime?: string;

    /** id */
    Id?: number;

    /** 诊断卡号 */
    PatCardNo?: string;

    /** 登记号 */
    PatHisNo?: string;

    /** 就诊号 */
    PatVisitNumber?: string;

    /** 患者姓名 */
    PatientName?: string;

    /** 缴费金额 */
    PaymentAmount?: string;

    /** 项目 */
    Project?: string;

    /** 会诊状态 */
    RcpStatusName?: string;

    /** 缴费状态 */
    Status?: number;
  }

  export class SettlementDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SettlementDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class ShortMessageBulkAllocationParam {
    /** 条数 */
    Count: string;

    /** 专家ID */
    ExpertId: number;

    /** id */
    Id: Array<number>;
  }

  export class SiteDropDto {
    /** id */
    Id?: number;

    /** 名称 */
    Name?: string;
  }

  export class SiteDropDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SiteDropDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteDto {
    /** 市 */
    CityName?: string;

    /** 站点id */
    Id?: number;

    /** 登录名称 */
    LoginName?: string;

    /** 电话号码 */
    Mobile?: string;

    /** 机构 */
    OrganizeName?: string;

    /** 省份 */
    ProvinceName?: string;

    /** 站点名称 */
    SiteName?: string;

    /** 状态 */
    Status?: number;

    /** false不存在 / ture存在于表中 */
    UserExist?: boolean;

    /** id */
    UserId?: number;
  }

  export class SiteDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SiteDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteExpertDto {
    /** 简介 */
    BriefIntroduction?: string;

    /** 市 */
    CityCode?: number;

    /** 市 */
    CityName?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 昵称 */
    ExpertTypeName?: string;

    /** 职务 */
    ExpertleveName?: string;

    /** 头像 */
    HeadPic?: string;

    /** 头像 */
    HeadPicUrl?: string;

    /** Id */
    Id?: number;

    /** 专家类型（此处和职称互换位置） */
    NickName?: string;

    /** 病例可提交数量 */
    NumberOfCases?: string;

    /** 已提交病例数量 */
    NumberOfSubmittedCases?: string;

    /** 电话号码 */
    Phone?: string;

    /** 省 */
    ProvinceCode?: number;

    /** 省 */
    ProvinceName?: string;
  }

  export class SiteExpertDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SiteExpertDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteExpertDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SiteExpertDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteInformationDto {
    /** 市code */
    CityCode: number;

    /** 站点缩写 */
    Code: string;

    /** 所属机构id */
    DepartmentId: number;

    /** 机构 */
    DepartmentName?: string;

    /** 邮箱 */
    Email?: string;

    /** 专家 */
    Expert?: Array<defs.SiteExpertDto>;

    /** 专家 */
    ExpertJson?: Array<defs.IdParam>;

    /** 头像 */
    HeadPic?: string;

    /** 头像url */
    HeadPicUrl?: string;

    /** 自增id */
    Id?: number;

    /** 是否提交给专家 */
    IsDiagnosticMode: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd?: string;

    /** 电话号码 */
    Mobile: string;

    /** 站点名称 */
    Name: string;

    /** 省份code */
    ProvinceCode: number;

    /** 分中心 */
    SubCenterId: number;

    /** 分中心下拉列表 */
    SubCenterName?: string;

    /** 后缀 */
    Suffix?: string;

    /** 自增id */
    UserId?: number;
  }

  export class SiteInformationDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SiteInformationDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteManagerReqModel {
    /** 市code */
    CityCode: number;

    /** 站点缩写 */
    Code: string;

    /** 所属机构id */
    DepartmentId: number;

    /** 邮箱 */
    Email?: string;

    /** 专家 */
    ExpertJson?: Array<defs.IdParam>;

    /** 头像 */
    HeadPic?: string;

    /** 是否提交给专家 */
    IsDiagnosticMode: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd?: string;

    /** 电话号码 */
    Mobile: string;

    /** 站点名称 */
    Name: string;

    /** 省份code */
    ProvinceCode: number;

    /** 分中心 */
    SubCenterId: number;

    /** 后缀 */
    Suffix?: string;
  }

  export class SiteUserDto {
    /** id */
    Id?: number;

    /** 账号 */
    LoginName?: string;

    /** 名称 */
    TrueName?: string;
  }

  export class SiteUserDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SiteUserDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteUserInforDto {
    /** 市 */
    CityCode?: number;

    /** 邮箱 */
    Email?: string;

    /** 用户头像 */
    HeadPic?: string;

    /** 头像 */
    HeadPicUrl?: string;

    /** 用户id */
    Id?: number;

    /** 账号 */
    LoginName?: string;

    /** 手机号码 */
    Mobile?: string;

    /** 省 */
    ProvinceCode?: number;

    /** 名称 */
    TrueName?: string;
  }

  export class SiteUserInforDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SiteUserInforDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SiteUserReqModel {
    /** 市 */
    CityCode: number;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 账号 */
    LoginName: string;

    /** 密码 */
    LoginPwd: string;

    /** 手机号码 */
    Mobile: string;

    /** 省 */
    ProvinceCode: number;

    /** 名称 */
    TrueName: string;
  }

  export class SliceDescriptionUpdateReqModel {
    /** 切片ID */
    CaseUploadsliceId?: number;

    /** 评价值 */
    SliceDescription?: string;
  }

  export class SliceQualityAssessmentsRate {
    /** 切片质量评价 */
    RateText?: string;

    /** 切片名称 */
    SliceName?: string;
  }

  export class SliceQualityControlDto {
    /** 条形码/会诊编号 */
    BarCode?: string;

    /** 病例id */
    CaseId?: number;

    /** 病理类型 */
    CaseType?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 送检项目 */
    CheckItemName?: string;

    /** 送检项目 */
    CheckItems?: string;

    /** 切片上传时间 */
    CreateTime?: string;

    /** 评价值 */
    Evaluation?: number;

    /** 评价时间 */
    EvaluationTime?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** 切片id */
    Id?: number;

    /** 站点名称 */
    Inspection?: string;

    /** 患者名称 */
    Name?: string;

    /** 病例号 */
    PathologyNumber?: string;

    /** 备注 */
    Remark?: string;

    /** 报告发布时间 */
    ReportTime?: string;

    /** 复合专家 */
    ReviewExpertName?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位 */
    SampleLocationName?: string;

    /** 切片总数 */
    SliceCount?: number;

    /** 切片描述 */
    SliceDescription?: string;

    /** 切片描述时间 */
    SliceDescriptionTime?: string;

    /** 切片名称 */
    SliceName?: string;

    /** 亚专科 */
    SubProfessional?: string;
  }

  export class SliceQualityControlDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.SliceQualityControlDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SliceReqModel {
    /** 创建人 */
    CreateUser?: string;

    /** 磁盘路径 */
    DiskPath?: string;

    /** 染色 */
    Dyeing?: string;

    /** 切片名称 */
    FileName?: string;

    /** 切片大小 */
    FileSize?: number;

    /** 切片类型 */
    FileType?: string;

    /** 标签 */
    Label?: string;

    /** 宏观图 */
    MacroImageUrl?: string;

    /** 备注 */
    Remark?: string;

    /** 切片类型 */
    SliceType?: number;

    /** 状态 */
    Status?: number;

    /** 缩略图 */
    Thumbnail?: string;

    /** 切片路径 */
    Url?: string;
  }

  export class SpotCheckCaseCountDto {
    /** Currently selected */
    CurrentlySelected?: number;

    /** 未抽查病例 */
    UnsampledCase?: number;

    /** 全部 */
    Whole?: number;
  }

  export class SpotCheckCaseCountDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SpotCheckCaseCountDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SpotCheckCaseDto {
    /** 年龄 */
    Age?: string;

    /** 诊断中心 */
    DiagnosticCenter?: string;

    /** 诊断专家 */
    DiagnosticExpert?: string;

    /** 诊断编号 */
    DiagnosticNumber?: string;

    /** 专家诊断 */
    ExpertDiagnosis?: string;

    /** Id */
    Id?: number;

    /** 姓名 */
    Name?: string;

    /** 原病理号 */
    OriginalPathologyNumber?: string;

    /** 病理类型 */
    PathologicalType?: string;

    /** 病理类型名称 */
    PathologicalTypeName?: string;

    /** 性别 */
    Sex?: number;

    /** 提交时间 */
    SubmissionTime?: string;
  }

  export class SpotCheckCaseDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SpotCheckCaseDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StatisticsShowDto {
    /** 年龄 */
    Age?: string;

    /** 病理类型 */
    CaseType?: string;

    /** 病理子类型 */
    CheckItems?: string;

    /** 发布时间 */
    CompleteTime?: string;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 初诊专家 */
    ExpertName?: string;

    /** 诊断时间 */
    ExpertTime?: string;

    /** 患者姓名 */
    Name?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 患者性别 */
    Sex?: string;

    /** 会诊状态 */
    Status?: string;

    /** 会诊状态名称 */
    StatusName?: string;

    /** 分中心名称 */
    SubCentersName?: string;

    /** 会诊编号 */
    TurnoverId?: string;
  }

  export class StatisticsShowDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.StatisticsShowDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StatusDto {
    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;
  }

  export class StatusDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.StatusDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StopRecordTaskDto {
    /** Code */
    Code?: string;

    /** Record */
    Record?: defs.StopRecordTaskRecordDto;
  }

  export class StopRecordTaskDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.StopRecordTaskDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StopRecordTaskRecordDto {
    /** 房间id(停止录制和查询云端录制文件时需要) */
    Cid?: string;

    /** State */
    State?: number;

    /** 任务id */
    TaskId?: string;
  }

  export class StorageConfigDto {
    /** AccessKeyId */
    AccessKeyId?: string;

    /** AccessKeySecret */
    AccessKeySecret?: string;

    /** 附件 */
    Annex?: string;

    /** 存储桶 */
    BucketName?: string;

    /** Url */
    Endpoint?: string;

    /** 头像 */
    HeadPortrait?: string;

    /** Region */
    Region?: string;

    /** S3ForcePathStyle */
    S3ForcePathStyle?: boolean;

    /** 标注 */
    Sdpl?: string;

    /** 切片 */
    Slice?: string;

    /** 切片截图 */
    SliceScreenshot?: string;

    /** SslEnabled */
    SslEnabled?: boolean;

    /** 对象存储名称 */
    StorageName?: string;

    /** 对象存储类型 */
    StorageType?: number;

    /** 缩略图 */
    Thumbnail?: string;

    /** 令牌 */
    Token?: string;
  }

  export class StorageConfigDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.StorageConfigDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StorageUserModel {
    /** Desc:邮箱
Default:
Nullable:False */
    Mail?: string;

    /** Phone */
    Phone?: string;

    /** Desc:备注
Default:
Nullable:True */
    Remark?: string;

    /** Desc:用户名称
Default:
Nullable:False */
    UserName?: string;
  }

  export class StrDto {
    /** Str */
    Str?: string;
  }

  export class StrDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.StrDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StrParam {
    /** 字符串 */
    Str?: string;
  }

  export class StrParamApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.StrParam;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StrParamListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.StrParam>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StringApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: string;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StringInt32DictionaryApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: ObjectMap<any, number>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StringObjectDictionaryApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: ObjectMap<any, any>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StringParam {
    /** id */
    Id: number;

    /** 字符串 */
    Str?: string;
  }

  export class StringParamApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.StringParam;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class StringStringValueTuple {}

  export class SubCenterInformationDto {
    /** 条数 */
    ExpertCount?: number;

    /** 专家下拉 */
    ExpertDrop?: Array<defs.CommonDto>;

    /** 医院权限下拉 */
    HospitalAuthority?: Array<defs.CommonDto>;
  }

  export class SubCenterInformationDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SubCenterInformationDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SubCenterListDto {
    /** 预约号前缀 */
    ConsultationNumber?: string;

    /** 默认专家 */
    ExpertId?: number;

    /** 医院权限 */
    HospitalAuthority?: string;

    /** id */
    Id?: number;

    /** 名称 */
    Name: string;

    /** 手抄手机号 */
    Phone: string;

    /** 分诊号前缀/会诊号 */
    PrefixForTriageNumber?: string;
  }

  export class SubCenterListDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SubCenterListDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SubCenterListUpdateParam {
    /** 预约号前缀 */
    ConsultationNumber?: string;

    /** 默认专家 */
    ExpertId?: number;

    /** 医院权限 */
    HospitalAuthority: number;

    /** id */
    Id: number;

    /** 名称 */
    Name: string;

    /** 手抄手机号 */
    Phone: string;

    /** 分诊号前缀/会诊号 */
    PrefixForTriageNumber?: string;
  }

  export class SubaggregateDto {
    /** 编码 */
    Code?: string;

    /** Id */
    Id?: number;

    /** ImmunohistochemistryId */
    ImmunohistochemistryId?: number;

    /** 子集合（免疫组化套餐） */
    List?: Array<defs.OrderTypeDto>;

    /** Name */
    Name?: string;

    /** ParentId */
    ParentId?: number;
  }

  export class SubmitInspectionDto {
    /** 名称 */
    Name?: string;

    /** 百分比 */
    Percentage?: string;

    /** 备注 */
    Remark?: string;
  }

  export class SubmitInspectionDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.SubmitInspectionDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SubmitReturnReqModel {
    /** id */
    Id: number;

    /** 是否为冰冻 */
    IsFrozen?: boolean;

    /** 退回原因 */
    Remark?: string;

    /** 退回类型 */
    ReturnType?: string;
  }

  export class Suffix {
    /** Id */
    Id?: number;

    /** Name */
    Name?: string;
  }

  export class SuffixListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.Suffix>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SuggestDto {
    /** 联系方式 */
    ContactDetails?: string;

    /** 内容 */
    Content?: string;

    /** 提交时间 */
    CreateTime?: string;

    /** 创建人姓名 */
    CreateUserName?: string;

    /** id */
    Id?: number;

    /** 组织名称 */
    OrganizeName?: string;

    /** 平台名称 */
    PlatformName?: string;

    /** 站点名称 */
    SiteName?: string;
  }

  export class SuggestDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SuggestDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysCentralMessageLog {
    /** 添加时间 */
    CreateTime?: string;

    /** Id */
    Id?: number;

    /** 记录简讯 */
    LogMsg?: string;

    /** 用户ID */
    UserId?: number;
  }

  export class SysCentralMessageLogPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SysCentralMessageLog>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysOrganize {
    /** Address */
    Address?: string;

    /** EName */
    EName?: string;

    /** Id */
    Id?: number;

    /** LimsName */
    LimsName?: string;

    /** Name */
    Name?: string;

    /** ParentIdList */
    ParentIdList?: string;

    /** Phone */
    Phone?: string;

    /** Status */
    Status?: number;

    /** UpdateTime */
    UpdateTime?: string;

    /** UpdateUser */
    UpdateUser?: string;

    /** UpdateUserName */
    UpdateUserName?: string;
  }

  export class SysOrganizeApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SysOrganize;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysUpdateLog {
    /** 更新时间 */
    CreateTime?: string;

    /** 版本说明 */
    Description?: string;

    /** 自增Id */
    Id?: number;

    /** 版本简介 */
    Introduction?: string;

    /** 脚本描述 */
    ScriptDescription?: string;

    /** 脚本名称 */
    ScriptName?: string;

    /** 脚本版本 */
    ScriptVersion?: string;

    /** 更新状态 */
    Status?: number;

    /** 版本代号 */
    Version: string;

    /** 版本名称 */
    VersionName?: string;
  }

  export class SysUpdateLogPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SysUpdateLog>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysUserLog {
    /** 添加时间 */
    AddTime?: string;

    /** Code */
    Code?: string;

    /** Id */
    Id?: number;

    /** 用户IP地址 */
    Ip?: string;

    /** 记录详情 */
    LogDetails?: string;

    /** 记录的操作名称 */
    LogMethod?: string;

    /** 记录简讯 */
    LogMsg?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 请求端口 */
    Port?: string;

    /** 用户ID */
    UserId?: number;
  }

  export class SysUserLogApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.SysUserLog;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysUserLogDto {
    /** 添加时间 */
    AddTime?: string;

    /** Id */
    Id?: number;

    /** 用户IP地址 */
    Ip?: string;

    /** 记录详情 */
    LogDetailObj?: any;

    /** 记录详情 */
    LogDetails?: string;

    /** 记录的操作名称 */
    LogMethod?: string;

    /** 记录简讯 */
    LogMsg?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 请求端口 */
    Port?: string;

    /** 用户ID */
    UserId?: number;

    /** 用户Name */
    UserName?: string;
  }

  export class SysUserLogDtoListApiResult {
    /** 状态码 */
    Code?: number;

    /** 数据集 */
    Data?: Array<defs.SysUserLogDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class SysUserLogDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.SysUserLogDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class TemplateExistModel {
    /** 类型 */
    CaseTypeCode?: string;

    /** 项目 */
    CheckItemCode?: string;

    /** 分中心或站点 */
    Id?: number;

    /** 截图个数 */
    ShotNum?: number;

    /** 模板id */
    TemplateId?: number;

    /** 1、分中心 2、站点 */
    TypeEnum?: number;
  }

  export class TenantRolesDot {
    /** 角色Id */
    RoleId?: number;

    /** 租户Id */
    TenantId?: number;
  }

  export class ToBeAssignedDto {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 意向专家 */
    IntentPersonName?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** SliceThumbnail */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class ToBeAssignedDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.ToBeAssignedDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class UpdateAdminParam {
    /** 机构id */
    DepartmentId: string;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** Id */
    Id: number;

    /** 是否为工程师 */
    IsEngineer?: boolean;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile: string;

    /** 名称 */
    TrueName: string;
  }

  export class UpdateCaseReqModel {
    /** 年龄 */
    Age: string;

    /** 附件 */
    AnnexList?: Array<defs.AnnexReqModel>;

    /** 会诊号 */
    BarCode: string;

    /** 床号 */
    BedNumber?: string;

    /** 评价 */
    CaseSiteEvaluateList?: Array<defs.CaseSiteEvaluate>;

    /** 病例类型 */
    CaseType: string;

    /** 送检项目 */
    CheckItems: string;

    /** 临床资料 */
    ClinicalData: string;

    /** 临床诊断 */
    ClinicalDiagnosis: string;

    /** 留言内容 */
    Content?: string;

    /** 原病理诊断 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 民族 */
    EthnicId?: number;

    /** 住院号 */
    HospitalizedId?: string;

    /** 病例id */
    Id?: number;

    /** 身份证号 */
    IdNumber?: string;

    /** 免疫组化 */
    Immunohistochemical?: string;

    /** 病房 */
    InpatientWard?: string;

    /** 送检单位 */
    Inspection: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 意向专家Id */
    IntentPerson?: number;

    /** 意向专家名称 */
    IntentPersonName?: string;

    /** 是否为疑难病 */
    IsRefractoryDisease?: number;

    /** 工作 */
    Job?: string;

    /** 婚姻状况 */
    MaritalStatus?: number;

    /** 女性月经时间 */
    MenstruationDate?: string;

    /** 患者名称 */
    Name: string;

    /** 病理号 */
    PathologyNumber: string;

    /** 病人id */
    PatientId?: number;

    /** 联系电话 */
    Phone?: string;

    /** 备注信息 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode: string;

    /** 采样日期 */
    SamplingTime: string;

    /** 性别 */
    Sex: number;

    /** 切片 */
    SliceList?: Array<defs.UpdateSlice>;

    /** 亚专科 */
    SubProfessional: string;

    /** 预约日期 */
    SubscribeTime: string;

    /** 大体可见 */
    Thus?: string;

    /** 就诊号 */
    VisitingNumber?: string;
  }

  export class UpdateDistributorReqModel {
    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** id */
    Id: number;

    /** 账号 */
    LoginName: string;

    /** 电话 */
    Mobile: string;

    /** 站点分中心list */
    SiteSubCenterList?: Array<defs.DistributorSiteDto>;

    /** 名称 */
    TrueName: string;
  }

  export class UpdateExpertLevelReqModel {
    /** 收费标准 */
    Charges: string;

    /** 专家级别ID */
    Id?: number;

    /** 级别名称 */
    Name: string;

    /** 收费标准说明 */
    Remark: string;
  }

  export class UpdateExpertParam {
    /** 简介 */
    BriefIntroduction: string;

    /** 关联业务 */
    Business?: string;

    /** 收费标准 */
    Charges: number;

    /** 市code 440300 */
    CityCode: number;

    /** 工作单位 */
    Company: number;

    /** 邮箱 */
    Email?: string;

    /** 专家类型 */
    ExpertType: string;

    /** 专家级别 */
    ExpertlevelCode: string;

    /** 头像 */
    HeadPic?: string;

    /** Id */
    Id: number;

    /** 账号 */
    LoginName: string;

    /** 电话号码 */
    Mobile: string;

    /** 医生职称 */
    NickName: string;

    /** 病例数量 */
    NumberOfCases?: string;

    /** 省code 440000 */
    ProvinceCode: number;

    /** 质控权限 */
    QualityControl?: string;

    /** 签名 */
    Signature: string;

    /** 关联集合 */
    SiteSubCenter?: Array<defs.ExpertSiteDto>;

    /** 专家专长 */
    SpecialistExpertise: string;

    /** 名称 */
    TrueName: string;
  }

  export class UpdateExpertVocabularysReqModel {
    /** 类型 */
    CaseTypeCode: string;

    /** 送检项 */
    CheckItemsCode: string;

    /** 英文模板 */
    EnglishTemplate?: string;

    /** id */
    Id: number;

    /** 名称 */
    Name: string;

    /** 描述 */
    Remark?: string;

    /** 中文模板 */
    Template: string;
  }

  export class UpdateFrozenParam {
    /** 年龄 */
    Age: string;

    /** 附件 */
    AnnexList?: Array<defs.AnnexBaseReqModel>;

    /** 预约日期 */
    AppointmentTime: string;

    /** 临床诊断 */
    ClinicalDiagnosis?: string;

    /** 临床病历 */
    ClinicalMedicalRecords?: string;

    /** 医生名称 */
    Doctor?: string;

    /** Id */
    Id?: number;

    /** 意向专家Id */
    IntentPerson: number;

    /** 意向专家名称 */
    IntentPersonName: string;

    /** 名称 */
    Name: string;

    /** 预约号码 */
    Number: string;

    /** 备注 */
    Remark?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 性别 */
    Sex: number;

    /** 手术所见 */
    SurgicalFindings?: string;

    /** 号码 */
    UserPhone?: string;
  }

  export class UpdateNimidParam {
    /** 云信Id */
    AccId: string;

    /** 网易云信Token */
    Token: string;
  }

  export class UpdateOrderTypeParam {
    /** 编码 */
    Code?: string;

    /** id */
    Id?: number;

    /** Name */
    Name?: string;
  }

  export class UpdateOrganizenReqModel {
    /** 地址 */
    Address: string;

    /** 英文 */
    EName: string;

    /** id */
    Id: number;

    /** LimsName */
    LimsName?: string;

    /** 名称 */
    Name: string;

    /** 电话 */
    Phone: string;
  }

  export class UpdatePassWordReqModel {
    /** 旧密码 */
    OldPassWord?: string;

    /** 密码 */
    PassWord?: string;
  }

  export class UpdatePubBaseCodeParam {
    /** 即将超时 */
    AboutToTimeOut?: string;

    /** 即将超时单位 */
    AboutToTimeOutUnit?: number;

    /** 业务类型 */
    BusType?: string;

    /** 编码或英文 */
    Code?: string;

    /** 诊断超时 */
    DiagnosticTimeOut?: string;

    /** 诊断超时单位 */
    DiagnosticTimeOutUnit?: number;

    /** 扩展名 */
    ExtName?: string;

    /** his编码 */
    HisCode?: string;

    /** id */
    Id: number;

    /** json绑定 */
    JsonData?: number;

    /** 业务线属性 */
    LineOfBusinessProperties?: number;

    /** 名称 */
    Name: string;

    /** 父节点 */
    ParentId?: number;

    /** 备注 */
    Remark?: string;
  }

  export class UpdateQualityEvaluationListQueryParam {
    /** 诊断符合率 */
    CoincidenceRate?: string;

    /** 诊断符合率具体评价 */
    CoincidenceRateEvaluation?: string;

    /** 诊断符合率备注 */
    CoincidenceRateRemark?: string;

    /** 病例id */
    Id?: number;

    /** 切片质量评价 */
    Quality?: number;

    /** 切片质量评价备注 */
    QualityRemark?: string;

    /** 切片质量具体评价 */
    QualityRemarkEvaluation?: string;
  }

  export class UpdateSiteParam {
    /** 市code */
    CityCode: number;

    /** 站点缩写 */
    Code: string;

    /** 所属机构id */
    DepartmentId: number;

    /** 邮箱 */
    Email?: string;

    /** 专家 */
    ExpertJson?: Array<defs.IdParam>;

    /** 头像 */
    HeadPic?: string;

    /** id */
    Id: number;

    /** 是否提交给专家 */
    IsDiagnosticMode: number;

    /** 是否报告工作单位 */
    IsShowCompany?: number;

    /** 账号 */
    LoginName: string;

    /** 电话号码 */
    Mobile: string;

    /** 站点名称 */
    Name: string;

    /** 省份code */
    ProvinceCode: number;

    /** 分中心 */
    SubCenterId: number;

    /** 后缀 */
    Suffix?: string;
  }

  export class UpdateSiteUserReqModel {
    /** 市 */
    CityCode: number;

    /** 邮箱 */
    Email?: string;

    /** 头像 */
    HeadPic?: string;

    /** 用户id */
    Id: number;

    /** 账号 */
    LoginName?: string;

    /** 手机号码 */
    Mobile: string;

    /** 省 */
    ProvinceCode: number;

    /** 名称 */
    TrueName: string;
  }

  export class UpdateSlice {
    /** 创建人 */
    CreateUser?: string;

    /** 磁盘路径 */
    DiskPath?: string;

    /** 染色 */
    Dyeing?: string;

    /** 切片名称 */
    FileName?: string;

    /** 切片大小 */
    FileSize?: number;

    /** 切片类型 */
    FileType?: string;

    /** Id */
    Id?: number;

    /** 标签 */
    Label?: string;

    /** 宏观图 */
    MacroImageUrl?: string;

    /** 备注 */
    Remark?: string;

    /** 切片类型 */
    SliceType?: number;

    /** 状态 */
    Status?: number;

    /** 缩略图 */
    Thumbnail?: string;

    /** 切片路径 */
    Url?: string;
  }

  export class UserCaseDto {
    /** 即将超时/分钟 */
    AboutToTimeOut?: number;

    /** 年龄 */
    Age?: string;

    /** 会诊号 */
    BarCode?: string;

    /** 病例类型code */
    CaseTypeCode?: string;

    /** 病理类型 */
    CaseTypeName?: string;

    /** 项目code */
    CheckItemsCode?: string;

    /** 项目 */
    CheckItemsName?: string;

    /** 是否收藏 */
    Collect?: boolean;

    /** 会诊号 */
    ConsultationNumber?: string;

    /** 流转 */
    DataFlow?: boolean;

    /** 诊断超时/分钟 */
    DiagnosticTimeOut?: number;

    /** 病例显示时间 */
    DisplayTime?: string;

    /** 专家名称 */
    ExpertName?: string;

    /** id */
    Id?: number;

    /** 站点 */
    Inspection?: string;

    /** 是否可打印 */
    IsPrint?: number;

    /** 病人名称 */
    Name?: string;

    /** 诊断意见 */
    Opinion?: string;

    /** 超时创建时间 */
    OutTimeCreateTime?: string;

    /** 送检病例号 */
    PathologyNumber?: string;

    /** 年龄 */
    PatientAge?: string;

    /** 病人id */
    PatientId?: number;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: number;

    /** 性别 */
    SexName?: string;

    /** 站点Id */
    SiteId?: number;

    /** 缩略图 */
    SliceThumbnail?: string;

    /** 状态 */
    Status?: number;

    /** 状态名称 */
    StatusName?: string;

    /** 亚专科 */
    SubProfessional?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 缩略图objname */
    ThumbnailObjName?: string;

    /** 提交时间 */
    Time?: string;

    /** 订单流水号/会诊编号 */
    TurnoverId?: string;
  }

  export class UserCaseDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.UserCaseDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class UserConfig {
    /** 账号 */
    Account?: string;

    /** 地区code */
    AreaCode?: number;

    /** 地区 */
    Avatar?: number;

    /** 头像 */
    HeadPic?: string;

    /** id */
    Id?: number;

    /** 是否为工程师账号 */
    IsEngineer?: boolean;

    /** 级别 */
    Level?: string;

    /** 平台 */
    PlatformId?: number;

    /** RcpSiteData */
    RcpSiteData?: defs.RcpSiteData;

    /** 名称 */
    RealName?: string;

    /** 角色id */
    RoleId?: number;

    /** 角色名称 */
    RoleName?: string;

    /** 签名 */
    Signature?: string;

    /** 签名 */
    SignatureObjectName?: string;

    /** 站点 */
    SiteId?: number;

    /** 站点名称 */
    SiteName?: string;

    /** 启用状态 */
    Status?: number;

    /** StorageType */
    StorageType?: number;

    /** 分中心 */
    SubCenterId?: number;
  }

  export class UserConfigApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.UserConfig;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class VerificationCodeReqModel {
    /** 参数类型 */
    PhoneOrMail: string;

    /** SendType */
    SendType?: defs.SendTypeEnum;
  }

  export class VocabularysLikeDto {
    /** 字段 */
    Template?: string;
  }

  export class VocabularysLikeDtoPageResponse {
    /** 响应码 200为成功 401为未授权 500内部报错等 对应httpcode */
    Code?: number;

    /** Data */
    Data?: Array<defs.VocabularysLikeDto>;

    /** 响应消息 */
    Message?: string;

    /** PageIndex */
    PageIndex?: number;

    /** PageSize */
    PageSize?: number;

    /** 是否成功 */
    Success?: boolean;

    /** TotalCount */
    TotalCount?: number;
  }

  export class VocabularysLikeDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.VocabularysLikeDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class WorkUnitDto {
    /** Id */
    Id?: number;

    /** Name */
    Name?: string;
  }

  export class WorkUnitDtoPageResult {
    /** 状态码 */
    Code?: number;

    /** 总记录数 */
    Count?: number;

    /** 数据集 */
    Data?: Array<defs.WorkUnitDto>;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }

  export class WriteDiagnosticsDto {
    /** 年龄 */
    Age?: string;

    /** 条形码 */
    BarCode?: string;

    /** 床号 */
    BedNumber?: string;

    /** 病例id */
    CaseId?: number;

    /** CaseTypeCode */
    CaseTypeCode?: string;

    /** CaseTypeName */
    CaseTypeName?: string;

    /** 细胞量 */
    CellMass?: string;

    /** CheckItemsCode */
    CheckItemsCode?: string;

    /** CheckItemsName */
    CheckItemsName?: string;

    /** 大体可见 */
    ClinicalDiagnosis?: string;

    /** 上传时间 */
    CreateTime?: string;

    /** 原病理号 */
    DiagnosisContent?: string;

    /** 医生名称 */
    DoctorName?: string;

    /** 医生电话 */
    DoctorPhone?: string;

    /** 电镜描述 */
    ElectronMicroscopyDescription?: string;

    /** 免疫荧光 */
    Fluorescence?: string;

    /** 医院英文名称 */
    HospitalEnglishName?: string;

    /** 住院号 */
    HospitalizedId?: string;

    /** 病例Id */
    Id?: number;

    /** 炎症细胞 */
    InflammatoryCells?: string;

    /** 送检日期 */
    InspectionDate?: string;

    /** 送检科室 */
    InspectionDept?: string;

    /** 数据源 */
    JsonData?: string;

    /** 光镜所见 */
    LightMicroScope?: string;

    /** 月经 */
    Menstruation?: string;

    /** 名称 */
    Name?: string;

    /** 专家意见 */
    Opinion?: string;

    /** 病理号 */
    PathologyNumber?: string;

    /** 电话 */
    Phone?: string;

    /** 质控 */
    Quality?: number;

    /** 报告code */
    ReportCode?: string;

    /** 报告名称 */
    ReportName?: string;

    /** 报告类型 */
    ReportType?: string;

    /** 复核专家光镜所见 */
    ReviewLightMicroScope?: string;

    /** 复核专家意见 */
    ReviewOpinion?: string;

    /** 取材部位 */
    SampleLocationCode?: string;

    /** 取材部位名称 */
    SampleLocationName?: string;

    /** 性别 */
    Sex?: string;
    SamplingTime? :string;

    /** 站点名称 */
    SiteName?: string;

    /** 亚专科 */
    SubProfessionalCode?: string;

    /** 亚专科名称 */
    SubProfessionalName?: string;

    /** 预约日期 */
    SubscribeTime?: string;

    /** 附注建议 */
    Suggest?: string;

    /** TBS标准诊断 */
    Tbs?: string;

    /** 大体可见 */
    Thus?: string;

    /** 就诊号 */
    VisitingNumber?: string;

    /** 阴阳 1为阴2为阳 */
    YinPositive?: number;
  }

  export class WriteDiagnosticsDtoApiResult {
    /** 状态码 */
    Code?: number;

    /** Data */
    Data?: defs.WriteDiagnosticsDto;

    /** 信息 */
    Message?: string;

    /** 请求是否成功 */
    Success?: boolean;
  }
}

declare namespace API {
  /**
   * 用户
   */
  export namespace accountApi {
    /**
     * 获取是否开启美国网易云信/短信配置
     * /api/account/america
     */
    export namespace getAmerica {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AmericaConfigApiResult>;
    }

    /**
     * 获取是否开启安徽服务
     * /api/account/anhuiservice
     */
    export namespace getAnhuiservice {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }

    /**
     * 注销
     * /api/account/cancellation
     */
    export namespace postCancellation {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<boolean>;
    }

    /**
     * 获取冰冻预约流转到普通病例的id
     * /api/account/caseid
     */
    export namespace getCaseid {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 发送验证码 0是电话号码 1是邮箱
     * /api/account/code
     */
    export namespace putCode {
      export function request(
        params: { bodyParams: defs.VerificationCodeReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 加密
     * /api/account/encrypt
     */
    export namespace getEncrypt {
      export class Params {
        /** dec */
        dec?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 是否为工程师
     * /api/account/engineer
     */
    export namespace postEngineer {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }

    /**
     * 返回下载预签名
     * /api/account/getpresigned
     */
    export namespace getGetpresigned {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StrDtoApiResult>;
    }

    /**
     * 获取当前用户信息
     * /api/account/info
     */
    export namespace getInfo {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.UserConfigApiResult>;
    }

    /**
     * 获取用户id
     * /api/account/info/id
     */
    export namespace getInfoId {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.UserConfigApiResult>;
    }

    /**
     * 获取ip【可能是服务器ip】
     * /api/account/ip
     */
    export namespace getIp {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<string>;
    }

    /**
     * 解密
     * /api/account/jiemi
     */
    export namespace getJiemi {
      export class Params {
        /** dec */
        dec?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 登录
     * /api/account/login
     */
    export namespace postLogin {
      export function request(
        params: { bodyParams: defs.LoginReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }

    /**
     * 邮箱验证码登录
     * /api/account/login/factor
     */
    export namespace postLoginFactor {
      export function request(
        params: { bodyParams: defs.LoginReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }

    /**
     * 获取对象存储配置
     * /api/account/oss
     */
    export namespace getOss {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StorageConfigDtoApiResult>;
    }
 /**
     * 获取对象存储配置
     * /api/account/oss
     */
 export namespace getH3c {
  export function request(
    params: {},
    axiosConfig?: AxiosRequestConfig,
  ): Promise<defs.StorageConfigDtoApiResult>;
}
    /**
     * 修改密码
     * /api/account/password/update
     */
    export namespace putPasswordUpdate {
      export function request(
        params: { bodyParams: defs.UpdatePassWordReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 刷新token
     * /api/account/refreshtoken
     */
    export namespace getRefreshtoken {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RefreshTokenResponseApiResult>;
    }

    /**
     * 登录发送email
     * /api/account/send/email
     */
    export namespace postSendEmail {
      export class Params {
        /** account */
        account?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 切片修改状态--已上传
     * /api/account/slice/update
     */
    export namespace putSliceUpdate {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 授权中心增加
     * /api/account/storageuseradd
     */
    export namespace getStorageuseradd {
      export function request(
        params: { bodyParams: defs.StorageUserModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 找回密码
     * /api/account/update/password
     */
    export namespace putUpdatePassword {
      export function request(
        params: { bodyParams: defs.RetrievePasswordReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * /上传文件
     * /api/account/uploadobject
     */
    export namespace getUploadobject {
      export class Params {
        /** 预签名url */
        Url?: string;
        /** 切片id */
        Id?: number;
      }

      export function request(
        params: { params: Params; bodyParams: object },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 返回上传预签名
     * /api/account/uploadpresigned
     */
    export namespace getUploadpresigned {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StrDtoApiResult>;
    }

    /**
     * 获取用户ip
     * /api/account/userip
     */
    export namespace getUserip {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<string>;
    }
  }

  /**
   * 管理员管理
   */
  export namespace adminApi {
    /**
     * 完善管理员
     * /api/admin/complete
     */
    export namespace postComplete {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params; bodyParams: defs.CompleteAdminReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 创建管理员
     * /api/admin/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.CreateAdminReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 管理员列表
     * /api/admin/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminListDtoPageResult>;
    }

    /**
     * 获取管理员信息
     * /api/admin/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminUpdateDtoApiResult>;
    }

    /**
     * 添加授权中心角色
     * /api/admin/role
     */
    export namespace postRole {
      export class Params {
        /** Id */
        Id?: number;
        /** 名称 */
        Name?: string;
        /** code */
        Code?: string;
        /** 备注 */
        Remark?: string;
        /** 租户Id */
        TenantId?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改管理员
     * /api/admin/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateAdminParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 管理员短信服务
   */
  export namespace adminShortMessageApi {
    /**
     * 质控短信
     * /api/admin/shortmessage/quality/control
     */
    export namespace putQualityControl {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 报告补打申请成功(管理员)
     * /api/admin/shortmessage/report/refill/application/successful
     */
    export namespace putReportRefillApplicationSuccessful {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回报告成功
     * /api/admin/shortmessage/withdrawal/report/successful
     */
    export namespace putWithdrawalReportSuccessful {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 管理员切片/病例统计
   */
  export namespace adminStatisticsApi {
    /**
     * 发布统计
     * /api/AdminStatistics/PublishedStatistics
     */
    export namespace getPublishedStatistics {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PublishedStatisticsDtoListApiResult>;
    }

    /**
     * 质控统计
     * /api/AdminStatistics/QualityControlStatistics
     */
    export namespace getQualityControlStatistics {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.QualityControlStatisticsDtoListApiResult>;
    }

    /**
     * 诊断质控统计流
     * /api/AdminStatistics/QualityControlStatisticsStream
     */
    export namespace getQualityControlStatisticsStream {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 切片质控统计
     * /api/AdminStatistics/Slice
     */
    export namespace getSlice {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SliceQualityControlDtoListApiResult>;
    }

    /**
     * 切片质控流
     * /api/AdminStatistics/SliceStream
     */
    export namespace getSliceStream {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }
  }

  /**
   * 管理端与其他端通用
   */
  export namespace allCommonApi {
    /**
     * 修改申请日志
     * /api/all/common/amend/application/log
     */
    export namespace getAmendApplicationLog {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.EditHistoryDtoPageResult>;
    }

    /**
     * 专家今日病例
     * /api/all/common/expert/casecount
     */
    export namespace getExpertCasecount {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int32ApiResult>;
    }

    /**
     * 专家基本信息
     * /api/all/common/expert/infor
     */
    export namespace getExpertInfor {
      export class Params {
        /** 是否为意向专家 */
        IntentionalExpert?: boolean;
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteExpertDtoApiResult>;
    }

    /**
     * 获取站点下的专家   id为站点id
     * /api/all/common/expert/list
     */
    export namespace getExpertList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteExpertDtoPageResult>;
    }

    /**
     * 申请日志
     * /api/all/common/history/list
     */
    export namespace getHistoryList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.EditHistoryDtoPageResult>;
    }

    /**
     * 操作日志
     * /api/all/common/log
     */
    export namespace getLog {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.OperationRecordDtoPageResult>;
    }

    /**
     * 病史详情列表
     * /api/all/common/medicalhistor/infor
     */
    export namespace getMedicalhistorInfor {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseRenderingDtoPageResult>;
    }

    /**
     * 拒绝理由
     * /api/all/common/print/rejection/reason
     */
    export namespace getPrintRejectionReason {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StrDtoApiResult>;
    }
  }

  /**
   * 缴费管理
   */
  export namespace billApi {
    /**
     * 缴费
     * /api/admin/bill/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 开票
     * /api/admin/bill/enableinvoicing
     */
    export namespace putEnableinvoicing {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 发票管理列表
     * /api/admin/bill/list
     */
    export namespace getList {
      export class Params {
        /** SiteId */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 病理号 */
        PathologyNumber?: string;
        /** 发票状态 不用给值，内部处理了 */
        Status?: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.InvoiceDtoPageResult>;
    }
  }

  /**
   * 病例锁模块
   */
  export namespace caseLockingApi {
    /**
     * 添加
     * /api/caselocking/add
     */
    export namespace postAdd {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/caselocking/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否存在
     * /api/caselocking/exist
     */
    export namespace getExist {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 病例留言
   */
  export namespace caseMessageApi {
    /**
     * 留言列表
     * /api/user/casemessage/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseLeavemsgDtoPageResult>;
    }
  }

  /**
   * his操作记录控制器
   */
  export namespace caseOperationRecordApi {
    /**
     * 增
     * /api/CaseOperationRecord
     */
    export namespace postCaseOperationRecord {
      export function request(
        params: { bodyParams: defs.CaseOperationRecordsReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 质量评价列表
   */
  export namespace caseQualityAssessmentApi {
    /**
        * 质量评价列表导出

查询参数：
PathologyNumber 病理号
SubmissionTime 提交时间
Name 患者姓名
CaseType 病理类型
        * /api/CaseQualityAssessment/AdminStreamCaseStatistics/Stream
        */
    export namespace getAdminStreamCaseStatisticsStream {
      export class Params {
        /** 时间类型 0：提交时间 1：诊断时间 */
        DateType?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 新增
     * /api/CaseQualityAssessment/Create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.CaseQualityAssessmentSingleDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseQualityAssessmentApiResult>;
    }

    /**
     * 删除
     * /api/CaseQualityAssessment/Delete
     */
    export namespace deleteDelete {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 根据CaseID获取切片评价信息
     * /api/CaseQualityAssessment/GetAllSliceByCaseId
     */
    export namespace getGetAllSliceByCaseId {
      export class Params {
        /** caseId */
        caseId?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }

    /**
        * 分页查询

查询参数：
PathologyNumber 病理号
SubmissionTime 提交时间
Name 患者姓名
CaseType 病理类型
        * /api/CaseQualityAssessment/Search
        */
    export namespace getSearch {
      export class Params {
        /** 时间类型 0：提交时间 1：诊断时间 */
        DateType?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseQualityAssessmentDtoPageResult>;
    }

    /**
     * 查询
     * /api/CaseQualityAssessment/SearchSingle
     */
    export namespace getSearchSingle {
      export class Params {
        /** 传入病例ID long型 */
        id?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseQualityAssessmentSingleDtoApiResult>;
    }

    /**
     * 更新
     * /api/CaseQualityAssessment/Update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.CaseQualityAssessmentSingleDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 专家质控统计
   */
  export namespace caseQualityControlStatisticsApi {
    /**
     * 诊断符合率
     * /api/CaseQualityControlStatistics/DiagnosisCoincidenceRate
     */
    export namespace getDiagnosisCoincidenceRate {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticComplianceRateDtoListApiResult>;
    }

    /**
     * 冰冻初诊和复核符合率
     * /api/CaseQualityControlStatistics/FrozenInitialDiagnosisAndReviewCoincidenceRate
     */
    export namespace getFrozenInitialDiagnosisAndReviewCoincidenceRate {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<
        defs.FrozenInitialDiagnosisAndReviewCoincidenceRateDtoApiResult
      >;
    }

    /**
     * 非妇科细胞阳性检出率
     * /api/CaseQualityControlStatistics/NonGynecological
     */
    export namespace getNonGynecological {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.NonGynecologicalDtoApiResult>;
    }

    /**
     * 切片优良率
     * /api/CaseQualityControlStatistics/PathologySliceExcellentRate
     */
    export namespace getPathologySliceExcellentRate {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExcellentRateOfSliceDtoListApiResult>;
    }

    /**
     * 病例退回率
     * /api/CaseQualityControlStatistics/PathologySliceReturnedRate
     */
    export namespace getPathologySliceReturnedRate {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PathologySliceReturnedRateDtoApiResult>;
    }

    /**
     * 诊断及时率
     * /api/CaseQualityControlStatistics/PathologySliceTimelinessOfDiagnosis
     */
    export namespace getPathologySliceTimelinessOfDiagnosis {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PathologySliceTimelinessOfDiagnosisDtoListApiResult>;
    }

    /**
     * 妇科细胞阳性检出率
     * /api/CaseQualityControlStatistics/Positive
     */
    export namespace getPositive {
      export class Params {
        /** 站点id */
        SiteId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PositiveDtoApiResult>;
    }
  }

  /**
   * 病例超时原因
   */
  export namespace caseTimeoutMessageApi {
    /**
     * 新增
     * /api/CaseTimeoutMessage/Post
     */
    export namespace postPost {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 切片评价
   */
  export namespace caseUploadsliceApi {
    /**
     * 评价切片接口
     * /api/CaseUploadslice/EvaluationUpdate
     */
    export namespace postEvaluationUpdate {
      export function request(
        params: { bodyParams: defs.EvaluationUpdateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 评价切片备注接口
     * /api/CaseUploadslice/SliceDescriptionUpdate
     */
    export namespace postSliceDescriptionUpdate {
      export function request(
        params: { bodyParams: defs.SliceDescriptionUpdateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 业务类型配置控制器
   */
  export namespace chargeApi {
    /**
     * 业务类型配置修改
     * /api/charge/businesstype
     */
    export namespace postBusinesstype {
      export function request(
        params: { bodyParams: Array<defs.BusinessTypeChargeParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 全部业务类型配置列表（不显示金额）
     * /api/charge/businesstype/list
     */
    export namespace getBusinesstypeList {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseBusTypeChargeDtoPageResult>;
    }

    /**
     * 站点业务类型配置列表（根据站点显示金额）
     * /api/charge/businesstype/site/list
     */
    export namespace getBusinesstypeSiteList {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseBusTypeChargeDtoPageResult>;
    }
  }

  /**
   * 收费项目配置
   */
  export namespace chargingItemConfigurationApi {
    /**
     * 禁用
     * /api/chargingitemconfiguration/disable
     */
    export namespace postDisable {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 启用
     * /api/chargingitemconfiguration/enable
     */
    export namespace postEnable {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/chargingitemconfiguration/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ChargingItemConfigurationListDtoPageResult>;
    }
  }

  /**
   * 智慧云平台病例质控统计
   */
  export namespace cloudCaseQualityControlStatisticsApi {
    /**
     * 查询
     * /api/CloudCaseQualityControlStatistics
     */
    export namespace getCloudCaseQualityControlStatistics {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
        /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
        DataType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }
  }

  /**
   * 管理员公共模块
   */
  export namespace commonApi {
    /**
     * 删除角色
     * /api/admin/common/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 禁用角色
     * /api/admin/common/disable
     */
    export namespace putDisable {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 启用角色
     * /api/admin/common/dormal
     */
    export namespace putDormal {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 下拉列表通用接口
     * /api/admin/common/drop/add
     */
    export namespace getDropAdd {
      export class Params {
        /** code */
        code?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringObjectDictionaryApiResult>;
    }

    /**
     * 分中心树
     * /api/admin/common/plaform/tree
     */
    export namespace getPlaformTree {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PlatformTreeDtoPageResult>;
    }

    /**
     * 重置密码
     * /api/admin/common/reset
     */
    export namespace putReset {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 添加租户权限
     * /api/admin/common/tenant/add
     */
    export namespace postTenantAdd {
      export function request(
        params: { bodyParams: defs.AddTenantUserRelationModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取租户列表
     * /api/admin/common/tenant/list
     */
    export namespace getTenantList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.GetOpenTenantResponseDtoPageResponse>;
    }

    /**
     * 修改密码
     * /api/admin/common/update/pwd
     */
    export namespace putUpdatePwd {
      export function request(
        params: { bodyParams: defs.CommLoginParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 授权中心用户渲染
     * /api/admin/common/user/render
     */
    export namespace getUserRender {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.GetUserResponseDtoApiResult>;
    }
  }

  /**
   * 数据流转
   */
  export namespace dataFlowApi {
    /**
     * 获取附件
     * /api/dataflow/annex
     */
    export namespace getAnnex {
      export class Params {
        /** 项目 */
        Project: number;
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AnnexDtoPageResult>;
    }

    /**
     * 批量流转
     * /api/dataflow/batch/add
     */
    export namespace putBatchAdd {
      export function request(
        params: { bodyParams: Array<defs.DataFlowReqModel> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 批量渲染编辑
     * /api/dataflow/batchedit
     */
    export namespace getBatchedit {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowEditDtoPageResult>;
    }

    /**
     * 流转
     * /api/dataflow/create
     */
    export namespace putCreate {
      export function request(
        params: { bodyParams: defs.DataFlowReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 下拉列表 SUB_SPECIALTY/亚专科  CASE_TYPE/病理类型
     * /api/dataflow/drop
     */
    export namespace getDrop {
      export class Params {
        /** code */
        code?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringObjectDictionaryApiResult>;
    }

    /**
     * 科研已流转查询
     * /api/dataflow/list
     */
    export namespace getList {
      export class Params {
        /** 项目/1科研 */
        Project: number;
        /** 亚专科 */
        SampleLocationCode?: string;
        /** 名称 */
        Name?: string;
        /** 最大id（上一页取第一条） */
        MaxId?: number;
        /** 每页总条数 */
        PageSize: number;
        /** 最小id(取当前页最后一条，下一页时传入) */
        MinId?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowDtoPageResult>;
    }

    /**
     * 流转历史记录
     * /api/dataflow/log
     */
    export namespace getLog {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowLogDtoPageResult>;
    }

    /**
     * 渲染编辑
     * /api/dataflow/render
     */
    export namespace getRender {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowEditDtoApiResult>;
    }

    /**
     * 病例渲染
     * /api/dataflow/rendering
     */
    export namespace getRendering {
      export class Params {
        /** 项目 */
        Project: number;
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseRenderingDtoApiResult>;
    }

    /**
     * 数据扭转报告单
     * /api/dataflow/report
     */
    export namespace getReport {
      export class Params {
        /** 项目 */
        Project: number;
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowReportDtoApiResult>;
    }

    /**
     * 病例切片
     * /api/dataflow/slice
     */
    export namespace getSlice {
      export class Params {
        /** 项目 */
        Project: number;
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DataFlowSliceDtoPageResult>;
    }

    /**
     * 撤回
     * /api/dataflow/withdraw
     */
    export namespace putWithdraw {
      export function request(
        params: { bodyParams: defs.DataFlowWithdrawReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 生成整体框架
   */
  export namespace dbFirstApi {
    /**
     * 获取 整体框架 文件
     * /api/DbFirst/GetFrameFiles
     */
    export namespace getGetFrameFiles {
      export class Params {
        /** dbName */
        dbName?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }
  }

  /**
   * 已诊断
   */
  export namespace diagnosedApi {
    /**
     * 修改申请
     * /api/user/diagnosed/amend/application
     */
    export namespace postAmendApplication {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 提交打印申请
     * /api/user/diagnosed/create/history
     */
    export namespace postCreateHistory {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 已诊断列表
     * /api/user/diagnosed/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }

    /**
     * 打印
     * /api/user/diagnosed/print
     */
    export namespace postPrint {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 诊疗卡
   */
  export namespace diagnosisTreatmentCardApi {
    /**
     * 增
     * /api/DiagnosisTreatmentCard
     */
    export namespace postDiagnosisTreatmentCard {
      export function request(
        params: { bodyParams: defs.CasehistoryReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 查
     * /api/DiagnosisTreatmentCard
     */
    export namespace getDiagnosisTreatmentCard {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.HsiCasehistoryDtoPageResult>;
    }

    /**
     * 实体
     * /api/DiagnosisTreatmentCard/model
     */
    export namespace getDiagnosisTreatmentCardModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.HsiCasehistoryDtoApiResult>;
    }
  }

  /**
   * 模板类型
   */
  export namespace diagnosticVocabularyPrivateTypesApi {
    /**
     * 管理员诊断词汇列表（私有）
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyPrivateTypesList
     */
    export namespace getDiagnosticVocabularyPrivateTypesList {
      export class Params {
        /** searchText */
        searchText?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularySimpleTypesDtoPageResult>;
    }

    /**
     * 管理员诊断词汇列表（公有）
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyPublicTypesList
     */
    export namespace getDiagnosticVocabularyPublicTypesList {
      export class Params {
        /** searchText */
        searchText?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularySimpleTypesDtoPageResult>;
    }

    /**
     * 新增
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeAdd
     */
    export namespace postDiagnosticVocabularyTypeAdd {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTypeDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeDelete
     */
    export namespace deleteDiagnosticVocabularyTypeDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 编辑
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeEdit
     */
    export namespace putDiagnosticVocabularyTypeEdit {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTypeDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 根据主键ID获取获取
     * /api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeModel
     */
    export namespace getDiagnosticVocabularyTypeModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularyTypeDtoApiResult>;
    }
  }

  /**
   * 分配员
   */
  export namespace distributorApi {
    /**
     * 完善分配员
     * /api/admin/distributor/complete
     */
    export namespace postComplete {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: {
          params: Params;
          bodyParams: defs.CompleteDistributorReqModel;
        },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 新增分配员
     * /api/admin/distributor/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.CreateDistributorReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 分配员列表
     * /api/admin/distributor/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorDtoPageResult>;
    }

    /**
     * 渲染分配员
     * /api/admin/distributor/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorUpdateDtoApiResult>;
    }

    /**
     * 修改分配员
     * /api/admin/distributor/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateDistributorReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 分配员专家控制器
   */
  export namespace distributorExpertApi {
    /**
     * 退回原因列表
     * /api/distributor/expert/reason/return/list
     */
    export namespace getReasonReturnList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ReasonForReturnDtoPageResult>;
    }

    /**
     * 退回站点
     * /api/distributor/expert/return/submit
     */
    export namespace putReturnSubmit {
      export function request(
        params: { bodyParams: defs.SubmitReturnReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 分配员短信服务
   */
  export namespace distributorShortMessageApi {
    /**
     * 批量分配
     * /api/distributor/shortmessage/bulk/allocation
     */
    export namespace putBulkAllocation {
      export function request(
        params: { bodyParams: defs.ShortMessageBulkAllocationParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 冰冻分配
     * /api/distributor/shortmessage/frozen/distribution
     */
    export namespace putFrozenDistribution {
      export function request(
        params: { bodyParams: defs.OrdinaryCaseAssignmentParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 分配普通病例/疑难/冰冻
     * /api/distributor/shortmessage/ordinary/case/assignment
     */
    export namespace putOrdinaryCaseAssignment {
      export function request(
        params: { bodyParams: defs.OrdinaryCaseAssignmentParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 医嘱确认
   */
  export namespace doctorOrderApi {
    /**
     * 拒绝医嘱
     * /api/doctor/order/delete
     */
    export namespace putDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否禁用按钮
     * /api/doctor/order/disabled
     */
    export namespace putDisabled {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 执行医嘱
     * /api/doctor/order/executed
     */
    export namespace putExecuted {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改快递地址
     * /api/doctor/order/express
     */
    export namespace postExpress {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 医嘱确认
     * /api/doctor/order/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.MedicalOrderConfirmationDtoPageResult>;
    }

    /**
     * 获取具体医嘱内容
     * /api/doctor/order/model
     */
    export namespace getModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertDoctorsAdviceDtoApiResult>;
    }
  }

  /**
   * 工程师账号
   */
  export namespace engineerApi {
    /**
     * 创建工程师
     * /api/engineer/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.EngineerReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 工程师列表
     * /api/engineer/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.EngineerDtoPageResult>;
    }

    /**
     * 修改工程师
     * /api/engineer/update
     */
    export namespace postUpdate {
      export function request(
        params: { bodyParams: defs.EngineerReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 专家端app
   */
  export namespace expertAppStatisticsApi {
    /**
     * 专家条数统计
     * /api/ExpertAppStatistics/ExpertHomeCount
     */
    export namespace getExpertHomeCount {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertHomeCountDtoApiResult>;
    }
  }

  /**
   * 专家病例管理列表
   */
  export namespace expertCaseManagementApi {
    /**
     * 获取管理员列表
     * /api/expert/admin/list
     */
    export namespace getAdminList {
      export function request(
        params: { bodyParams: defs.PageParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertAdminDtoPageResult>;
    }

    /**
     * 专家收藏
     * /api/expert/enshrine/list
     */
    export namespace getEnshrineList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }

    /**
     * 专家冰冻预约
     * /api/expert/frozen/list
     */
    export namespace getFrozenList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorFrozenDtoPageResult>;
    }

    /**
     * 查看病例邀请
     * /api/expert/invitation/list
     */
    export namespace getInvitationList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertInviteeDtoPageResult>;
    }

    /**
     * 受邀诊断
     * /api/expert/invited/diagnosis/list
     */
    export namespace getInvitedDiagnosisList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }

    /**
     * 待诊断
     * /api/expert/list
     */
    export namespace getList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertCaseDtoPageResult>;
    }

    /**
     * 已发布病例
     * /api/expert/published/list
     */
    export namespace getPublishedList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }

    /**
     * 专家端质控评价
     * /api/expert/qualityevaluation/list
     */
    export namespace getQualityevaluationList {
      export class Params {
        /** 病例类型 */
        CaseType?: string;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertQualityControlEvaluationDtoPageResult>;
    }

    /**
     * 专家端质控评价导出
     * /api/expert/qualityevaluation/stream
     */
    export namespace getQualityevaluationStream {
      export class Params {
        /** 病例类型 */
        CaseType?: string;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 退回
     * /api/expert/return/list
     */
    export namespace getReturnList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }

    /**
     * 复核列表
     * /api/expert/review/list
     */
    export namespace getReviewList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertCaseDtoPageResult>;
    }

    /**
     * 专家数据统计
     * /api/expert/statistics/list
     */
    export namespace getStatisticsList {
      export class Params {
        /** 专家下拉【0默认值，1初审，2复审】 */
        ExpertType?: number;
        /** 标签id */
        LabelId?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertStatisticsDtoPageResult>;
    }
  }

  /**
   * 专家功能操作控制器
   */
  export namespace expertFunctionApi {
    /**
     * 同意签发
     * /api/expert/function/agree/issue
     */
    export namespace putAgreeIssue {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 生成报告单 存在则更新
     * /api/expert/function/buildReport
     */
    export namespace postBuildReport {
      export function request(
        params: { bodyParams: defs.CaseReportReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 取消收藏
     * /api/expert/function/cancelenshrine
     */
    export namespace putCancelenshrine {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取病例是否收藏
     * /api/expert/function/collect
     */
    export namespace getCollect {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BoolDtoApiResult>;
    }

    /**
     * 确认回复
     * /api/expert/function/confirm/reply
     */
    export namespace putConfirmReply {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 邀请专家
     * /api/expert/function/create/invitee
     */
    export namespace postCreateInvitee {
      export function request(
        params: { bodyParams: Array<defs.ExpertInviteeReqModel> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 渲染病例数据
     * /api/expert/function/diagnosis
     */
    export namespace getDiagnosis {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.WriteDiagnosticsDtoApiResult>;
    }

    /**
     * 诊断-状态为待诊断或复核请求此接口
     * /api/expert/function/diagnosis
     */
    export namespace postDiagnosis {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 收藏病例
     * /api/expert/function/enshrine
     */
    export namespace putEnshrine {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取切片质量
     * /api/expert/function/evaluation/model
     */
    export namespace getEvaluationModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.GetQualityDtoApiResult>;
    }

    /**
     * 修改切片质量
     * /api/expert/function/evaluation/quality
     */
    export namespace postEvaluationQuality {
      export function request(
        params: { bodyParams: defs.UpdateQualityEvaluationListQueryParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否存在专家邀请
     * /api/expert/function/existexpertinvitee
     */
    export namespace getExistexpertinvitee {
      export class Params {
        /** 专家id */
        ExpertId: number;
        /** 病例id */
        CaseId: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取专家类型
     * /api/expert/function/experttype
     */
    export namespace getExperttype {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CommonDtoApiResult>;
    }

    /**
     * 冰冻确定
     * /api/expert/function/frozen
     */
    export namespace putFrozen {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取邀请理由
     * /api/expert/function/invitee
     */
    export namespace getInvitee {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertInviteeDtoApiResult>;
    }

    /**
     * 新增词汇
     * /api/expert/function/privatevocabulary/add
     */
    export namespace postPrivatevocabularyAdd {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTextDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除诊断模板
     * /api/expert/function/privatevocabulary/delete
     */
    export namespace deletePrivatevocabularyDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 诊断词汇列表
     * /api/expert/function/privatevocabulary/list
     */
    export namespace getPrivatevocabularyList {
      export class Params {
        /** typeCode */
        typeCode?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularyTextDtoPageResult>;
    }

    /**
     * 渲染获取实体
     * /api/expert/function/privatevocabulary/model
     */
    export namespace getPrivatevocabularyModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularyTextDtoApiResult>;
    }

    /**
     * 词汇编辑
     * /api/expert/function/privatevocabulary/update
     */
    export namespace putPrivatevocabularyUpdate {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTextDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取质控权限
     * /api/expert/function/quality/cntrol
     */
    export namespace getQualityCntrol {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.QualityControlDtoApiResult>;
    }

    /**
     * 实时大体
     * /api/expert/function/realtimeroughly
     */
    export namespace getRealtimeroughly {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RealTimeRoughlyDtoPageResult>;
    }

    /**
     * 发布报告
     * /api/expert/function/release
     */
    export namespace putRelease {
      export function request(
        params: { bodyParams: defs.CaseReportReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取专家关联的站点
     * /api/expert/function/site
     */
    export namespace getSite {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteDropDtoPageResult>;
    }

    /**
     * 模板模糊查询（共有、所有）
     * /api/expert/function/vocabulary/like
     */
    export namespace getVocabularyLike {
      export class Params {
        /** TypeCode */
        TypeCode?: string;
        /** likeStr */
        likeStr?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.VocabularysLikeDtoPageResult>;
    }

    /**
     * 私有模板添加
     * /api/expert/function/vocabularys/add
     */
    export namespace postVocabularysAdd {
      export function request(
        params: { bodyParams: defs.ExpertVocabularysReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/expert/function/vocabularys/delete
     */
    export namespace putVocabularysDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 渲染
     * /api/expert/function/vocabularys/infor
     */
    export namespace getVocabularysInfor {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RenderExpertVocabularysDtoApiResult>;
    }

    /**
     * 模糊查询
     * /api/expert/function/vocabularys/like
     */
    export namespace getVocabularysLike {
      export class Params {
        /** 查询字段 */
        Str: string;
        /** 父级 */
        CaseTypeCode?: string;
        /** 子集 */
        CheckItemsCode?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.VocabularysLikeDtoPageResponse>;
    }

    /**
     * 集合
     * /api/expert/function/vocabularys/list
     */
    export namespace getVocabularysList {
      export class Params {
        /** 病例类型 */
        CaseTypeCode: string;
        /** 送检项 */
        CheckItemsCode: string;
        /** 最大id（上一页取第一条） */
        MaxId?: number;
        /** 每页总条数 */
        PageSize: number;
        /** 最小id(取当前页最后一条，下一页时传入) */
        MinId?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertVocabularysDtoPageResult>;
    }

    /**
     * 修改
     * /api/expert/function/vocabularys/update
     */
    export namespace putVocabularysUpdate {
      export function request(
        params: { bodyParams: defs.UpdateExpertVocabularysReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回诊断
     * /api/expert/function/withdrawa/diagnosis
     */
    export namespace postWithdrawaDiagnosis {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回复核
     * /api/expert/function/withdrawalofreview
     */
    export namespace postWithdrawalofreview {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 专家标签
   */
  export namespace expertLabelApi {
    /**
     * 添加
     * /api/expert/label/add
     */
    export namespace postAdd {
      export function request(
        params: { bodyParams: defs.CaseLabelParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 病例关联标签集合
     * /api/expert/label/case/list
     */
    export namespace getCaseList {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.LabelDtoPageResult>;
    }

    /**
     * 删除
     * /api/expert/label/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.CaseLabelParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/expert/label/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.LabelDtoPageResult>;
    }
  }

  /**
   * 专家管理
   */
  export namespace expertManagementApi {
    /**
     * 专家类型
     * /api/admin/expert/bustype
     */
    export namespace getBustype {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PubBaseCodeTreeDtoPageResult>;
    }

    /**
     * 完善专家
     * /api/admin/expert/complete
     */
    export namespace postComplete {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params; bodyParams: defs.CompleteCreateExpertModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 新增专家
     * /api/admin/expert/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.CreateExpertParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 创建专家级别
     * /api/admin/expert/level/create
     */
    export namespace postLevelCreate {
      export function request(
        params: { bodyParams: defs.ExpertLevelReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除专家级别
     * /api/admin/expert/level/delete
     */
    export namespace deleteLevelDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 专家级别列表
     * /api/admin/expert/level/list
     */
    export namespace getLevelList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertLevelPageResult>;
    }

    /**
     * 渲染专家级别
     * /api/admin/expert/level/rendering
     */
    export namespace getLevelRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertLevelApiResult>;
    }

    /**
     * 修改专家级别
     * /api/admin/expert/level/update
     */
    export namespace putLevelUpdate {
      export function request(
        params: { bodyParams: defs.UpdateExpertLevelReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取专家列表
     * /api/admin/expert/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminExpertPageResult>;
    }

    /**
     * 获取专家基本信息
     * /api/admin/expert/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertRenderingDtoApiResult>;
    }

    /**
     * 修改专家
     * /api/admin/expert/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateExpertParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 复核模块
   */
  export namespace expertReviewApi {
    /**
     * 复核邀请/退回
     * /api/review/review
     */
    export namespace postReview {
      export function request(
        params: { bodyParams: defs.ReviewReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 复核查看意见
     * /api/review/review/opinion
     */
    export namespace getReviewOpinion {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ReviewOpinionDtoApiResult>;
    }

    /**
     * 修改复核意见
     * /api/review/update/review/opinion
     */
    export namespace postUpdateReviewOpinion {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 报告操作
   */
  export namespace expertShortMessageApi {
    /**
     * 专家同意冰冻预约
     * /api/expert/shortmessage/agree/frozen
     */
    export namespace putAgreeFrozen {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 发布报告
     * /api/expert/shortmessage/release
     */
    export namespace putRelease {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 病例退回
     * /api/expert/shortmessage/return/case
     */
    export namespace putReturnCase {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 专家撤回报告申请
     * /api/expert/shortmessage/withdraws/report/request
     */
    export namespace putWithdrawsReportRequest {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 站点收费提示
   */
  export namespace feeReminderApi {
    /**
     * 判断站点收费是否为启用
     * /api/feereminder/status
     */
    export namespace getStatus {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改消息通知 [应用：所有收费数据为0....的勾选加确认]
     * /api/feereminder/update/pointout
     */
    export namespace postUpdatePointout {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 收费
   */
  export namespace feeStatisticsApi {
    /**
     * 是否开启二维码 1开启 其他值不开启 在sys_qrcode配置
     * /api/admin/feestatistics/codedisplay
     */
    export namespace getAdminFeestatisticsCodedisplay {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int32ApiResult>;
    }

    /**
     * 收费统计
     * /api/admin/feestatistics/list
     */
    export namespace getAdminFeestatisticsList {
      export class Params {
        /** 站点id */
        SiteId: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseChargesDtoPageResult>;
    }

    /**
     * 病例收费导出
     * /api/admin/feestatistics/list/stream
     */
    export namespace getAdminFeestatisticsListStream {
      export class Params {
        /** 开始时间 */
        StartingTime: string;
        /** 结束时间 */
        EndTime: string;
        /** 站点id */
        SiteId: number;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * undefined
     * /api/admin/feestatistics/search
     */
    export namespace getAdminFeestatisticsSearch {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FeeStatisticsDtoPageResult>;
    }

    /**
     * undefined
     * /api/admin/feestatistics/searchsum
     */
    export namespace getAdminFeestatisticsSearchsum {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringStringValueTuple>;
    }

    /**
     * 病例收费导出
     * /api/admin/feestatistics/stream
     */
    export namespace getAdminFeestatisticsStream {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 金额
     * /api/admin/feestatistics/sum
     */
    export namespace getAdminFeestatisticsSum {
      export class Params {
        /** 站点id */
        SiteId: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AmountDtoApiResult>;
    }

    /**
     * 收费统计
     * /api/site/feestatistics/list
     */
    export namespace getSiteFeestatisticsList {
      export class Params {
        /** 站点id */
        SiteId: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseChargesDtoPageResult>;
    }

    /**
     * 病例收费导出
     * /api/site/feestatistics/list/stream
     */
    export namespace getSiteFeestatisticsListStream {
      export class Params {
        /** 开始时间 */
        StartingTime: string;
        /** 结束时间 */
        EndTime: string;
        /** 站点id */
        SiteId: number;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 站点收费查询
     * /api/site/feestatistics/search
     */
    export namespace getSiteFeestatisticsSearch {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FeeStatisticsDtoPageResult>;
    }

    /**
     * 站点收费
     * /api/site/feestatistics/searchsum
     */
    export namespace getSiteFeestatisticsSearchsum {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringStringValueTuple>;
    }

    /**
     * 病例收费导出
     * /api/site/feestatistics/stream
     */
    export namespace getSiteFeestatisticsStream {
      export class Params {
        /** 分中心id */
        SubCenterId?: number;
        /** 站点id */
        SiteId?: number;
        /** 缴费流水号 */
        PaymentOrderNumber?: string;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** AdvancedSearch */
        AdvancedSearch?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 站点计算金额
     * /api/site/feestatistics/sum
     */
    export namespace getSiteFeestatisticsSum {
      export class Params {
        /** 站点id */
        SiteId: number;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 病理号 */
        PathologyNumber?: string;
        /** 名称 */
        Name?: string;
        /** 会诊专家 */
        ExpertId?: number;
        /** 复核专家 */
        ReviewExpertId?: number;
        /** 缴费状态 */
        CaseChargingStatus?: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AmountDtoApiResult>;
    }
  }

  /**
   * 首页控制器
   */
  export namespace frontPageApi {
    /**
     * 送检统计
     * /api/front/page/casetype
     */
    export namespace getCasetype {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SubmitInspectionDtoListApiResult>;
    }

    /**
     * 获取首页条数
     * /api/front/page/count
     */
    export namespace getCount {
      export class Params {
        /** 病例类型 */
        CaseType?: defs.CaseTypeShowEnum;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringInt32DictionaryApiResult>;
    }

    /**
     * 消息统计
     * /api/front/page/notify
     */
    export namespace getNotify {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpNotifyPageResult>;
    }

    /**
     * 修改消息已读或已评论
     * /api/front/page/notify/status
     */
    export namespace postNotifyStatus {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 统计报表
     * /api/front/page/statistical/report
     */
    export namespace getStatisticalReport {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.InspectionItemsDtoApiResult>;
    }
  }

  /**
   * 冰冻预约列表
   */
  export namespace frozenAppointmentApi {
    /**
     * 已退回病例
     * /api/distributor/frozen/returned/list
     */
    export namespace getDistributorFrozenReturnedList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorFrozenDtoPageResult>;
    }

    /**
     * 已预约病例
     * /api/distributor/frozen/scheduled/list
     */
    export namespace getDistributorFrozenScheduledList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorFrozenDtoPageResult>;
    }

    /**
     * 待分诊预约
     * /api/distributor/frozen/tobetriaged/list
     */
    export namespace getDistributorFrozenTobetriagedList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorFrozenDtoPageResult>;
    }

    /**
     * 待撤回病例
     * /api/distributor/frozen/tobewithdrawn/list
     */
    export namespace getDistributorFrozenTobewithdrawnList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributorFrozenDtoPageResult>;
    }

    /**
     * 冰冻预约列表
     * /api/user/frozen/list
     */
    export namespace getUserFrozenList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FrozenDtoPageResult>;
    }

    /**
     * 冰冻预约待处理列表
     * /api/user/frozen/pending/list
     */
    export namespace getUserFrozenPendingList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FrozenDtoPageResult>;
    }
  }

  /**
   * 门户
   */
  export namespace gateWayApi {
    /**
     * 门户添加
     * /api/GateWay
     */
    export namespace postGateWay {
      export function request(
        params: { bodyParams: defs.GateWayAddParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * his外包对接控制器
   */
  export namespace hisApi {
    /**
     * 缴费完成
     * /api/his/pay
     */
    export namespace postPay {
      export function request(
        params: { bodyParams: defs.PaymentCompletedReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取报告单状态
     * /api/his/reportstatus
     */
    export namespace postReportstatus {
      export function request(
        params: { bodyParams: defs.AnhuiReportStatusReModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }
  }

  /**
   * his病例远程绑定
   */
  export namespace hisCaseinformationApi {
    /**
     * 增 [提交病例后请求]
     * /api/HisCaseinformation
     */
    export namespace postHisCaseinformation {
      export function request(
        params: { bodyParams: defs.HisCaseinformationReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 添加待缴费记录
     * /api/HisCaseinformation/AddCaseId
     */
    export namespace postHisCaseinformationAddCaseId {
      export function request(
        params: { bodyParams: defs.AddCaseIdReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 管理员标签
   */
  export namespace labelApi {
    /**
     * 添加
     * /api/admin/label/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/admin/label/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/admin/label/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.LabelDtoPageResult>;
    }

    /**
     * 获取一条
     * /api/admin/label/model
     */
    export namespace getModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.LabelDtoApiResult>;
    }

    /**
     * 修改
     * /api/admin/label/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 专家医嘱
   */
  export namespace medicalAdviceApi {
    /**
     * 发布医嘱/修改医嘱
     * /api/expert/medicaladvice/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/expert/medicaladvice/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/expert/medicaladvice/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertOrderTypeDtoPageResult>;
    }
  }

  /**
   * 病史
   */
  export namespace medicalHistoryApi {
    /**
     * 新增
     * /api/MedicalHistory/Add
     */
    export namespace postAdd {
      export function request(
        params: { bodyParams: Array<defs.CaseMedicalHistoryModel> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 病史列表
     * /api/MedicalHistory/MedicalHistor
     */
    export namespace getMedicalHistor {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseInforDtoPageResult>;
    }

    /**
     * 病史列表 id为病例id
     * /api/MedicalHistory/MedicalHistorById
     */
    export namespace getMedicalHistorById {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseInforDtoPageResult>;
    }

    /**
     * 删除
     * /api/MedicalHistory/Update
     */
    export namespace deleteUpdate {
      export function request(
        params: { bodyParams: defs.CaseMedicalHistoryModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 留言管理
   */
  export namespace messageApi {
    /**
     * 删除
     * /api/admin/message/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取列表
     * /api/admin/message/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminWordDtoPageResult>;
    }
  }

  /**
   * 网易云信
   */
  export namespace netEaseYunXinApi {
    /**
     * 创建频道
     * /api/neteaseyunxin/channel
     */
    export namespace postChannel {
      export function request(
        params: { bodyParams: defs.CreateParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 重新获取推拉流地址
     * /api/neteaseyunxin/channel/address
     */
    export namespace getChannelAddress {
      export class Params {
        /** 频道id */
        cid?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ChannelUrApiResult>;
    }

    /**
     * 获取直播间id
     * /api/neteaseyunxin/channel/id
     */
    export namespace getChannelId {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 直播列表
     * /api/neteaseyunxin/channel/list
     */
    export namespace getChannelList {
      export class Params {
        /** 单页记录数，默认值为10，最多1000条。 */
        Records?: number;
        /** 要取第几页，默认值为1。 */
        Pnum?: number;
        /** 排序的域，支持的排序域为：ctime（默认） */
        Ofield?: string;
        /** 升序还是降序，1升序，0降序S */
        Sort?: number;
        /** 筛选频道状态，status取值：（0：空闲，1：直播，2：禁用，3：录制中） */
        Status?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ItemPageResult>;
    }

    /**
     * 改变频道状态
     * /api/neteaseyunxin/channel/status
     */
    export namespace putChannelStatus {
      export function request(
        params: { bodyParams: defs.ChannelStatusParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取频道状态
     * /api/neteaseyunxin/channel/status
     */
    export namespace getChannelStatus {
      export class Params {
        /** 频道id */
        cid?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RetChannelStatusApiResult>;
    }

    /**
     * 获取房间token
     * /api/neteaseyunxin/channel/token
     */
    export namespace getChannelToken {
      export class Params {
        /** Uid */
        UId: number;
        /** 房间名称 */
        ChannelName?: string;
        /** 过期时间 */
        ExpireAt: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 创建录制任务
     * /api/neteaseyunxin/create/record/task
     */
    export namespace getCreateRecordTask {
      export class Params {
        /** 频道id */
        CName?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CreateRecordTaskDtoApiResult>;
    }

    /**
     * 创建群聊 【病例,实时大体共用建议拆分】
     * /api/neteaseyunxin/create/team
     */
    export namespace postCreateTeam {
      export function request(
        params: { bodyParams: defs.CreateTeamParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 获取录制文件信息
     * /api/neteaseyunxin/get/record/file/info
     */
    export namespace getGetRecordFileInfo {
      export class Params {
        /** 房间id(创建录制和停止录制时会返回) */
        Cid?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.GetRecordFileInfoDtoApiResult>;
    }

    /**
     * 群聊是否存在
     * /api/neteaseyunxin/group/chat/exist
     */
    export namespace getGroupChatExist {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<boolean>;
    }

    /**
     * 群聊人员列表
     * /api/neteaseyunxin/group/chat/list
     */
    export namespace getGroupChatList {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取网易云信配置
     * /api/neteaseyunxin/infor
     */
    export namespace getInfor {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.NeteaseYunXinApiResult>;
    }

    /**
     * 拉入群聊 受邀诊断、复核
     * /api/neteaseyunxin/pull/case/team
     */
    export namespace postPullCaseTeam {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 拉入群聊 id直接拉入方式
     * /api/neteaseyunxin/pull/team
     */
    export namespace postPullTeam {
      export function request(
        params: { bodyParams: defs.PullGroupChatParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 拉入群聊 token方式
     * /api/neteaseyunxin/pull/url/team
     */
    export namespace postPullUrlTeam {
      export function request(
        params: { bodyParams: defs.PullGroupChatUrlParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 创建房间
     * /api/neteaseyunxin/room
     */
    export namespace postRoom {
      export function request(
        params: { bodyParams: defs.CreateRoomParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 获取房间id
     * /api/neteaseyunxin/room
     */
    export namespace getRoom {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     *
     * /api/neteaseyunxin/sms/message
     */
    export namespace postSmsMessage {
      export function request(
        params: { bodyParams: any },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 停止录制任务
     * /api/neteaseyunxin/stop/record/task
     */
    export namespace getStopRecordTask {
      export class Params {
        /** 频道id */
        CName?: string;
        /** 任务id */
        TaskId?: string;
        /** 房间id(停止录制和查询云端录制文件时需要) */
        Cid?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StopRecordTaskDtoApiResult>;
    }

    /**
     * 解散群组
     * /api/neteaseyunxin/team
     */
    export namespace deleteTeam {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取群聊id id病例id
     * /api/neteaseyunxin/team
     */
    export namespace getTeam {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 获取历史记录（群聊）
     * /api/neteaseyunxin/team/record
     */
    export namespace getTeamRecord {
      export class Params {
        /** 群聊id */
        Tid: string;
        /** 用户id */
        Accid: string;
        /** 查询多少条 */
        Limit?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 发送消息（群聊）
     * /api/neteaseyunxin/team/send
     */
    export namespace postTeamSend {
      export function request(
        params: { bodyParams: defs.SendMessageParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 网易云信游客登录
     * /api/neteaseyunxin/tourist
     */
    export namespace getTourist {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.NeteaseyunxinUserApiResult>;
    }

    /**
     * 生成用户的云信ID
     * /api/neteaseyunxin/user/infor
     */
    export namespace getUserInfor {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.NeteaseyunxinUserApiResult>;
    }

    /**
     * 根据id获取用户信息
     * /api/neteaseyunxin/user/infor/id
     */
    export namespace getUserInforId {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.NeteaseyunxinUserApiResult>;
    }

    /**
     * 刷新用户token
     * /api/neteaseyunxin/user/update
     */
    export namespace postUserUpdate {
      export function request(
        params: { bodyParams: defs.UpdateNimidParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 医嘱金额
   */
  export namespace orderAmountApi {
    /**
     * 删除设置金额
     * /api/orderamount/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取实体
     * /api/orderamount/model
     */
    export namespace getModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseMedicalAdviceFeeApiResult>;
    }

    /**
     * 设置金额
     * /api/orderamount/setcharges
     */
    export namespace putSetcharges {
      export class Params {
        /** caseId */
        caseId?: number;
      }

      export function request(
        params: { params: Params; bodyParams: Array<defs.IdParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 医嘱类型
   */
  export namespace orderTypeApi {
    /**
     * 批量删除项目
     * /api/admin/ordertype/batch/delete
     */
    export namespace deleteBatchDelete {
      export function request(
        params: { bodyParams: Array<defs.IdParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 新增项目
     * /api/admin/ordertype/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.OrderTypeParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除项目
     * /api/admin/ordertype/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否使用过项目
     * /api/admin/ordertype/isdoctorsadvice
     */
    export namespace getIsdoctorsadvice {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/admin/ordertype/list
     */
    export namespace getList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.OrderTypeDtoPageResult>;
    }

    /**
     * 修改
     * /api/admin/ordertype/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateOrderTypeParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 医嘱类型配置
   */
  export namespace orderTypeConfigurationApi {
    /**
     * 医嘱类型列表
     * /api/ordertypeconfiguration/list
     */
    export namespace getList {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.MedicalOrderFeesDtoPageResult>;
    }

    /**
     * 站点医嘱类型列表
     * /api/ordertypeconfiguration/site/list
     */
    export namespace getSiteList {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.MedicalOrderFeesDtoPageResult>;
    }

    /**
     * 医嘱类型配置修改
     * /api/ordertypeconfiguration/update
     */
    export namespace postUpdate {
      export function request(
        params: { bodyParams: Array<defs.BusinessTypeChargeParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 免疫组化套餐子项
   */
  export namespace orderTypeSubitemApi {
    /**
     * 批量删除子项目
     * /api/admin/ordertypesubitem/batch/delete
     */
    export namespace deleteBatchDelete {
      export function request(
        params: { bodyParams: Array<defs.IdParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除子项目
     * /api/admin/ordertypesubitem/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 新增子项目
     * /api/admin/ordertypesubitem/subitem
     */
    export namespace postSubitem {
      export function request(
        params: { bodyParams: Array<defs.ImmunohistochemicalPackage> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 机构管理
   */
  export namespace organizenApi {
    /**
     * 创建机构
     * /api/admin/organizen/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.OrganizenReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除机构
     * /api/admin/organizen/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取机构list [{FieldName:"Name",ConditionType:1,FieldValue:"测"}]
     * /api/admin/organizen/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ListOranizenDtoPageResult>;
    }

    /**
     * 渲染机构
     * /api/admin/organizen/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysOrganizeApiResult>;
    }

    /**
     * 修改机构
     * /api/admin/organizen/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateOrganizenReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 其他收费
   */
  export namespace otherChargeApi {
    /**
     * 获取一条数据
     * /api/othercharge/model
     */
    export namespace getModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.OtherChargesDtoApiResult>;
    }

    /**
     * 业务类型配置修改
     * /api/othercharge/update
     */
    export namespace postUpdate {
      export function request(
        params: { bodyParams: Array<defs.OtherChargesParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 病理切片标记功能
   */
  export namespace pathologicalSectionMarkApi {
    /**
     * 获取切片标注文件的string
     * /api/PathologicalSectionMark
     */
    export namespace getPathologicalSectionMark {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<string>;
    }

    /**
     * 上传切片标注
     * /api/PathologicalSectionMark
     */
    export namespace postPathologicalSectionMark {
      export function request(
        params: { bodyParams: defs.PathologicalSectionMarkDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<boolean>;
    }
  }

  /**
   * 外包对接控制器
   */
  export namespace pathologyIntegrationApi {
    /**
     * 插入诊断、插入医嘱
     * /api/PathologyIntegration/AddDocAdvice
     */
    export namespace postAddDocAdvice {
      export function request(
        params: { bodyParams: defs.AddCaseDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AddDocAdviceResDtoApiResult>;
    }

    /**
     * 根据身份证号获取院内档案
     * /api/PathologyIntegration/BasePatientByIdNo
     */
    export namespace postBasePatientByIdNo {
      export function request(
        params: { bodyParams: defs.BasePatientByIdNoReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BasePatientByIdNoResDtoApiResult>;
    }

    /**
     * 撤销医嘱
     * /api/PathologyIntegration/CancelDocAdvice
     */
    export namespace postCancelDocAdvice {
      export function request(
        params: { bodyParams: defs.CancelDocAdviceReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CancelDocAdviceResDtoApiResult>;
    }

    /**
     * 取消挂号
     * /api/PathologyIntegration/CancelRegister
     */
    export namespace postCancelRegister {
      export function request(
        params: { bodyParams: defs.CancelRegisterReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CancelRegisterResDtoApiResult>;
    }

    /**
     * 建档
     * /api/PathologyIntegration/CreatePatientCard
     */
    export namespace postCreatePatientCard {
      export function request(
        params: { bodyParams: defs.CreatePatientCardReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CreatePatientCardResDtoApiResult>;
    }

    /**
     * 登记号/卡号查询
     * /api/PathologyIntegration/QueryPatient
     */
    export namespace postQueryPatient {
      export function request(
        params: { bodyParams: defs.QueryPatientReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.QueryPatientResDtoApiResult>;
    }

    /**
     * 获取排班
     * /api/PathologyIntegration/QueryRegSource
     */
    export namespace postQueryRegSource {
      export function request(
        params: { bodyParams: defs.QueryRegSourceReqDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.QueryRegSourceResDtoApiResult>;
    }

    /**
     * 确认挂号
     * /api/PathologyIntegration/RegisterNotice
     */
    export namespace postRegisterNotice {
      export function request(
        params: { bodyParams: defs.RegisteredDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RegisterNoticeResDtoApiResult>;
    }
  }

  /**
   * 缴费完成
   */
  export namespace paymentcompletedApi {
    /**
     * 已缴费列表
     * /api/Paymentcompleted
     */
    export namespace getPaymentcompleted {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SettlementDtoPageResult>;
    }
  }

  /**
   * 基础代码
   */
  export namespace pubBaseCodeApi {
    /**
     * 获取语言枚举
     * /api/admin/basecode/Language
     */
    export namespace getLanguage {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<Array<defs.CommonDto>>;
    }

    /**
     * 批量刪除基础代码
     * /api/admin/basecode/batch/delete
     */
    export namespace deleteBatchDelete {
      export function request(
        params: { bodyParams: Array<defs.IdParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取附件类型父id
     * /api/admin/basecode/casetype
     */
    export namespace getCasetype {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CommonDtoPageResult>;
    }

    /**
     * 管理员病理类型，送检项
     * /api/admin/basecode/casetype/list
     */
    export namespace getCasetypeList {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PubBaseCodeTreeDtoPageResult>;
    }

    /**
     * 创建基础代码
     * /api/admin/basecode/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.PubBaseCodeReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除基础代码
     * /api/admin/basecode/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取附件类型父id
     * /api/admin/basecode/filetype
     */
    export namespace getFiletype {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringParamApiResult>;
    }

    /**
     * 获取附件类型
     * /api/admin/basecode/filetype/list
     */
    export namespace getFiletypeList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPubaseCodeListPageResult>;
    }

    /**
     * 是否使用过病理类型
     * /api/admin/basecode/iscasetype
     */
    export namespace getIscasetype {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否使用过项目
     * /api/admin/basecode/ischeckitem
     */
    export namespace getIscheckitem {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 根据type获取集合
     * /api/admin/basecode/list
     */
    export namespace getList {
      export class Params {
        /** 基础代码类型 */
        BusType?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPubaseCodeListPageResult>;
    }

    /**
     * 获取父节点id
     * /api/admin/basecode/parentId
     */
    export namespace getParentId {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 获取具体项
     * /api/admin/basecode/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PuBaseCodeDtoApiResult>;
    }

    /**
     * 获取取材部位父id
     * /api/admin/basecode/sample/location
     */
    export namespace getSampleLocation {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringParamApiResult>;
    }

    /**
     * 获取取材部位
     * /api/admin/basecode/samplelocation/list
     */
    export namespace getSamplelocationList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPubaseCodeListPageResult>;
    }

    /**
     * 修改基础代码
     * /api/admin/basecode/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdatePubBaseCodeParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 质控评价
   */
  export namespace qualityControlEvaluationApi {
    /**
     * 抽查病例
     * /api/QualityControlEvaluation/SpotCheckCase
     */
    export namespace getSpotCheckCase {
      export class Params {
        /** 病理类型 */
        CaseType?: string;
        /** 亚专科 */
        SubProfessional?: string;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SpotCheckCaseCountDtoApiResult>;
    }

    /**
     * 评价病例列表
     * /api/QualityControlEvaluation/SpotCheckCaseListTask
     */
    export namespace getSpotCheckCaseListTask {
      export class Params {
        /** 病理类型 */
        CaseType?: string;
        /** 亚专科 */
        SubProfessional?: string;
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SpotCheckCaseDtoPageResult>;
    }

    /**
     * 批量修改质控
     * /api/QualityControlEvaluation/UpdateBatchTask
     */
    export namespace putUpdateBatchTask {
      export function request(
        params: { bodyParams: Array<defs.QualityControlEvaluationUpdateParam> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改质控
     * /api/QualityControlEvaluation/UpdateTask
     */
    export namespace putUpdateTask {
      export function request(
        params: { bodyParams: defs.QualityControlEvaluationUpdateParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 病理模板
   */
  export namespace rcpPathologyTemplateApi {
    /**
     * 新增
     * /api/RcpPathologyTemplate/Create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.RcpPathologyTemplate },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPathologyTemplateApiResult>;
    }

    /**
     * 删除
     * /api/RcpPathologyTemplate/Delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除模板文件
     * /api/RcpPathologyTemplate/DeleteTemplateFile
     */
    export namespace deleteDeleteTemplateFile {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 下载模板文件
     * /api/RcpPathologyTemplate/DownloadFileForPlatform
     */
    export namespace getDownloadFileForPlatform {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }

    /**
        * 获取模板文件List
入参为模板Code
        * /api/RcpPathologyTemplate/GetReportTemplateList
        */
    export namespace getGetReportTemplateList {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ObjectApiResult>;
    }

    /**
     * 模板是否重复
     * /api/RcpPathologyTemplate/PathologyTemplateIstExist
     */
    export namespace putPathologyTemplateIstExist {
      export function request(
        params: { bodyParams: defs.TemplateExistModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 渲染模板
     * /api/RcpPathologyTemplate/RenderPathologyReportTemplate
     */
    export namespace postRenderPathologyReportTemplate {
      export function request(
        params: { bodyParams: defs.RenderTemplatePostModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 分页查询分中心
     * /api/RcpPathologyTemplate/SearchForPlatform
     */
    export namespace getSearchForPlatform {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPathologyTemplateDtoPageResult>;
    }

    /**
     * 分页查询站点（医院）
     * /api/RcpPathologyTemplate/SearchForSite
     */
    export namespace getSearchForSite {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPathologyTemplateDtoPageResult>;
    }

    /**
     * 查询
     * /api/RcpPathologyTemplate/SearchSingle
     */
    export namespace getSearchSingle {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RcpPathologyTemplateApiResult>;
    }

    /**
     * 更新
     * /api/RcpPathologyTemplate/Update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.RcpPathologyTemplate },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 上传模板文件
     * /api/RcpPathologyTemplate/UploadFileForPlatform
     */
    export namespace postUploadFileForPlatform {
      export class Params {
        /** templateCode */
        templateCode?: string;
        /** templateFileCode */
        templateFileCode?: string;
      }

      export function request(
        params: { params: Params; bodyParams: object },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 获取病理模板Code
   */
  export namespace rcpPathologyTemplateCodeApi {
    /**
     * 获取病理模板Code
     * /api/RcpPathologyTemplateCode/SearchCode
     */
    export namespace getSearchCode {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }
  }

  /**
   * 已挂号列表
   */
  export namespace registrationmanagementApi {
    /**
     * 增
     * /api/Registrationmanagement
     */
    export namespace postRegistrationmanagement {
      export function request(
        params: { bodyParams: defs.RegistrationManagementReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 查
     * /api/Registrationmanagement
     */
    export namespace getRegistrationmanagement {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RegistrationManagementDtoPageResult>;
    }

    /**
     * 删
     * /api/Registrationmanagement
     */
    export namespace deleteRegistrationmanagement {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 远程诊断管理
   */
  export namespace remoteManagementApi {
    /**
     * 申请管理
     * /api/admin/remotemanagement/application/management/list
     */
    export namespace getApplicationManagementList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApplicationManagementDtoPageResult>;
    }

    /**
     * 申请管理拒绝
     * /api/admin/remotemanagement/application/management/operate
     */
    export namespace postApplicationManagementOperate {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 会诊管理
     * /api/admin/remotemanagement/consultation/management/list
     */
    export namespace getConsultationManagementList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminRemoteManagementDtoPageResult>;
    }

    /**
     * 会诊管理导出
     * /api/admin/remotemanagement/consultation/management/list/stream
     */
    export namespace getConsultationManagementListStream {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 预约管理
     * /api/admin/remotemanagement/frozen/list
     */
    export namespace getFrozenList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FrozenRemoteManagementDtoPageResult>;
    }

    /**
     * 打印授权-操作
     * /api/admin/remotemanagement/print
     */
    export namespace postPrint {
      export function request(
        params: { bodyParams: defs.OperateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 打印授权
     * /api/admin/remotemanagement/print/authorization/list
     */
    export namespace getPrintAuthorizationList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminRemoteManagementDtoPageResult>;
    }

    /**
     * 打印记录
     * /api/admin/remotemanagement/print/log
     */
    export namespace getPrintLog {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StrParamApiResult>;
    }

    /**
     * 获取站点
     * /api/admin/remotemanagement/site
     */
    export namespace getSite {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CommonDtoPageResult>;
    }

    /**
     * 获取状态
     * /api/admin/remotemanagement/status
     */
    export namespace getStatus {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StatusDtoPageResult>;
    }

    /**
     * 修改病例
     * /api/admin/remotemanagement/update/case
     */
    export namespace postUpdateCase {
      export function request(
        params: { bodyParams: defs.UpdateCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回确定-同意拒绝
     * /api/admin/remotemanagement/withdraw
     */
    export namespace postWithdraw {
      export function request(
        params: { bodyParams: defs.OperateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回确定
     * /api/admin/remotemanagement/withdrawal/management
     */
    export namespace getWithdrawalManagement {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminRemoteManagementDtoPageResult>;
    }
  }

  /**
   * 被退回
   */
  export namespace returnedApi {
    /**
     * 被退回列表
     * /api/user/returned/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }
  }

  /**
   * 系统配置
   */
  export namespace safeManagerApi {
    /**
     * 获取所有配置
     * /api/admin/safemanager/all
     */
    export namespace getAll {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseConfigPageResult>;
    }

    /**
     * 修改系统配置表
     * /api/admin/safemanager/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: Array<defs.ConfigReqModel> },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 病例公共类
   */
  export namespace siteCommonApi {
    /**
     * 附件修改状态
     * /api/site/common/annex/update
     */
    export namespace putAnnexUpdate {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取条形码
     * /api/site/common/barcode
     */
    export namespace getBarcode {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 创建病例
     * /api/site/common/case/create
     */
    export namespace postCaseCreate {
      export function request(
        params: { bodyParams: defs.CaseCreateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 创建待缴费病例
     * /api/site/common/case/pendingpayment
     */
    export namespace postCasePendingpayment {
      export function request(
        params: { bodyParams: defs.CaseCreateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 保存病例
     * /api/site/common/case/save
     */
    export namespace postCaseSave {
      export function request(
        params: { bodyParams: defs.CaseCreateReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 获取站点收费状态
     * /api/site/common/chargingstatus
     */
    export namespace getChargingstatus {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int32ApiResult>;
    }

    /**
     * 关联病例
     * /api/site/common/correlation
     */
    export namespace postCorrelation {
      export function request(
        params: { bodyParams: defs.CorrelationParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 附件删除
     * /api/site/common/delete/annex
     */
    export namespace deleteDeleteAnnex {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 切片删除
     * /api/site/common/delete/slice
     */
    export namespace deleteDeleteSlice {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 诊断逻辑是否提交给专家【这里需要判断是否分配员被禁用】
     * /api/site/common/diagnostic
     */
    export namespace getDiagnostic {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }

    /**
     * 获取站点下的专家
     * /api/site/common/expert/list
     */
    export namespace getExpertList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteExpertDtoPageResult>;
    }

    /**
     * 冰冻保存草稿
     * /api/site/common/frozen/draft
     */
    export namespace postFrozenDraft {
      export function request(
        params: { bodyParams: defs.FrozenCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseFrozenApiResult>;
    }

    /**
     * 冰冻修改状态
     * /api/site/common/frozen/status
     */
    export namespace putFrozenStatus {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 冰冻预约提交
     * /api/site/common/frozen/submit
     */
    export namespace postFrozenSubmit {
      export function request(
        params: { bodyParams: defs.FrozenCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseFrozenApiResult>;
    }

    /**
     * 修改冰冻
     * /api/site/common/frozen/update
     */
    export namespace putFrozenUpdate {
      export function request(
        params: { bodyParams: defs.UpdateFrozenParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回
     * /api/site/common/frozen/withdraw
     */
    export namespace postFrozenWithdraw {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * HIS保存病例
     * /api/site/common/his/case/save
     */
    export namespace postHisCaseSave {
      export function request(
        params: { bodyParams: defs.HISCreateCase },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 历史诊断记录列表
     * /api/site/common/historicaldiagnosticrecords
     */
    export namespace getHistoricaldiagnosticrecords {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseHistoricalDiagnosticRecordsDtoPageResult>;
    }

    /**
     * 医嘱创建病例
     * /api/site/common/medical/advice
     */
    export namespace postMedicalAdvice {
      export function request(
        params: { bodyParams: defs.OrderToCreateCaseModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 获取预约编号
     * /api/site/common/reservenumber
     */
    export namespace getReservenumber {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 修改不改状态
     * /api/site/common/save/case
     */
    export namespace putSaveCase {
      export function request(
        params: { bodyParams: defs.UpdateCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 保存草稿（退回，撤回）
     * /api/site/common/savedraft
     */
    export namespace putSavedraft {
      export function request(
        params: { bodyParams: defs.UpdateCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取站点名称
     * /api/site/common/sitename
     */
    export namespace getSitename {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 切片修改状态--已上传
     * /api/site/common/slice/update
     */
    export namespace putSliceUpdate {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 切片修改状态--上传中
     * /api/site/common/slice/uploading
     */
    export namespace putSliceUploading {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取状态
     * /api/site/common/status
     */
    export namespace getStatus {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StatusDtoPageResult>;
    }

    /**
     * 冰冻预约预约时间
     * /api/site/common/timeofappointment
     */
    export namespace getTimeofappointment {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DateTimeApiResult>;
    }

    /**
     * 修改备注
     * /api/site/common/update/remark
     */
    export namespace putUpdateRemark {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改病例 提交
     * /api/site/common/update/submit
     */
    export namespace putUpdateSubmit {
      export function request(
        params: { bodyParams: defs.UpdateCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改附件类型
     * /api/site/common/updateannextype
     */
    export namespace putUpdateannextype {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改染色类型
     * /api/site/common/updatedyeing
     */
    export namespace putUpdatedyeing {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回病例
     * /api/site/common/withdraw
     */
    export namespace postWithdraw {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 站点管理
   */
  export namespace siteManagerApi {
    /**
     * 完善站点
     * /api/admin/site/complete
     */
    export namespace postComplete {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: {
          params: Params;
          bodyParams: defs.CompleteSiteManagerReqModel;
        },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 创建站点
     * /api/admin/site/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.SiteManagerReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64ApiResult>;
    }

    /**
     * 删除
     * /api/admin/site/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取站点下拉
     * /api/admin/site/drop
     */
    export namespace getDrop {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteDropDtoPageResult>;
    }

    /**
     * 站点列表
     * /api/admin/site/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteDtoPageResult>;
    }

    /**
     * 渲染站点信息
     * /api/admin/site/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteInformationDtoApiResult>;
    }

    /**
     * 分中心下的专家
     * /api/admin/site/subcenter/expert
     */
    export namespace getSubcenterExpert {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminExpertPageResult>;
    }

    /**
     * 修改站点
     * /api/admin/site/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateSiteParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 站点短信服务
   */
  export namespace siteShortMessageApi {
    /**
     * 修改申请
     * /api/site/shortmessage/amend/application
     */
    export namespace putAmendApplication {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 提交病例
     * /api/site/shortmessage/case/submission
     */
    export namespace putCaseSubmission {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 报告打印申请
     * /api/site/shortmessage/report/refill/application
     */
    export namespace putReportRefillApplication {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 补打申请成功(管理员)
     * /api/site/shortmessage/report/refill/application/successful
     */
    export namespace putReportRefillApplicationSuccessful {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 撤回病例
     * /api/site/shortmessage/withdraw/case
     */
    export namespace putWithdrawCase {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * websocket控制器
   */
  export namespace socketApi {
    /**
     * 发送消息
     * /api/websocket/send/mesagge
     */
    export namespace postMesagge {
      export class Params {
        /** SendType */
        SendType?: defs.SendType;
        /** Data */
        Data?: string;
        /** TargetUserId */
        TargetUserId?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }
  }

  /**
   * 统计控制器
   */
  export namespace statisticsManagerApi {
    /**
     * 管理端诊断统计
     * /api/statisticsmanager/admin/list
     */
    export namespace getAdminList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 切片质量 */
        Quality?: number;
        /** 站点id */
        SiteId?: string;
        /** 关键字（会诊编号/名称） */
        Str?: string;
        /** 病例类型 */
        CaseType?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminDtoPageResult>;
    }

    /**
     * 管理端质控评价统计
     * /api/statisticsmanager/admin/quality/list
     */
    export namespace getAdminQualityList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 切片质量 */
        Quality?: number;
        /** 站点id */
        SiteId?: string;
        /** 关键字（会诊编号/名称） */
        Str?: string;
        /** 病例类型 */
        CaseType?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AdminQualityControlStatisticsDtoPageResult>;
    }

    /**
     * 管理端质控统计导出
     * /api/statisticsmanager/admin/quality/stream
     */
    export namespace getAdminQualityStream {
      export class Params {
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 切片质量 */
        Quality?: number;
        /** 站点id */
        SiteId?: string;
        /** 关键字（会诊编号/名称） */
        Str?: string;
        /** 病例类型 */
        CaseType?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 收费对账表
     * /api/statisticsmanager/admin/reconciliationcharge
     */
    export namespace getAdminReconciliationcharge {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 关键字搜索 */
        Str?: string;
        /** 站点Id */
        SiteId?: number;
        /** 病理类型 */
        CaseType?: string;
        /** 初审专家Id */
        ExpertId?: number;
        /** 复审Id */
        ReviewExpertId?: number;
        /** 收费项目 */
        CheckItems?: string;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 排序 */
        OrderByType?: string;
        /** 查询时间类型  0为不查询 1为提交时间 2为诊断时间 */
        TimeType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ReconciliationChargeDtoPageResult>;
    }

    /**
     * 对账导出
     * /api/statisticsmanager/admin/reconciliationcharge/stream
     */
    export namespace getAdminReconciliationchargeStream {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 关键字搜索 */
        Str?: string;
        /** 站点Id */
        SiteId?: number;
        /** 病理类型 */
        CaseType?: string;
        /** 初审专家Id */
        ExpertId?: number;
        /** 复审Id */
        ReviewExpertId?: number;
        /** 收费项目 */
        CheckItems?: string;
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 排序 */
        OrderByType?: string;
        /** 查询时间类型  0为不查询 1为提交时间 2为诊断时间 */
        TimeType?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 管理端诊断统计导出
     * /api/statisticsmanager/admin/stream
     */
    export namespace getAdminStream {
      export class Params {
        /** 开始时间 */
        SubscribeTime?: string;
        /** 结束时间 */
        CompleteTime?: string;
        /** 切片质量 */
        Quality?: number;
        /** 站点id */
        SiteId?: string;
        /** 关键字（会诊编号/名称） */
        Str?: string;
        /** 病例类型 */
        CaseType?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 分诊端统计
     * /api/statisticsmanager/distribution/list
     */
    export namespace getDistributionList {
      export class Params {
        /** 病例类型 */
        CaseType?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 最大id */
        MaxId?: number;
        /** 状态 */
        Status?: number;
        /** 搜索字符串 */
        Str?: string;
        /** 诊断开始时间 */
        SubscribeTime?: string;
        /** 诊断完成时间 */
        CompleteTime?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DistributionDtoPageResult>;
    }

    /**
     * 获取专家年业绩
     * /api/statisticsmanager/expert/list
     */
    export namespace getExpertList {
      export class Params {
        /** 时间 */
        Time?: string;
        /** 诊断专家 */
        Name?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ConsultationExpertResultDtoPageResult>;
    }

    /**
     * 会诊统计
     * /api/statisticsmanager/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 最大id */
        MaxId?: number;
        /** 状态 */
        CaseType?: string;
        /** 状态 */
        Status?: number;
        /** 搜索字符串 */
        Str?: string;
        /** 诊断开始时间 */
        SubscribeTime?: string;
        /** 诊断完成时间 */
        CompleteTime?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StatisticsShowDtoPageResult>;
    }

    /**
     * 会诊统计返回流
     * /api/statisticsmanager/list/stream
     */
    export namespace getListStream {
      export class Params {
        /** 状态 */
        CaseType?: string;
        /** 状态 */
        Status?: number;
        /** 搜索字符串 */
        Str?: string;
        /** 诊断开始时间 */
        SubscribeTime?: string;
        /** 诊断完成时间 */
        CompleteTime?: string;
        /** 排序 */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }
  }

  /**
   * 分中心管理
   */
  export namespace subCentersApi {
    /**
     * 创建分中心
     * /api/admin/subcenter/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.CreateSubCenterReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/admin/subcenter/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 分中心基本信息
     * /api/admin/subcenter/information
     */
    export namespace getInformation {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SubCenterInformationDtoApiResult>;
    }

    /**
     * 分中心列表
     * /api/admin/subcenter/list
     */
    export namespace getList {
      export class Params {
        /** 用户名称 */
        UserName?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SubCenterListDtoPageResult>;
    }

    /**
     * 分中心渲染
     * /api/admin/subcenter/rendering
     */
    export namespace getRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.PlatformRenderingDtoApiResult>;
    }

    /**
     * 修改分中心
     * /api/admin/subcenter/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.SubCenterListUpdateParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 建议管理
   */
  export namespace suggestApi {
    /**
     * 删除
     * /api/admin/suggest/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 建议详情
     * /api/admin/suggest/infor
     */
    export namespace getInfor {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.RecommendationDetailsDtoApiResult>;
    }

    /**
     * 获取列表
     * /api/admin/suggest/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SuggestDtoPageResult>;
    }
  }

  /**
   * 系统日志
   */
  export namespace sysAdminLogApi {
    /**
     * 添加记录
     * /api/SysAdminLog/Add
     */
    export namespace postAdd {
      export function request(
        params: { bodyParams: defs.LogParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 集合
     * /api/SysAdminLog/List
     */
    export namespace getList {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.LogDtoPageResult>;
    }
  }

  /**
   * 智慧云平台中心消息接口
   */
  export namespace sysCentralMessageLogApi {
    /**
     * 删除
     * /api/SysCentralMessageLog/Delete
     */
    export namespace deleteDelete {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }

    /**
     * 分页查询
     * /api/SysCentralMessageLog/Search
     */
    export namespace getSearch {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysCentralMessageLogPageResult>;
    }
  }

  /**
   * 系统更新记录
   */
  export namespace sysUpdateLogApi {
    /**
     * 获取最新消息id
     * /api/SysUpdateLog/MessageIdAsync
     */
    export namespace getMessageIdAsync {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int64NullableApiResult>;
    }

    /**
     * 获取系统更新记录
     * /api/SysUpdateLog/SysUpdateLogListAsync
     */
    export namespace getSysUpdateLogListAsync {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUpdateLogPageResult>;
    }

    /**
     * 添加系统更新记录
     * /api/SysUpdateLog/SystemNotificationMessages
     */
    export namespace postSystemNotificationMessages {
      export function request(
        params: { bodyParams: defs.SysUpdateLog },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 用户日志
   */
  export namespace sysUserLogApi {
    /**
     * 新增
     * /api/SysUserLog/Create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.SysUserLog },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogApiResult>;
    }

    /**
     * 新增质控评价日志
     * /api/SysUserLog/CreateCaseQualityAssessmentLog
     */
    export namespace postCreateCaseQualityAssessmentLog {
      export function request(
        params: { bodyParams: defs.CaseQualityAssessmentLogDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogApiResult>;
    }

    /**
     * 删除
     * /api/SysUserLog/Delete
     */
    export namespace deleteDelete {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 查询质控评价日志
     * /api/SysUserLog/GetCaseQualityAssessmentLog
     */
    export namespace getGetCaseQualityAssessmentLog {
      export class Params {
        /** code */
        code?: string;
        /** pathologyNumber */
        pathologyNumber?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogDtoListApiResult>;
    }

    /**
     * 分页查询
     * /api/SysUserLog/Search
     */
    export namespace getSearch {
      export class Params {
        /** 开始时间 */
        StartingTime?: string;
        /** 结束时间 */
        EndTime?: string;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogDtoPageResult>;
    }

    /**
     * 根据Code以及病例ID查询
     * /api/SysUserLog/SearchByType
     */
    export namespace getSearchByType {
      export class Params {
        /** code */
        code?: string;
        /** pathologyNumber */
        pathologyNumber?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogDtoListApiResult>;
    }

    /**
     * 查询
     * /api/SysUserLog/SearchSingle
     */
    export namespace getSearchSingle {
      export class Params {
        /** id */
        id?: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SysUserLogApiResult>;
    }

    /**
     * 更新
     * /api/SysUserLog/Update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.SysUserLog },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 用户系统消息关联表
   */
  export namespace sysUserMessageApi {
    /**
     * 添加一条记录
     * /api/SysUserMessage
     */
    export namespace postSysUserMessage {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }

    /**
     * 是否收到过消息
     * /api/SysUserMessage
     */
    export namespace getSysUserMessage {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.BooleanApiResult>;
    }
  }

  /**
   * 管理员模拟板中心
   */
  export namespace templateCenterApi {
    /**
     * 新增词汇
     * /api/admin/templatecenter/add
     */
    export namespace postAdd {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTextDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 管理员删除诊断模板
     * /api/admin/templatecenter/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 管理员诊断词汇列表
     * /api/admin/templatecenter/list
     */
    export namespace getList {
      export class Params {
        /** 词汇类型Code */
        typeCode?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularyTextDtoPageResult>;
    }

    /**
     * 渲染获取实体
     * /api/admin/templatecenter/model
     */
    export namespace getModel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.DiagnosticVocabularyTextDtoApiResult>;
    }

    /**
     * 词汇编辑
     * /api/admin/templatecenter/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.DiagnosticVocabularyTextDto },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 待诊断
   */
  export namespace toBeDiagnosedApi {
    /**
     * 待诊断病例
     * /api/user/tobediagnosed/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }
  }

  /**
   * 分诊员功能
   */
  export namespace triageFunctionApi {
    /**
     * 同意撤回
     * /api/distributor/function/agree/withdraw
     */
    export namespace putAgreeWithdraw {
      export function request(
        params: { bodyParams: defs.DistributorWithdrawReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 批量分配
     * /api/distributor/function/bulk/allocation
     */
    export namespace putBulkAllocation {
      export function request(
        params: { bodyParams: defs.BulkAllocationReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 病例分配
     * /api/distributor/function/distribute
     */
    export namespace putDistribute {
      export function request(
        params: { bodyParams: defs.AssignExpertReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 忽略
     * /api/distributor/function/neglect
     */
    export namespace postNeglect {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 拒绝撤回
     * /api/distributor/function/refusal/withdraw
     */
    export namespace putRefusalWithdraw {
      export function request(
        params: { bodyParams: defs.DistributorWithdrawReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取状态
     * /api/distributor/function/status
     */
    export namespace getStatus {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StatusDtoPageResult>;
    }

    /**
     * 查看撤回理由
     * /api/distributor/function/withdrawal
     */
    export namespace getWithdrawal {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }
  }

  /**
   * 分诊管理所有列表
   */
  export namespace triageManagementApi {
    /**
     * 获取下拉列表 DISTRIBUTOR_SITE,CASE_TYPE
     * /api/distributor/triaged/drop
     */
    export namespace getDrop {
      export class Params {
        /** code */
        code?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<any>;
    }

    /**
     * 待分诊列表
     * /api/distributor/triaged/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ToBeAssignedDtoPageResult>;
    }

    /**
     * 分配员被退回
     * /api/distributor/triaged/returned/list
     */
    export namespace getReturnedList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AssignedPageResult>;
    }

    /**
     * 已分诊
     * /api/distributor/triaged/triage/list
     */
    export namespace getTriageList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AssignedPageResult>;
    }

    /**
     * 分配员待撤回
     * /api/distributor/triaged/withdraw/list
     */
    export namespace getWithdrawList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AssignedPageResult>;
    }
  }

  /**
   * 管理员分配员通用接口
   */
  export namespace universalApi {
    /**
     * 重新分配
     * /api/universal/redistribute
     */
    export namespace putRedistribute {
      export function request(
        params: { bodyParams: defs.Redistribute },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }

  /**
   * 未提交病例
   */
  export namespace unsubmittedApi {
    /**
     * 未提交病例
     * /api/user/unsubmitted/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }
  }

  /**
   * 用户公共模块
   */
  export namespace userCommonApi {
    /**
     * 账号是否存在
     * /api/common/accountexist
     */
    export namespace putAccountexist {
      export function request(
        params: { bodyParams: defs.AccountExistModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 是否存在
     * /api/common/anhuihisexist
     */
    export namespace getAnhuihisexist {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 获取附件
     * /api/common/annex/list
     */
    export namespace getAnnexList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.AnnexDtoPageResult>;
    }

    /**
     * 提交病例判断是否有分配员
     * /api/common/bycase
     */
    export namespace putBycase {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除病例
     * /api/common/case/delete
     */
    export namespace deleteCaseDelete {
      export function request(
        params: { bodyParams: defs.DeleteCaseReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 病例渲染
     * /api/common/case/pendering
     */
    export namespace getCasePendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseRenderingDtoApiResult>;
    }

    /**
     * 下拉列表通用接口
     * /api/common/drop/add
     */
    export namespace getDropAdd {
      export class Params {
        /** code */
        code?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringObjectDictionaryApiResult>;
    }

    /**
     * 邮箱是否存在
     * /api/common/emailexist
     */
    export namespace putEmailexist {
      export function request(
        params: { bodyParams: defs.AccountExistModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 冰冻数据渲染
     * /api/common/frozen/rendering
     */
    export namespace getFrozenRendering {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.FrozenInformationDtoApiResult>;
    }

    /**
     * 获取与his关联的病例
     * /api/common/hismodel
     */
    export namespace getHismodel {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.HisCaseinformationReqModelApiResult>;
    }

    /**
     * 获取一条医嘱
     * /api/common/medicaladvice
     */
    export namespace getMedicaladvice {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ExpertDoctorsAdviceDtoApiResult>;
    }

    /**
     * 病例留言
     * /api/common/message
     */
    export namespace getMessage {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseMessageDtoPageResult>;
    }

    /**
     * 查看退回原因
     * /api/common/reason/list
     */
    export namespace getReasonList {
      export class Params {
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseReturnedDtoPageResult>;
    }

    /**
     * 获取报告单
     * /api/common/report
     */
    export namespace getReport {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }

    /**
     * 获取报告单模板
     * /api/common/report/template
     */
    export namespace getReportTemplate {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StrParamListApiResult>;
    }

    /**
     * 专家退回判断是否有分配员
     * /api/common/returnbyexpert
     */
    export namespace putReturnbyexpert {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 留言发送
     * /api/common/send
     */
    export namespace postSend {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 电话号码是否存在
     * /api/common/siteexist
     */
    export namespace putSiteexist {
      export function request(
        params: { bodyParams: defs.AccountExistModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 切片集合
     * /api/common/slice/list
     */
    export namespace getSliceList {
      export class Params {
        /** 切片类型 1为文件夹类型 */
        SliceType?: number;
        /** id */
        Id: number;
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseSliceDtoPageResult>;
    }

    /**
     * 获取切片状态
     * /api/common/slice/status
     */
    export namespace getSliceStatus {
      export class Params {
        /** 字符串 */
        Str?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.Int32ApiResult>;
    }

    /**
     * 获取后缀集合
     * /api/common/suffix
     */
    export namespace getSuffix {
      export function request(
        params: {},
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SuffixListApiResult>;
    }

    /**
     * 新增意见
     * /api/common/suggest
     */
    export namespace postSuggest {
      export function request(
        params: { bodyParams: defs.CreateSuggesReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 查看撤回理由
     * /api/common/withdrawal
     */
    export namespace getWithdrawal {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.StringApiResult>;
    }
  }

  /**
   * 站点管理
   */
  export namespace userManagementApi {
    /**
     * 创建站点用户
     * /api/user/management/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.SiteUserReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除用户
     * /api/user/management/delete
     */
    export namespace putDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 站点用户列表
     * /api/user/management/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteUserDtoPageResult>;
    }

    /**
     * 重置密码
     * /api/user/management/reset
     */
    export namespace putReset {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改站点用户
     * /api/user/management/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.UpdateSiteUserReqModel },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 修改密码
     * /api/user/management/update/pwd
     */
    export namespace putUpdatePwd {
      export function request(
        params: { bodyParams: defs.CommLoginParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 用户病例
     * /api/user/management/user/case
     */
    export namespace getUserCase {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.UserCaseDtoPageResult>;
    }

    /**
     * 获取用户基本信息
     * /api/user/management/user/infor
     */
    export namespace getUserInfor {
      export class Params {
        /** id */
        Id: number;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.SiteUserInforDtoApiResult>;
    }
  }

  /**
   * 已撤回
   */
  export namespace withdrawnApi {
    /**
     * 已撤回列表
     * /api/user/withdrawn/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.CaseDtoPageResult>;
    }
  }

  /**
   * 工作单位（医院数据）
   */
  export namespace workUnitApi {
    /**
     * 添加
     * /api/workunit/create
     */
    export namespace postCreate {
      export function request(
        params: { bodyParams: defs.StrParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 删除
     * /api/workunit/delete
     */
    export namespace deleteDelete {
      export function request(
        params: { bodyParams: defs.IdParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }

    /**
     * 医院数据列表
     * /api/workunit/list
     */
    export namespace getList {
      export class Params {
        /** 当前页 */
        PageIndex: number;
        /** 每页总条数 */
        PageSize: number;
        /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
        AdvancedSearch?: string;
        /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
        OrderByType?: string;
      }

      export function request(
        params: { params: Params },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.WorkUnitDtoPageResult>;
    }

    /**
     * 修改
     * /api/workunit/update
     */
    export namespace putUpdate {
      export function request(
        params: { bodyParams: defs.StringParam },
        axiosConfig?: AxiosRequestConfig,
      ): Promise<defs.ApiResult>;
    }
  }
}
