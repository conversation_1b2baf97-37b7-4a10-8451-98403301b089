/**
 * @description 获取切片标注文件的string
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<string> {
  return RequestHelper.request(
    {
      url: '/api/PathologicalSectionMark',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
