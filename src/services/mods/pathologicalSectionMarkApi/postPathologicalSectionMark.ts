/**
 * @description 上传切片标注
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PathologicalSectionMarkDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<boolean> {
  return RequestHelper.request(
    {
      url: '/api/PathologicalSectionMark',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
