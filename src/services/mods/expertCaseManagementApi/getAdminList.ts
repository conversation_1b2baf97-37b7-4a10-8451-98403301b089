/**
 * @description 获取管理员列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PageParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertAdminDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/admin/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
