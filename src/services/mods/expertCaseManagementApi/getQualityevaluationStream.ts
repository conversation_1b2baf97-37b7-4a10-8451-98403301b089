/**
 * @description 专家端质控评价导出
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 病例类型 */
  CaseType?: string;
  /** 开始时间 */
  SubscribeTime?: string;
  /** 结束时间 */
  CompleteTime?: string;
  /** 当前页 */
  PageIndex: number;
  /** 每页总条数 */
  PageSize: number;
  /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
  AdvancedSearch?: string;
  /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
  OrderByType?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/expert/qualityevaluation/stream',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
