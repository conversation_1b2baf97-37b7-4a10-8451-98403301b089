/**
 * @description 建议详情
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RecommendationDetailsDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/suggest/infor',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
