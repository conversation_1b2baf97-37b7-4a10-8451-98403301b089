/**
 * @description 下拉列表 SUB_SPECIALTY/亚专科  CASE_TYPE/病理类型
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** code */
  code?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringObjectDictionaryApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/drop',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
