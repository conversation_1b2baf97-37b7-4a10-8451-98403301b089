/**
 * @description 撤回
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DataFlowWithdrawReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/withdraw',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
