/**
 * @description 数据流转
 */
import * as getAnnex from './getAnnex';
import * as putBatchAdd from './putBatchAdd';
import * as getBatchedit from './getBatchedit';
import * as putCreate from './putCreate';
import * as getDrop from './getDrop';
import * as getList from './getList';
import * as getLog from './getLog';
import * as getRender from './getRender';
import * as getRendering from './getRendering';
import * as getReport from './getReport';
import * as getSlice from './getSlice';
import * as putWithdraw from './putWithdraw';

export {
  getAnnex,
  putBatchAdd,
  getBatchedit,
  putCreate,
  getDrop,
  getList,
  getLog,
  getRender,
  getRendering,
  getReport,
  getSlice,
  putWithdraw,
};
