/**
 * @description 批量流转
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.DataFlowReqModel> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/batch/add',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
