/**
 * @description 数据扭转报告单
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 项目 */
  Project: number;
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DataFlowReportDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/report',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
