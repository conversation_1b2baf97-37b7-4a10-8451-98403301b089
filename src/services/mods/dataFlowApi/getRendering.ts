/**
 * @description 病例渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 项目 */
  Project: number;
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseRenderingDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
