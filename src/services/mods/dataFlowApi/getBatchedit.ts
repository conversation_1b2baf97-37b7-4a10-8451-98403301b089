/**
 * @description 批量渲染编辑
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DataFlowEditDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/batchedit',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
