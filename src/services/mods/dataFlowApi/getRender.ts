/**
 * @description 渲染编辑
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DataFlowEditDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/render',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
