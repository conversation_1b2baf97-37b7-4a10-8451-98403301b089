/**
 * @description 科研已流转查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 项目/1科研 */
  Project: number;
  /** 亚专科 */
  SampleLocationCode?: string;
  /** 名称 */
  Name?: string;
  /** 最大id（上一页取第一条） */
  MaxId?: number;
  /** 每页总条数 */
  PageSize: number;
  /** 最小id(取当前页最后一条，下一页时传入) */
  MinId?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DataFlowDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/dataflow/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
