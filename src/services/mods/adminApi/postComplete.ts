/**
 * @description 完善管理员
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params; bodyParams: defs.CompleteAdminReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/complete',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
