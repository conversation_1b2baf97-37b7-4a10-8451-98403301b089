/**
 * @description 获取管理员信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.AdminUpdateDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
