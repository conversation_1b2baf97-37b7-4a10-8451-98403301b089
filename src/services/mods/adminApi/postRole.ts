/**
 * @description 添加授权中心角色
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** Id */
  Id?: number;
  /** 名称 */
  Name?: string;
  /** code */
  Code?: string;
  /** 备注 */
  Remark?: string;
  /** 租户Id */
  TenantId?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/role',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
