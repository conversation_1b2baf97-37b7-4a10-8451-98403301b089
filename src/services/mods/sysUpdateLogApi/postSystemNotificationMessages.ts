/**
 * @description 添加系统更新记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SysUpdateLog },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUpdateLog/SystemNotificationMessages',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
