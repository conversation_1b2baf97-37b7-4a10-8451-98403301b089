/**
 * @description 获取系统更新记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUpdateLogPageResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUpdateLog/SysUpdateLogListAsync',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
