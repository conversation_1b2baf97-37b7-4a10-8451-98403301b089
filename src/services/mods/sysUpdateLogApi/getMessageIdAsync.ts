/**
 * @description 获取最新消息id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64NullableApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUpdateLog/MessageIdAsync',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
