/**
 * @description 提交打印申请
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/user/diagnosed/create/history',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
