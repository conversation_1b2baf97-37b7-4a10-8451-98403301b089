/**
 * @description 获取一条数据
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.OtherChargesDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/othercharge/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
