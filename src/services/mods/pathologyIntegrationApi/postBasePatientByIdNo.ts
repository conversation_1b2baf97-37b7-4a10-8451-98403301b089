/**
 * @description 根据身份证号获取院内档案
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.BasePatientByIdNoReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.BasePatientByIdNoResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/BasePatientByIdNo',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
