/**
 * @description 获取排班
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.QueryRegSourceReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.QueryRegSourceResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/QueryRegSource',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
