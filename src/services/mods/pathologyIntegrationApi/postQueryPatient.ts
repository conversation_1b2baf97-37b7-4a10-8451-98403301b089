/**
 * @description 登记号/卡号查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.QueryPatientReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.QueryPatientResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/QueryPatient',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
