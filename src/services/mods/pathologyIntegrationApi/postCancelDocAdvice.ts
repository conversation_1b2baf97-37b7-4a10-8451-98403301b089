/**
 * @description 撤销医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CancelDocAdviceReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CancelDocAdviceResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/CancelDocAdvice',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
