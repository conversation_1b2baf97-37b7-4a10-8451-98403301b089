/**
 * @description 取消挂号
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CancelRegisterReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CancelRegisterResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/CancelRegister',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
