/**
 * @description 建档
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CreatePatientCardReqDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CreatePatientCardResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/CreatePatientCard',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
