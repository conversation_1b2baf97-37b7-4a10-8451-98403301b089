/**
 * @description 确认挂号
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RegisteredDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RegisterNoticeResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/RegisterNotice',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
