/**
 * @description 外包对接控制器
 */
import * as postAddDocAdvice from './postAddDocAdvice';
import * as postBasePatientByIdNo from './postBasePatientByIdNo';
import * as postCancelDocAdvice from './postCancelDocAdvice';
import * as postCancelRegister from './postCancelRegister';
import * as postCreatePatientCard from './postCreatePatientCard';
import * as postQueryPatient from './postQueryPatient';
import * as postQueryRegSource from './postQueryRegSource';
import * as postRegisterNotice from './postRegisterNotice';

export {
  postAddDocAdvice,
  postBasePatientByIdNo,
  postCancelDocAdvice,
  postCancelRegister,
  postCreatePatientCard,
  postQueryPatient,
  postQueryRegSource,
  postRegisterNotice,
};
