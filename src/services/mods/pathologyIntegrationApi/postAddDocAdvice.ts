/**
 * @description 插入诊断、插入医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.AddCaseDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.AddDocAdviceResDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/PathologyIntegration/AddDocAdvice',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
