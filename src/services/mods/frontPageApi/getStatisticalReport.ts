/**
 * @description 统计报表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.InspectionItemsDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/front/page/statistical/report',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
