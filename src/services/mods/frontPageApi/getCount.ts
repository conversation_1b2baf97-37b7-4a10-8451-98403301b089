/**
 * @description 获取首页条数
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 病例类型 */
  CaseType?: defs.CaseTypeShowEnum;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringInt32DictionaryApiResult> {
  return RequestHelper.request(
    {
      url: '/api/front/page/count',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
