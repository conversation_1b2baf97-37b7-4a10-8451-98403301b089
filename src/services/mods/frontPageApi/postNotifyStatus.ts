/**
 * @description 修改消息已读或已评论
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/front/page/notify/status',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
