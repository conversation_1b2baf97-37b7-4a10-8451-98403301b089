/**
 * @description 补打申请成功(管理员)
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/shortmessage/report/refill/application/successful',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
