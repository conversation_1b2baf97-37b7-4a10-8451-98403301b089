/**
 * @description 修改申请
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/shortmessage/amend/application',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
