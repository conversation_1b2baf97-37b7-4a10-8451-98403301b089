/**
 * @description 报告打印申请
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/shortmessage/report/refill/application',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
