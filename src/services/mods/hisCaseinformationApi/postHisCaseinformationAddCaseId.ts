/**
 * @description 添加待缴费记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.AddCaseIdReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/HisCaseinformation/AddCaseId',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
