/**
 * @description 增 [提交病例后请求]
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.HisCaseinformationReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/HisCaseinformation',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
