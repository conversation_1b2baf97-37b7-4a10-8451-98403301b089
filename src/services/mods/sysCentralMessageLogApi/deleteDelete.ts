/**
 * @description 删除
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.BooleanApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysCentralMessageLog/Delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
