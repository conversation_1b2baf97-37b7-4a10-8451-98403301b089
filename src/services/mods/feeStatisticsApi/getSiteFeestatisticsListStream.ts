/**
 * @description 病例收费导出
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 开始时间 */
  StartingTime: string;
  /** 结束时间 */
  EndTime: string;
  /** 站点id */
  SiteId: number;
  /** 病理号 */
  PathologyNumber?: string;
  /** 名称 */
  Name?: string;
  /** 会诊专家 */
  ExpertId?: number;
  /** 复核专家 */
  ReviewExpertId?: number;
  /** 缴费状态 */
  CaseChargingStatus?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/site/feestatistics/list/stream',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
