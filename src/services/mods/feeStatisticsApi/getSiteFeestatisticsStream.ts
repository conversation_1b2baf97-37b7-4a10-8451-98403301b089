/**
 * @description 病例收费导出
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 分中心id */
  SubCenterId?: number;
  /** 站点id */
  SiteId?: number;
  /** 缴费流水号 */
  PaymentOrderNumber?: string;
  /** 缴费状态 */
  CaseChargingStatus?: number;
  /** 开始时间 */
  StartingTime?: string;
  /** 结束时间 */
  EndTime?: string;
  /** AdvancedSearch */
  AdvancedSearch?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/site/feestatistics/stream',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
