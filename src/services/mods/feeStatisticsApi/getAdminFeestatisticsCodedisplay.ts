/**
 * @description 是否开启二维码 1开启 其他值不开启 在sys_qrcode配置
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int32ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/feestatistics/codedisplay',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
