/**
 * @description 保存草稿（退回，撤回）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateCaseReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/savedraft',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
