/**
 * @description 冰冻保存草稿
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.FrozenCaseReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseFrozenApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/frozen/draft',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
