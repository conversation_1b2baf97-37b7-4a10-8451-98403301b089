/**
 * @description 冰冻预约预约时间
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DateTimeApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/timeofappointment',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
