/**
 * @description 切片修改状态--已上传
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StrParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/slice/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
