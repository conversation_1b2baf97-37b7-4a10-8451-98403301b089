/**
 * @description 关联病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CorrelationParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/correlation',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
