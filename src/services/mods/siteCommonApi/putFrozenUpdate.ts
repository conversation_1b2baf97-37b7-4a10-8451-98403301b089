/**
 * @description 修改冰冻
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateFrozenParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/frozen/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
