/**
 * @description 病例公共类
 */
import * as putAnnexUpdate from './putAnnexUpdate';
import * as getBarcode from './getBarcode';
import * as postCaseCreate from './postCaseCreate';
import * as postCasePendingpayment from './postCasePendingpayment';
import * as postCaseSave from './postCaseSave';
import * as getChargingstatus from './getChargingstatus';
import * as postCorrelation from './postCorrelation';
import * as deleteDeleteAnnex from './deleteDeleteAnnex';
import * as deleteDeleteSlice from './deleteDeleteSlice';
import * as getDiagnostic from './getDiagnostic';
import * as getExpertList from './getExpertList';
import * as postFrozenDraft from './postFrozenDraft';
import * as putFrozenStatus from './putFrozenStatus';
import * as postFrozenSubmit from './postFrozenSubmit';
import * as putFrozenUpdate from './putFrozenUpdate';
import * as postFrozenWithdraw from './postFrozenWithdraw';
import * as postHisCaseSave from './postHisCaseSave';
import * as getHistoricaldiagnosticrecords from './getHistoricaldiagnosticrecords';
import * as postMedicalAdvice from './postMedicalAdvice';
import * as getReservenumber from './getReservenumber';
import * as putSaveCase from './putSaveCase';
import * as putSavedraft from './putSavedraft';
import * as getSitename from './getSitename';
import * as putSliceUpdate from './putSliceUpdate';
import * as putSliceUploading from './putSliceUploading';
import * as getStatus from './getStatus';
import * as getTimeofappointment from './getTimeofappointment';
import * as putUpdateRemark from './putUpdateRemark';
import * as putUpdateSubmit from './putUpdateSubmit';
import * as putUpdateannextype from './putUpdateannextype';
import * as putUpdatedyeing from './putUpdatedyeing';
import * as postWithdraw from './postWithdraw';

export {
  putAnnexUpdate,
  getBarcode,
  postCaseCreate,
  postCasePendingpayment,
  postCaseSave,
  getChargingstatus,
  postCorrelation,
  deleteDeleteAnnex,
  deleteDeleteSlice,
  getDiagnostic,
  getExpertList,
  postFrozenDraft,
  putFrozenStatus,
  postFrozenSubmit,
  putFrozenUpdate,
  postFrozenWithdraw,
  postHisCaseSave,
  getHistoricaldiagnosticrecords,
  postMedicalAdvice,
  getReservenumber,
  putSaveCase,
  putSavedraft,
  getSitename,
  putSliceUpdate,
  putSliceUploading,
  getStatus,
  getTimeofappointment,
  putUpdateRemark,
  putUpdateSubmit,
  putUpdateannextype,
  putUpdatedyeing,
  postWithdraw,
};
