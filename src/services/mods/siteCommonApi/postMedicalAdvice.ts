/**
 * @description 医嘱创建病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OrderToCreateCaseModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/medical/advice',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
