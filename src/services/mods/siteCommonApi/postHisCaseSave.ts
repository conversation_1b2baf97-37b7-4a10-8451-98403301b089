/**
 * @description HIS保存病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.HISCreateCase },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/his/case/save',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
