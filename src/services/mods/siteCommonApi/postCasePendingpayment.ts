/**
 * @description 创建待缴费病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CaseCreateReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/case/pendingpayment',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
