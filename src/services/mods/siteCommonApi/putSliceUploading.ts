/**
 * @description 切片修改状态--上传中
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StrParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/site/common/slice/uploading',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
