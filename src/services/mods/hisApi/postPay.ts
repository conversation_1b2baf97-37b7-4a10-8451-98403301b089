/**
 * @description 缴费完成
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PaymentCompletedReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/his/pay',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
