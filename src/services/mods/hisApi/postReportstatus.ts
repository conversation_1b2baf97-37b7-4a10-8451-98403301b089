/**
 * @description 获取报告单状态
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.AnhuiReportStatusReModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/his/reportstatus',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
