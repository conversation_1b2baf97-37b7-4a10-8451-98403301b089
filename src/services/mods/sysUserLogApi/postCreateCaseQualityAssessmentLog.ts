/**
 * @description 新增质控评价日志
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CaseQualityAssessmentLogDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUserLogApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserLog/CreateCaseQualityAssessmentLog',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
