/**
 * @description 查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUserLogApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserLog/SearchSingle',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
