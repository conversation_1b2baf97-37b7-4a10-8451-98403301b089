/**
 * @description 用户日志
 */
import * as postCreate from './postCreate';
import * as postCreateCaseQualityAssessmentLog from './postCreateCaseQualityAssessmentLog';
import * as deleteDelete from './deleteDelete';
import * as getGetCaseQualityAssessmentLog from './getGetCaseQualityAssessmentLog';
import * as getSearch from './getSearch';
import * as getSearchByType from './getSearchByType';
import * as getSearchSingle from './getSearchSingle';
import * as putUpdate from './putUpdate';

export {
  postCreate,
  postCreateCaseQualityAssessmentLog,
  deleteDelete,
  getGetCaseQualityAssessmentLog,
  getSearch,
  getSearchByType,
  getSearchSingle,
  putUpdate,
};
