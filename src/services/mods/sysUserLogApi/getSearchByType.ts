/**
 * @description 根据Code以及病例ID查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** code */
  code?: string;
  /** pathologyNumber */
  pathologyNumber?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUserLogDtoListApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserLog/SearchByType',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
