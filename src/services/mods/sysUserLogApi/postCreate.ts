/**
 * @description 新增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SysUserLog },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUserLogApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserLog/Create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
