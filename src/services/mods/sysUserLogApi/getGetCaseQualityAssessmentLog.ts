/**
 * @description 查询质控评价日志
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** code */
  code?: string;
  /** pathologyNumber */
  pathologyNumber?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysUserLogDtoListApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserLog/GetCaseQualityAssessmentLog',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
