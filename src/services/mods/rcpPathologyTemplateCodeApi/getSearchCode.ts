/**
 * @description 获取病理模板Code
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplateCode/SearchCode',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
