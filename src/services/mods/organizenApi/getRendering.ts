/**
 * @description 渲染机构
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SysOrganizeApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/organizen/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
