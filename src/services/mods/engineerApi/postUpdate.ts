/**
 * @description 修改工程师
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.EngineerReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/engineer/update',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
