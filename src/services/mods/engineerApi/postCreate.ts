/**
 * @description 创建工程师
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.EngineerReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/engineer/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
