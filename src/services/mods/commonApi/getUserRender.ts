/**
 * @description 授权中心用户渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.GetUserResponseDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/common/user/render',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
