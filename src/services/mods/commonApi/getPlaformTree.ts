/**
 * @description 分中心树
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.PlatformTreeDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/common/plaform/tree',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
