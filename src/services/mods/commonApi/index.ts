/**
 * @description 管理员公共模块
 */
import * as deleteDelete from './deleteDelete';
import * as putDisable from './putDisable';
import * as putDormal from './putDormal';
import * as getDropAdd from './getDropAdd';
import * as getPlaformTree from './getPlaformTree';
import * as putReset from './putReset';
import * as postTenantAdd from './postTenantAdd';
import * as getTenantList from './getTenantList';
import * as putUpdatePwd from './putUpdatePwd';
import * as getUserRender from './getUserRender';

export {
  deleteDelete,
  putDisable,
  putDormal,
  getDropAdd,
  getPlaformTree,
  putReset,
  postTenantAdd,
  getTenantList,
  putUpdatePwd,
  getUserRender,
};
