/**
 * @description 添加租户权限
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.AddTenantUserRelationModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/common/tenant/add',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
