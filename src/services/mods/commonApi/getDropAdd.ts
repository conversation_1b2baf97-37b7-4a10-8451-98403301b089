/**
 * @description 下拉列表通用接口
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** code */
  code?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringObjectDictionaryApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/common/drop/add',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
