/**
 * @description 添加记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.LogParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysAdminLog/Add',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
