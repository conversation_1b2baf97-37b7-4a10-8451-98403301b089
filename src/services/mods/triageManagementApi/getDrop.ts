/**
 * @description 获取下拉列表 DISTRIBUTOR_SITE,CASE_TYPE
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** code */
  code?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/distributor/triaged/drop',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
