/**
 * @description 是否存在
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/caselocking/exist',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
