/**
 * @description 修改系统配置表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.ConfigReqModel> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/safemanager/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
