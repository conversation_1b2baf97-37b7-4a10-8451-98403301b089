/**
 * @description 门户添加
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.GateWayAddParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/GateWay',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
