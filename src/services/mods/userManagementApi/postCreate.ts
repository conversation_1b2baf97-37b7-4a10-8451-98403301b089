/**
 * @description 创建站点用户
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SiteUserReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/user/management/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
