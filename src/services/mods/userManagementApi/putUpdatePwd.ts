/**
 * @description 修改密码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CommLoginParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/user/management/update/pwd',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
