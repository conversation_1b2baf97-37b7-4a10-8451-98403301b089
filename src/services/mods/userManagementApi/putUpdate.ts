/**
 * @description 修改站点用户
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateSiteUserReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/user/management/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
