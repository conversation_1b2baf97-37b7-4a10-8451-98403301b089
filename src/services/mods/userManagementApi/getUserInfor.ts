/**
 * @description 获取用户基本信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SiteUserInforDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/user/management/user/infor',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
