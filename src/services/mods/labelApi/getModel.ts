/**
 * @description 获取一条
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.LabelDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/label/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
