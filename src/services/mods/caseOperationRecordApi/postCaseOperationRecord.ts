/**
 * @description 增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CaseOperationRecordsReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseOperationRecord',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
