/**
 * @description 发送消息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** SendType */
  SendType?: defs.SendType;
  /** Data */
  Data?: string;
  /** TargetUserId */
  TargetUserId?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/websocket/send/mesagge',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
