/**
 * @description 增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RegistrationManagementReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/Registrationmanagement',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
