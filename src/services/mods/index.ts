import * as accountApi from './accountApi';
import * as adminApi from './adminApi';
import * as adminShortMessageApi from './adminShortMessageApi';
import * as adminStatisticsApi from './adminStatisticsApi';
import * as allCommonApi from './allCommonApi';
import * as billApi from './billApi';
import * as caseLockingApi from './caseLockingApi';
import * as caseMessageApi from './caseMessageApi';
import * as caseOperationRecordApi from './caseOperationRecordApi';
import * as caseQualityAssessmentApi from './caseQualityAssessmentApi';
import * as caseQualityControlStatisticsApi from './caseQualityControlStatisticsApi';
import * as caseTimeoutMessageApi from './caseTimeoutMessageApi';
import * as caseUploadsliceApi from './caseUploadsliceApi';
import * as chargeApi from './chargeApi';
import * as chargingItemConfigurationApi from './chargingItemConfigurationApi';
import * as cloudCaseQualityControlStatisticsApi from './cloudCaseQualityControlStatisticsApi';
import * as commonApi from './commonApi';
import * as dataFlowApi from './dataFlowApi';
import * as dbFirstApi from './dbFirstApi';
import * as diagnosedApi from './diagnosedApi';
import * as diagnosisTreatmentCardApi from './diagnosisTreatmentCardApi';
import * as diagnosticVocabularyPrivateTypesApi from './diagnosticVocabularyPrivateTypesApi';
import * as distributorApi from './distributorApi';
import * as distributorExpertApi from './distributorExpertApi';
import * as distributorShortMessageApi from './distributorShortMessageApi';
import * as doctorOrderApi from './doctorOrderApi';
import * as engineerApi from './engineerApi';
import * as expertAppStatisticsApi from './expertAppStatisticsApi';
import * as expertCaseManagementApi from './expertCaseManagementApi';
import * as expertFunctionApi from './expertFunctionApi';
import * as expertLabelApi from './expertLabelApi';
import * as expertManagementApi from './expertManagementApi';
import * as expertReviewApi from './expertReviewApi';
import * as expertShortMessageApi from './expertShortMessageApi';
import * as feeReminderApi from './feeReminderApi';
import * as feeStatisticsApi from './feeStatisticsApi';
import * as frontPageApi from './frontPageApi';
import * as frozenAppointmentApi from './frozenAppointmentApi';
import * as gateWayApi from './gateWayApi';
import * as hisApi from './hisApi';
import * as hisCaseinformationApi from './hisCaseinformationApi';
import * as labelApi from './labelApi';
import * as medicalAdviceApi from './medicalAdviceApi';
import * as medicalHistoryApi from './medicalHistoryApi';
import * as messageApi from './messageApi';
import * as netEaseYunXinApi from './netEaseYunXinApi';
import * as orderAmountApi from './orderAmountApi';
import * as orderTypeApi from './orderTypeApi';
import * as orderTypeConfigurationApi from './orderTypeConfigurationApi';
import * as orderTypeSubitemApi from './orderTypeSubitemApi';
import * as organizenApi from './organizenApi';
import * as otherChargeApi from './otherChargeApi';
import * as pathologicalSectionMarkApi from './pathologicalSectionMarkApi';
import * as pathologyIntegrationApi from './pathologyIntegrationApi';
import * as paymentcompletedApi from './paymentcompletedApi';
import * as pubBaseCodeApi from './pubBaseCodeApi';
import * as qualityControlEvaluationApi from './qualityControlEvaluationApi';
import * as rcpPathologyTemplateApi from './rcpPathologyTemplateApi';
import * as rcpPathologyTemplateCodeApi from './rcpPathologyTemplateCodeApi';
import * as registrationmanagementApi from './registrationmanagementApi';
import * as remoteManagementApi from './remoteManagementApi';
import * as returnedApi from './returnedApi';
import * as safeManagerApi from './safeManagerApi';
import * as siteCommonApi from './siteCommonApi';
import * as siteManagerApi from './siteManagerApi';
import * as siteShortMessageApi from './siteShortMessageApi';
import * as socketApi from './socketApi';
import * as statisticsManagerApi from './statisticsManagerApi';
import * as subCentersApi from './subCentersApi';
import * as suggestApi from './suggestApi';
import * as sysAdminLogApi from './sysAdminLogApi';
import * as sysCentralMessageLogApi from './sysCentralMessageLogApi';
import * as sysUpdateLogApi from './sysUpdateLogApi';
import * as sysUserLogApi from './sysUserLogApi';
import * as sysUserMessageApi from './sysUserMessageApi';
import * as templateCenterApi from './templateCenterApi';
import * as toBeDiagnosedApi from './toBeDiagnosedApi';
import * as triageFunctionApi from './triageFunctionApi';
import * as triageManagementApi from './triageManagementApi';
import * as universalApi from './universalApi';
import * as unsubmittedApi from './unsubmittedApi';
import * as userCommonApi from './userCommonApi';
import * as userManagementApi from './userManagementApi';
import * as withdrawnApi from './withdrawnApi';
import * as workUnitApi from './workUnitApi';

(window as any).API = {
  accountApi,
  adminApi,
  adminShortMessageApi,
  adminStatisticsApi,
  allCommonApi,
  billApi,
  caseLockingApi,
  caseMessageApi,
  caseOperationRecordApi,
  caseQualityAssessmentApi,
  caseQualityControlStatisticsApi,
  caseTimeoutMessageApi,
  caseUploadsliceApi,
  chargeApi,
  chargingItemConfigurationApi,
  cloudCaseQualityControlStatisticsApi,
  commonApi,
  dataFlowApi,
  dbFirstApi,
  diagnosedApi,
  diagnosisTreatmentCardApi,
  diagnosticVocabularyPrivateTypesApi,
  distributorApi,
  distributorExpertApi,
  distributorShortMessageApi,
  doctorOrderApi,
  engineerApi,
  expertAppStatisticsApi,
  expertCaseManagementApi,
  expertFunctionApi,
  expertLabelApi,
  expertManagementApi,
  expertReviewApi,
  expertShortMessageApi,
  feeReminderApi,
  feeStatisticsApi,
  frontPageApi,
  frozenAppointmentApi,
  gateWayApi,
  hisApi,
  hisCaseinformationApi,
  labelApi,
  medicalAdviceApi,
  medicalHistoryApi,
  messageApi,
  netEaseYunXinApi,
  orderAmountApi,
  orderTypeApi,
  orderTypeConfigurationApi,
  orderTypeSubitemApi,
  organizenApi,
  otherChargeApi,
  pathologicalSectionMarkApi,
  pathologyIntegrationApi,
  paymentcompletedApi,
  pubBaseCodeApi,
  qualityControlEvaluationApi,
  rcpPathologyTemplateApi,
  rcpPathologyTemplateCodeApi,
  registrationmanagementApi,
  remoteManagementApi,
  returnedApi,
  safeManagerApi,
  siteCommonApi,
  siteManagerApi,
  siteShortMessageApi,
  socketApi,
  statisticsManagerApi,
  subCentersApi,
  suggestApi,
  sysAdminLogApi,
  sysCentralMessageLogApi,
  sysUpdateLogApi,
  sysUserLogApi,
  sysUserMessageApi,
  templateCenterApi,
  toBeDiagnosedApi,
  triageFunctionApi,
  triageManagementApi,
  universalApi,
  unsubmittedApi,
  userCommonApi,
  userManagementApi,
  withdrawnApi,
  workUnitApi,
};
