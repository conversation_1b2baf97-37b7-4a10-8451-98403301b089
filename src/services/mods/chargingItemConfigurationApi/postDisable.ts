/**
 * @description 禁用
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/chargingitemconfiguration/disable',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
