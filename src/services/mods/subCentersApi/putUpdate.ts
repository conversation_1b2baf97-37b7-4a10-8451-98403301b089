/**
 * @description 修改分中心
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SubCenterListUpdateParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/subcenter/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
