/**
 * @description 分中心渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.PlatformRenderingDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/subcenter/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
