/**
 * @description 创建分中心
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CreateSubCenterReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/subcenter/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
