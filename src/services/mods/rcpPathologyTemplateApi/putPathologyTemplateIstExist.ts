/**
 * @description 模板是否重复
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.TemplateExistModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/PathologyTemplateIstExist',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
