/**
 * @description 更新
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RcpPathologyTemplate },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/Update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
