/**
 * @description 查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RcpPathologyTemplateApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/SearchSingle',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
