/**
 * @description 删除
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/Delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
