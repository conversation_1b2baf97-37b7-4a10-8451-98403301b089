/**
 * @description 删除模板文件
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StrParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/DeleteTemplateFile',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
