/**
 * @description 下载模板文件
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ObjectApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/DownloadFileForPlatform',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
