/**
 * @description 病理模板
 */
import * as postCreate from './postCreate';
import * as deleteDelete from './deleteDelete';
import * as deleteDeleteTemplateFile from './deleteDeleteTemplateFile';
import * as getDownloadFileForPlatform from './getDownloadFileForPlatform';
import * as getGetReportTemplateList from './getGetReportTemplateList';
import * as putPathologyTemplateIstExist from './putPathologyTemplateIstExist';
import * as postRenderPathologyReportTemplate from './postRenderPathologyReportTemplate';
import * as getSearchForPlatform from './getSearchForPlatform';
import * as getSearchForSite from './getSearchForSite';
import * as getSearchSingle from './getSearchSingle';
import * as putUpdate from './putUpdate';
import * as postUploadFileForPlatform from './postUploadFileForPlatform';

export {
  postCreate,
  deleteDelete,
  deleteDeleteTemplateFile,
  getDownloadFileForPlatform,
  getGetReportTemplateList,
  putPathologyTemplateIstExist,
  postRenderPathologyReportTemplate,
  getSearchForPlatform,
  getSearchForSite,
  getSearchSingle,
  putUpdate,
  postUploadFileForPlatform,
};
