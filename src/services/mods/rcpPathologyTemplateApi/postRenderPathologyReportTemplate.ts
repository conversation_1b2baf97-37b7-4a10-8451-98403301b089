/**
 * @description 渲染模板
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RenderTemplatePostModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/RenderPathologyReportTemplate',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
