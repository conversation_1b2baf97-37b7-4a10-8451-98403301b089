/**
     * @description 获取模板文件List
入参为模板Code
     */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ObjectApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/GetReportTemplateList',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
