/**
 * @description 新增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RcpPathologyTemplate },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RcpPathologyTemplateApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/Create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
