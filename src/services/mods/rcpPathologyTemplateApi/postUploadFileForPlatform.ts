/**
 * @description 上传模板文件
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** templateCode */
  templateCode?: string;
  /** templateFileCode */
  templateFileCode?: string;
}

export async function request(
  params: { params: Params; bodyParams: object },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/RcpPathologyTemplate/UploadFileForPlatform',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
