/**
 * @description 新增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DiagnosticVocabularyTypeDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeAdd',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
