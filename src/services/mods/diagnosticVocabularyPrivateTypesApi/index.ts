/**
 * @description 模板类型
 */
import * as getDiagnosticVocabularyPrivateTypesList from './getDiagnosticVocabularyPrivateTypesList';
import * as getDiagnosticVocabularyPublicTypesList from './getDiagnosticVocabularyPublicTypesList';
import * as postDiagnosticVocabularyTypeAdd from './postDiagnosticVocabularyTypeAdd';
import * as deleteDiagnosticVocabularyTypeDelete from './deleteDiagnosticVocabularyTypeDelete';
import * as putDiagnosticVocabularyTypeEdit from './putDiagnosticVocabularyTypeEdit';
import * as getDiagnosticVocabularyTypeModel from './getDiagnosticVocabularyTypeModel';

export {
  getDiagnosticVocabularyPrivateTypesList,
  getDiagnosticVocabularyPublicTypesList,
  postDiagnosticVocabularyTypeAdd,
  deleteDiagnosticVocabularyTypeDelete,
  putDiagnosticVocabularyTypeEdit,
  getDiagnosticVocabularyTypeModel,
};
