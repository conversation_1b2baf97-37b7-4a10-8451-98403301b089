/**
 * @description 管理员诊断词汇列表（公有）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** searchText */
  searchText?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DiagnosticVocabularySimpleTypesDtoPageResult> {
  return RequestHelper.request(
    {
      url:
        '/api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyPublicTypesList',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
