/**
 * @description 根据主键ID获取获取
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DiagnosticVocabularyTypeDtoApiResult> {
  return RequestHelper.request(
    {
      url:
        '/api/DiagnosticVocabularyPrivateTypes/DiagnosticVocabularyTypeModel',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
