/**
 * @description 实体
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.HsiCasehistoryDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/DiagnosisTreatmentCard/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
