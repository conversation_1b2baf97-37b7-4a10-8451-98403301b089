/**
 * @description 增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CasehistoryReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/DiagnosisTreatmentCard',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
