/**
 * @description 专家质控统计
 */
import * as getDiagnosisCoincidenceRate from './getDiagnosisCoincidenceRate';
import * as getFrozenInitialDiagnosisAndReviewCoincidenceRate from './getFrozenInitialDiagnosisAndReviewCoincidenceRate';
import * as getNonGynecological from './getNonGynecological';
import * as getPathologySliceExcellentRate from './getPathologySliceExcellentRate';
import * as getPathologySliceReturnedRate from './getPathologySliceReturnedRate';
import * as getPathologySliceTimelinessOfDiagnosis from './getPathologySliceTimelinessOfDiagnosis';
import * as getPositive from './getPositive';

export {
  getDiagnosisCoincidenceRate,
  getFrozenInitialDiagnosisAndReviewCoincidenceRate,
  getNonGynecological,
  getPathologySliceExcellentRate,
  getPathologySliceReturnedRate,
  getPathologySliceTimelinessOfDiagnosis,
  getPositive,
};
