/**
 * @description 病例退回率
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 站点id */
  SiteId?: number;
  /** 开始时间 */
  StartingTime?: string;
  /** 结束时间 */
  EndTime?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.PathologySliceReturnedRateDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseQualityControlStatistics/PathologySliceReturnedRate',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
