/**
 * @description /上传文件
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 预签名url */
  Url?: string;
  /** 切片id */
  Id?: number;
}

export async function request(
  params: { params: Params; bodyParams: object },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/uploadobject',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
