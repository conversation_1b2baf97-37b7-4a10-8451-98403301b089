/**
 * @description 解密
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** dec */
  dec?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/account/jiemi',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
