/**
 * @description 获取冰冻预约流转到普通病例的id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/caseid',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
