/**
 * @description 返回下载预签名
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StrDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/getpresigned',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
