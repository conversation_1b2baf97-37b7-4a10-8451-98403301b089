/**
 * @description 授权中心增加
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StorageUserModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/storageuseradd',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
