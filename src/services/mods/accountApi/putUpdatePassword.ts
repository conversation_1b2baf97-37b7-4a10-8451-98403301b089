/**
 * @description 找回密码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.RetrievePasswordReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/update/password',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
