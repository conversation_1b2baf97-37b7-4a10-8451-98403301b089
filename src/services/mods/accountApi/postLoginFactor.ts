/**
 * @description 邮箱验证码登录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.LoginReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ObjectApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/login/factor',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
