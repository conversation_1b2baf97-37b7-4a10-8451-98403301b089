/**
 * @description 用户
 */
import * as getAmerica from "./getAmerica";
import * as getAnhuiservice from "./getAnhuiservice";
import * as postCancellation from "./postCancellation";
import * as getCaseid from "./getCaseid";
import * as putCode from "./putCode";
import * as getEncrypt from "./getEncrypt";
import * as postEngineer from "./postEngineer";
import * as getGetpresigned from "./getGetpresigned";
import * as getInfo from "./getInfo";
import * as getInfoId from "./getInfoId";
import * as getIp from "./getIp";
import * as getJiemi from "./getJiemi";
import * as postLogin from "./postLogin";
import * as postLoginFactor from "./postLoginFactor";
import * as getOss from "./getOss";
import * as getH3c from "./getH3c";
import * as putPasswordUpdate from "./putPasswordUpdate";
import * as getRefreshtoken from "./getRefreshtoken";
import * as postSendEmail from "./postSendEmail";
import * as putSliceUpdate from "./putSliceUpdate";
import * as getStorageuseradd from "./getStorageuseradd";
import * as putUpdatePassword from "./putUpdatePassword";
import * as getUploadobject from "./getUploadobject";
import * as getUploadpresigned from "./getUploadpresigned";
import * as getUserip from "./getUserip";

export {
  getAmerica,
  getAnhuiservice,
  postCancellation,
  getCaseid,
  putCode,
  getEncrypt,
  postEngineer,
  getGetpresigned,
  getInfo,
  getInfoId,
  getIp,
  getJiemi,
  postLogin,
  postLoginFactor,
  getOss,
  getH3c,
  putPasswordUpdate,
  getRefreshtoken,
  postSendEmail,
  putSliceUpdate,
  getStorageuseradd,
  putUpdatePassword,
  getUploadobject,
  getUploadpresigned,
  getUserip,
};
