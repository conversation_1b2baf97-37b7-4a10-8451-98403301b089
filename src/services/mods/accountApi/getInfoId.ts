/**
 * @description 获取用户id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.UserConfigApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/info/id',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
