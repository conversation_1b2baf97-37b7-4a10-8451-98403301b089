/**
 * @description 刷新token
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RefreshTokenResponseApiResult> {
  return RequestHelper.request(
    {
      url: '/api/account/refreshtoken',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
