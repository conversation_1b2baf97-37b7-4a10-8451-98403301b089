/**
 * @description 新增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.CaseMedicalHistoryModel> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/MedicalHistory/Add',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
