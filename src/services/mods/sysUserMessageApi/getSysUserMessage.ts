/**
 * @description 是否收到过消息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.BooleanApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserMessage',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
