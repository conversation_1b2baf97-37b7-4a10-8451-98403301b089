/**
 * @description 添加一条记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.BooleanApiResult> {
  return RequestHelper.request(
    {
      url: '/api/SysUserMessage',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
