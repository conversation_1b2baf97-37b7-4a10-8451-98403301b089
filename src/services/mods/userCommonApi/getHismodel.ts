/**
 * @description 获取与his关联的病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.HisCaseinformationReqModelApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/hismodel',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
