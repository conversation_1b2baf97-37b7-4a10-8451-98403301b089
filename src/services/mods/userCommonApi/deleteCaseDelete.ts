/**
 * @description 删除病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DeleteCaseReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/case/delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
