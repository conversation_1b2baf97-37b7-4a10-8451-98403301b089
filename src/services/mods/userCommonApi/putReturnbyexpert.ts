/**
 * @description 专家退回判断是否有分配员
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/returnbyexpert',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
