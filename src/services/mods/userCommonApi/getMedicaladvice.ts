/**
 * @description 获取一条医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertDoctorsAdviceDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/medicaladvice',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
