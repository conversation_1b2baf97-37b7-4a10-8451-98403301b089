/**
 * @description 冰冻数据渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.FrozenInformationDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/frozen/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
