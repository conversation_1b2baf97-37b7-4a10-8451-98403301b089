/**
 * @description 切片集合
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 切片类型 1为文件夹类型 */
  SliceType?: number;
  /** id */
  Id: number;
  /** 当前页 */
  PageIndex: number;
  /** 每页总条数 */
  PageSize: number;
  /** 或查询，针对同一字段匹配多种搜索结果 如查询姓名或昵称=admin 格式：
[{"ConditionalList":[
{"Key":0,"Value":{"FieldName":"allocationStatus","FieldValue":"admin","ConditionalType":0}}
,{"Key":1,"Value":{"FieldName":"patiendName","FieldValue":"admin","ConditionalType":0}}
]}] */
  AdvancedSearch?: string;
  /** 列表排序 列名+Desc(降序) 或者列明+Asc(升序) */
  OrderByType?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseSliceDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/common/slice/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
