/**
 * @description 病例渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseRenderingDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/case/pendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
