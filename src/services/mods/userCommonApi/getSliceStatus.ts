/**
 * @description 获取切片状态
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 字符串 */
  Str?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int32ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/slice/status',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
