/**
 * @description 账号是否存在
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.AccountExistModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/common/accountexist',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
