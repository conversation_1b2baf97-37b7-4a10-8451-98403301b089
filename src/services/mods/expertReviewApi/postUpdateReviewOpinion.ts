/**
 * @description 修改复核意见
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/review/update/review/opinion',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
