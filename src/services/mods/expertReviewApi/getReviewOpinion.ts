/**
 * @description 复核查看意见
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ReviewOpinionDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/review/review/opinion',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
