/**
 * @description 复核邀请/退回
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.ReviewReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/review/review',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
