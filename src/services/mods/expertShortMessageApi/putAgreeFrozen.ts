/**
 * @description 专家同意冰冻预约
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/shortmessage/agree/frozen',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
