/**
 * @description 专家撤回报告申请
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/shortmessage/withdraws/report/request',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
