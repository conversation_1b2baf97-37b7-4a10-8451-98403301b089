/**
 * @description 完善站点
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params; bodyParams: defs.CompleteSiteManagerReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/site/complete',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
