/**
 * @description 创建站点
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SiteManagerReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int64ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/site/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
