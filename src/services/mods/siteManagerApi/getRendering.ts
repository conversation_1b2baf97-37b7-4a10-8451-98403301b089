/**
 * @description 渲染站点信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SiteInformationDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/site/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
