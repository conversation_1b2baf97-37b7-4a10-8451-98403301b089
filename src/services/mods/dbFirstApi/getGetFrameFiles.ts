/**
 * @description 获取 整体框架 文件
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** dbName */
  dbName?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/DbFirst/GetFrameFiles',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
