/**
 * @description 新增分配员
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CreateDistributorReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/distributor/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
