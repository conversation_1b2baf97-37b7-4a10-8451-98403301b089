/**
 * @description 渲染分配员
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DistributorUpdateDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/distributor/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
