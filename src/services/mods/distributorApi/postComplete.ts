/**
 * @description 完善分配员
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params; bodyParams: defs.CompleteDistributorReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/distributor/complete',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
