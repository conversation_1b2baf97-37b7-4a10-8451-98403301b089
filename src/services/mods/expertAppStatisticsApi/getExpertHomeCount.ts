/**
 * @description 专家条数统计
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 开始时间 */
  StartingTime?: string;
  /** 结束时间 */
  EndTime?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertHomeCountDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/ExpertAppStatistics/ExpertHomeCount',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
