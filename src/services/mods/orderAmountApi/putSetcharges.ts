/**
 * @description 设置金额
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** caseId */
  caseId?: number;
}

export async function request(
  params: { params: Params; bodyParams: Array<defs.IdParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/orderamount/setcharges',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
