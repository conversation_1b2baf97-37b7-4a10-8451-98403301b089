/**
 * @description 切片质控统计
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 开始时间 */
  StartingTime?: string;
  /** 结束时间 */
  EndTime?: string;
  /** AdvancedSearch */
  AdvancedSearch?: string;
  /** 切片【1为创建时间 2为质控时间】 病例【1发布时间 2诊断符合率时间】 */
  DataType?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SliceQualityControlDtoListApiResult> {
  return RequestHelper.request(
    {
      url: '/api/AdminStatistics/Slice',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
