/**
 * @description 分诊员功能
 */
import * as putAgreeWithdraw from './putAgreeWithdraw';
import * as putBulkAllocation from './putBulkAllocation';
import * as putDistribute from './putDistribute';
import * as postNeglect from './postNeglect';
import * as putRefusalWithdraw from './putRefusalWithdraw';
import * as getStatus from './getStatus';
import * as getWithdrawal from './getWithdrawal';

export {
  putAgreeWithdraw,
  putBulkAllocation,
  putDistribute,
  postNeglect,
  putRefusalWithdraw,
  getStatus,
  getWithdrawal,
};
