/**
 * @description 同意撤回
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DistributorWithdrawReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/distributor/function/agree/withdraw',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
