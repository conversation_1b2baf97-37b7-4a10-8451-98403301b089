/**
 * @description 批量分配
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.BulkAllocationReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/distributor/function/bulk/allocation',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
