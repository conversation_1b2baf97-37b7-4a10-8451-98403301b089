/**
 * @description 撤回报告成功
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/shortmessage/withdrawal/report/successful',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
