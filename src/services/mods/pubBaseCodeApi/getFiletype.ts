/**
 * @description 获取附件类型父id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringParamApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/filetype',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
