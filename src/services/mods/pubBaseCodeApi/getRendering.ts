/**
 * @description 获取具体项
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.PuBaseCodeDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
