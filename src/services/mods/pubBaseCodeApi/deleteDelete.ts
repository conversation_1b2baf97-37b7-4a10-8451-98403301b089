/**
 * @description 删除基础代码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
