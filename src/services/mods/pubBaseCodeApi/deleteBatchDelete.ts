/**
 * @description 批量刪除基础代码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.IdParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/batch/delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
