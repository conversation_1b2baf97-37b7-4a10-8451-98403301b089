/**
 * @description 基础代码
 */
import * as getLanguage from './getLanguage';
import * as deleteBatchDelete from './deleteBatchDelete';
import * as getCasetype from './getCasetype';
import * as getCasetypeList from './getCasetypeList';
import * as postCreate from './postCreate';
import * as deleteDelete from './deleteDelete';
import * as getFiletype from './getFiletype';
import * as getFiletypeList from './getFiletypeList';
import * as getIscasetype from './getIscasetype';
import * as getIscheckitem from './getIscheckitem';
import * as getList from './getList';
import * as getParentId from './getParentId';
import * as getRendering from './getRendering';
import * as getSampleLocation from './getSampleLocation';
import * as getSamplelocationList from './getSamplelocationList';
import * as putUpdate from './putUpdate';

export {
  getLanguage,
  deleteBatchDelete,
  getCasetype,
  getCasetypeList,
  postCreate,
  deleteDelete,
  getFiletype,
  getFiletypeList,
  getIscasetype,
  getIscheckitem,
  getList,
  getParentId,
  getRendering,
  getSampleLocation,
  getSamplelocationList,
  putUpdate,
};
