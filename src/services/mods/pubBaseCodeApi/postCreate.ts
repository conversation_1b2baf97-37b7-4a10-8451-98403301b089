/**
 * @description 创建基础代码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PubBaseCodeReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
