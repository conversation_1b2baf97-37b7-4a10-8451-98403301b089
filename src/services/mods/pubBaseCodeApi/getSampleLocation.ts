/**
 * @description 获取取材部位父id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringParamApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/sample/location',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
