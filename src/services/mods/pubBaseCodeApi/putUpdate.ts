/**
 * @description 修改基础代码
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdatePubBaseCodeParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
