/**
 * @description 管理员病理类型，送检项
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.PubBaseCodeTreeDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/basecode/casetype/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
