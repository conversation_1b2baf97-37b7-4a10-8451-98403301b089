/**
 * @description 渲染专家级别
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertLevelApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/level/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
