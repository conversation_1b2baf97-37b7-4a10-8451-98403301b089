/**
 * @description 创建专家级别
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.ExpertLevelReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/level/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
