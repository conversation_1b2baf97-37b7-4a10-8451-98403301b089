/**
 * @description 修改专家级别
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateExpertLevelReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/level/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
