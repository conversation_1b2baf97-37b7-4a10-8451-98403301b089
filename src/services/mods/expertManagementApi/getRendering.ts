/**
 * @description 获取专家基本信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertRenderingDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/rendering',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
