/**
 * @description 完善专家
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  id?: number;
}

export async function request(
  params: { params: Params; bodyParams: defs.CompleteCreateExpertModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/complete',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
