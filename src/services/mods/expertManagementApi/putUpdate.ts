/**
 * @description 修改专家
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateExpertParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/expert/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
