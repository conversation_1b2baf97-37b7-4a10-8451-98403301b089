/**
 * @description 是否使用过项目
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/ordertype/isdoctorsadvice',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
