/**
 * @description 批量删除项目
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.IdParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/ordertype/batch/delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
