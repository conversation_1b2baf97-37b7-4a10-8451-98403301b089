/**
 * @description 新增项目
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OrderTypeParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/ordertype/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
