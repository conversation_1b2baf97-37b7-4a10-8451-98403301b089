/**
 * @description 修改
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateExpertVocabularysReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabularys/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
