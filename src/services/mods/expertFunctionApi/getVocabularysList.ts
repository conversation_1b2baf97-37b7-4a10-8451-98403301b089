/**
 * @description 集合
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 病例类型 */
  CaseTypeCode: string;
  /** 送检项 */
  CheckItemsCode: string;
  /** 最大id（上一页取第一条） */
  MaxId?: number;
  /** 每页总条数 */
  PageSize: number;
  /** 最小id(取当前页最后一条，下一页时传入) */
  MinId?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertVocabularysDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabularys/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
