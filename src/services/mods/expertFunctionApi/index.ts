/**
 * @description 专家功能操作控制器
 */
import * as putAgreeIssue from './putAgreeIssue';
import * as postBuildReport from './postBuildReport';
import * as putCancelenshrine from './putCancelenshrine';
import * as getCollect from './getCollect';
import * as putConfirmReply from './putConfirmReply';
import * as postCreateInvitee from './postCreateInvitee';
import * as getDiagnosis from './getDiagnosis';
import * as postDiagnosis from './postDiagnosis';
import * as putEnshrine from './putEnshrine';
import * as getEvaluationModel from './getEvaluationModel';
import * as postEvaluationQuality from './postEvaluationQuality';
import * as getExistexpertinvitee from './getExistexpertinvitee';
import * as getExperttype from './getExperttype';
import * as putFrozen from './putFrozen';
import * as getInvitee from './getInvitee';
import * as postPrivatevocabularyAdd from './postPrivatevocabularyAdd';
import * as deletePrivatevocabularyDelete from './deletePrivatevocabularyDelete';
import * as getPrivatevocabularyList from './getPrivatevocabularyList';
import * as getPrivatevocabularyModel from './getPrivatevocabularyModel';
import * as putPrivatevocabularyUpdate from './putPrivatevocabularyUpdate';
import * as getQualityCntrol from './getQualityCntrol';
import * as getRealtimeroughly from './getRealtimeroughly';
import * as putRelease from './putRelease';
import * as getSite from './getSite';
import * as getVocabularyLike from './getVocabularyLike';
import * as postVocabularysAdd from './postVocabularysAdd';
import * as putVocabularysDelete from './putVocabularysDelete';
import * as getVocabularysInfor from './getVocabularysInfor';
import * as getVocabularysLike from './getVocabularysLike';
import * as getVocabularysList from './getVocabularysList';
import * as putVocabularysUpdate from './putVocabularysUpdate';
import * as postWithdrawaDiagnosis from './postWithdrawaDiagnosis';
import * as postWithdrawalofreview from './postWithdrawalofreview';

export {
  putAgreeIssue,
  postBuildReport,
  putCancelenshrine,
  getCollect,
  putConfirmReply,
  postCreateInvitee,
  getDiagnosis,
  postDiagnosis,
  putEnshrine,
  getEvaluationModel,
  postEvaluationQuality,
  getExistexpertinvitee,
  getExperttype,
  putFrozen,
  getInvitee,
  postPrivatevocabularyAdd,
  deletePrivatevocabularyDelete,
  getPrivatevocabularyList,
  getPrivatevocabularyModel,
  putPrivatevocabularyUpdate,
  getQualityCntrol,
  getRealtimeroughly,
  putRelease,
  getSite,
  getVocabularyLike,
  postVocabularysAdd,
  putVocabularysDelete,
  getVocabularysInfor,
  getVocabularysLike,
  getVocabularysList,
  putVocabularysUpdate,
  postWithdrawaDiagnosis,
  postWithdrawalofreview,
};
