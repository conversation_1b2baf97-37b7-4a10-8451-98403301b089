/**
 * @description 撤回诊断
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/withdrawa/diagnosis',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
