/**
 * @description 渲染病例数据
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.WriteDiagnosticsDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/diagnosis',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
