/**
 * @description 诊断-状态为待诊断或复核请求此接口
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/diagnosis',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
