/**
 * @description 诊断词汇列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** typeCode */
  typeCode?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DiagnosticVocabularyTextDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/privatevocabulary/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
