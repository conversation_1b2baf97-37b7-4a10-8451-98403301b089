/**
 * @description 词汇编辑
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DiagnosticVocabularyTextDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/privatevocabulary/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
