/**
 * @description 撤回复核
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/withdrawalofreview',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
