/**
 * @description 新增词汇
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DiagnosticVocabularyTextDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/privatevocabulary/add',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
