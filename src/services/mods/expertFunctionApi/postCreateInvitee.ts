/**
 * @description 邀请专家
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.ExpertInviteeReqModel> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/create/invitee',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
