/**
 * @description 私有模板添加
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.ExpertVocabularysReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabularys/add',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
