/**
 * @description 修改切片质量
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateQualityEvaluationListQueryParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/evaluation/quality',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
