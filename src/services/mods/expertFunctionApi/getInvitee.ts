/**
 * @description 获取邀请理由
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertInviteeDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/invitee',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
