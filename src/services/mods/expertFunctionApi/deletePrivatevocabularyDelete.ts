/**
 * @description 删除诊断模板
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/privatevocabulary/delete',
      method: 'delete',
      params: params,
    },
    axiosConfig,
  );
}
