/**
 * @description 确认回复
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/confirm/reply',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
