/**
 * @description 渲染
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RenderExpertVocabularysDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabularys/infor',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
