/**
 * @description 是否存在专家邀请
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 专家id */
  ExpertId: number;
  /** 病例id */
  CaseId: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/existexpertinvitee',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
