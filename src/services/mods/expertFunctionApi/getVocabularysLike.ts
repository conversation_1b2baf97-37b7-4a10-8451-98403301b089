/**
 * @description 模糊查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 查询字段 */
  Str: string;
  /** 父级 */
  CaseTypeCode?: string;
  /** 子集 */
  CheckItemsCode?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.VocabularysLikeDtoPageResponse> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabularys/like',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
