/**
 * @description 获取切片质量
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.GetQualityDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/evaluation/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
