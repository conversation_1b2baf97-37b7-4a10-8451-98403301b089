/**
 * @description 获取病例是否收藏
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.BoolDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/collect',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
