/**
 * @description 模板模糊查询（共有、所有）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** TypeCode */
  TypeCode?: string;
  /** likeStr */
  likeStr?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.VocabularysLikeDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/vocabulary/like',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
