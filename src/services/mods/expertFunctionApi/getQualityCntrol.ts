/**
 * @description 获取质控权限
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.QualityControlDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/function/quality/cntrol',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
