/**
 * @description 站点业务类型配置列表（根据站点显示金额）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseBusTypeChargeDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/charge/businesstype/site/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
