/**
 * @description 全部业务类型配置列表（不显示金额）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseBusTypeChargeDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/charge/businesstype/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
