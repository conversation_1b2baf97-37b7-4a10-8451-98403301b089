/**
 * @description 业务类型配置修改
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.BusinessTypeChargeParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/charge/businesstype',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
