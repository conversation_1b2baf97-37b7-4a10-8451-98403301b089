/**
 * @description 打印授权-操作
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OperateReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/remotemanagement/print',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
