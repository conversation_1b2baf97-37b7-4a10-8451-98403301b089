/**
 * @description 远程诊断管理
 */
import * as getApplicationManagementList from './getApplicationManagementList';
import * as postApplicationManagementOperate from './postApplicationManagementOperate';
import * as getConsultationManagementList from './getConsultationManagementList';
import * as getConsultationManagementListStream from './getConsultationManagementListStream';
import * as getFrozenList from './getFrozenList';
import * as postPrint from './postPrint';
import * as getPrintAuthorizationList from './getPrintAuthorizationList';
import * as getPrintLog from './getPrintLog';
import * as getSite from './getSite';
import * as getStatus from './getStatus';
import * as postUpdateCase from './postUpdateCase';
import * as postWithdraw from './postWithdraw';
import * as getWithdrawalManagement from './getWithdrawalManagement';

export {
  getApplicationManagementList,
  postApplicationManagementOperate,
  getConsultationManagementList,
  getConsultationManagementListStream,
  getFrozenList,
  postPrint,
  getPrintAuthorizationList,
  getPrintLog,
  getSite,
  getStatus,
  postUpdateCase,
  postWithdraw,
  getWithdrawalManagement,
};
