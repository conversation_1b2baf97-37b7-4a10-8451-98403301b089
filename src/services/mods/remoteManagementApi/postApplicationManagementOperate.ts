/**
 * @description 申请管理拒绝
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/remotemanagement/application/management/operate',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
