/**
 * @description 获取状态
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StatusDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/remotemanagement/status',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
