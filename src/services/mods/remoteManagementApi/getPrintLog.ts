/**
 * @description 打印记录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StrParamApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/remotemanagement/print/log',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
