/**
 * @description 撤回确定-同意拒绝
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OperateReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/remotemanagement/withdraw',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
