/**
 * @description 退回站点
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SubmitReturnReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/distributor/expert/return/submit',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
