/**
 * @description 评价切片备注接口
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.SliceDescriptionUpdateReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseUploadslice/SliceDescriptionUpdate',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
