/**
 * @description 评价切片接口
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.EvaluationUpdateReqModel },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseUploadslice/EvaluationUpdate',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
