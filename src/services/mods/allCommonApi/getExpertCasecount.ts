/**
 * @description 专家今日病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.Int32ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/all/common/expert/casecount',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
