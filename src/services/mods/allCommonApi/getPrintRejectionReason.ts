/**
 * @description 拒绝理由
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StrDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/all/common/print/rejection/reason',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
