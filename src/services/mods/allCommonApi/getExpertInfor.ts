/**
 * @description 专家基本信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 是否为意向专家 */
  IntentionalExpert?: boolean;
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SiteExpertDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/all/common/expert/infor',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
