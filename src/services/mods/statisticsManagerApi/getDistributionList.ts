/**
 * @description 分诊端统计
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 病例类型 */
  CaseType?: string;
  /** 当前页 */
  PageIndex: number;
  /** 每页总条数 */
  PageSize: number;
  /** 最大id */
  MaxId?: number;
  /** 状态 */
  Status?: number;
  /** 搜索字符串 */
  Str?: string;
  /** 诊断开始时间 */
  SubscribeTime?: string;
  /** 诊断完成时间 */
  CompleteTime?: string;
  /** 排序 */
  OrderByType?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DistributionDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/statisticsmanager/distribution/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
