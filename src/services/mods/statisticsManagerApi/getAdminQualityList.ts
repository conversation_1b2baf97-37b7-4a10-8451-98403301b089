/**
 * @description 管理端质控评价统计
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 当前页 */
  PageIndex: number;
  /** 每页总条数 */
  PageSize: number;
  /** 开始时间 */
  SubscribeTime?: string;
  /** 结束时间 */
  CompleteTime?: string;
  /** 切片质量 */
  Quality?: number;
  /** 站点id */
  SiteId?: string;
  /** 关键字（会诊编号/名称） */
  Str?: string;
  /** 病例类型 */
  CaseType?: string;
  /** 排序 */
  OrderByType?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.AdminQualityControlStatisticsDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/statisticsmanager/admin/quality/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
