/**
 * @description 会诊统计返回流
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 状态 */
  CaseType?: string;
  /** 状态 */
  Status?: number;
  /** 搜索字符串 */
  Str?: string;
  /** 诊断开始时间 */
  SubscribeTime?: string;
  /** 诊断完成时间 */
  CompleteTime?: string;
  /** 排序 */
  OrderByType?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/statisticsmanager/list/stream',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
