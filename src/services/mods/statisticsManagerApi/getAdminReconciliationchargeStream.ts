/**
 * @description 对账导出
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 当前页 */
  PageIndex: number;
  /** 每页总条数 */
  PageSize: number;
  /** 关键字搜索 */
  Str?: string;
  /** 站点Id */
  SiteId?: number;
  /** 病理类型 */
  CaseType?: string;
  /** 初审专家Id */
  ExpertId?: number;
  /** 复审Id */
  ReviewExpertId?: number;
  /** 收费项目 */
  CheckItems?: string;
  /** 开始时间 */
  SubscribeTime?: string;
  /** 结束时间 */
  CompleteTime?: string;
  /** 排序 */
  OrderByType?: string;
  /** 查询时间类型  0为不查询 1为提交时间 2为诊断时间 */
  TimeType?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<any> {
  return RequestHelper.request(
    {
      url: '/api/statisticsmanager/admin/reconciliationcharge/stream',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
