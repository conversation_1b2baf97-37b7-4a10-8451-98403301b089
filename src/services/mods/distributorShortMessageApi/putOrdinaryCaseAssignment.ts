/**
 * @description 分配普通病例/疑难/冰冻
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OrdinaryCaseAssignmentParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/distributor/shortmessage/ordinary/case/assignment',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
