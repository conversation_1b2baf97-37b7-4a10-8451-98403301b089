/**
 * @description 冰冻分配
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.OrdinaryCaseAssignmentParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/distributor/shortmessage/frozen/distribution',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
