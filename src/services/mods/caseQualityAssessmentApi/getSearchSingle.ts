/**
 * @description 查询
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 传入病例ID long型 */
  id?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseQualityAssessmentSingleDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseQualityAssessment/SearchSingle',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
