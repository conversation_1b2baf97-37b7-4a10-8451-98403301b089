/**
 * @description 根据CaseID获取切片评价信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** caseId */
  caseId?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ObjectApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseQualityAssessment/GetAllSliceByCaseId',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
