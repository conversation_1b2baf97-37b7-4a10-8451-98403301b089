/**
 * @description 新增
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CaseQualityAssessmentSingleDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CaseQualityAssessmentApiResult> {
  return RequestHelper.request(
    {
      url: '/api/CaseQualityAssessment/Create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
