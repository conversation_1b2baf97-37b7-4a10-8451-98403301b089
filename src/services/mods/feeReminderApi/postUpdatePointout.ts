/**
 * @description 修改消息通知 [应用：所有收费数据为0....的勾选加确认]
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/feereminder/update/pointout',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
