/**
 * @description 新增子项目
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.ImmunohistochemicalPackage> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/ordertypesubitem/subitem',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
