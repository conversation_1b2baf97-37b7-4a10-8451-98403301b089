/**
 * @description 病例关联标签集合
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.LabelDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/label/case/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
