/**
 * @description 修改质控
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.QualityControlEvaluationUpdateParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/QualityControlEvaluation/UpdateTask',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
