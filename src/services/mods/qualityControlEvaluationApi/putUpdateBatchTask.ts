/**
 * @description 批量修改质控
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.QualityControlEvaluationUpdateParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/QualityControlEvaluation/UpdateBatchTask',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
