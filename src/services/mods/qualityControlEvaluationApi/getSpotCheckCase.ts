/**
 * @description 抽查病例
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 病理类型 */
  CaseType?: string;
  /** 亚专科 */
  SubProfessional?: string;
  /** 开始时间 */
  StartingTime?: string;
  /** 结束时间 */
  EndTime?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.SpotCheckCaseCountDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/QualityControlEvaluation/SpotCheckCase',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
