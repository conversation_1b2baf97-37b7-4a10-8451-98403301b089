/**
 * @description 重新获取推拉流地址
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 频道id */
  cid?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ChannelUrApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/channel/address',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
