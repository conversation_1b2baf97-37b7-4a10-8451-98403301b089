/**
 * @description 创建房间
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CreateRoomParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/room',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
