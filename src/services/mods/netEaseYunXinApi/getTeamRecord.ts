/**
 * @description 获取历史记录（群聊）
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 群聊id */
  Tid: string;
  /** 用户id */
  Accid: string;
  /** 查询多少条 */
  Limit?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/team/record',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
