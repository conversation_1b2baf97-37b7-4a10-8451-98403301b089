/**
 * @description 拉入群聊 id直接拉入方式
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PullGroupChatParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/pull/team',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
