/**
 * @description 刷新用户token
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.UpdateNimidParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/user/update',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
