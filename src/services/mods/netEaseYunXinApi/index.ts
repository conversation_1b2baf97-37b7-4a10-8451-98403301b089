/**
 * @description 网易云信
 */
import * as postChannel from './postChannel';
import * as getChannelAddress from './getChannelAddress';
import * as getChannelId from './getChannelId';
import * as getChannelList from './getChannelList';
import * as putChannelStatus from './putChannelStatus';
import * as getChannelStatus from './getChannelStatus';
import * as getChannelToken from './getChannelToken';
import * as getCreateRecordTask from './getCreateRecordTask';
import * as postCreateTeam from './postCreateTeam';
import * as getGetRecordFileInfo from './getGetRecordFileInfo';
import * as getGroupChatExist from './getGroupChatExist';
import * as getGroupChatList from './getGroupChatList';
import * as getInfor from './getInfor';
import * as postPullCaseTeam from './postPullCaseTeam';
import * as postPullTeam from './postPullTeam';
import * as postPullUrlTeam from './postPullUrlTeam';
import * as postRoom from './postRoom';
import * as getRoom from './getRoom';
import * as postSmsMessage from './postSmsMessage';
import * as getStopRecordTask from './getStopRecordTask';
import * as deleteTeam from './deleteTeam';
import * as getTeam from './getTeam';
import * as getTeamRecord from './getTeamRecord';
import * as postTeamSend from './postTeamSend';
import * as getTourist from './getTourist';
import * as getUserInfor from './getUserInfor';
import * as getUserInforId from './getUserInforId';
import * as postUserUpdate from './postUserUpdate';

export {
  postChannel,
  getChannelAddress,
  getChannelId,
  getChannelList,
  putChannelStatus,
  getChannelStatus,
  getChannelToken,
  getCreateRecordTask,
  postCreateTeam,
  getGetRecordFileInfo,
  getGroupChatExist,
  getGroupChatList,
  getInfor,
  postPullCaseTeam,
  postPullTeam,
  postPullUrlTeam,
  postRoom,
  getRoom,
  postSmsMessage,
  getStopRecordTask,
  deleteTeam,
  getTeam,
  getTeamRecord,
  postTeamSend,
  getTourist,
  getUserInfor,
  getUserInforId,
  postUserUpdate,
};
