/**
 * @description 获取频道状态
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 频道id */
  cid?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.RetChannelStatusApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/channel/status',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
