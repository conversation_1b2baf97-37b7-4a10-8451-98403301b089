/**
 * @description 根据id获取用户信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.NeteaseyunxinUserApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/user/infor/id',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
