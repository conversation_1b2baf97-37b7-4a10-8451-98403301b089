/**
 * @description 直播列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 单页记录数，默认值为10，最多1000条。 */
  Records?: number;
  /** 要取第几页，默认值为1。 */
  Pnum?: number;
  /** 排序的域，支持的排序域为：ctime（默认） */
  Ofield?: string;
  /** 升序还是降序，1升序，0降序S */
  Sort?: number;
  /** 筛选频道状态，status取值：（0：空闲，1：直播，2：禁用，3：录制中） */
  Status?: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ItemPageResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/channel/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
