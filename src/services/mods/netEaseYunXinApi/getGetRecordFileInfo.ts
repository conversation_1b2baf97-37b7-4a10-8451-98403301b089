/**
 * @description 获取录制文件信息
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 房间id(创建录制和停止录制时会返回) */
  Cid?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.GetRecordFileInfoDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/get/record/file/info',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
