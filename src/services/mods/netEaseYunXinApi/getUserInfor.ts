/**
 * @description 生成用户的云信ID
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.NeteaseyunxinUserApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/user/infor',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
