/**
 * @description 停止录制任务
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 频道id */
  CName?: string;
  /** 任务id */
  TaskId?: string;
  /** 房间id(停止录制和查询云端录制文件时需要) */
  Cid?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StopRecordTaskDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/stop/record/task',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
