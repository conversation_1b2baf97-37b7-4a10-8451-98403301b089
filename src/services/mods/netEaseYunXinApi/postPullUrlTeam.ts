/**
 * @description 拉入群聊 token方式
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.PullGroupChatUrlParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/pull/url/team',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
