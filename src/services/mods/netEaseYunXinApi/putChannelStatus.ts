/**
 * @description 改变频道状态
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.ChannelStatusParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/channel/status',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
