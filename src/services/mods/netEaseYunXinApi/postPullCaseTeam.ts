/**
 * @description 拉入群聊 受邀诊断、复核
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/pull/case/team',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
