/**
 * @description 获取群聊id id病例id
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/team',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
