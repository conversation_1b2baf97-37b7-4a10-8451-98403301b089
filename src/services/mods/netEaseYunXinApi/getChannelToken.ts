/**
 * @description 获取房间token
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** Uid */
  UId: number;
  /** 房间名称 */
  ChannelName?: string;
  /** 过期时间 */
  ExpireAt: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/channel/token',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
