/**
 * @description 群聊是否存在
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<boolean> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/group/chat/exist',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
