/**
 * @description 创建录制任务
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 频道id */
  CName?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.CreateRecordTaskDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/create/record/task',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
