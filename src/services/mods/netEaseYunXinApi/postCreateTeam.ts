/**
 * @description 创建群聊 【病例,实时大体共用建议拆分】
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.CreateTeamParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.StringApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/create/team',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
