/**
 * @description 网易云信游客登录
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.NeteaseyunxinUserApiResult> {
  return RequestHelper.request(
    {
      url: '/api/neteaseyunxin/tourist',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
