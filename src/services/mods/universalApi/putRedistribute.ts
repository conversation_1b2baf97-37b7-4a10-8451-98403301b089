/**
 * @description 重新分配
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.Redistribute },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/universal/redistribute',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
