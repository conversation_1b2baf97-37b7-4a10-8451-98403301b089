/**
 * @description 发布医嘱/修改医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/expert/medicaladvice/create',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
