/**
 * @description 医嘱类型配置修改
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: Array<defs.BusinessTypeChargeParam> },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/ordertypeconfiguration/update',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
