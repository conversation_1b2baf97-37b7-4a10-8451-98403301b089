/**
 * @description 站点医嘱类型列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.MedicalOrderFeesDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/ordertypeconfiguration/site/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
