/**
 * @description 医嘱类型列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: {},
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.MedicalOrderFeesDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/ordertypeconfiguration/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
