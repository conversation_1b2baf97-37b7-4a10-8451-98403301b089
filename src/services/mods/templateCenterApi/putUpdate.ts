/**
 * @description 词汇编辑
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.DiagnosticVocabularyTextDto },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/templatecenter/update',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
