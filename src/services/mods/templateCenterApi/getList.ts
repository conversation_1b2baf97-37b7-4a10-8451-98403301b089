/**
 * @description 管理员诊断词汇列表
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** 词汇类型Code */
  typeCode?: string;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DiagnosticVocabularyTextDtoPageResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/templatecenter/list',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
