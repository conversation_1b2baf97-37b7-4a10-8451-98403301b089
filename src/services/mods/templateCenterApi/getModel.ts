/**
 * @description 渲染获取实体
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.DiagnosticVocabularyTextDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/admin/templatecenter/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
