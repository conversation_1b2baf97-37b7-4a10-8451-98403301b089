/**
 * @description 是否禁用按钮
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/doctor/order/disabled',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
