/**
 * @description 执行医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/doctor/order/executed',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
