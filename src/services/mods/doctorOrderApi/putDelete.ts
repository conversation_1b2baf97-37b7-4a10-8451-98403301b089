/**
 * @description 拒绝医嘱
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.IdParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/doctor/order/delete',
      method: 'put',
      params: params,
    },
    axiosConfig,
  );
}
