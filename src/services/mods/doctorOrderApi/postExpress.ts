/**
 * @description 修改快递地址
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export async function request(
  params: { bodyParams: defs.StringParam },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ApiResult> {
  return RequestHelper.request(
    {
      url: '/api/doctor/order/express',
      method: 'post',
      params: params,
    },
    axiosConfig,
  );
}
