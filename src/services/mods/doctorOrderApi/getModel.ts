/**
 * @description 获取具体医嘱内容
 */
import RequestHelper from '@/utils/BaseRequest';
import { AxiosRequestConfig } from 'axios';

export class Params {
  /** id */
  Id: number;
}

export async function request(
  params: { params: Params },
  axiosConfig?: AxiosRequestConfig,
): Promise<defs.ExpertDoctorsAdviceDtoApiResult> {
  return RequestHelper.request(
    {
      url: '/api/doctor/order/model',
      method: 'get',
      params: params,
    },
    axiosConfig,
  );
}
