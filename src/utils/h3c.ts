// h3c 辅助方法

import AWS from "aws-sdk";
import { v4 as uuidv4 } from "uuid";
import RcpWeb from "RcpWeb"; //项目配置
import axios from "axios";
import { method } from "lodash";
export class H3<PERSON><PERSON>elper {
  //设置截图ObjectUrl
  static async GetSliceScreenshotDirectory(
    store: any,
    fileName: string
  ): Promise<string> {
    const ossconfig: defs.H3cConfigDto = await store.getH3cConfig();
    const sliceScreenshot = ossconfig.SliceScreenshot as string;
    return (
      sliceScreenshot + "/" + uuidv4() + fileName.replace(/.sdpc$/, "") + ".jpg"
    );
  }

  //简单上传到ThumbnailDirectory文件夹
  static async SimpleUploadThumbnailDirectory(
    uuidName: string,
    data: Blob | ArrayBuffer | string
  ): Promise<boolean | undefined> {
    if (data) {
      try {
        const result = await API.accountApi.getH3c.request({});
        const params = {
          bucketName: result.Data?.BucketName as string,
          objectName: encodeURIComponent(uuidName),
        };
        const formData = new FormData();
        const blob = new Blob([data]);
        formData.append("file", blob);
        axios({
          method: "post",
          url: `${RcpWeb.BackEndUrl}/api/SliceService/file-store/buckets/${params.bucketName}/objects/${params.objectName}/small?bucketName=${params.bucketName}&objectName=${params.objectName}`,
          data: formData,
          headers: {
            Authorization: result.Data?.Token as string,
          },
        });

        return true;
      } catch (error) {
        console.log(error);
      }
    } else {
      return true;
    }
  }
  //dataUrl转成blob
  static dataURLtoBlob(dataurl: any) {
    var arr = dataurl.split(","),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    // return new File([u8arr])
    return new Blob([u8arr], { type: mime });
  }
}
