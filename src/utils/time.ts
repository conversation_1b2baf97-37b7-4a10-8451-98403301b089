import dayjs from "dayjs";

export const TimeRangeArr = <const>["今日", "昨日", "近七日"];

export type TimeRangeType = typeof TimeRangeArr[number];

export function GetTimeRange(type: TimeRangeType): [string, string] {
  switch (type) {
    case "今日":
      return [
        dayjs().startOf("date").format("YYYY-MM-DD HH:mm:ss"),
        dayjs().endOf("date").format("YYYY-MM-DD HH:mm:ss"),
      ];

    case "昨日":
      return [
        dayjs()
          .startOf("date")
          .subtract(1, "day")
          .format("YYYY-MM-DD HH:mm:ss"),
        dayjs().endOf("date").subtract(1, "day").format("YYYY-MM-DD HH:mm:ss"),
      ];

    case "近七日":
      return [
        dayjs()
          .startOf("date")
          .subtract(6, "day")
          .format("YYYY-MM-DD HH:mm:ss"),
        dayjs().endOf("date").format("YYYY-MM-DD HH:mm:ss"),
      ];
    default:
      return ["", ""];
  }
}
