export function awaitWrap<T, U = any>(
  promise: Promise<T>
): Promise<[U | null, T | null]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, null]>((err) => [err, null]);
}

/*
 *  获取URL中的某个参数
 */
export const getRequestParam = (paramName: string) => {
  let url = document.location.toString();
  console.log(url);
  // 特殊字符则解码
  url = decodeURI(url);
  const arrObj = url.split("?");
  if (arrObj.length > 1) {
    const arrPara = arrObj[1].split("&");
    let arr;
    for (let i = 0; i < arrPara.length; i++) {
      arr = arrPara[i].split("=");
      if (arr != null && arr[0] == paramName) {
        return decodeURIComponent(arr[1]);
      }
    }
    return "";
  } else {
    return "";
  }
};
/*
 * 获取URL所有参数
 */
export const getQueryParams = <T extends {}>(
  url = document.location.toString()
) => {
  // 特殊字符则解码
  url = decodeURI(url);
  const arr1 = url.split("?");
  const obj: any = {};
  if (arr1.length > 1) {
    const index = arr1[1].indexOf("#");
    arr1[1] = index == -1 ? arr1[1] : arr1[1].slice(0, index);
    const arr2 = arr1[1].split("&");
    for (let i = 0; i < arr2.length; i++) {
      const curArr: string[] = arr2[i].split("=");
      obj[curArr[0]] = decodeURIComponent(curArr[1]);
    }
  }
  return obj as { [key: string]: T };
};
