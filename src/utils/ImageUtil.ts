import axios from "axios";
import Rcp<PERSON><PERSON> from "RcpWeb"; //项目配置
import { ref, computed } from "vue";
export function ImageUtil() {
  const idSlices = ref();
  const hasPlug = ref();
  const BackEndUrl = RcpWeb.BackEndUrl;
  const readingStore = RcpWeb.readingStore;
  // 根据预签名地址获取ID
  const getSliceID = async (urlPath: string) => {
    try {
      const result = await API.accountApi.getOss.request({});
      var path =
        new RegExp(
          '^\\\\\\\\[a-zA-Z0-9._-]+(?:\\\\[^\\\\/:*?"<>|\\r\\n]+)*\\\\[^\\\\/:*?"<>|\\r\\n]+\\.[^\\\\/:*?"<>|\\r\\n]+$'
        ).test(urlPath) ||
        new RegExp(
          '^[a-zA-Z]:\\\\(?:[^\\\\/:*?"<>|\\r\\n]+\\\\)*[^\\\\/:*?"<>|\\r\\n]+\\.[^\\\\/:*?"<>|\\r\\n]+$'
        ).test(urlPath) ||
        new RegExp(
          /^\/(?:[\w\u4e00-\u9fa5.-]+\/)*[\w\u4e00-\u9fa5.-]+\.\w+$/
        ).test(urlPath) ||
        new RegExp("^(/[^/ ]*)+/?$").test(urlPath);

      let urlId = encodeURIComponent(urlPath);
      let url;
      if (path) {
        hasPlug.value = true;
        url = ``;
      } else {
        hasPlug.value = false;
        // url = `${BackEndUrl}/api/SliceService/image-slices/open-url/${urlId}`
        url = `${BackEndUrl}/api/SliceService/image-slices/open-server/${readingStore}/${result.Data?.BucketName}/${urlId}`;
      }
      await axios({
        url,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "X-Requested-With",
        },
        // data:{
        //     path :urlPath
        // },
        method: "post",
      }).then((res) => {
        idSlices.value = res;
      });
      return true;
    } catch (err) {
      idSlices.value = "";
      console.error(err);
      return false;
    }
  };
  // 根据ID获取信息
  const getExtraInfo = async (urlPath: string) => {
    try {
      let data;
      let url;

      url = `${BackEndUrl}/api/SliceService/image-slices/${urlPath}/info`;

      await axios
        .get(url, {
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "X-Requested-With",
          },
        })
        .then((res) => {
          data = res;
        });
      return data;
    } catch (err) {
      console.error(err);
      return false;
    }
  };

  return {
    // flagSliceID,
    idSlices,
    getSliceID,
    getExtraInfo,
    hasPlug,
  };
}
