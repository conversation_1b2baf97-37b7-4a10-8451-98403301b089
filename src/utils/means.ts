//日期格式转换
export const formatDate = (date: Date) => {
  let y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? Number("0" + m) : m;
  let d = date.getDate();
  d = d < 10 ? Number("0" + d) : d;
  const time = y + "-" + m + "-" + d;
  return time;
};
//格式化文件大小
export function GetFileSize(fileByte?: number): string {
  let message: string = "";
  const b = 1024;
  if (fileByte) {
    if (fileByte < Math.pow(b, 2)) {
      message = (fileByte / b).toFixed(2) + "KB";
    } else if (fileByte === Math.pow(b, 2)) {
      message = "1MB";
    } else if (fileByte > Math.pow(b, 2) && fileByte < Math.pow(b, 3)) {
      message = (fileByte / Math.pow(b, 2)).toFixed(2) + "MB";
    } else if (fileByte === Math.pow(b, 3)) {
      message = "1TB";
    } else if (fileByte > Math.pow(b, 3) && fileByte < Math.pow(b, 4)) {
      message = (fileByte / Math.pow(b, 3)).toFixed(2) + "GB";
    } else {
      message = "文件超过1TB";
    }
  }
  return message;
}
