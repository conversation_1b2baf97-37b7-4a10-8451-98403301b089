/**
 * 封装axios请求
 */
import useStore from "@/store";
import axios, { AxiosRequestConfig, Method } from "axios";
import { Toast } from "vant";
import RcpWeb from "RcpWeb"; //项目配置
export default class RequestHelper {
  static source = axios.CancelToken.source();

  // 控制同一错误，回车后反复弹窗
  static errorFlag = true;

  //初始化请求配置
  static async init(): Promise<any> {
    RequestHelper.setBaseUrl();

    RequestHelper.initInterceptors();
  }

  static setBaseUrl() {
    const remoteUrl: string = RcpWeb.RemoteUrl;
    axios.defaults.baseURL = remoteUrl;
  }

  static initInterceptors(): void {
    // 添加请求拦截器
    axios.interceptors.request.use(
      function (config: any) {
        const IsSliceService = config.url.includes("SliceService"); //后台块存储接口判断
        if (IsSliceService) {
          return config;
        }
        //设置token
        const { tokens } = useStore();
        const token = tokens.token;
        // const token = store.getters.token || LocalstorageHelper.getToken();
        if (token) {
          config.headers.Authorization = "Bearer " + token;
        }

        return config;
      },
      function (error: any) {
        // 对请求错误做些什么
        return Promise.reject(error);
      }
    );

    // 添加响应拦截器
    axios.interceptors.response.use(
      function (response: any) {
        // 对响应数据做点什么
        //@ts-ignore
        const data = response.data;
        if (data && data.code == "200") {
          if (Object.prototype.hasOwnProperty.call(data, "count")) {
            data.Count = data.count;
          }
          if (Object.prototype.hasOwnProperty.call(data, "data")) {
            data.Data = data.data;
          }
          if (Object.prototype.hasOwnProperty.call(data, "code")) {
            data.Code = data.code;
          }
          return data;
        } else {
          if (data.code !== undefined) {
            let message = "";
            if (data.message) {
              message = data.message;
            }
            if (data.data) {
              message = message + "," + data.data;
            }
            if (RequestHelper.errorFlag) {
              Toast.fail(message);

              return Promise.reject(response.data);
            }
          }
          return data;
        }
      },

      function (error: any) {
        // 对响应错误做点什么
        const response = error && error.response ? error.response : null;
        const { tokens } = useStore();
        if (
          response &&
          response.status &&
          (response.status == 401 || response.status == 403)
        ) {
          tokens.redirectLogin();
          return Promise.reject(error.response);
        } else if (response && response.data && response.data.code == 409) {
          Toast(response.data.message);
          tokens.redirectLogin();
        } else {
          console.log("请求错误", response);
          // Toast.fail(response.data?.message);
          return Promise.reject(error.response);
        }
      }
    );
  }

  //网络请求的处理
  static async request(
    api: {
      url: string;
      method: Method;
      params: { params?: any; bodyParams?: any };
    },
    axiosConfig: AxiosRequestConfig = {
      cancelToken: RequestHelper.source.token,
    }
  ): Promise<any> {
    const { params, bodyParams } = api.params;
    const config: AxiosRequestConfig = axiosConfig ? axiosConfig : {};

    config.url = api.url;
    config.method = api.method;
    if (params) {
      config.params = params;
    }
    if (bodyParams) {
      config.data = bodyParams;
    }
    try {
      const result = await axios.request(config);

      return result;
    } catch (error) {
      // 这里主要是判断这个错误是取消请求导致的还是其他原因导致的
      if (axios.isCancel(error)) {
      }
      return Promise.reject(error);
    }
  }

  //取消所有请求
  static async cancelAllRequest() {
    RequestHelper.source.cancel();
  }
}
