/**
 * 本地缓存
 */
export default class localStorageHelper {
  static keys = {
    token: "token_mobile",
  };
  static setToken(token: string): void {
    localStorage.setItem(localStorageHelper.keys.token, token);
  }
  static getToken(): string | null {
    return localStorage.getItem(localStorageHelper.keys.token);
  }
  static clearToken(keys: string): void {
    localStorage.removeItem(localStorageHelper.keys.token);
  }
}
