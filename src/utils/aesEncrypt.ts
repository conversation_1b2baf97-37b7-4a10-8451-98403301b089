/**
 * aes解密
 */
import CryptoJS from "crypto-js";

export class Aes {
  static key = "Rew2342fdfsfdgrgbxvfwr13dfFEgsvr";
  static iv = "9Efwgrsxv13dsfsD";
  static aesConfig: any = {
    iv: CryptoJS.enc.Utf8.parse(Aes.iv),
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  };

  //解密
  static Decrypt(data: string) {
    return CryptoJS.AES.decrypt(
      data,
      CryptoJS.enc.Utf8.parse(Aes.key),
      Aes.aesConfig
    ).toString(CryptoJS.enc.Utf8);
  }

  //加密
  static Encrypt(data: string) {
    return CryptoJS.AES.encrypt(
      data,
      CryptoJS.enc.Utf8.parse(Aes.key),
      Aes.aesConfig
    ).toString();
  }
}
