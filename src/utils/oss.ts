// oss 辅助方法

import OSS from "ali-oss";
import { v4 as uuidv4 } from "uuid";

export class OssHelper {
  //设置截图ObjectUrl
  static async GetSliceScreenshotDirectory(
    store: any,
    fileName: string
  ): Promise<string> {
    const ossconfig: defs.OssConfigDto = await store.getOssConfig();
    const sliceScreenshot = ossconfig.SliceScreenshot as string;
    return (
      sliceScreenshot + "/" + uuidv4() + fileName.replace(/.sdpc$/, "") + ".jpg"
    );
  }

  //简单上传到ThumbnailDirectory文件夹
  static async SimpleUploadThumbnailDirectory(
    store: any,
    uuidName: string,
    data: Blob | ArrayBuffer
  ): Promise<boolean | undefined> {
    if (data) {
      try {
        const ossconfig = await store.getOssConfig();;
        const client = new OSS({
          accessKeyId: ossconfig.AccessKeyId as string,
          accessKeySecret: ossconfig.AccessKeySecret as string,
          bucket: ossconfig.BucketName as string,
          stsToken: ossconfig.Token as string,
          endpoint: ossconfig.Endpoint as string,
        });

        // const uuidName = await OssHelper.GetThumbnailDirectory(store, fileName);
        // await client.put(uuidName, data);
        await client.put(
          uuidName,
          new File([data], uuidName, { type: "image/jpg" })
        );
        return true;
      } catch (error) {
        console.log(error);
      }
    } else {
      return true;
    }
  }

  //dataUrl转成blob
  static dataURLtoBlob(dataurl: any) {
    var arr = dataurl.split(","),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    // return new File([u8arr])
    return new Blob([u8arr], { type: mime });
  }
}
