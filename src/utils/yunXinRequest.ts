/**
 * 封装fetch请求
 */

export default class YunXinRequestHelper {
  static baseUrl = "http://192.168.10.50:8003";

  //弹窗显示错误信息
  static showErrorMessage(message: string, code: any) {
    // alert(message);
  }

  //网络请求的处理
  static async request(url: string, params?: string): Promise<any> {
    try {
      url = params
        ? YunXinRequestHelper.baseUrl + "/" + url + params
        : YunXinRequestHelper.baseUrl + "/" + url;
      const response = await fetch(url, {
        method: "GET",
      });
      const jsonObj = await response.json();
      return jsonObj && jsonObj.data ? jsonObj.data : {};
    } catch (error) {
      return Promise.reject(false);
    }
  }

  /**
   * 获取网易云信信息
   * api/neteaseyunxin/infor
   */
  static async getYunxinInfo(): Promise<{
    AppKey: string;
    AppSecret: string;
  }> {
    try {
      return await YunXinRequestHelper.request("api/neteaseyunxin/infor");
    } catch (error) {
      return Promise.reject();
    }
  }

  /**
   * 获取用户信息
   * api/neteaseyunxin/user/infor
   */
  static async getUserInfo(): Promise<{
    AccId: string;
    Token: string;
    RealName: string;
  }> {
    try {
      return await YunXinRequestHelper.request("api/neteaseyunxin/user/infor");
    } catch (error) {
      return Promise.reject();
    }
  }
  /**
   * 创建房间
   * api/neteaseyunxin/room
   */
  static async createRoom(channelName: string, caseId: number): Promise<any> {
    const response = await fetch(
      YunXinRequestHelper.baseUrl + "/api/neteaseyunxin/room",
      {
        headers: {
          "content-type": "application/json-patch+json",
        },
        method: "POST",
        body: JSON.stringify({
          ChannelName: channelName,
          CaseId: caseId,
        }),
      }
    );
    const jsonObj = await response.json();
    return jsonObj && jsonObj.data ? jsonObj.data : {};
  }
}
