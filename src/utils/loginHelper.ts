
import { computed } from "vue";
import { useRoute } from "vue-router";

export class LoginHelper {
  static account: string;
  static setAccount(str: string) {
    LoginHelper.account = str;
  }
  static getAccount() {
    return LoginHelper.account;
  }
}

export default function useLogin() {
  const route = useRoute();
  const account = computed(() => route.params.account);
  function setAccount() {
    // console.log("autoLogin-----------------------------");
    // console.log(route?.params?.account);
    if (route.params && route.params.account) {
      LoginHelper.setAccount(route.params.account as string);
    }
  }
  return {
    account,
    setAccount
  };
}
