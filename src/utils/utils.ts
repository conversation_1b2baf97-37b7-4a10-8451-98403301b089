export function Min(x: number, y: number): number {
  return x - y < 0 ? x : y;
}
/**
 * 动态加载script
 * @param src 资源地址
 */
export function LoadScript(src: string): Promise<HTMLScriptElement> {
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = src;
    script.setAttribute("type", "text/javascript");
    script.onload = () => {
      resolve(script);
    };

    script.onerror = () => {
      reject();
    };
    document.body.append(script);
  });
}

//验证手机号
export function CheckPhone(phone: string): boolean {
  return /^1[3456789]\d{9}$/.test(phone);
}

export function SampleLocationName(item: string) {
  try {
    return JSON.parse(item).join("，");
  } catch (error) {
    return item;
  }
}
