export class WhereParams {
  // 1是模糊查询，0是等于
  // ConditionalType: 0 | 1;
  // 0与查询，1是或查询
  // Key: 0 | 1
  constructor(
    public FieldName: string,
    public FieldValue?: any,
    public ConditionalType: 0 | 1 = 1,
    public Key: 0 | 1 = 1
  ) {}
}

export class AdvancedSearchItem {
  Key: number;
  Value: WhereParams;
}

//高级查询
export class AdvancedSearch {
  protected list: {
    ConditionalList: AdvancedSearchItem[];
  }[] = [{ ConditionalList: [] }];
  constructor(advancedSearchList?: WhereParams[]) {
    if (advancedSearchList && advancedSearchList.length) {
      this.addParams(advancedSearchList);
    }
  }

  //添加单个查询条件
  addParam(item: WhereParams) {
    // const key = this.list[0].ConditionalList.length;
    const Key = item.Key;
    delete (item as any).Key;
    this.list[0].ConditionalList.push({ Key, Value: item });
  }

  //添加多个查询条件
  addParams(advancedSearchList: WhereParams[]) {
    advancedSearchList.forEach((item) => {
      this.addParam(item);
    });
  }
  //转换查询参数
  changeArrayToString(where: any[]): string | undefined {
    // return where ? JSON.stringify(where).replace(/\"/g, "'") : undefined;
    return where ? JSON.stringify(where) : undefined;
  }
  //获取高级查询条件字符串
  getParams(): string | undefined {
    return this.getConditionalList()
      ? this.changeArrayToString(this.list)
      : undefined;
  }
  //获取高级查询条件字符串
  //获取高级查询列表
  // getConditionalList(): AdvancedSearchItem[] {
  //   return this.list[0].ConditionalList;
  // }
  //获取高级查询列表
  getConditionalList(): AdvancedSearchItem[] | null {
    return this.list[0].ConditionalList && this.list[0].ConditionalList.length
      ? this.list[0].ConditionalList
      : null;
  }
}

// /**
//  * 使用
//  */
// const params = { AdvancedSearch: "" };
// const advancedSearch = new AdvancedSearch();
// advancedSearch.addParam(new WhereParams("keyword", "test"));
// params.AdvancedSearch = advancedSearch.getParams();
