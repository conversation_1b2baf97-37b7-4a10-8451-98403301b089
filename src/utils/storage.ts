export enum LocalStorageName {
  /** 登录用户信息 */
  user = "userInfo_mobile",

  /** 记住密码 */
  remember = "remember_mobile",
  isTestVersion = "isTestVersion_mobile",
}

/**
 * 获取LocalStorage缓存数据
 * @param name key
 * @param key  对象路径
 */
export function getLocalStorage(name: LocalStorageName, key: string[] = []) {
  const value = localStorage.getItem(name);
  if (value && key) {
    const json = JSON.parse(value);
    if (typeof json === "object") {
      return key.reduce((pre: Record<string, any>, cur) => {
        return pre[cur];
      }, json);
    }
    return json;
  }
  return undefined;
}

/**
 * 设置LocalStorage数据
 * @param name key
 * @param key 对象路径
 * @param value 值
 */
export function setLocalStorage(
  name: LocalStorageName,
  key: string[],
  value: any
): void {
  if (key.length === 0) {
    localStorage.setItem(name, JSON.stringify(value));
    return;
  }

  const str = localStorage.getItem(name);
  let data = str ? JSON.parse(str) : {};

  if (typeof data !== "object") {
    data = {};
  }

  key.reduce((pre: Record<string, any>, cur, index) => {
    if (!pre.hasOwnProperty(cur)) {
      pre[cur] = {};
    }

    if (index === key.length - 1) {
      pre[cur] = value;
    }
    return pre[cur];
  }, data);

  localStorage.setItem(name, JSON.stringify(data));
}

/**
 * 删除LocalStorage
 * @param name
 */
export function clearLocalStorage(name?: LocalStorageName) {
  if (name) {
    localStorage.removeItem(name);
  } else {
    localStorage.clear();
  }
}
