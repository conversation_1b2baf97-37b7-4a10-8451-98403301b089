<template>
  <div>
    <!-- 占位 -->
    <div class="occupied"></div>
    <div class="case-bottom">
      <!-- 插槽 -->
      <slot name="specialist"></slot>
      <div class="case-bottom-container">
        <template v-if="props.btnShow">
          <div class="case-diagnose" @click="gotoVideoPage()">
            {{ props.leftName }}
          </div>
        </template>
        <div class="case-center" @click="diagnoseClick">
          {{ props.rightName }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  leftName: {
    type: String,
    default: "保存报告",
  },
  rightName: {
    type: String,
    default: "发布报告",
  },
  btnShow: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(["leftBtn", "rightBtn"]);
const gotoVideoPage = () => {
  emits("leftBtn");
};
const diagnoseClick = () => {
  emits("rightBtn");
};
</script>

<style lang="scss" scoped>
//底部区域
.case-bottom {
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 88px;
  background: #ffffff;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
  opacity: 1;
}

.case-bottom-container {
  padding: 13px 14px 0 14px;
  display: flex;
}

.case-diagnose {
  margin-right: 16px;
  width: 100px;
  height: 32px;
  background: #ffffff;
  color: #650faa;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  border: 1px solid #650faa;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}

.case-center {
  flex: 1;
  height: 32px;
  background: #650faa;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  color: #fff;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
</style>
