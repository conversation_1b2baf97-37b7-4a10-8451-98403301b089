<template>
  <div class="pdfBox" :style="pdfBoxheight">
    <div id="previewPdf"></div>
  </div>
</template>
<script>
import pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";
export default {
  name: "pdfh5",
  data() {
    return {
      pdfh5: null,
    };
  },
  methods: {
    openPdf(url) {
      //url：PDF文件地址
      this.pdfh5 = new pdfh5("#previewPdf", {
        pdfurl: url,
      });
      this.pdfh5.on("success", () => {
        console.log("pdf渲染成功");
      });
    },
  },
  computed: {
    pdfBoxheight() {
      return {
        // height: "90vh"
      };
    },
  },
};
</script>
<style scoped>
.pdfBox {
  width: 100vw;
  height: 100%;
  background: #fff;
  overflow: hidden;
  z-index: 99;
}

#previewPdf {
  height: calc(100vh - 50px);
}
</style>
