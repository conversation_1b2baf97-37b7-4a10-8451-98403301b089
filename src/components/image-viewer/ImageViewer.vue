<template>
  <div>
    <div class="case-image">
      <div class="case-dropdown">
        <div @click="isShow = true">
          <van-icon name="ellipsis" size="25" color="#8B96A6" />
        </div>
        <div class="case-dropdown-left">
          <van-dropdown-menu active-color="#650FAA">
            <van-dropdown-item
              v-model="value1"
              :options="option"
              @change="changeClick"
            />
          </van-dropdown-menu>
        </div>
        <div v-if="sliceDescription" class="evaluate evaluates">已评价</div>
        <div v-else class="evaluate">未评价</div>
      </div>

      <div
        class="image-box"
        :style="[imageHeight]"
        :class="{ fullscreen: isFullscreen }"
      >
        <ImageBrowser ref="imageBrowser" slideUrl />

        <!-- 缩放倍率 start -->
        <ImageZoom
          :viewer="viewer"
          :zoom="zoom"
          :rate="rate"
          :registration3D="registration3D"
          v-if="viewer"
        />
        <!-- 缩放倍率 end -->

        <div class="enlargeClass" @click="toggleFullscreen">
          <van-icon name="enlarge" />
        </div>
        <div class="imageScalesClass">
          <ImageScales
            v-if="viewer"
            :LoadingCompletedOrNot="LoadingCompletedOrNot"
            :sdpc="sdpc"
            :zoom="zoom"
            :registration3D="registration3D"
          />
        </div>
      </div>
      <!-- 切片标注信息 start -->
      <div v-show="isShow" class="informatization">
        <Informatization :selectData="selectData" @is-show="isShowClick" />
      </div>
      <!-- 切片标注信息 end -->
    </div>
    <div class="occupied"></div>
    <!-- 工具 start -->
    <ImageEditor
      class="image-editor"
      ref="imageEditor"
      :viewer="viewer"
      :disabled="true"
      @screenshot="screenshot"
      v-if="viewer && imageEditorss"
    />

    <!-- 工具 end -->
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, Ref } from "vue";
import ImageBrowser from "./components/imageBrowser/index.vue";
import ImageEditor from "./components/imageEditor/index.vue";
import ImageScales from "./components/imageScales/index.vue";
import Informatization from "./Informatization.vue";
import useStore from "@/store";
import { useImageBrowser } from "@/hooks/useImageBrowser";
import { useSliceList } from "@/hooks/useSliceList";
export default defineComponent({
  name: "MyImageBrowser",
  components: {
    ImageBrowser,
    ImageEditor,
    Informatization,
    ImageScales,
  },
  props: {
    imageEditorss: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    // const hevcDecoderUrl: any = 'https://rcp.sqray.com:5500/hevc.js'https://dev.sqray.com/webapp; //切片地址
    // const hevcDecoderUrl: any = '/webapp/hevc.js'; //切片地址
    // const hevcDecoderWasmUrl: any = "/webapp/hevc.wasm"; //切片地址
    const { imageEditorss } = toRefs(props);

    const imageBrowser = ref<InstanceType<typeof ImageBrowser>>();
    const {
      viewer,
      refreshViewer,
      zoom,
      rate,
      registration3D,
      sdpc,
      LoadingCompletedOrNot,
    } = useImageBrowser(imageBrowser as any);
    //浏览组件
    const isShow = ref(false);
    //关闭isShow
    const isShowClick = (e: boolean) => {
      isShow.value = e;
    };
    const { zjsection, tokens, label } = useStore();
    const route = useRoute();
    const { id } = route.query;
    const { refreshViewers } = useSliceList(); //列表组件hooks
    const isFullscreen = ref(false);
    const option: Ref<any> = ref([]);
    const sliceDescription = computed(() => {
      return (
        (zjsection.selectSection?.Evaluation &&
          zjsection.selectSection?.Evaluation < 4) ||
        (zjsection.selectSection?.Evaluation === 4 &&
          zjsection.selectSection?.SliceDescription)
      );
    });
    zjsection.sectionList.map((item, index) => {
      // if (item.SliceUrl) {
      option.value.push({
        ...item,
        text:
          item.FileName && item.FileName.length > 16
            ? `（${item.StatusName}）` + item.FileName?.slice(0, 16) + "..."
            : `（${item.StatusName}）` + item.FileName,
        value: index,
      });
      // }
    });
    //刷新切片
    const refreshViewersClcik = (
      PathologyNumber: string,
      isFrozen: boolean
    ) => {
      console.log(PathologyNumber, "11");

      refreshViewers(
        selectData.value,
        useImageBrowser(imageBrowser as any),
        PathologyNumber,
        isFrozen
      );
    };

    //导航是否显示
    const value1 = ref(option.value.length > 0 && option.value[0].value);
    //选中的数据
    const selectData = computed(() => {
      zjsection.selectSectionFn(option.value[value1.value]);
      return option.value[value1.value];
    });
    // const slideUrl = ref(option.value[0].SliceUrl);
    if (id) {
      option.value.map((item: { Id: number; value: any; SliceUrl: any }) => {
        if (item.Id === Number(id)) {
          value1.value = item.value;
        }
      });
    }
    function toggleFullscreen() {
      isFullscreen.value = !isFullscreen.value;
    }
    if (!tokens.token) {
      setTimeout(() => {
        changeClick();
      }, 100);
    }
    //防抖

    const timeout: any = ref(null);
    function debounce(
      doSomeThing: () => void,
      time: number = 2000
    ): () => void {
      return () => {
        if (timeout.value) {
          clearTimeout(timeout.value);
        }
        timeout.value = setTimeout(() => {
          doSomeThing();
        }, time);
      };
    }
    //切换切片
    const changeClick = () => {
      debounce(async () => {
        label.deleteLabel();
        emit("changeClicks");
      }, 2000)();
    };
    //截图
    function screenshot(src: string) {
      emit("screenshot", src);
    }
    const imageHeight = computed(() => {
      if (isFullscreen.value) {
        return {
          height: "100%",
          zIndex: 10000,
        };
      }
      if (!id) {
        return {
          height: "483px",
        };
      } else {
        return {
          height: "500px",
        };
      }
    });
    return {
      imageBrowser,
      viewer,
      isFullscreen,
      screenshot,
      changeClick,
      isShow,
      selectData,
      option,
      value1,
      refreshViewer,
      id,
      imageHeight,
      refreshViewersClcik,
      toggleFullscreen,
      zoom,
      sdpc,
      isShowClick,
      registration3D,
      imageEditorss,
      rate,
      LoadingCompletedOrNot,
      zjsection,
      sliceDescription,
    };
  },
});
</script>
<style lang="scss" scoped>
.case-image {
  position: relative;
  background-color: #fff;
  margin: 13px 14px 0 14px;
  padding-bottom: 8px;
}

.case-dropdown {
  padding: 0 0 0 8px;
  display: flex;
  align-items: center;
}

.image-box {
  position: relative;
  margin: 8px 8px 0 8px;
  height: 483px;
  border: 1px solid #e6ecfb;
  background-color: #fff;
}

.fullscreen {
  background-color: white;
  position: fixed;
  top: -9px;
  left: -9px;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.enlargeClass {
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 30px;
  color: #8b96a6;
  z-index: 9;
}
.imageScalesClass {
  position: absolute;
  left: 12px;
  bottom: 30px;
  z-index: 9;
}
:deep(.van-dropdown-menu__bar) {
  box-shadow: none;
}

.van-dropdown-item.van-dropdown-item--down {
  margin: 0 14px;
}

.case-dropdown-left {
  width: 100%;
  // margin: 0 11px 0 0px;
}

// :deep(#image-browser-navigator) {
//   position: absolute !important;
//   left: 20px !important;
//   top: 20px !important;
//   z-index: 8 !important;
//   border: 1px solid #ff0000 !important;
// }

.informatization {
  position: absolute;
  top: 0;
  left: -14px;
  width: 238px;
  height: 525px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.15);
  border-radius: 0px 4px 4px 0px;
  opacity: 1;
  z-index: 1001;
}

div#image-browser {
  height: 100%;
}
.occupied {
  height: 150px;
  width: 100%;
}
:deep(.van-cell__title) {
  flex: 1.5;
}
:deep(.van-cell__value) {
  flex: 0.5;
}

.evaluate {
  width: 60px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff5ea;
  border-radius: 4px 0px 0px 4px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #ff8743;
}
.evaluates {
  background: #f1f8e7;
  color: #6ab10e;
}
</style>
