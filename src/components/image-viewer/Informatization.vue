<template>
  <div class="infrom">
    <div class="infrom-left">
      <div
        class="infrom-left-btn"
        :style="isSelect ? 'background-color:#650FAA;color:#fff' : ''"
        @click="isSelectClick"
      >
        切片信息
      </div>
      <div
        class="infrom-left-btn"
        :style="isMark ? 'background-color:#650FAA;color:#fff' : ''"
        @click="isMarkClick"
      >
        标注信息
      </div>
    </div>
    <iconpark-icon
      @click="takeBack"
      class="Component3"
      name="Component3"
    ></iconpark-icon>
  </div>

  <div v-if="isSelect">
    <div class="infrom-title">{{ props.selectData.FileName }}</div>
    <div class="infrom-image">
      <van-image
        class="imageClass1"
        fit="contain"
        :src="props.selectData.SliceLabel"
        @click="showImagePreview(props.selectData.SliceLabel)"
        :show-loading="false"
        :show-error="false"
      />
      <van-image
        class="imageClass2"
        fit="contain"
        :src="props.selectData.SliceMacroImageUrl"
        @click="showImagePreview(props.selectData.SliceMacroImageUrl)"
        :show-loading="false"
        :show-error="false"
      />
    </div>
    <div class="infrom-title-name">
      切片描述：{{ zjsection.selectSection.Remark }}
    </div>
    <div class="infrom-title-name">
      切片大小：{{ Math.trunc(props.selectData.FileSize / 1024 / 1024) }}（MB）
    </div>
    <div class="infrom-title-name">
      图像分辨率：{{
        `${zjsection.sectionDetail.srcWidth || 0} * ${
          zjsection.sectionDetail.srcHeight || 0
        }`
      }}
    </div>
    <div class="infrom-title-name">
      扫描倍率：{{ zjsection.sectionDetail.rate }}X
    </div>
    <div class="infrom-title-name">
      扫描分辨率：{{ zjsection.sectionDetail.ruler?.toFixed(6) }}um/pixel
    </div>
    <div
      v-if="zjsection.fillingDetails?.CaseTypeName"
      class="infrom-title-name"
    >
      病理类型：{{ zjsection.fillingDetails?.CaseTypeName }}
    </div>
    <div
      v-if="zjsection.fillingDetails?.SampleLocationName"
      class="infrom-title-name"
    >
      取材部位：{{
        SampleLocationName(zjsection.fillingDetails?.SampleLocationName)
      }}
    </div>
    <div v-if="zjsection.fillingDetails?.SiteName" class="infrom-title-name">
      送检医院：{{ zjsection.fillingDetails?.SiteName }}
    </div>
    <div v-if="zjsection.selectSection?.CreateTime" class="infrom-title-name">
      上传时间：{{ zjsection.selectSection?.CreateTime }}
    </div>

    <div v-if="disqualification" class="infrom-title-name">
      <van-radio-group
        v-model="checked"
        icon-size="14px"
        direction="horizontal"
        checked-color="#650FAA"
        @click="evaluationChange"
      >
        <van-radio :name="1">优</van-radio>
        <van-radio :name="2">良</van-radio>
        <van-radio :name="3">合格</van-radio>
        <van-radio :name="4">不合格</van-radio>
      </van-radio-group>
      <div class="disqualification" v-if="disqualificationShow">
        <van-checkbox-group
          v-model="disqualificationChecked"
          icon-size="14px"
          checked-color="#650FAA"
          @click="disqualificationCheckedClick"
        >
          <van-checkbox shape="square" name="细胞或组织结构缺失"
            >细胞或组织结构缺失</van-checkbox
          >
          <van-checkbox shape="square" name="细胞或组织延展变形"
            >细胞或组织延展变形</van-checkbox
          >
          <van-checkbox shape="square" name="有杂质或污染物"
            >有杂质或污染物</van-checkbox
          >
          <van-checkbox shape="square" name="染色过度或不足"
            >染色过度或不足</van-checkbox
          >
          <van-checkbox shape="square" name="切片厚度不均匀"
            >切片厚度不均匀</van-checkbox
          >
          <van-checkbox shape="square" name="切片断裂或损坏"
            >切片断裂或损坏</van-checkbox
          >
        </van-checkbox-group>
      </div>
    </div>
  </div>
  <div class="isMark-class" v-if="isMark">
    <van-swipe-cell
      v-for="(item, index) in name"
      :key="item._id"
      @touchstart="gtouchstart(item)"
      @touchmove="gtouchmove()"
      @touchend="showDeleteButton(item)"
      class="isMarkClass"
    >
      <div class="swipedetails">
        <div class="checkboxClass">
          <van-checkbox
            v-if="checkboxShow"
            v-model="item.checkedShow"
            shape="square"
            icon-size="15px"
            checked-color="#650FAA"
          >
          </van-checkbox>
        </div>
        <div class="isMarkClass-left">
          <div class="iconparks">
            <iconpark-icon
              v-if="item?.className == 'Rect'"
              :style="`color:${item.attrs.stroke}`"
              name="weibiaoti-1huaban1fuben10"
            ></iconpark-icon>
            <iconpark-icon
              v-else-if="item.className == 'Ellipse'"
              :style="`color:${item.attrs.stroke}`"
              name="weibiaoti-1huaban1fuben9"
            >
            </iconpark-icon>
            <iconpark-icon
              v-else-if="item.className == 'Line'"
              :style="`color:${item.attrs.stroke}`"
              name="weibiaoti-1huaban1fuben12"
            >
            </iconpark-icon>
          </div>
          <div v-if="item?.className == 'Rect'" class="iconparks-title">
            {{ "矩形" }}
          </div>
          <div v-else-if="item.className == 'Ellipse'" class="iconparks-title">
            {{ "圆形" }}
          </div>
          <div v-else-if="item.className == 'Line'" class="iconparks-title">
            {{ "绘制" }}
          </div>
        </div>
        <div class="isMarkClass-right">{{ item.attrs.remarkValue }}</div>
      </div>
      <template #right>
        <div class="swipe-delete" @click="clearMarker(item)">删除</div>
      </template>
    </van-swipe-cell>

    <div
      v-if="name.length > 0 && !checkboxShow"
      class="isMark-delete"
      @click="clearMarker(0)"
    >
      全部清除
    </div>
    <div
      v-if="name.length > 0 && checkboxShow"
      class="isMark-delete"
      @click="clearMarkers"
    >
      删除
    </div>
  </div>
  <preview ref="previews"></preview>
</template>
<script lang="ts" setup>
import bus from "@/bus";
import { Ref } from "vue";
import "vant/es/image-preview/style";
import Preview from "vue3-preview";
import useStore from "@/store";
import { cloneDeep } from "lodash";
import { SampleLocationName } from "@/utils/utils";
const props = defineProps({
  selectData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  disqualification: {
    type: Boolean,
    default: true,
  },
});

const { zjsection } = useStore();

const checked = ref();
const disqualificationChecked = ref<string[]>([]);
const disqualificationShow = ref(false);
//当前是第几张切片
const index = ref(0);
const emits = defineEmits(["isShow"]);
//收回
const takeBack = () => {
  emits("isShow", false);
};

//全部清除
const clearMarker = (item: any) => {
  bus.$emit("clearMarkerBus", item);
};
//删除标注
const clearMarkers = () => {
  name.value.forEach((item: any) => {
    if (item.checkedShow) {
      bus.$emit("clearMarkerBus", item);
    }
  });
};
const previews = ref();
//查看图片
const showImagePreview = (signerImgUrl: string | null) => {
  previews.value?.open(signerImgUrl);
};
//切片是否选中
const isSelect = ref(true);
const isSelectClick = () => {
  isSelect.value = true;
  isMark.value = false;
};
//标注是否选中
const isMark = ref(false);
const isMarkClick = () => {
  isMark.value = true;
  isSelect.value = false;
};

const name: Ref<any> = ref([]);
bus.$on("Deom", (data: any) => {
  data._drawer._layer.children.map((item: { checkedShow: boolean }) => {
    item.checkedShow = false;
  });
  name.value = cloneDeep(data._drawer._layer.children);
  // console.log(name.value);
});
const checkboxShow = ref(false);
const timeOutEvent = ref(0);
//长按事件（起始）
const gtouchstart = (item: any) => {
  timeOutEvent.value = setTimeout(() => {
    longPress(item);
  }, 500);
};
//手释放，如果在500毫秒内就释放，则取消长按事件，此时可以执行onclick应该执行的事件
const showDeleteButton = (item: any) => {
  clearTimeout(timeOutEvent.value);
  if (timeOutEvent.value != 0) {
  }
};
//如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
const gtouchmove = () => {
  clearTimeout(timeOutEvent.value);
  timeOutEvent.value = 0;
};
////真正长按后应该执行的内容
const longPress = (val: any) => {
  timeOutEvent.value = 0;
  checkboxShow.value = !checkboxShow.value;
};

const evaluationChange = () => {
  const index = zjsection.sectionList.findIndex(
    (item) => item.Id === zjsection.selectSection.Id
  );

  if (checked.value !== 4 && zjsection.selectSection.SliceDescription) {
    zjsection.casequalityEvaluationFalse([]);
    zjsection.selectSection.SliceDescription = "";
    zjsection.sectionList[index].SliceDescription = "";
  }
  if (checked.value === 4) {
    disqualificationShow.value = !disqualificationShow.value;
    if (zjsection.selectSection.SliceDescription) {
      disqualificationChecked.value =
        zjsection.selectSection.SliceDescription?.split(",");
    }
  } else {
    disqualificationShow.value = false;
    disqualificationChecked.value = [];
  }
  zjsection.sectionList[index].Evaluation = checked.value;
  zjsection.selectSection.Evaluation = checked.value;
  zjsection.casequalityEvaluation(checked.value);
};

const disqualificationCheckedClick = () => {
  const index = zjsection.sectionList.findIndex(
    (item) => item.Id === zjsection.selectSection.Id
  );
  zjsection.sectionList[index].SliceDescription =
    disqualificationChecked.value.toString();
  zjsection.selectSection.SliceDescription =
    disqualificationChecked.value.toString();
  zjsection.casequalityEvaluationFalse(disqualificationChecked.value);
};
watch(
  () => zjsection.selectSection,
  (val: defs.CaseSliceDto) => {
    if (val) {
      checked.value = val?.Evaluation || 0;
      disqualificationChecked.value = [];
      nextTick(() => {
        disqualificationShow.value = false;
        if (checked.value === 4) {
          disqualificationShow.value = true;
          if (zjsection.selectSection.SliceDescription) {
            disqualificationChecked.value =
              zjsection.selectSection.SliceDescription?.split(",");
          }
        } else {
          disqualificationShow.value = false;
        }
      });
    }
  },
  { immediate: true }
);
onMounted(() => {
  if (zjsection.sectionList.length > 0) {
    zjsection.selectSectionFn(zjsection.sectionList[0]);
    checked.value = zjsection.selectSection?.Evaluation || 0;
  }
});
</script>
<style lang="scss" scoped>
.infrom {
  padding: 8px 12px 8px 8px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e6ecfb;
}

.infrom-left {
  padding: 2px 0;
  align-items: center;
  display: flex;
  width: 164px;
  height: 32px;
  background: #f3f6fd;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
}

.infrom-left-btn {
  width: 80px;
  height: 28px;
  border-radius: 14px 14px 14px 14px;
  opacity: 1;
  font-size: 14px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #4d545f;
  text-align: center;
  line-height: 28px;
}

.Component3 {
  font-size: 12px;
  transform: rotate(90deg);
  color: #8c8c8c;
}

.infrom-title {
  margin-left: 10px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #4d545f;
  line-height: 33px;
  word-wrap: break-word;
}

.infrom-image {
  margin-left: 12px;
  display: flex;
  width: 214px;
  height: 53px;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid #e6ecfb;
}

.imageClass1 {
  width: 45%;
  // 旋转
  // transform: rotate(90deg);
}

.imageClass2 {
  width: 45%;
}

.infrom-title-name {
  position: relative;
  margin-left: 10px;
  padding: 8px 0;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
  border-bottom: 1px solid #e6ecfb;
  word-break: break-all;
}

.isMark-class {
  height: 471px;
  overflow: auto;
}

.isMarkClass {
  align-items: center;
  margin: 8px 12px 8px 12px;
  padding-bottom: 7px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
  border-bottom: 1px solid #e6ecfb;
}

.swipedetails {
  display: flex;
}

.checkboxClass {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
}

.iconparks {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 28px;
  width: 28px;
  height: 28px;
  background: #eeebfe;
  opacity: 1;
  border-radius: 28px;
}

.iconparks-title {
  margin-left: 4px;
}

.isMarkClass-left {
  // width: 65px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e6ecfb;
}

.isMarkClass-right {
  display: flex;
  align-items: center;
  width: 150px;
  margin-left: 5px;
  word-break: break-all;
}

.swipe-delete {
  width: 54px;
  height: 32px;
  background: #ff7272;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  text-align: center;
  line-height: 32px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #ffffff;
}

.isMark-delete {
  margin: 16px 35px;
  height: 32px;
  background: #ff7272;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #ffffff;
}

// :deep(img.van-image__img) {
//   transform: rotate(90deg);
// }

// :deep(.van-image-preview__index) {
//   display: none;
// }
// :deep(.van-swipe-item.van-image-preview__swipe-item) {
//   transform: rotate(45deg);
// }
// :deep(.van-popup.van-popup--center.van-image-preview) {
//   transform: rotate(90deg) !important;
// }

.disqualification {
  box-sizing: border-box;
  padding: 11px;
  position: absolute;
  top: -195px;
  right: 10px;
  width: 175px;
  height: 194px;
  background: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 6px 6px 6px 6px;
  //最下方插个三角形
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 195px;
    right: 50px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #fff;
    border-bottom: 9px solid transparent;
    z-index: 12;
  }
  &::before {
    content: "";
    display: block;
    position: absolute;
    top: 195px;
    right: 49px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid #e5e5e5;
    border-bottom: 9px solid transparent;
    z-index: 10;
  }
}
:deep(.van-checkbox) {
  margin-bottom: 10px;
}
</style>
