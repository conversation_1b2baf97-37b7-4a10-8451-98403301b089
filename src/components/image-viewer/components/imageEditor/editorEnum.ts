//画图状态
export enum DrawStatus {
  drawing = "drawing", //正在画图
  end = "end", //结束画图
  select = "select", //选择
}

//绘画形状
export enum DrawType {
  rect = "rect",
  line = "line",
  ellipse = "ellipse",
  arrow = "arrow",
}

//工具类型
export enum BarType {
  rect = "rect",
  line = "line",
  ellipse = "ellipse",
  arrow = "arrow",
}

//绘画形状
export enum BtnType {
  rect = "btn_rect",
  line = "btn_brush",
  ellipse = "btn_elips",
  arrow = "arrow",
  line_pen = "btn_pen", //线性 五边形
}
