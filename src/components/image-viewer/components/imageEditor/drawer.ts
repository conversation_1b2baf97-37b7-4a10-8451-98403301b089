/**
 * 绘画器
 */
import { Circle } from "konva/lib/shapes/Circle";
import { Ellipse } from "konva/lib/shapes/Ellipse";
import { Line } from "konva/lib/shapes/Line";
import { Rect } from "konva/lib/shapes/Rect";
import Konva from "konva";
import { Layer } from "konva/lib/Layer";
import { DrawStatus, DrawType } from "./editorEnum";
import { Arrow } from "konva/lib/shapes/Arrow";
import { Transformer } from "konva/lib/shapes/Transformer";
import useStore from "@/store";
import { Point } from "openseadragon";
export interface DrawerConfig {
  layer: Layer; //画图层
  stroke?: string; //绘图颜色
  strokeWidth?: number; //边框宽度
}

export interface DrawTypeConfig {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  radiusX?: number;
  radiusY?: number;
  radius?: number;
  points?: number[];
  endX?: number;
  endY?: number;
  startPoint?: any;
  endPoint?: any;
}

export class Drawer {
  _drawType: DrawType | null; //绘画形状类型

  _layer: Layer; //画图层

  _stroke: string; //绘图颜色

  _strokeWidth: number = 1; //边框宽度

  _node: Rect | Circle | Ellipse | Line | null; //绘画元素

  _status: DrawStatus = DrawStatus.end; //画图状态

  _transformer: Transformer; //用来调整元素的调整器

  _viewer: OpenSeadragon.Viewer; //用来调整元素的调整器

  constructor(config: DrawerConfig) {
    const { selectColor } = useStore();
    this._layer = config.layer;
    this._stroke = selectColor.selectColor;
    this._strokeWidth = selectColor.strokeWidth;
  }

  //是否有绘画对象
  get hasDrawer(): boolean {
    return this._node ? true : false;
  }
  //视图
  get viewport(): OpenSeadragon.Viewport {
    return this._viewer.viewport;
  }

  //绘画状态
  get status(): DrawStatus {
    return this._status;
  }

  //缩放大小
  get scale(): number {
    return this._node ? this._node.scaleX() : 1;
  }

  //绘画元素
  get node(): Rect | Circle | Ellipse | Line | null {
    return this._node;
  }

  //画图
  draw(config: {
    x: number;
    y: number;
    width: number;
    height: number;
    startPoint?: any;
    endPoint?: any;
  }): Rect;
  draw(config: {
    x: number;
    y: number;
    radiusX: number;
    radiusY: number;
    startPoint?: any;
    endPoint?: any;
  }): Ellipse;
  draw(config: { points: number[]; startPoint?: any }): Line;
  draw(config: { x: number; y: number; endX: number; endY: number }): Arrow;

  draw(config: DrawTypeConfig): Rect | Circle | Ellipse | Line | Arrow | null {
    this.start();
    const {
      x,
      y,
      radius,
      radiusX,
      radiusY,
      points,
      width,
      height,
      endX,
      endY,
      startPoint,
      endPoint,
    } = config;
    const hasDrawer = this.hasDrawer;
    if (!x || !y) {
      if (points) {
        // console.log(points)
        this.drawLine(points, startPoint);
      }
    } else if (radiusX != undefined && radiusY != undefined) {
      // console.log("radiusX,",x, y, radiusX, radiusY)
      this.drawEllipse(x, y, radiusX, radiusY, startPoint, endPoint);
    } else {
      // console.log("points")
      // console.log("points")
      this.drawRect(x, y, width, height, startPoint, endPoint);
    }
    if (this._node) {
      // console.log("颜色");

      const { selectColor } = useStore();
      this._node.stroke(selectColor.selectColor); //设置边框颜色
      this._node.strokeWidth(selectColor.strokeWidth); //设置边框宽度
      this._node.strokeScaleEnabled(false); //设置边框固定宽度
      if (!hasDrawer) {
        this._layer.add(this._node);
      }
    }
    this.onNode(this._node);
    return this._node;
  }

  onNode(evs: Rect | Circle | Ellipse | Line | undefined | null) {
    // @ts-ignore
    evs?.on("transform dragmove scale rotate", (ev) => {
      let node = ev.target;
      const { x, y, width, height } = node.getClientRect({
        skipShadow: true,
        skipStroke: true,
      });
      // 提取公共的转换逻辑
      const getTransformedCoordinates = (
        x: number,
        y: number,
        width: number,
        height: number
      ) => {
        var viewportPoint = this.viewport.pointFromPixel(new Point(x, y));
        var viewportcurPoint = this.viewport.pointFromPixel(
          new Point(x + width, y + height)
        );
        var imagePoint =
          this.viewport.viewportToImageCoordinates(viewportPoint);
        var curPoint =
          this.viewport.viewportToImageCoordinates(viewportcurPoint);
        return { imagePoint, curPoint };
      };

      let coordinates;
      switch (node.getClassName()) {
        case "Rect":
          node.attrs.type = "btn_rect";
          coordinates = getTransformedCoordinates(x, y, width, height);
          node.attrs.startPoint = [
            { X: coordinates.imagePoint.x, Y: coordinates.imagePoint.y },
            { X: coordinates.curPoint.x, Y: coordinates.curPoint.y },
          ];
          break;

        case "Ellipse":
          node.attrs.type = "btn_elips";
          coordinates = getTransformedCoordinates(x, y, width, height);
          node.attrs.startPoint = [
            { X: coordinates.imagePoint.x, Y: coordinates.imagePoint.y },
            { X: coordinates.curPoint.x, Y: coordinates.curPoint.y },
          ];
          break;

        case "Arrow":
          node.attrs.type = "btn_arrow";
          coordinates = getTransformedCoordinates(x, y, width, height);
          node.attrs.startPoint = [
            { X: coordinates.imagePoint.x, Y: coordinates.curPoint.y },
            { X: coordinates.curPoint.x, Y: coordinates.imagePoint.y },
          ];
          break;

        case "Line":
          node.attrs.type = "btn_pen";
          var dx = node.x();
          var dy = node.y();
          var scale = node.scale();
          var scaleX = scale?.x as number;
          var scaleY = scale?.y as number;
          var points = node.attrs.points;
          var newPoints = points.map((point: any, index: number) =>
            index % 2 === 0 ? point * scaleX + dx : point * scaleY + dy
          );

          node.attrs.startPoint = [];
          newPoints.forEach((e: any, i: number) => {
            if (i % 2 === 0) {
              var imagePoint = this.viewport.viewportToImageCoordinates(
                new Point(e, newPoints[i + 1])
              );
              node.attrs.startPoint.push({
                X: imagePoint.x / 1000,
                Y: imagePoint.y / 1000,
              });
            }
          });
          node.attrs.startPoint.push(node.attrs.startPoint[0]);
          break;
        case "Shape":
          node.attrs.type = "btn_ruler";
          coordinates = getTransformedCoordinates(x, y, width, height);
          node.attrs.startPoint[0].X = coordinates.imagePoint.x;
          node.attrs.startPoint[0].Y = coordinates.imagePoint.y;
          node.attrs.startPoint[1].X = coordinates.curPoint.y;
          node.attrs.startPoint[1].Y = coordinates.curPoint.x;
          break;
        // 可以在这里添加其他类型的处理
        default:
          // 如果没有匹配的类型，执行默认操作
          break;
      }

      node.attrs.drawing = true;
    });
  }

  //画正方形
  drawRect(
    x: number,
    y: number,
    width?: number,
    height?: number,
    startPoint?: any,
    endPoint?: any
  ): void {
    if (this._node) {
      this._node.setAttrs({
        x,
        y,
        width,
        height,
        startPoint: [startPoint, endPoint],
      });
    } else {
      this._node = new Konva.Rect({
        x: x,
        y: y,
        width,
        height,
        startPoint: ["startPoint", "endPoint"],
      });
    }
    this._drawType = DrawType.rect;
  }

  //画圆形
  // drawCircle(x: number, y: number, radius: number): void {
  //   if (this._node) {
  //     this._node.setAttrs({
  //       x,
  //       y,
  //       radius: radius,
  //     });
  //   } else {
  //     this._node = new Konva.Circle({
  //       x: x,
  //       y: y,
  //       radius,
  //     });
  //   }
  //   this._drawType = DrawType.circle;
  // }

  //画椭圆
  drawEllipse(
    x: number,
    y: number,
    radiusX: number,
    radiusY: number,
    startPoint?: any,
    endPoint?: any
  ): void {
    if (this._node) {
      this._node.setAttrs({
        x,
        y,
        radiusX,
        radiusY,
        startPoint: [startPoint, endPoint],
      });
    } else {
      this._node = new Konva.Ellipse({
        x: x,
        y: y,
        radiusX,
        radiusY,
        startPoint: [startPoint, endPoint],
      });
    }
    this._drawType = DrawType.ellipse;
  }

  //画线和多边形
  drawLine(points: number[], startPoint?: number[]): void {
    if (this._node) {
      this._node.setAttrs({
        points: [...this._node.attrs.points, ...points],
        startPoint: [
          ...this._node.attrs.startPoint,
          ...(startPoint as number[]),
        ],
      });
    } else {
      this._node = new Konva.Line({
        points: points,
        closed: true,
        lineCap: "round",
        lineJoin: "round",
        startPoint: startPoint,
      });
    }
    this._drawType = DrawType.line;
  }

  //画三角形
  drawArraw(x: number, y: number, endX: number, endY: number): void {
    const points = [x, y, endX, endY];
    if (this._node) {
      this._node.setAttrs({
        points,
      });
    } else {
      this._node = new Konva.Arrow({
        points,
        // closed: true,
        fill: this._stroke,
        pointerLength: 10,
        pointerWidth: 10,
      });
    }
    this._drawType = DrawType.arrow;
  }

  //开始绘画
  start() {
    this._status = DrawStatus.drawing;
  }

  //结束绘画
  end() {
    this._status = DrawStatus.end;
    this._node = null;
  }

  select(node: any) {
    this.cancelSelect();
    this._status = DrawStatus.select;
    this._transformer = new Konva.Transformer({
      node: node as Rect,
      //   centeredScaling: true,
    });
    this._node = node;
    this._node?.setAttrs({
      draggable: true,
    });
    this._layer.add(this._transformer);
  }

  cancelSelect() {
    this._status = DrawStatus.end;
    if (this._node) {
      this._node?.setAttrs({
        draggable: false,
      });
    }
    if (this._transformer) {
      this._transformer.destroy();
    }
    this._node = null;
  }

  deleteNode() {
    this._transformer.destroy();
    this._node?.destroy();
    this._node = null;
    this._status = DrawStatus.end;
  }
}
