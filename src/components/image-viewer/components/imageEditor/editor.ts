/**
 * 标记工具
 */

import { Min } from "@/utils/utils";
import Konva from "konva";
import { Shape, ShapeConfig } from "konva/lib/Shape";
import { Arrow } from "konva/lib/shapes/Arrow";
import { Ellipse } from "konva/lib/shapes/Ellipse";
import { Line } from "konva/lib/shapes/Line";
import { Rect } from "konva/lib/shapes/Rect";
import { Transformer, TransformerConfig } from "konva/lib/shapes/Transformer";
import OpenSeadragon, { Point, Viewer } from "openseadragon";
import { MouseTracker } from "openseadragon";
import { Drawer } from "./drawer";
import { DrawStatus, DrawType, BtnType } from "./editorEnum";
import { Layer } from "./konvaOverlayer";
import useStore from "@/store";
//绘画点
export interface DrawPoint {
  x: number;
  y: number;
  imageX?: number;
  imageY?: number;
}

//标记
export class Marker {
  Area?: number;
  Coordinates?: any[];
  Description?: string;
  FontColor?: string;
  FontSize?: number;
  ID: number;
  LineColor?: string;
  LineWidth?: number;
  Name: string;
  PartOfGroup?: string;
  Type?: string;
  attrs?: any;
}

export class Editor {
  _layer: Layer; //画图层

  _mouseTracker: MouseTracker; //鼠标（指针设备）追踪器

  _drawType: DrawType; //绘画形状类型

  _stroke: string = "black"; //绘图颜色

  _strokeWidth: number = 1; //边框宽度

  _drawer: Drawer; //绘图器

  _startPoint: DrawPoint; //起始点

  _endPoint: DrawPoint; //终止点

  _viewer: OpenSeadragon.Viewer;

  //缩放倍率
  get scale(): number {
    return this._layer ? this._layer.scaleX() : 1;
  }

  //Konva.Stage 是树的根节点
  get stage() {
    return this._layer.getStage();
  }

  //画纸容器
  get canvasDiv() {
    return this._layer.getStage().container();
  }

  //判断是否有绘图器
  get hasDrawer(): boolean {
    return this._drawer ? true : false;
  }

  //视图
  get viewport(): OpenSeadragon.Viewport {
    return this._viewer.viewport;
  }

  //绘图状态
  get status(): DrawStatus {
    return this._drawer.status;
  }
  constructor(viewer: OpenSeadragon.Viewer, layer: Layer) {
    console.log(viewer, layer);

    this._viewer = viewer;
    this._layer = layer;
    this._mouseTracker = new OpenSeadragon.MouseTracker({
      element: this.canvasDiv,
      startDisabled: true,
    });
    this._drawer = new Drawer({ layer });
    this.addListener();
  }

  borderX = 0;
  borderY = 0;
  numx = 50;
  numy = 50;
  flags = false;
  addListener() {
    //进入编辑模式
    // this._viewer?.addHandler("canvas-double-click", (ev) => {
    //   console.log("canvas-double-click", ev);
    // });

    // this.canvasDiv.tabIndex = 1;
    // this.canvasDiv.addEventListener("keydown", (e) => {
    //   if (e.key === "Delete") {
    //     this._drawer.deleteNode();
    //   }
    // });

    this.stage.on("touchstart mousedown", (ev: any) => {
      console.log("touchstart", ev.evt.offsetX, ev.evt.offsetY);

      if (this.status === DrawStatus.end) {
        let touchPos = this.stage.getPointerPosition();
        const x = touchPos?.x as number;
        const y = touchPos?.y as number;
        console.log(touchPos, "touchstart");

        this._startPoint = this.konvaPointFromPixl(x, y);
        this._endPoint = this.konvaPointFromPixl(x, y);
        this.draw(this._startPoint.x, this._startPoint.y, 0.02, 0.02);
      }
      if (this.status === DrawStatus.select) {
        this._drawer.cancelSelect();
      }
    });

    this.stage.on("touchmove mousemove", (ev: any) => {
      if (this.status === DrawStatus.drawing) {
        let touchPos = this.stage.getPointerPosition();
        //console.log(touchPos,'touchPos');
        const x = touchPos?.x as number;
        const y = touchPos?.y as number;
        const x1 = ev.evt.offsetX;
        const y1 = ev.evt.offsetY;
        console.log(x, y, x1, y1, "x1,y1");

        this._endPoint = this.konvaPointFromPixl(x, y);

        if (this.status === DrawStatus.drawing) {
          const { x, y, width, height } = this.getRect(
            this._startPoint,
            this._endPoint
          );
          this.draw(x, y, width, height, this._startPoint, this._endPoint);
        }
      }
    });

    this.stage.on("touchend mouseup", (ev: any) => {
      let touchPos = this.stage.getPointerPosition();
      const x = touchPos?.x as number;
      const y = touchPos?.y as number;
      this._endPoint = this.konvaPointFromPixl(x, y);

      if (this.status === DrawStatus.drawing) {
        const { width, height } = this.getRect(
          this._startPoint,
          this._endPoint
        );
        if (width < 0.06 || height < 0.06) {
          this._drawer.node?.destroy();
        }
        this._drawer.end();
        this.saveMarker();
      }
    });

    // 双击退出编辑模式
    // this.stage.on("dbltap", (ev) => {
    //   console.log(12);

    //   setTimeout(() => {
    //     this.close();
    //   }, 10);
    // });

    this._layer.on("tap", (ev) => {
      console.log("tab", ev);

      if (ev.target.attrs.draggable) {
        ev.cancelBubble = true;
      }
    });

    //双击选中
    // this._layer.on("dbltap", (ev) => {
    //   console.log(12);
    //   this._drawer.deleteNode();
    //   ev.cancelBubble = true;
    //   this._drawer.select(ev.target);
    // });
  }
  //保存标注
  async saveMarker() {
    const { zjsection, tokens, label } = useStore();
    const childrens = this._layer.getChildren();

    interface MarkerDetail {
      GroupModel: {
        Color: string;
        Groups: any;
        Labels: Marker[];
        Name: string;
      };
      Version: string;
    }
    const markerDetail: MarkerDetail = {
      GroupModel: {
        Color: "",
        Groups: [],
        Labels: [],
        Name: "root",
      },
      Version: "V2.0",
    };
    for (let item of childrens) {
      // console.log(item, item.getAttrs(), 12312);

      let type;
      let Coordinates: any[] = [];
      if (item.getClassName() === "Rect") {
        type = "btn_rect";
        Coordinates = [
          `${item.getAttrs().x},${
            item.getAttrs().y -
            Math.abs(
              zjsection.sectionDetail.srcHeight -
                zjsection.sectionDetail.srcWidth
            ) *
              0.5003720238095238
          }`,
          `${item.getAttrs().x + item.getAttrs().width},${
            item.getAttrs().y +
            item.getAttrs().height -
            Math.abs(
              zjsection.sectionDetail.srcHeight -
                zjsection.sectionDetail.srcWidth
            ) *
              0.5003720238095238
          }`,
        ];
      } else if (item.getClassName() === "Ellipse") {
        type = "btn_elips";
        Coordinates = [
          `${item.getAttrs().x - item.getAttrs().radiusX},${
            item.getAttrs().y -
            item.getAttrs().radiusY -
            Math.abs(
              zjsection.sectionDetail.srcHeight -
                zjsection.sectionDetail.srcWidth
            ) *
              0.5003720238095238
          }`,
          `${
            item.getAttrs().x -
            item.getAttrs().radiusX +
            item.getAttrs().radiusX * 2
          },${
            item.getAttrs().y -
            item.getAttrs().radiusY -
            Math.abs(
              zjsection.sectionDetail.srcHeight -
                zjsection.sectionDetail.srcWidth
            ) *
              0.5003720238095238 +
            item.getAttrs().radiusY * 2
          }`,
        ];
      } else {
        type = "btn_pen";
        //遍历奇数item.getAttrs().points
        item.getAttrs().points.forEach((item1: number, index: number) => {
          // console.log(item, index, 123123);

          if (index % 2 == 0) {
            Coordinates.push(
              `${item1},${
                item.getAttrs().points[index + 1] -
                Math.abs(
                  zjsection.sectionDetail.srcHeight -
                    zjsection.sectionDetail.srcWidth
                ) *
                  0.5003720238095238
              }`
            );
          }
        });
        Coordinates.push(
          `${item.getAttrs().points[0]},${
            item.getAttrs().points[1] -
            Math.abs(
              zjsection.sectionDetail.srcHeight -
                zjsection.sectionDetail.srcWidth
            ) *
              0.5003720238095238
          }`
        );
      }
      const markerItem: Marker = {
        Name: item.getClassName(),
        ID: item._id,
        Coordinates: Coordinates,
        attrs: item.getAttrs(),
        PartOfGroup: "",
        Type: type,
        LineWidth: item.getAttrs().strokeWidth,
        LineColor: item.getAttrs().stroke,
        Description: "",
      };
      markerDetail.GroupModel.Labels.push(markerItem);
    }
    if (tokens.token) {
      label.saveOrUpdateLabel(markerDetail);
    }
  }

  //将像素点转换为konva的坐标点

  konvaPointFromPixl(x: number, y: number) {
    //左上坐标
    var pixelNoRotate = this.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );

    var ass = this.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(1, 1),
      true
    );

    if (this.viewport.getHomeBoundsNoRotate().height > 0.91) {
      this.flags = true;
    } else {
      this.flags = false;
    }

    const point = this.viewport.pointFromPixel(new Point(x, y));
    var pixel = this.viewport.pixelFromPointNoRotate(point); //当前坐标转化为旋转度数为0度的实际坐标
    // console.log("this.borderY",Math.abs(pixel.y - pixelNoRotate.y) / this.scale,(Math.abs(pixel.y - ass.y) / this.scale),pixelNoRotate.y,ass.y)
    // console.log(this.viewport.getHomeBoundsNoRotate(),this.viewport.getHomeBoundsNoRotate().getBottomRight());
    // console.log(this.viewport.getBounds());
    // console.log("point",point,this.scale,pixel);

    if (Math.abs(pixel.x - ass.x) / this.scale < this.numx) {
      if (this.numx > 1 && this.numx != 1) {
        this.numx -= 10;
        if (this.numx == 1 || this.numx == 0) {
          this.numx = 1;
        }
      }
      this.borderX =
        Math.abs(pixel.x - pixelNoRotate.x) / this.scale -
        Math.abs(pixel.x - ass.x) / this.scale;
    }
    if (this.flags) {
      this.borderY = this.viewport.getHomeBoundsNoRotate().height * 1000;
      if (this.viewport.getHomeBoundsNoRotate().y < 0) {
        // console.log("缩放了窗口大小");
        this.borderY =
          (this.viewport.getHomeBoundsNoRotate().getBottomRight().y -
            Math.abs(this.viewport.getHomeBoundsNoRotate().y)) *
          1000;
      }
      // console.log("++",this.borderY);
    } else {
      this.borderY =
        (this.viewport.getHomeBoundsNoRotate().getBottomRight().y -
          Math.abs(this.viewport.getHomeBoundsNoRotate().y)) *
        1000;
      // console.log("--",this.borderY);
    }

    if (
      Math.abs(pixel.y - pixelNoRotate.y) / this.scale > this.borderY &&
      this.borderY != 0
    ) {
      if (
        Math.abs(pixel.y - pixelNoRotate.y) / this.scale > this.borderY &&
        this.borderY != 0 &&
        Math.abs(pixel.x - pixelNoRotate.x) / this.scale > this.borderX &&
        this.borderX != 0
      ) {
        return {
          x:
            Math.abs(pixel.x - pixelNoRotate.x) / this.scale -
            (Math.abs(pixel.x - ass.x) / this.scale) * 2,
          y:
            this.borderY -
            (Math.abs(pixel.y - pixelNoRotate.y) / this.scale - this.borderY) *
              2,
        };
      }
      return {
        x: Math.abs(pixel.x - pixelNoRotate.x) / this.scale,
        y:
          this.borderY -
          (Math.abs(pixel.y - pixelNoRotate.y) / this.scale - this.borderY) * 2,
      };
    } else if (
      Math.abs(pixel.x - pixelNoRotate.x) / this.scale > this.borderX &&
      this.borderX != 0
    ) {
      return {
        x:
          Math.abs(pixel.x - pixelNoRotate.x) / this.scale -
          (Math.abs(pixel.x - ass.x) / this.scale) * 2,
        y: Math.abs(pixel.y - pixelNoRotate.y) / this.scale,
      };
    } else {
      return {
        x: Math.abs(pixel.x - pixelNoRotate.x) / this.scale,
        y: Math.abs(pixel.y - pixelNoRotate.y) / this.scale,
      };
    }
  }

  //绘画
  draw(
    x: number,
    y: number,
    width: number,
    height: number,
    startPoint?: any,
    endPoint?: any
  ) {
    console.log("draw", startPoint, endPoint);

    // console.log(this._drawType);

    if (this._drawType === DrawType.ellipse) {
      const radiusX = width / 2;
      const radiusY = height / 2;
      const centerX = x + width / 2;
      const centerY = y + height / 2;
      this._drawer.draw({
        x: centerX,
        y: centerY,
        radiusX,
        radiusY,
        startPoint,
        endPoint,
      });
    } else if (this._drawType === DrawType.rect) {
      this._drawer.draw({ x, y, width, height, startPoint, endPoint });
    } else if (this._drawType === DrawType.line) {
      this._drawer.draw({
        points: [this._endPoint.x, this._endPoint.y],
        startPoint: [this._endPoint.imageX, this._endPoint.imageY],
      });
    }
  }

  //获取顶点和宽高
  getRect(
    startPoint: DrawPoint,
    endPoint: DrawPoint
  ): { x: number; y: number; width: number; height: number } {
    const width = Math.abs(this._startPoint.x - endPoint.x);
    const height = Math.abs(this._startPoint.y - endPoint.y);
    // console.log(Min(startPoint.x, endPoint.x),Min(startPoint.y, endPoint.y),width,height);
    let x = Min(startPoint.x, endPoint.x);
    let y = Min(startPoint.y, endPoint.y);

    return {
      x,
      y,
      width,
      height,
    };
  }

  //开启编辑模式
  start(drawType?: DrawType) {
    this._mouseTracker.setTracking(true);
    this.stage.container().style.cursor = "crosshair";
    this._drawer.cancelSelect();
    if (drawType != undefined) {
      this.setDrawType(drawType);
    }
    if (this.canvasDiv) {
      this.canvasDiv.style.zIndex = "2";
    }
  }

  //关闭编辑模式
  close() {
    this._drawer.cancelSelect();
    if (this._mouseTracker) {
      this.stage.container().style.cursor = "default";
      this._mouseTracker.setTracking(false);
    }
    if (this.canvasDiv) {
      this.canvasDiv.style.zIndex = "1";
    }
  }

  //设置类型
  setDrawType(drawType: DrawType) {
    this._drawType = drawType;
  }

  //给元素添加调整器，调整大小和旋转
  addTransformer(transformerConfig: TransformerConfig): Transformer {
    const defaultConfig = { centeredScaling: true };
    const transformer = new Konva.Transformer(
      Object.assign({}, defaultConfig, transformerConfig)
    );
    this._layer.add(transformer);
    return transformer;
  }

  //添加标注
  async addMarker(markers: any[]): Promise<void> {
    setTimeout(() => {
      if (!Array.isArray(markers) || markers.length === 0) {
        console.warn("Markers is empty or invalid");
        return;
      }
      if (!this._drawer || !this._layer || !this.viewport) {
        console.error("Drawer or layer or viewport not initialized yet.");
        return;
      }

      for (let item of markers) {
        item.typeName =
          item.typeName?.toLowerCase() || item.Name?.toLowerCase();

        let node;
        switch (item.typeName) {
          case DrawType.rect:
            node = new Rect();
            break;
          case DrawType.line:
            node = new Line();
            break;
          case DrawType.arrow:
            node = new Arrow();
            break;
          case DrawType.ellipse:
            node = new Ellipse();
            break;
          default:
            console.warn("Unknown typeName:", item.typeName);
            continue;
        }

        // 判断是否是新/旧格式的数据，设置属性
        const isClientData = !!item.Name;
        let attrs;

        if (isClientData) {
          if (item.attrs) {
            attrs = item.attrs;
          } else {
            attrs = this.clientSideMarker(item, item.Type);
          }
          node?.setAttrs(attrs);
          node?.id(item.ID);
        } else {
          if (!item.attrs) {
            console.warn("Missing attrs in server-side marker:", item);
            continue;
          }
          node?.setAttrs(item.attrs);
          node?.id(item.id || item._id);
        }

        this._drawer.onNode(node);
        this._layer.add(node as Shape<ShapeConfig>);
      }
    }, 1000);
  }
  //兼容客户端标注方法
  clientSideMarker(item: any, type: BtnType) {
    let attrs: any;
    if (
      type != BtnType.line &&
      type != BtnType.arrow &&
      type != BtnType.line_pen
    ) {
      let coordinates = item.Coordinates;
      let data1: any, data2;
      // 判断是旧格式还是新格式
      if (Array.isArray(coordinates) && typeof coordinates[0] === "string") {
        // 旧格式：字符串数组
        data1 = coordinates[0].split(",");
        data2 = coordinates[1].split(",");
      } else if (
        Array.isArray(coordinates) &&
        typeof coordinates[0] === "object"
      ) {
        // 新格式：对象数组
        data1 = [coordinates[0].X, coordinates[0].Y];
        data2 = [coordinates[1].X, coordinates[1].Y];
      } else {
        // 处理不符合预期的数据格式
        console.error("Unsupported data format for Coordinates");
      }
      const width = Number(data2[0]) - Number(data1[0]);
      const height = Number(data2[1]) - Number(data1[1]);
      var imagePoint = this.viewport.imageToViewportCoordinates(
        new Point(Number(data1[0]), Number(data1[1]))
      );
      var imagePointa = this.viewport.imageToViewportCoordinates(
        new Point(width, height)
      );
      attrs = {
        x: 0,
        y: 0,
        stroke: this.normalizeHexColor(item.LineColor),
        height: imagePointa.y * 1000,
        width: imagePointa.x * 1000,
        strokeScaleEnabled: false,
        strokeWidth: item.LineWidth,
        startPoint: item.Coordinates,
        AdditionalInformation: item.AdditionalInformation,
      };
      switch (type) {
        case BtnType.rect:
          attrs.x = imagePoint.x * 1000;
          attrs.y = imagePoint.y * 1000;
          break;
        case BtnType.ellipse:
          attrs.x = (imagePoint.x + imagePointa.x / 2) * 1000;
          attrs.y = (imagePoint.y + imagePointa.y / 2) * 1000;
          break;
        // case BtnType.rect:

        //   break;
        default:
          break;
      }
    } else {
      attrs = {
        stroke: this.normalizeHexColor(item.LineColor),
        strokeScaleEnabled: false,
        strokeWidth: item.LineWidth,
        startPoint: item.Coordinates,
        pointerLength: 0.2,
        pointerWidth: 0.2,
        closed: true,
        fill:
          type != BtnType.arrow ? "" : this.normalizeHexColor(item.LineColor), // 箭头的填充颜色
        points: [],
        AdditionalInformation: item.AdditionalInformation,
      };
      let startPointarr: any = [];
      item.Coordinates.forEach((element: any) => {
        if (typeof element === "string") {
          let data = element.split(",");
          var imagePoint = this.viewport.imageToViewportCoordinates(
            new Point(Number(data[0]), Number(data[1]))
          );
          // var imagePointa = this.viewport.imageToViewportCoordinates(new Point(width, height))
          attrs.points.push(Number(imagePoint.x) * 1000);
          attrs.points.push(Number(imagePoint.y) * 1000);
        } else if (typeof element === "object") {
          startPointarr.push(element.X);
          startPointarr.push(element.Y);
          let data = [element.X, element.Y];
          var imagePoint = this.viewport.imageToViewportCoordinates(
            new Point(Number(data[0]), Number(data[1]))
          );
          // var imagePointa = this.viewport.imageToViewportCoordinates(new Point(width, height))
          attrs.points.push(Number(imagePoint.x) * 1000);
          attrs.points.push(Number(imagePoint.y) * 1000);
        } else {
          // 处理不符合预期的数据格式
          console.error("Unsupported data format for Coordinates");
        }
      });
      attrs.startPoint = startPointarr;
    }
    return attrs;
  }
  //兼容客户端8位十六进制颜色
  normalizeHexColor(hex: string) {
    // 检查是否是8位十六进制颜色（#AARRGGBB格式）
    if (/^#[0-9A-Fa-f]{8}$/.test(hex)) {
      return "#" + hex.substring(3); // 保留#，并去掉前3个字符（AA部分），保留RGB部分
    }
    // 如果是6位十六进制颜色或颜色名称，直接返回原值
    return hex;
  }

  //清除画布的子元素
  clear() {
    // this._layer.removeChildren();
    this._layer.children = [];
    // this._layer.removeChildren();
    console.log(this._layer.children);
    console.log("this._layer", this._layer);
  }
}
