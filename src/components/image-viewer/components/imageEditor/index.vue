<template>
  <!-- <div class="occupieds" :style="occupieds"></div> -->
  <div class="tool">
    <div
      v-if="isSelect || isRotate"
      class="tool-top"
      :style="
        isSelect || isRotate || isCut
          ? 'box-shadow:0px -2px 4px 0px rgba(0, 0, 0, 0.15)'
          : ''
      "
    >
      <div v-if="isRotate" class="tool-tops">
        <div class="tool-tops-left">
          <div class="tool-tops-title">旋转方式</div>
          <div class="tool-tops-lefts">
            <div class="tool-tops-circle tool-tops-circles" @click="vertical">
              <iconpark-icon name="weibiaoti-1huaban1fuben16"></iconpark-icon>
            </div>
            <div class="tool-tops-circle" @click="horizontal">
              <iconpark-icon name="weibiaoti-1huaban1fuben16"></iconpark-icon>
            </div>
          </div>
        </div>
        <div class="tool-tops-right">
          <div class="tool-tops-title">度数</div>
          <div class="tool-tops-lefts">
            <div class="tool-tops-limit">
              <span>{{ degrees }}°</span>
            </div>
            <div class="tool-tops-limits">
              <van-slider
                class="tool-tops-slider"
                v-model="value"
                :max="360"
                :min="0"
                :step="1"
                @change="onChange"
                inactive-color="#E6ECFB"
                active-color="#650FAA"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-if="isSelect" class="isSelectClass">
        <div class="isSelectClass-top">
          <div
            class="isSelect-top"
            @click="drawCricle"
            :style="isCircle ? 'background:#650FAA' : ''"
          >
            <iconpark-icon
              :style="isCircle ? 'color: #fff' : ''"
              name="weibiaoti-1huaban1fuben9"
            ></iconpark-icon>
          </div>
          <div
            class="isSelect-top"
            @click="drawRect"
            :style="isRect ? 'background:#650FAA' : ''"
          >
            <iconpark-icon
              :style="isRect ? 'color: #fff' : ''"
              name="weibiaoti-1huaban1fuben10"
            ></iconpark-icon>
          </div>
          <div
            class="isSelect-top"
            @click="drawLine"
            :style="isLine ? 'background:#650FAA' : ''"
          >
            <iconpark-icon
              :style="isLine ? 'color: #fff' : ''"
              name="weibiaoti-1huaban1fuben11"
            ></iconpark-icon>
          </div>
        </div>
        <div class="isSelectClass-bottom">
          <div>颜色</div>
          <div
            class="isSelectClass-cirle"
            @click="isShowClcik('isRed')"
            :style="isShow.isRed ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div
            class="isSelectClass-cirle isSelectClass-cirle1"
            @click="isShowClcik('isYellow')"
            :style="isShow.isYellow ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div
            class="isSelectClass-cirle isSelectClass-cirle2"
            @click="isShowClcik('isGreen')"
            :style="isShow.isGreen ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div
            class="isSelectClass-cirle isSelectClass-cirle3"
            @click="isShowClcik('isBlue')"
            :style="isShow.isBlue ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div
            class="isSelectClass-cirle isSelectClass-cirle4"
            @click="isShowClcik('isPurple')"
            :style="isShow.isPurple ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div
            class="isSelectClass-cirle isSelectClass-cirle5"
            @click="isShowClcik('isBlack')"
            :style="isShow.isBlack ? 'border: 2px solid #E6ECFB;' : ''"
          ></div>
          <div class="isSelectClass-line"></div>
          <div>大小</div>
          <div class="tool-tops-limits tool-tops-limitss">
            <van-slider
              class="tool-tops-slider"
              v-model="strokeWidth"
              :max="10"
              :min="1"
              :step="1"
              @change="onChangeWidth"
              inactive-color="#E6ECFB"
              active-color="#650FAA"
            />
          </div>
        </div>
      </div>
    </div>
    <div v-if="isSelect || isRotate" class="tool-line"></div>
    <div
      class="tool-bottom"
      :style="
        isSelect || isRotate
          ? ''
          : 'box-shadow:0px -2px 4px 0px rgba(0, 0, 0, 0.15)'
      "
    >
      <div class="tool-bottom-left" @click="select">
        <div class="tool-icon" :style="isSelect ? 'background:#650FAA' : ''">
          <iconpark-icon
            :style="isSelect ? 'color:#fff' : ''"
            name="weibiaoti-1huaban1fuben12"
          >
          </iconpark-icon>
        </div>
        <div>标注工具</div>
      </div>
      <div class="tool-bottom-left" @click="rotate">
        <div class="tool-icon" :style="isRotate ? 'background:#650FAA' : ''">
          <iconpark-icon
            :style="isRotate ? 'color:#fff' : ''"
            name="weibiaoti-1huaban1fuben13"
          >
          </iconpark-icon>
        </div>
        <div>旋转工具</div>
      </div>
      <div class="tool-bottom-left" @click="cut">
        <div class="tool-icon" :style="isCut ? 'background:#650FAA' : ''">
          <iconpark-icon
            :style="isCut ? 'color:#fff' : ''"
            name="weibiaoti-1huaban1fuben14"
          ></iconpark-icon>
        </div>
        <div>一键截取</div>
      </div>
      <!-- <div @click="clearMarker">删除标注</div> -->
    </div>
  </div>
</template>

<script lang="ts">
import OpenSeadragon from "openseadragon";
import { KonvaOverlay, Layer } from "./konvaOverlayer";
import { Editor, Marker } from "./editor";
import bus from "@/bus";

import {
  defineComponent,
  PropType,
  ref,
  toRefs,
  computed,
  watch,
  Ref,
  inject,
} from "vue";
import { DrawType } from "./editorEnum";
import { KonvaScreenshotOverlayer } from "./konvaScreenshotOverlayer";
import KSlider from "@/components/k-slider/KSlider.vue";
import useStore from "@/store";

export default defineComponent({
  name: "ImageEditor",
  components: {
    KSlider,
  },
  props: {
    //openseadragon组件对象
    viewer: Object as PropType<OpenSeadragon.Viewer>,
    disabled: {
      type: Boolean,
      default: false,
    },
    barNames: {
      type: Array,
      default: ["矩形", "圆形", "自由绘制", "截图", "旋转"],
    },
  },
  emits: ["screenshot", "update:fullscreen"],
  setup(props, { emit }) {
    const { viewer, barNames } = toRefs(props);
    const degrees = ref(0); //旋转角度
    const marker: Ref<any> = ref([]); //标记
    const { selectColor, label, tokens } = useStore();
    //默认值
    const rangeValue = ref(0);
    // let horizontalY = inject("horizontalY");
    //标注是否选中
    let isSelect = ref(false);
    let isRotate = ref(false);
    let isCut = ref(false);
    const value = ref(0);
    const onChange = (e: any) => {
      value.value = e;
      degrees.value = e;
      degre();
    };
    function cut() {
      isCut.value = !isCut.value;
      isSelect.value = false;
      isRotate.value = false;
      if (isCut.value) {
        closeScreenshots();
        screenshots();
        isCut.value = false;
      } else {
        closeScreenshots();
      }
    }
    function select() {
      isSelect.value = !isSelect.value;
      if (!isSelect.value && (isCircle.value || isRect.value || isLine.value)) {
        isRect.value = false;
        isLine.value = false;
        isCircle.value = false;

        editor.value.setDrawType(DrawType.arrow);
        editor.value.close();
      }
      isCut.value = false;
      isRotate.value = false;
    }
    function rotate() {
      isRotate.value = !isRotate.value;
      isCut.value = false;
      isSelect.value = false;
    }
    //激活的工具名称
    // const activeBarName = ref("");

    //启用的绘图工具
    // const enableBars = computed(() => {
    //   return barNames.value.map(item => {
    //     return bars.value.find(bar => bar.barName === item);
    //   });
    // });

    //视口
    const viewport = computed(() => {
      return viewer.value?.viewport as OpenSeadragon.Viewport;
    });

    //画图层
    const konvaOverlay = ref(
      new KonvaOverlay(viewer.value as OpenSeadragon.Viewer)
    );

    //画布
    const layer = computed(() => {
      return konvaOverlay.value.layer as Layer;
    });

    //编辑器
    const editor = ref(
      new Editor(viewer.value as OpenSeadragon.Viewer, layer.value as Layer)
    );

    //截图对象
    const konvaScreenshotOverlayer: Ref<KonvaScreenshotOverlayer | null> =
      ref(null);
    const occupieds = computed(() => {
      if (isSelect.value || isRotate.value) {
        return "height: 250px";
      } else {
        return "height: 160px";
      }
    });
    watch(
      () => viewer.value,
      () => {
        updateKonvaOverlay();
        if (konvaScreenshotOverlayer.value) {
          konvaScreenshotOverlayer.value.destroy();
          konvaScreenshotOverlayer.value = null;
        }
      }
    );

    //更新画图层
    function updateKonvaOverlay() {
      konvaOverlay.value.destroy();
      konvaOverlay.value = new KonvaOverlay(
        viewer.value as OpenSeadragon.Viewer
      );
    }
    watch(
      () => label.markLabels,
      (val) => {
        if (val) {
          nextTick(() => {
            // console.log(val, "______________");
            // clearMarker();
            marker.value = [];
            editor.value?.addMarker(val);
          });
        }
      },
      { immediate: true }
    );
    //左旋转
    function rotateLeft() {
      degrees.value -= 90;
      viewport.value.setRotation(degrees.value);
    }
    //水平翻转
    function horizontal() {
      degrees.value += 180;
      degrees.value >= 360
        ? (degrees.value = degrees.value % 180)
        : degrees.value;
      viewport.value.setRotation(degrees.value);
    }
    //垂直翻转
    function vertical() {
      degrees.value += 180;
      degrees.value >= 360
        ? (degrees.value = degrees.value % 180)
        : degrees.value;
      viewport.value.setRotation(degrees.value);
    }

    //右旋转
    function rotateRight() {
      degrees.value += 90;
      degrees.value > 360
        ? (degrees.value %= 90) == 0
          ? (degrees.value = 90)
          : degrees.value
        : degrees.value;
      viewport.value.setRotation(degrees.value);
    }
    //输入旋转值
    function degre() {
      degrees.value = parseInt(degrees.value + "");
      if (degrees.value > 360) {
        degrees.value = 360;
      } else if (degrees.value <= 0) {
        degrees.value = 0;
      } else if (isNaN(degrees.value)) {
        degrees.value = 0;
      }
      viewport.value.setRotation(degrees.value);
    }

    //定位到中心点
    function goHome() {
      degrees.value = 0;
      viewport.value.setRotation(degrees.value);
      viewport.value.goHome();
    }
    //画圆是否选中
    const isCircle = ref(false);
    //画圆
    function drawCricle() {
      editor.value.setDrawType(DrawType.arrow);
      editor.value.close();
      isCircle.value = !isCircle.value;
      isRect.value = false;
      isLine.value = false;
      if (isCircle.value) {
        editor.value.start(DrawType.ellipse);
      } else {
        isRect.value = false;
        editor.value.setDrawType(DrawType.arrow);
        editor.value.close();
      }
    }
    //画矩形是否选中
    const isRect = ref(false);
    //画正方形
    function drawRect() {
      editor.value.setDrawType(DrawType.arrow);
      editor.value.close();
      isRect.value = !isRect.value;
      isCircle.value = false;
      isLine.value = false;
      if (isRect.value) {
        editor.value.start(DrawType.rect);
      } else {
        isRect.value = false;
        editor.value.setDrawType(DrawType.arrow);
        editor.value.close();
      }
    }

    //画三角形
    function drawArrow() {
      editor.value.start(DrawType.arrow);
    }
    //画线是否选中
    const isLine = ref(false);
    //画线
    function drawLine() {
      editor.value.setDrawType(DrawType.arrow);
      editor.value.close();
      isLine.value = !isLine.value;
      isRect.value = false;
      isCircle.value = false;
      if (isLine.value) {
        editor.value.start(DrawType.line);
      } else {
        isRect.value = false;
        editor.value.setDrawType(DrawType.arrow);
        editor.value.close();
      }
    }
    //清除标注
    function clearMarker() {
      // layer.value.getChildren().map((item) => {
      //   item.destroy();
      // });
      // if (layer.value.getChildren().length > 0) {
      //   clearMarker();
      // }
      layer.value.draw();
      marker.value = {};
      interface MarkerDetail {
        GroupModel: {
          Color: string;
          Groups: any;
          Labels: Marker[];
          Name: string;
        };
        Version: string;
      }
      const markerDetail: MarkerDetail = {
        GroupModel: {
          Color: "",
          Groups: [],
          Labels: [],
          Name: "root",
        },
        Version: "V2.0",
      };

      for (let item of layer.value.getChildren()) {
        console.log(item, "hahahhhahh");

        const markerItem: Marker = {
          Name: item.getClassName(),
          ID: item._id,
          attrs: item.getAttrs(),
        };

        markerDetail.GroupModel.Labels.push(markerItem);
      }
      if (tokens.token) {
        debounce(() => {
          label.saveOrUpdateLabel(markerDetail);
        }, 1000)();
      }
    }
    //防抖
    const timeout: any = ref(null);
    function debounce(
      doSomeThing: () => void,
      time: number = 1000
    ): () => void {
      return () => {
        if (timeout.value) {
          clearTimeout(timeout.value);
        }
        timeout.value = setTimeout(() => {
          doSomeThing();
        }, time);
      };
    }
    bus.$on("clearMarkerBus", (item: any) => {
      console.log(item, "item");

      if (item === 0) {
        layer.value.getChildren().map((item) => {
          setTimeout(() => {
            item.destroy();
          }, 100);
        });
      } else {
        layer.value.getChildren().map((item1: any) => {
          if (item.index === item1.index) {
            //去除index相同的标注
            item1.destroy();
          }
        });
      }
      debounce(() => {
        clearMarker();
      }, 1000)();
    });
    //关闭编辑模式
    function closeEditModel() {
      editor.value.close();
    }

    //打开编辑模式
    function openEditModel() {
      editor.value.start();
    }

    //测量
    function measurement() {}

    //截图
    function screenshots() {
      if (!konvaScreenshotOverlayer.value) {
        konvaScreenshotOverlayer.value = new KonvaScreenshotOverlayer(
          viewer.value as OpenSeadragon.Viewer
        );
      }
      konvaScreenshotOverlayer.value.attach({
        update: (src?: string) => {
          console.log(src, "111111");

          if (src) {
            emit("screenshot", src);
          }
          closeScreenshots();
        },
      });
      konvaScreenshotOverlayer.value.start();
    }

    //关闭截图模式
    function closeScreenshots() {
      konvaScreenshotOverlayer.value?.close();
    }
    onMounted(() => {
      closeEditModel();
      // getMarker()
    });
    watch(
      () => editor.value,
      () => {
        bus.$emit("Deom", editor.value);
      },
      { deep: true, immediate: true }
    );
    //线条粗细
    const strokeWidth = ref(selectColor.strokeWidth);
    const onChangeWidth = (res: number) => {
      selectColor.strokeWidthFn(res);
    };
    //红色是否选中
    const isShow = reactive({
      isRed: false,
      isYellow: false,
      isBlue: false,
      isGreen: false,
      isPurple: false,
      isBlack: false,
    });
    const isShowClcik = (res: string) => {
      switch (res) {
        case "isRed":
          isShow.isRed = true;
          isShow.isYellow = false;
          isShow.isBlue = false;
          isShow.isGreen = false;
          isShow.isPurple = false;
          isShow.isBlack = false;
          selectColor.selectColorFn("#FF2020");
          break;
        case "isYellow":
          isShow.isRed = false;
          isShow.isYellow = true;
          isShow.isBlue = false;
          isShow.isGreen = false;
          isShow.isPurple = false;
          isShow.isBlack = false;
          selectColor.selectColorFn("#FF9A03");

          break;
        case "isBlue":
          isShow.isRed = false;
          isShow.isYellow = false;
          isShow.isBlue = true;
          isShow.isGreen = false;
          isShow.isPurple = false;
          isShow.isBlack = false;
          selectColor.selectColorFn("#001AFF");

          break;
        case "isGreen":
          isShow.isRed = false;
          isShow.isYellow = false;
          isShow.isBlue = false;
          isShow.isGreen = true;
          isShow.isPurple = false;
          isShow.isBlack = false;
          selectColor.selectColorFn("#00DC09");

          break;
        case "isPurple":
          isShow.isRed = false;
          isShow.isYellow = false;
          isShow.isBlue = false;
          isShow.isGreen = false;
          isShow.isPurple = true;
          isShow.isBlack = false;
          selectColor.selectColorFn("#ED01B9");

          break;
        case "isBlack":
          isShow.isRed = false;
          isShow.isYellow = false;
          isShow.isBlue = false;
          isShow.isGreen = false;
          isShow.isPurple = false;
          isShow.isBlack = true;
          selectColor.selectColorFn("#000000");

          break;

        default:
          break;
      }
    };

    return {
      layer,
      editor,
      degrees,
      viewport,
      rotateRight,
      rotateLeft,
      goHome,
      drawCricle,
      drawRect,
      closeEditModel,
      drawArrow,
      drawLine,
      openEditModel,
      clearMarker,
      updateKonvaOverlay,
      measurement,
      screenshots,
      konvaScreenshotOverlayer,
      closeScreenshots,
      degre,
      horizontal,
      vertical,
      cut,
      isSelect,
      isRotate,
      isCut,
      select,
      rotate,
      rangeValue,
      occupieds,
      isCircle,
      isRect,
      isLine,
      isShow,
      isShowClcik,
      value,
      onChange,
      onChangeWidth,
      selectColor,
      strokeWidth,
    };
  },
});
</script>

<style scoped lang="scss">
.tool {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  z-index: 99;
}

.tool-line {
  width: 100%;
  background-color: #fff;
  height: 1px;
  border: 1px solid #e6ecfb;
}

.tool-bottom {
  position: relative;
  padding: 9px 75px 0 75px;
  display: flex;
  justify-content: space-between;
  height: 73px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  padding-bottom: 34px;
}

.tool-bottom-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
  line-height: 14px;
}

.tool-icon {
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: #eeebfe;
  border-radius: 32px;
  opacity: 1;
}

iconpark-icon {
  font-size: 18px;
  color: #650faa;
}

.tool-top {
  box-sizing: border-box;
  position: absolute;
  top: -80px;
  left: 0;
  width: 100%;
  height: 89px;
  background: #fff;
  border-radius: 4px 4px 4px 4px;
}

.tool-tops {
  padding-bottom: 18px;
  display: flex;
  // height: 100%;
  margin: 12px 14px 0 14px;
  border-bottom: 1px solid #e6ecfb;
}

.tool-tops-left {
  flex: 1;
  // width: 125px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
  border-right: 1px solid #e6ecfb;
}

// .tool-tops-lefts {
//   display: flex;
// }

.tool-tops-lefts {
  margin-left: 12px;
  display: flex;
  margin-top: 8px;
  justify-content: space-around;
}

.tool-tops-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #eeebfe;
  opacity: 1;
  border-radius: 28px;
}

.tool-tops-title {
  margin-left: 12px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
}

.tool-tops-circles {
  transform: rotate(-90deg);
}

.tool-tops-right {
  flex: 2;
}

.tool-tops-limit {
  padding-right: 7px;
  width: 80px;
  height: 28px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #e6ecfb;
  text-align: right;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
  line-height: 28px;
}

.tool-tops-limits {
  display: flex;
  align-items: center;
  margin-left: 5px;
  line-height: 24px;
}

.tool-tops-limitss {
  margin-left: 12px;
}

.tool-tops-slider {
  width: 89px;
  height: 4px;
  border-radius: 2px 2px 2px 2px;
}

:deep(.van-slider__button) {
  width: 10px;
  height: 10px;
  background: #650faa;
  opacity: 1;
}

// input[type=range] {

//   -webkit-appearance: none;

//   height: 8px;

//   border-radius: 4px;
//   background-color: #650FAA;
//   // background-size: 50% 100%;
//   /* 因为周期默认value=0.50正好占50% */

// }

.isSelectClass {
  margin: 0 14px;
}

.isSelectClass-top {
  display: flex;
  height: 48px;
  justify-content: space-around;
  align-items: center;
  border-bottom: 1px solid #e6ecfb;
}

.isSelect-top {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #eeebfe;
  opacity: 1;
  border-radius: 28px;
}

.isSelectClass-bottom {
  display: flex;
  height: 32px;
  align-items: center;
  border-bottom: 1px solid #e6ecfb;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
}

.isSelectClass-cirle {
  margin-left: 14px;
  width: 14px;
  height: 14px;
  background-color: #ff2020;
  opacity: 1;
  border-radius: 14px;
  // border: 2px solid #FFFFFF;
}

.isSelectClass-cirle1 {
  background-color: #ff9a03;
}

.isSelectClass-cirle2 {
  background-color: #00dc09;
}

.isSelectClass-cirle3 {
  background-color: #001aff;
}

.isSelectClass-cirle4 {
  background-color: #ed01b9;
}

.isSelectClass-cirle5 {
  background-color: #000000;
}

.isSelectClass-line {
  margin: 0 12px;
  width: 1px;
  height: 25px;
  border-left: 1px solid #e6ecfb;
}
</style>
