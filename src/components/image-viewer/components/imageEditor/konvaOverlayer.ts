/**
 * konva.js画图层
 */
import Konva from "konva";
import { Group } from "konva/lib/Group";
import { Layer } from "konva/lib/Layer";
import { Stage } from "konva/lib/Stage";
import OpenSeadragon, { MouseTracker } from "openseadragon";

interface Viewport extends OpenSeadragon.Viewport {
  _containerInnerSize: { x: number; y: number };
  _contentSize: { x: number; y: number };
}

export class KonvaOverlay {
  protected _viewer: OpenSeadragon.Viewer;
  protected _canvasDiv: HTMLDivElement;
  protected _containerWidth: number = 0;
  protected _defaultContainerWidth: number = 0; //第一次加载切片，容器的宽度
  protected _containerHeight: number = 0;
  protected _id: string = "konva-overlay";
  protected _konvaStage: Stage; //konva根节点
  protected _layer: Layer; //konva图层
  protected _scale: number = 0;
  protected _group: Group;
  protected _mouseTracker: MouseTracker;

  //openseadrogen Viewer对象
  get viewer() {
    return this._viewer;
  }

  //openseadrogen viewport对象
  get viewport(): Viewport {
    return this._viewer?.viewport as Viewport;
  }

  get canvasDiv(): HTMLDivElement {
    return this._canvasDiv;
  }

  //绘图层根节点
  get konvaStage(): Stage {
    return this._konvaStage;
  }

  //绘画图层
  get layer(): Layer {
    return this._layer;
  }

  constructor(viewer: OpenSeadragon.Viewer) {
    this._viewer = viewer;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    this._defaultContainerWidth = this.viewport._containerInnerSize.x;

    this.createKonvas();
    this.resize();
    this._viewer.addHandler("update-viewport", (ev) => {
      this.resize();
    });
  }

  //
  destroy() {
    this._konvaStage.destroy();
    this.canvasDiv.remove();
  }

  //创建画图层div
  createCanvasDiv() {
    this._canvasDiv = document.createElement("div");
    this._canvasDiv.style.position = "absolute";
    this._canvasDiv.style.left = "0";
    this._canvasDiv.style.top = "0";
    this._canvasDiv.style.width = "100%";
    this._canvasDiv.style.height = "100%";
    this._canvasDiv.setAttribute("id", this._id);
    this._viewer.canvas.appendChild(this._canvasDiv);
  }

  //设置konva对象
  createKonvas() {
    this.createCanvasDiv();
    this._konvaStage = new Konva.Stage({
      container: this._canvasDiv,
      width: this._containerWidth,
      height: this._containerHeight,
    });
    this._layer = new Konva.Layer();
    this._konvaStage.add(this._layer);
  }

  //视图大小调整
  resize() {
    if (this._containerWidth !== this._viewer.container.clientWidth) {
      this._containerWidth = this._viewer.container.clientWidth;
      this._canvasDiv.style.width = this._containerWidth + "px";
      this._konvaStage.width(this._containerWidth);
      this._layer.width(this._containerWidth);
    }

    if (this._containerHeight !== this._viewer.container.clientHeight) {
      this._containerHeight = this._viewer.container.clientHeight;
      this._canvasDiv.style.height = this._containerHeight + "px";
      this._konvaStage.height(this._containerHeight);
      this._layer.height(this._containerHeight);
    }
    this.resizecanvas();
  }

  //调整画图层
  resizecanvas() {
    var p = this.viewport.pixelFromPoint(new OpenSeadragon.Point(0, 0), true);
    // console.log(this.viewport.getFlip(),p)
    // if(this.viewport.getFlip()){
    //   p.x?p.x=-p.x:Math.abs(p.x)
    //   p.y?p.y=-p.y:Math.abs(p.y)
    // }
    // console.log(this.viewport.getFlip(),p)
    // console.log(this.viewport._contentSize);

    const zoom =
      (this.viewport._containerInnerSize.x / 1000) *
      this.viewport.getZoom(true);
    //
    // (this.viewport._containerInnerSize.x /
    //   (this.viewport._contentSize.x / 800)) *
    // this.viewport.getZoom(true);

    const zoom1 =
      (this.viewport._containerInnerSize.x / 1000) *
      this.viewport.getZoom(true);
    // console.log(zoom,zoom1);

    //已第一次加载切片容器的宽度做基准
    const rotation = this._viewer.viewport.getRotation();
    if (this._layer.scale()?.x != zoom) {
      this._layer.scale({ x: zoom, y: zoom });
    }
    this._layer.position({
      x: p.x,
      y: p.y,
    });
    this._layer.rotation(rotation);
    this._layer.draw();
  }
}

export { Stage, Layer };
