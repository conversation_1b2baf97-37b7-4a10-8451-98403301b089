/**
 * 截图工具类
 */
import Konva from "konva";
import { Context } from "konva/lib/Context";
import { Group } from "konva/lib/Group";
import OpenSeadragon from "openseadragon";
import { MouseTracker } from "openseadragon";
import { Layer, Stage } from "./konvaOverlayer";
// import closeImgSrc from "@/assets/close.png";
// import confirmImgSrc from "@/assets/confirm.png";
// import downloadImgSrc from "@/assets/download.png";

export class KonvaScreenshotOverlayer {
  protected _canvasDiv: HTMLDivElement | null; //容器div
  protected _viewer: OpenSeadragon.Viewer | null; //openseadragon的viewer对象
  protected _id: string = "konva-screenshot-overlay";
  protected _containerWidth: number = 0; //容器div宽
  protected _containerHeight: number = 0; //容器div高
  protected _konvaStage: Stage | null; //konva根节点
  protected _layer: Layer | null; //konva图层
  protected _node: any; //截图框
  protected _startPoint: any; //截图框起点
  protected _drawNodeEnding: boolean = false; //初次创建截图宽
  protected _group: Group | null; //绘图工具
  protected _mouseTracker: MouseTracker | null; //鼠标（指针设备）追踪器
  protected _transformer: any; //截图框选择、变换器
  protected _src: string | null; //截图
  protected _mask: any; //遮罩
  protected observers: ScreenshotObserver[] = [];

  //绘图层根节点
  get konvaStage(): Stage | null {
    return this._konvaStage;
  }

  //绘图层根节点
  get src(): string | null {
    return this._src;
  }
  constructor(viewer: OpenSeadragon.Viewer) {
    console.log(viewer, 56454646);

    this._viewer = viewer;
    this._containerWidth = this._viewer.container.clientWidth;
    this._containerHeight = this._viewer.container.clientHeight;
    console.log(
      this._containerWidth,
      this._containerHeight,
      "this._containerWidth"
    );

    this.createCanvas();
    this._mouseTracker = new OpenSeadragon.MouseTracker({
      element: this._canvasDiv as HTMLDivElement,
      startDisabled: true,
    });
    this._viewer.addHandler("update-viewport", (ev) => {
      this.resize();
    });
  }

  //创建截图canvas
  createCanvas() {
    const self = this;
    this._canvasDiv = document.createElement("div");
    this._canvasDiv.style.position = "absolute";
    this._canvasDiv.style.left = "0";
    this._canvasDiv.style.top = "0";
    this._canvasDiv.style.width = "100%";
    this._canvasDiv.style.height = "100%";
    this._canvasDiv.setAttribute("id", this._id);
    this._viewer?.canvas.appendChild(this._canvasDiv);
    this._konvaStage = new Konva.Stage({
      container: this._canvasDiv,
      width: this._containerWidth,
      height: this._containerHeight,
    });
  }

  //创建截图遮罩层
  createMask() {
    if (this._canvasDiv) {
      this._canvasDiv.style.zIndex = "20";
    }
    this._layer = new Konva.Layer();
    this._mask = new Konva.Shape({
      width: this._containerWidth,
      height: this._containerHeight,
      sceneFunc: (context: Context, shape) => {
        //@ts-ignore
        context.fillStyle = "rgba(0, 0, 0, 0.5)";
        context.fillRect(0, 0, shape.getAttr("width"), shape.getAttr("height"));
        if (this._node) {
          context.clear(this.getNodeAttrs());
        }
      },
    });
    this._layer.add(this._mask);
    this._konvaStage?.add(this._layer);
  }

  //注册事件
  addEvent() {
    //创建截图框
    // this._konvaStage?.on("mousedown", (ev: any) => {
    //   if (!this._node) {
    //     const x = ev.evt.offsetX;
    //     const y = ev.evt.offsetY;
    //     this._startPoint = { x, y };
    //     this.drawRect(x, y, 50, 50);
    //     console.log(x,y);

    //   }
    // });

    // //创建截图框
    // this._konvaStage?.on("mousemove", (ev: any) => {
    //   if (!this._drawNodeEnding && this._node) {
    //     const { x, y, width, height } = this.getRect(this._startPoint, {
    //       x: ev.evt.offsetX,
    //       y: ev.evt.offsetY,
    //     });
    //     console.log(x,y,width,height);

    //     this.drawRect(x, y, width, height);
    //   }
    // });
    // //创建截图工具
    // this._konvaStage?.on("mouseup", (ev: any) => {
    //   if (this._node && !this._drawNodeEnding) {
    //     this._drawNodeEnding = true;
    //     this.createEditBar();
    //   }
    // });
    //创建截图框
    this._konvaStage?.on("touchstart mousedown", (ev: any) => {
      if (!this._node) {
        let touchPos = this._konvaStage?.getPointerPosition();

        console.log(touchPos);
        const x = touchPos?.x as number;
        const y = touchPos?.y as number;
        // const x = ev.target.pointerPos.x;
        // const y = ev.target.pointerPos.y;
        this._startPoint = { x, y };
        this.drawRect(x, y, 100, 100);
        // console.log("touchstart", x, y);
      }
    });

    //创建截图框
    // this._konvaStage?.on("touchmove mousemove", (ev: any) => {
    //   if (!this._drawNodeEnding && this._node) {
    //     let touchPos = this._konvaStage?.getPointerPosition();
    //     const x1 = touchPos?.x as number;
    //     const y1 = touchPos?.y as number;
    //     const { x, y, width, height } = this.getRect(this._startPoint, {
    //       x: x1,
    //       y: y1,
    //     });
    //     let widths = height;
    //     let heights = width;

    //     this.drawRect(x, y, widths, heights);
    //     // console.log(x, y, width, height);
    //   }
    // });
    let x1: any, y1: any;
    this._konvaStage?.on("touchmove mousemove", (ev: any) => {
      let touchPos = this._konvaStage?.getPointerPosition();
      const x11 = touchPos?.x as number;
      const y11 = touchPos?.y as number;
      if (!this._drawNodeEnding && this._node) {
        let { x, y, width, height } = this.getRect(this._startPoint, {
          x: x11,
          y: y11,
        });
        x1 = x1 ? x1 : x;
        y1 = y1 ? y1 : y;
        width = height;
        height = width;
        this.drawRect(x1, y1, -width, -height);
      }
    });

    //创建截图工具
    this._konvaStage?.on("touchend mouseup", (ev: any) => {
      if (this._node && !this._drawNodeEnding) {
        this._drawNodeEnding = true;
        this.createEditBar();
        // console.log("touchend");
      }
    });
  }

  //创建截图工具栏
  createEditBar() {
    const closeImgSrc =
      "data:image/png;base64,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";
    const confirmImgSrc =
      "data:image/png;base64,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";
    const { x, y, width, height } = this.getNodeAttrs();
    if (!this._group) {
      this._group = new Konva.Group({
        x: x + width - 100,
        y: y + height,
      });
      this._group.add(
        new Konva.Rect({
          x: 0,
          y: 3,
          width: 100,
          height: 40,
          fill: "white",
        })
      );
      this.loadImages(
        {
          close: closeImgSrc,
          confirm: confirmImgSrc,
        },
        (images: { [imageUrl: string]: any }) => {
          // const downloadImg = new Konva.Image({
          //   image: images["download"],
          //   x: 30,
          //   y: 11,
          // });
          const closeImg = new Konva.Image({
            image: images["close"],
            x: 15,
            y: 11,
          });
          const confirmImg = new Konva.Image({
            image: images["confirm"],
            x: 65,
            y: 11,
          });
          //下载截图
          // downloadImg.on("touchstart", () => {
          //   const nowDate = new Date();
          //   let el: any = document.createElement("a");
          //   el.setAttribute("href", this.getScreenShot());
          //   el.setAttribute(
          //     "download",
          //     nowDate.getFullYear() +
          //       "" +
          //       (nowDate.getMonth() + 1) +
          //       nowDate.getDate() +
          //       nowDate.getHours() +
          //       nowDate.getMinutes() +
          //       nowDate.getSeconds() +
          //       ".png"
          //   );
          //   el.click();
          //   el = null;
          // });
          //取消截图
          closeImg.on("touchstart", () => {
            this.notifyAllObservers();
            this.close();
          });
          //确认截图
          confirmImg.on("touchstart click", () => {
            this.notifyAllObservers(this.getScreenShot());
          });
          if (this._konvaStage) {
            //设置鼠标样式
            closeImg.on("mouseleave", () => {
              (this._konvaStage as Stage).container().style.cursor = "default";
            });
            closeImg.on("mouseenter", () => {
              (this._konvaStage as Stage).container().style.cursor = "pointer";
            });
            confirmImg.on("mouseleave", () => {
              (this._konvaStage as Stage).container().style.cursor = "default";
            });
            confirmImg.on("mouseenter", () => {
              (this._konvaStage as Stage).container().style.cursor = "pointer";
            });
            // downloadImg.on("mouseleave", () => {
            //   (this._konvaStage as Stage).container().style.cursor = "default";
            // });
            // downloadImg.on("mouseenter", () => {
            //   (this._konvaStage as Stage).container().style.cursor = "pointer";
            // });
          }
          if (this._group) {
            this._group.add(closeImg);
            this._group.add(confirmImg);
            // this._group.add(downloadImg);
            this._layer?.add(this._group);
          }
          this._layer?.draw();
        }
      );
      this._layer?.draw();
    } else {
      this._group.setAttrs({
        x: width > 0 ? x + width - 180 : x - 180,
        y: height > 0 ? y + height : y,
      });
      this._group.show();
      this._layer?.draw();
    }
  }

  //加载截图工具图片
  loadImages(
    sources: { [urlName: string]: string },
    callback: (images: Object) => any
  ) {
    let images = {} as any;
    let loadedImages = 0;
    let numImages = Object.keys(sources).length || 0;
    for (let src in sources) {
      images[src] = new Image(24, 24);
      images[src].onload = function () {
        if (++loadedImages >= numImages) {
          callback(images);
        }
      };
      images[src].src = sources[src];
    }
  }

  //创建截图框
  drawRect(x: number, y: number, width: number, height: number) {
    if (this._node) {
      this._node.setAttrs({
        x,
        y,
        width,
        height,
      });
      this._layer?.draw();
    } else {
      this._node = new Konva.Rect({
        x: x,
        y: y,
        width,
        height,
        draggable: true,
      });
      this._node.on("dragstart", () => {
        console.log(1);

        if (this._group) {
          this._group.hide();
        }
      });
      this._node.on("dragend", () => {
        console.log(2);

        if (this._group) {
          this.createEditBar();
        }
      });
      this._layer?.add(this._node);
      this.createTransformer();
      this._layer?.draw();
    }
  }

  //创建截图框选择、变换器
  createTransformer() {
    this._transformer = new Konva.Transformer({
      node: this._node,
      rotateEnabled: false,
      anchorSize: 4,
    });
    this._transformer.on("transformstart", () => {
      if (this._group) {
        this._group.hide();
      }
    });
    this._transformer.on("transformend", () => {
      this.createEditBar();
    });
    this._layer?.add(this._transformer);
  }

  //获取截图框的信息
  getNodeAttrs() {
    let { x, y, width, height, scaleX, scaleY } = this._node.getAttrs();
    scaleX = scaleX || 1;
    scaleY = scaleY || 1;
    return {
      x,
      y,
      width: width * scaleX,
      height: height * scaleY,
    };
  }
  //获取矩形信息
  getRect(startPoint: any, endPoint: any) {
    // const width = Math.abs(startPoint.x - endPoint.x);
    // const height = Math.abs(startPoint.y - endPoint.y);
    const width = startPoint.x - endPoint.x;
    const height = startPoint.y - endPoint.y;

    return {
      x: this.Min(startPoint.x, endPoint.x),
      y: this.Min(startPoint.y, endPoint.y),
      width,
      height,
    };
  }

  Min(x: number, y: number) {
    return x - y < 0 ? x : y;
  }
  //获取屏幕百分比
  getRatio() {
    var ratio = 0;
    var screen: any = window.screen;
    var ua = navigator.userAgent.toLowerCase();
    if (window.devicePixelRatio !== undefined) {
      ratio = window.devicePixelRatio;
    } else if (~ua.indexOf("msie")) {
      if (screen.deviceXDPI && screen.logicalXDPI) {
        ratio = screen.deviceXDPI / screen.logicalXDPI;
      }
    } else if (
      window.outerWidth !== undefined &&
      window.innerWidth !== undefined
    ) {
      ratio = window.outerWidth / window.innerWidth;
    }

    if (ratio) {
      ratio = Math.round(ratio * 100);
    }
    return ratio;
  }

  //获取截图
  getScreenShot(): string {
    const tempCanvas = document.createElement("canvas");
    let zoom = this.getRatio() / 100;
    const context = tempCanvas.getContext("2d") as CanvasRenderingContext2D;
    let { x, y, width, height } = this.getNodeAttrs();
    if (this._konvaStage) {
      x *= this._konvaStage?.bufferCanvas?.pixelRatio;
      y *= this._konvaStage?.bufferCanvas?.pixelRatio;
      width *= this._konvaStage?.bufferCanvas?.pixelRatio;
      height *= this._konvaStage?.bufferCanvas?.pixelRatio;
    }
    if (width <= 0 || height <= 0) {
      width = Math.abs(width);
      height = Math.abs(height);
      x -= width;
      y -= height;
    }
    tempCanvas.setAttribute("width", String(width));
    tempCanvas.setAttribute("height", String(height));

    context.drawImage(
      this._viewer?.drawer.canvas as any,
      x,
      y,
      width,
      height,
      0,
      0,
      width,
      height
    );
    return tempCanvas.toDataURL();
  }

  //开启截图模式
  start() {
    this._mouseTracker?.setTracking(true);
    this._drawNodeEnding = false;
    this.createMask();
    this.addEvent();
  }

  //关闭截图模式
  close() {
    if (this._mouseTracker && this._konvaStage) {
      this._mouseTracker.setTracking(false);
    }
    if (this._layer) {
      this._layer.destroy();
      this._node = null;
      this._group = null;
    }
    this._src = null;
    this.observers = [];
    if (this._canvasDiv) {
      this._canvasDiv.style.zIndex = "1";
    }
  }
  //销毁组件
  destroy() {
    this.close();
    if (this._konvaStage) {
      this._konvaStage.destroy();
    }
    this._konvaStage = null;
    this._mouseTracker = null;
  }

  //添加事件订阅者
  attach(observer: ScreenshotObserver) {
    this.observers.push(observer);
  }

  //截图成功
  notifyAllObservers(screenshot?: string) {
    this.observers.forEach((observer) => {
      observer.update(screenshot);
    });
  }

  //视图大小调整
  resize() {
    if (this._containerWidth !== this._viewer?.container.clientWidth) {
      this._containerWidth = this._viewer?.container.clientWidth as number;
      (<HTMLDivElement>this._canvasDiv).style.width =
        this._containerWidth + "px";
      this._konvaStage?.width(this._containerWidth);
      this._mask?.width(this._containerWidth);
      this._layer?.width(this._containerWidth);
    }

    if (this._containerHeight !== this._viewer?.container.clientHeight) {
      this._containerHeight = this._viewer?.container.clientHeight as number;
      (<HTMLDivElement>this._canvasDiv).style.height =
        this._containerHeight + "px";
      this._konvaStage?.height(this._containerHeight);
      this._mask?.height(this._containerHeight);
      this._layer?.height(this._containerHeight);
    }
    this._layer?.draw();
  }
}

//订阅对象
export interface ScreenshotObserver {
  update: (screenshot?: string) => any;
}
