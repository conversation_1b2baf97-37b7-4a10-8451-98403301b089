<template>
  <div class="image-zooms">
    <div class="zoom-item" @click="isShow = !isShow">{{ zoom }}X</div>
    <div v-if="isShow" id="image-zoom">
      <div
        v-for="item in zoomRatios"
        :key="item"
        class="image-zoom-button"
        :class="{ active: zoom === item }"
        @click="zoomTo(item)"
      >
        {{ item }}X
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, defineComponent, PropType, ref, toRefs, watch } from "vue";
const props = defineProps({
  //预设放大缩小倍率数组
  zoomRatios: {
    type: Object as PropType<Array<number>>,
    default: [1, 4, 10, 20, 40],
  },
  zoom: {
    type: Number,
    required: true,
  },
  //openseadragon对象
  viewer: {
    type: Object as PropType<OpenSeadragon.Viewer>,
    required: true,
  },
  //扫描倍率
  rate: {
    default: 20,
  },
  //3d系数
  registration3D: {
    default: 1,
  },
});
const { viewer, rate, registration3D } = toRefs(props);
const viewPort = computed(() => {
  return viewer.value?.viewport;
});
//显示隐藏
const isShow = ref(false);
//放大
function zoomTo(ratioValue: number) {
  if (viewPort.value) {
    const viewPortZoom =
      viewPort.value.imageToViewportZoom(ratioValue / rate.value) *
      registration3D.value;
    viewPort.value.zoomTo(viewPortZoom);
  }
}
</script>
<style scoped lang="scss">
.image-zooms {
  position: absolute;
  top: 8px;
  right: 45px;
  z-index: 6;
}
#image-zoom {
  box-sizing: border-box;
  position: absolute;
  top: 40px;
  right: 0px;
  width: 40px;
  background: #fff;
  border-radius: 8px;
  z-index: 99;
  overflow: hidden;
}

.image-zoom-button {
  // margin: 11px auto 0 auto;
  width: 40px;
  height: 35px;
  font-size: 12px;
  line-height: 35px;
  text-align: center;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #686e78;
  cursor: pointer;
  border-radius: 8px;

  &.active {
    background-color: #eeebfe;
  }
}

.zoom-item {
  margin: 0 auto;
  width: 40px;
  height: 28px;
  padding: 0 2px;
  background: rgba(238, 235, 254, 0.8);
  border-radius: 4px 4px 4px 4px;
  font-size: 12px;
  text-align: center;
  line-height: 28px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #666b77;
}
</style>
