<template>
  <div id="image-browser">
    <div id="image-browser-main"></div>
    <div id="image-browser-navigator" :style="'border:1px solid red'"></div>
  </div>
</template>

<script lang="ts">
/**
 * 阅片组件
 */

import OpenSeadragon from "openseadragon";
import { Sdpc, SdpcWs, SdpcPlug } from "sdpc-web-ts";
import { PicHead } from "sdpc-web-ts/dist/src/sdpc/picHead";
import { SliceSource } from "sdpc-web-ts/dist/src/sdpc/sdpc";
import {
  computed,
  defineComponent,
  onUnmounted,
  PropType,
  Ref,
  ref,
  toRefs,
  watch,
} from "vue";
import { ImageUtil } from "@/utils/ImageUtil";
import RcpWeb from "RcpWeb"; //项目配置
import useStore from "@/store";
export default defineComponent({
  name: "ImageBrowser",
  props: {
    options: {
      //openseadragon设置
      type: Object as PropType<OpenSeadragon.Options>,
      default: {},
    },
    //切片的所在地址
    slideUrl: {
      required: true,
    },
  },
  setup(props) {
    const { options, slideUrl } = toRefs(props);
    const picHead: Ref<null | PicHead> = ref(null);

    const { zjsection } = useStore();
    const viewer: Ref<OpenSeadragon.Viewer | null> = ref(null); //openSeadragon查看器类
    const LoadingCompletedOrNot = ref(false);
    const sdpc = ref(null) as unknown as Ref<Sdpc | any>;
    let sdpcPlug = ref(null) as unknown as Ref<SdpcPlug | null>;
    //openSeadragon视图类
    const viewPort = computed(() => {
      return viewer.value?.viewport;
    });
    const sdpcWs: Ref<SdpcWs> = ref(null) as unknown as Ref<SdpcWs>;
    sdpcWs.value = new SdpcWs();

    const InitializeViewerState = ref(true);
    //3d标注系数
    const registration3D: Ref<number> = ref(1);
    //默认设置
    let defaultOptions: OpenSeadragon.Options = {
      id: "image-browser-main",
      prefixUrl: "/openseadragon/",
      navigatorId: "image-browser-navigator",
      navigatorAutoResize: true, //导航器大小自动调整
      navigatorBackground: "red",
      showNavigator: true, //显示导航图
      placeholderFillStyle: "#f3f3f3", //初始化时，图片未载入现实的样式，默认是白色
      debugGridColor: ["#000000"],
      minZoomImageRatio: 0.3,
      // maxZoomPixelRatio: 1,
      smoothTileEdgesMinZoom: Infinity,
      zoomPerScroll: 1.3, //滚轮放大倍率
      navigatorOpacity: 1, //导航图透明度
      showZoomControl: false, //不显示放大按钮
      showHomeControl: false, //显示主页按钮
      showFullPageControl: false, //
      gestureSettingsMouse: {
        clickToZoom: false,
        pinchToZoom: false,
      },
      preserveImageSizeOnResize: true,

      visibilityRatio: 0.5,
      // collectionMode: true,
      // immediateRender: true,
      // debugMode: true,
      iOSDevice: true,
      constrainDuringPan: true,
      maxImageCacheCount: 10,
      imageLoaderLimit: 0,
      springStiffness: 3, //缩放过渡效果，，值越小，则动画越趋于柔和
      animationTime: 1, //放大或缩小动画过渡的时间，这个值越小，过渡快
      crossOriginPolicy: "Anonymous", // 图片异步问题
      // autoResize: true,
      // pixelsPerWheelLine:
    };
    watch(
      () => slideUrl.value,
      async (url: any) => {
        try {
          await destroyViewer();
          if (url) {
            //正则表示是否为系统文件，系统文件路径为插件返回
            recordNewestUrl.value = url;

            Startws(url);
          }
        } catch (error) {
          console.log("未下载插件或未激活");
        }
      },
      {
        immediate: true,
      }
    );
    //复用ws
    async function Startws(
      url: any,
      isFrozen?: Boolean,
      WebSocketUrl?: string
    ) {
      console.log("参数", url, isFrozen);

      var path =
        new RegExp(
          "^([a-zA-Z]:(([\\\\/])[^\\\\/:*?<>|]+)*([\\\\/])[^\\\\/:*?<>|]+\\.[^\\\\/:*?<>|]+,)*[a-zA-Z]:(([\\\\/])[^\\\\/:*?<>|]+)*([\\\\/])[^\\\\/:*?<>|]+\\.[^\\\\/:*?<>|]+$"
        ).test(url.diskPath || url) || //盘符正则
        new RegExp(
          /^\/(?:[\w\u4e00-\u9fa5.-]+\/)*[\w\u4e00-\u9fa5.-]+\.\w+$/
        ).test(url.diskPath) ||
        new RegExp(
          /^\\\\(\d{1,3}\.){3}\d{1,3}(\\[^<>:"/\\|?*\x00-\x1F]+)+\\[^<>:"/\\|?*\x00-\x1F]+\.[a-zA-Z0-9]{1,5}$/
        ).test(url.diskPath); //网络地址正则

      console.log("对比", url, isFrozen, path);
      if (path || isFrozen) {
        sdpcPlug.value = new SdpcPlug({
          wsUrl: `${WebSocketUrl}?AppKey=${url.expertsId}`,
          userId: url.uploadId,
        });
        if (!sdpcPlug.value?.webSocket) {
          sdpcPlug.value.setwebSocket(
            `${WebSocketUrl}?AppKey=${url.expertsId}`
          );
        }
        (async function setws() {
          let i = 0;
          if (sdpcPlug.value?.webSocket.readyState != 1) {
            setTimeout(async () => {
              if (sdpcPlug.value?.webSocket.readyState != 1) {
                if (i == 4) {
                  i++;
                  return;
                }
                setws();
              } else {
                await sdpcPlug.value.open({ diskPath: url.diskPath });
                createViewer(sdpcPlug.value as SliceSource);
                recordNewestUrl.value = sdpcPlug.value;
                return;
              }
            }, 400);
          } else {
            await sdpcPlug.value.open({ diskPath: url.diskPath });
            createViewer(sdpcPlug.value as SliceSource);
            return;
          }
        })();
      } else {
        await createViewer(url as SliceSource);
      }
    }

    //isFrozen是否为插件冰冻转发
    let recordNewestUrl = ref();
    async function refreshViewer(
      url: any,
      isFrozen?: Boolean,
      WebSocketUrl?: string
    ) {
      recordNewestUrl.value = url;
      if (InitializeViewerState.value) {
        await destroyViewer();
        console.log(url, isFrozen, WebSocketUrl);

        Startws(url, isFrozen, WebSocketUrl);
      }
    }
    //创建病理图查看器
    // const hevcDecoderUrl: any = 'https://rcp.sqray.com:5500/hevc.js'https://dev.sqray.com/webapp; //切片地址
    const hevcDecoderUrl: any = RcpWeb.decodeSdpcCoreJsPath; //切片地址
    const hevcDecoderWasmUrl: any = RcpWeb.decodeSdpcCoreWasmPath; //切片地址

    //创建病理图查看器
    async function createViewer(
      slideUrl: SliceSource
    ): Promise<OpenSeadragon.Viewer | undefined> {
      if (!slideUrl) return;
      if (RcpWeb.IsBackEnd) {
        if (slideUrl instanceof File) {
          sdpc.value = null;
          InitializeViewerState.value = false;
          await Slice(slideUrl);
        } else {
          await BackEndSlice(slideUrl);
        }
      } else {
        sdpc.value = null;
        InitializeViewerState.value = false;
        await Slice(slideUrl);
      }
    }
    //本地创建解码
    async function Slice(slideUrl: SliceSource) {
      try {
        console.log(
          slideUrl,
          new Sdpc(slideUrl, hevcDecoderUrl, hevcDecoderWasmUrl),
          "+++++++result"
        );
        sdpc.value = new Sdpc(slideUrl, hevcDecoderUrl, hevcDecoderWasmUrl);

        if (!sdpc.value.isLoadDecoder) {
          await sdpc.value.loadDecoder();
        }

        const result = await sdpc.value.getPicHead();
        if (!result) {
          InitializeViewerState.value = true;
        }
        if (result) {
          zjsection.sectionDetailFn({
            rate: result.rate,
            srcHeight: result.srcHeight,
            srcWidth: result.srcWidth,
            ruler: result.ruler,
          });
          picHead.value = result;
          await sdpc.value.getExtraInfo();
          // registration3D.value = parseInt(result.ruler)
          setRegistration3D(result.ruler, result.rate);
          const openSeadragonOptions = await intOption();
          // console.log("openSeadragonOptions",openSeadragonOptions);
          viewer.value = OpenSeadragon(openSeadragonOptions);
          if (viewer.value) {
            /**
             * https://github.com/openseadragon/openseadragon/issues/1970
             * 图片加载失败，重新加载图片
             * */
            viewer.value.addHandler("tile-load-failed", (ev) => {
              setTimeout(function () {
                if (ev.tile) {
                  ev.tile.exists = true;
                }
              }, 1);
            });

            defaultOptions.showNavigator = true;

            viewer.value.addHandler("open", async (ev: any) => {
              if (sdpc.value.sliceUrl) {
                recordNewestUrl.value != sdpc.value.sliceUrl
                  ? (InitializeViewerState.value = true)
                  : (InitializeViewerState.value = false);
              }
              if (InitializeViewerState.value) {
                await destroyViewer();
                await Startws(recordNewestUrl.value);
              }
              InitializeViewerState.value = true;
              defaultOptions.showNavigator = true;
              LoadingCompletedOrNot.value = true;
            });
            viewer.value.addHandler("open-failed", (ev: any) => {
              defaultOptions.showNavigator = true;
              InitializeViewerState.value = true;
            });
          }
          return viewer.value;
        }
      } catch (error) {
        console.log(
          error,
          "未能正确加载切片，请确保网络正常和上传的切片格式正确,暂时只支持sdpc格式切片"
        );
        destroyViewer();
        InitializeViewerState.value = true;
        // alert(
        //   "未能正确加载切片，请确保网络正常和上传的切片格式正确,暂时只支持sdpc格式切片"
        // );
      }
    }
    //后端解码
    const { getSliceID, idSlices, getExtraInfo, hasPlug } = ImageUtil(); //后端代码接口
    async function BackEndSlice(slideUrl: SliceSource) {
      try {
        if (!slideUrl) return;
        idSlices.value = null;
        let extraInfo: any;
        if (slideUrl instanceof SdpcPlug) {
          extraInfo = await sdpcPlug.value?.getInfo();
        } else if (typeof slideUrl === "string") {
          await getSliceID(slideUrl as string);
          if (!idSlices.value) return;
          extraInfo = await getExtraInfo(idSlices.value);
        }
        setRegistration3D(
          extraInfo?.ruler as number,
          extraInfo?.rate as number
        );

        picHead.value = extraInfo;
        console.log(picHead.value, "+++++++");

        zjsection.sectionDetailFn({
          rate: picHead.value?.rate,
          srcHeight: picHead.value?.srcHeight,
          srcWidth: picHead.value?.srcWidth,
          ruler: picHead.value?.ruler,
        });
        const openSeadragonOptions = await intOptionBackEnd(
          idSlices.value,
          extraInfo,
          hasPlug.value
        );
        let view = OpenSeadragon(openSeadragonOptions);
        viewer.value = view;
        if (viewer.value) {
          defaultOptions.showNavigator = true;
          viewer.value.addHandler("open", async (ev: any) => {
            if (slideUrl instanceof SdpcPlug) {
            } else {
              // let Urldata = store.state.SlicedataModule.Slice.url;
              // if (Urldata) {
              //   recordNewestUrl.value != Urldata
              //     ? (InitializeViewerState.value = true)
              //     : (InitializeViewerState.value = false);
              // }
              // if (InitializeViewerState.value) {
              //   await destroyViewer();
              //   await Startws(recordNewestUrl.value);
              // }
            }
            InitializeViewerState.value = true;
            defaultOptions.showNavigator = true;
            LoadingCompletedOrNot.value = true;
          });
          viewer.value.addHandler("open-failed", (ev: any) => {
            // console.log(ev);
            defaultOptions.showNavigator = true;
            InitializeViewerState.value = true;
          });
        }
      } catch (error) {
        console.log(
          error,
          "未能正确加载切片，请确保网络正常和上传的切片格式正确,暂时只支持sdpc格式切片"
        );
        destroyViewer();
        InitializeViewerState.value = true;
      }
    }

    //根据3d系数进行缩放转换
    function setRegistration3D(ruler: number, rate: number) {
      const Mpp3D = 0.242797; // 分辨率
      const Rate3D = 20; //扫描倍率
      const Scale3D = 0.242797 / 0.5; //分辨率 0.242797um/pixel, 100pixels = 50um 映射比例
      registration3D.value = (ruler / Mpp3D) * Scale3D * (rate / Rate3D);
    }

    async function destroyViewer() {
      LoadingCompletedOrNot.value = false;
      try {
        //判断视图是否存在
        if (viewer.value && viewer.value.destroy) {
          if (viewer.value.navigator) {
            viewer.value.navigator.destroy();
          }
          viewer.value.destroy();
          viewer.value = null;
          // InitializeViewerState.value = true
        }
        //解码缓存数据是否存在
        if (sdpc.value) {
          if (sdpc.value.sdpcWs) {
            console.log("进入sdpcWs", sdpc.value);
            sdpcWs.value?.dispose();
            sdpc.value?.disposeTable();
            // sdpc.value.decodeHevc.dispose()
          }

          sdpc.value.decodeHevc?.dispose();
          sdpc.value = null;
          // InitializeViewerState.value = true
        }
        if (sdpcPlug.value) {
          sdpcPlug.value.dispose();
          sdpcPlug.value = null;
        }
        LoadingCompletedOrNot.value = false;
        console.log("销毁完毕—", sdpc.value);
        return true;
      } catch (error) {
        alert(error);
        InitializeViewerState.value = true;

        return false;
      }
      // debugger
    }

    //获取瓦片的url
    async function getTileUrl(
      level: number,
      x: number,
      y: number
    ): Promise<string> {
      const url = await sdpc.value.getTileUrl(level, x, y);
      return url;
    }

    //设置tileSource,设置切片，瓦片的大小
    async function setTileSources(openSeadragonOptions: OpenSeadragon.Options) {
      await sdpc.value.getPicInfo();
      const tile = await sdpc.value.getTileOption();

      openSeadragonOptions.minPixelRatio = tile.scale; //默认为0.5 需根据切片倍率设置 最小像素比.值越小，清晰度越高，体积越大
      const maxLevel = tile.level - 1;
      console.log(tile, "tile");

      openSeadragonOptions.tileSources = {
        tileWidth: tile.width, //瓦块宽高
        tileHeight: tile.height,
        height: tile.srcHeight, //切片整体宽高
        width: tile.srcWidth,
        maxLevel: maxLevel,
        minLevel: 0,
        getTileUrl: function (level: any, x: any, y: any) {
          return JSON.stringify({ level: maxLevel - level, x, y });
        },
      };
      await setLevelScale(
        openSeadragonOptions,
        openSeadragonOptions.minPixelRatio
      );
    }

    /**
     * https://github.com/openseadragon/openseadragon/issues/1922
     * 重写缩放比处理逻辑
     * 根据不同倍率切片动态传值
     * 切片默认倍率为0.5
     * */
    function setLevelScale(
      openSeadragonOptions: OpenSeadragon.Options,
      ratio: number = 0.5
    ) {
      (openSeadragonOptions.tileSources as any).getLevelScale = function (
        level: number
      ): any {
        const levelScaleCache: { [key: number]: number } = {};
        let i;
        for (i = 0; i <= this.maxLevel; i++) {
          levelScaleCache[i] = 1 / Math.pow(1 / ratio, this.maxLevel - i);
        }
        this.getLevelScale = function (_level: number) {
          return levelScaleCache[_level];
        };
        return this.getLevelScale(level);
      };
    }

    // 初始化设置
    async function intOption(): Promise<OpenSeadragon.Options> {
      const openSeadragonOptions = Object.assign(
        {},
        defaultOptions,
        { maxZoomPixelRatio: 2 * registration3D.value }, //放大的最大倍率
        options.value
      );
      await setTileSources(openSeadragonOptions);
      setImageJob(false);
      return openSeadragonOptions;
    }
    //初始化设置
    const intOptionBackEnd = async (
      urlPath?: string,
      extraInfo?: any,
      hasPlug?: any
    ) => {
      const BackEndUrl = RcpWeb.BackEndUrl;
      const openSeadragonOptions = { ...defaultOptions };
      openSeadragonOptions.minPixelRatio = extraInfo.scale; // 默认为0.5 需根据切片倍率设置 最小像素比.值越小，清晰度越高，体积越大
      const maxLevel = (extraInfo.hierarchy as any) - 1;
      (openSeadragonOptions.tileSources as any) = {
        tileWidth: extraInfo.sliceWidth,
        tileHeight: extraInfo.sliceHeight,
        height: extraInfo.srcHeight,
        width: extraInfo.srcWidth,
        maxLevel: maxLevel,
        minLevel: 0,
        getTileUrl: function (level: number, x: number, y: number) {
          return urlPath
            ? `${
                hasPlug ? RcpWeb.plugStorageUploaUrl : BackEndUrl
              }/api/SliceService/image-slices/${urlPath}/tile?Level=${
                maxLevel - level
              }&X=${x}&Y=${y}`
            : JSON.stringify({ level: maxLevel - level, x, y });
        },
      };
      setLevelScale(openSeadragonOptions, openSeadragonOptions.minPixelRatio);
      setImageJob(urlPath ? true : false);
      return openSeadragonOptions;
    };

    /**
     * https://github.com/openseadragon/openseadragon/issues/1707
     * 重写图片的加载规则
     * 异步加载
     * */
    function setImageJob(backEnd: boolean) {
      if (backEnd) {
        (OpenSeadragon as any).ImageJob.prototype.start = start;
      } else {
        (OpenSeadragon as any).ImageJob.prototype.start = async function () {
          try {
            let self = this;
            await loadImage(self);
          } catch (error) {}
        };
      }

      function loadImage(_this: any, count: number = 2500) {
        return new Promise(async (resolve, reject) => {
          if (count > 0) {
            const self = _this;
            const { level, x, y } = JSON.parse(self.src); //需要tileSources.getTileUrl方法转换返回src的格式
            let url = await sdpcPlug.value?.getTile(level, x, y);
            self.image = new Image();
            self.image.onload = function () {
              URL.revokeObjectURL(self.image.src);
              self.finish(true);
            };
            self.image.onabort = self.image.onerror = async function (
              err: any
            ) {
              URL.revokeObjectURL(self.image.src);
              count -= 1;
              await loadImage(_this, count);
            };
            self.jobId = window.setTimeout(function () {
              self.errorMsg =
                "Image load exceeded timeout (" + self.timeout + " ms)";
              self.finish(false);
            }, self.timeout);
            if (url) {
              self.image.src = url;
              resolve(true);
            } else {
              count -= 1;
              await loadImage(_this, count);
            }
          }
        });
      }
      function start(this: any) {
        var self = this;
        var selfAbort = this.abort;

        this.image = new Image();

        this.image.onload = function () {
          self.finish(true);
        };
        this.image.onabort = this.image.onerror = function () {
          self.errorMsg = "Image load aborted";
          self.finish(false);
        };

        this.jobId = window.setTimeout(function () {
          self.errorMsg =
            "Image load exceeded timeout (" + self.timeout + " ms)";
          self.finish(false);
        }, this.timeout);

        // Load the tile with an AJAX request if the loadWithAjax option is
        // set. Otherwise load the image by setting the source proprety of the image object.
        if (this.loadWithAjax) {
          //@ts-ignore
          this.request = $.makeAjaxRequest({
            url: this.src,
            withCredentials: this.ajaxWithCredentials,
            headers: this.ajaxHeaders,
            responseType: "arraybuffer",
            //@ts-ignore
            success: function (request) {
              var blb;
              // Make the raw data into a blob.
              // BlobBuilder fallback adapted from
              // http://stackoverflow.com/questions/15293694/blob-constructor-browser-compatibility
              try {
                blb = new window.Blob([request.response]);
              } catch (e) {
                var BlobBuilder = //@ts-ignore
                  window.BlobBuilder || //@ts-ignore
                  window.WebKitBlobBuilder || //@ts-ignore
                  window.MozBlobBuilder || //@ts-ignore
                  window.MSBlobBuilder; //@ts-ignore
                if (e.name === "TypeError" && BlobBuilder) {
                  var bb = new BlobBuilder();
                  bb.append(request.response);
                  blb = bb.getBlob();
                }
              }
              // If the blob is empty for some reason consider the image load a failure.
              if (blb.size === 0) {
                self.errorMsg = "Empty image response.";
                self.finish(false);
              }
              // Create a URL for the blob data and make it the source of the image object.
              // This will still trigger Image.onload to indicate a successful tile load.
              var url = (window.URL || window.webkitURL).createObjectURL(blb);
              self.image.src = url;
            },
            //@ts-ignore
            error: function (request) {
              self.errorMsg = "Image load aborted - XHR error";
              self.finish(false);
            },
          });

          // Provide a function to properly abort the request.
          this.abort = function () {
            self.request.abort();

            // Call the existing abort function if available
            if (typeof selfAbort === "function") {
              selfAbort();
            }
          };
        } else {
          if (this.crossOriginPolicy !== false) {
            this.image.crossOrigin = this.crossOriginPolicy;
          }
          this.image.src = this.src;
        }
      }
    }

    //销毁组件
    onUnmounted(() => {
      destroyViewer();
    });

    return {
      createViewer,
      destroyViewer,
      refreshViewer,
      sdpc,
      viewer,
      viewPort,
      picHead,
      registration3D,
      sdpcWs,
      LoadingCompletedOrNot,
    };
  },
});
</script>
<style scoped lang="scss">
#image-browser {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;

  #image-browser-main {
    height: 100%;
    width: 100%;
  }

  #image-browser-navigator {
    position: absolute !important;
    z-index: 9;
    width: 67px;
    height: 103px;
    top: 8px;
    left: 7px;
  }
}
</style>
