<template>
  <div id="image-scales">
    <span class="scales-value">{{ scales }}</span
    ><br />
    <span class="scales-line" :style="{ width: scalesWidth }"></span>
  </div>
</template>
<script lang="ts">
/**
 * 比例尺
 */
import { Sdpc } from "sdpc-web-ts";
import { PicHead } from "sdpc-web-ts/dist/src/sdpc/picHead";
import { defineComponent, PropType, Ref, ref, toRefs, watch } from "vue";
import RcpWeb from "RcpWeb"; //项目配置
import useStore from "@/store";
export default defineComponent({
  name: "ImageScales",
  props: {
    LoadingCompletedOrNot: {
      //是否加载完成
      type: Boolean,
      required: true,
      default: false,
    },
    sdpc: {
      type: Object as PropType<Sdpc>,
    },
    zoom: {
      type: Number,
      required: true,
    },
    registration3D: {
      type: Number,
      required: true,
    },
  },
  setup(props) {
    const { LoadingCompletedOrNot, sdpc, zoom, registration3D } = toRefs(props);
    const slideInfo: Ref<PicHead | null> = ref(null);
    // const ruler = ref(0); /*一个像素对应实际的大小，单位为um*/
    // const rate = ref(0); /*扫描倍率10x 20x 40x 100x*/
    const scales = ref("");
    const scalesWidth = ref("100px");
    const registration = ref();
    const { zjsection } = useStore();
    watch(
      () => zjsection.sectionDetail,
      async (n) => {
        if (!n.rate) return;
        nextTick(() => {
          try {
            slideInfo.value = zjsection.sectionDetail;
            getScales();
          } catch (error) {
            console.log(error);
          }
        });
      },
      { immediate: true }
    );
    watch(
      () => registration3D.value,
      async (n) => {
        if (registration3D.value) {
          try {
            registration.value = registration3D.value;
          } catch (error) {
            console.log(error);
          }
        }
      },
      { immediate: true }
    );
    watch(
      () => zoom.value,
      async (n) => {
        console.log(n, "zoom+++");
        if (n) {
          getScales();
        }
      },
      { immediate: true, deep: true }
    );
    function getScales() {
      if (slideInfo.value) {
        let ruler = 0; /*一个像素对应实际的大小，单位为um*/
        let rate = 0; /*扫描倍率10x 20x 40x 100x*/
        ruler = slideInfo.value.ruler;
        rate = slideInfo.value.rate;
        const scalesList = [
          0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, 25, 50, 100, 250, 500, 1000,
          2500, 5000, 10000, 25000, 50000,
        ];
        let distanceFor1Mm = 1000;
        let minPixel = 100;
        let CurPixelSize = (ruler * rate) / zoom.value / registration.value;
        let distanceFor100Pixel = CurPixelSize * minPixel; //um
        let distance;
        let uint;
        let uintDistance;
        for (let i = 0; i < scalesList.length; i++) {
          if (distanceFor100Pixel < scalesList[i]) {
            distance = scalesList[i] / CurPixelSize; //pixel
            uint = distanceFor100Pixel < distanceFor1Mm ? "um" : "mm";
            uintDistance =
              scalesList[i] < distanceFor1Mm
                ? `${scalesList[i]}um`
                : `${scalesList[i] / distanceFor1Mm}mm`;
            scales.value = uintDistance;
            scalesWidth.value = distance + "px";
            break;
          }
        }
      }
    }

    return {
      scales,
      scalesWidth,
    };
  },
});
</script>
<style scoped lang="scss">
#image-scales {
  text-align: center;

  .scales-value {
    color: #4d545f;
    font-size: 12px;
  }

  .scales-line {
    display: inline-block;
    width: 100px;
    border: 2px solid #303133;
    border-top: none;
    height: 8px;
  }
}
</style>
