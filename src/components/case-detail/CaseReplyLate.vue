<template>
  <div class="review">
    <div class="review-title">回复意见</div>
    <div class="review-content-center">
      <van-field
        v-model="message"
        rows="9"
        autosize
        show-word-limit
        type="textarea"
        :maxlength="100"
        placeholder="请输入"
      />
    </div>
    <div style="display: flex; justify-content: center">
      <van-button class="case-diagnose" color="#650FAA" @click="confirmReplys"
        >确认回复</van-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import { Toast } from "vant";
const router = useRouter();
const route = useRoute();
const { id } = route.query;
const message = ref("");
const confirmReplys = async () => {
  if (!message.value) {
    return Toast.fail("请输入回复意见");
  } else {
    await API.expertFunctionApi.putConfirmReply.request({
      bodyParams: {
        Id: Number(id),
        Str: message.value,
      },
    });
    Toast.success("回复成功");
    router.go(-1);
  }
};
</script>
<style lang="scss" scoped>
.review {
  position: relative;
  padding: 16px;
}
.review-title {
  margin-bottom: 12px;
  font-size: 17px;
  font-weight: bold;
  line-height: 19.89px;
  color: #000000;
}
.review-content {
  padding: 6px 9px;
  height: 60px;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  color: #8b96a6;
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  overflow-y: auto;
}
.review-content-center {
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  overflow: hidden;
}
:deep(.van-cell) {
  padding: 9px;
}
.case-diagnose {
  margin-top: 14px;
  width: 128px;
  height: 32px;
  border-radius: 16px;
}
</style>
