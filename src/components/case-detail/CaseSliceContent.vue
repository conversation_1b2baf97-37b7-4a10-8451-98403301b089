<template>
  <div class="case-info-content">
    <div class="form-title">
      <div></div>
      <div>切片信息</div>
    </div>
    <div v-if="zjsection.sectionList.length > 0" class="form-content">
      <!--切片信息Card-->
      <div
        class="case-slice-item"
        v-for="(item, index) in zjsection.sectionList"
        :key="index"
      >
        <div class="case-slice-content">
          <!--缩略图 左-->
          <div class="case-slice-thumbnail">
            <van-image
              class="imageClass"
              :src="item.SliceLabel || DefaultSection"
              fit="contain"
              @click="showImagePreview(item.SliceLabel)"
              :show-loading="false"
              :show-error="false"
            />
          </div>
          <!--宏观图 中-->
          <div class="case-slice-macro-img">
            <van-image
              class="imageClass"
              :src="item.SliceMacroImageUrl || DefaultSection"
              fit="contain"
              @click="showImagePreview(item.SliceMacroImageUrl)"
              :show-loading="false"
              :show-error="false"
            />
          </div>
          <!--基本信息 右-->
          <div class="case-slice-text-info">
            <div class="slice-name">
              <div class="g-ellipsis">{{ item.FileName }}</div>
            </div>
            <div class="slice-text">
              <div class="g-ellipsis">染色：{{ item.DyeingName }}</div>
            </div>
            <div class="slice-text slice-texts">
              <span
                :style="item.Status === 2 ? 'color:#beb833' : 'color:#828386'"
                >{{ item.StatusName }}</span
              >
              <div v-if="sliceEvaluation">
                <div
                  v-if="
                    (item.Evaluation && item.Evaluation < 4) ||
                    (item.Evaluation === 4 && item.SliceDescription)
                  "
                  class="sliceEvaluation sliceEvaluations"
                >
                  已评价
                </div>
                <div v-else class="sliceEvaluation">未评价</div>
              </div>
            </div>
          </div>
          <div class="examine" @click="examineClick(item)">查看</div>
        </div>
        <div class="describe">描述：{{ item.Remark }}</div>
      </div>
    </div>
    <van-empty v-else description="暂无切片信息" />
  </div>
</template>

<script lang="ts" setup>
import { Toast, ImagePreview } from "vant";
import useStore from "@/store";
import DefaultSection from "@/assets/images/default-section.png";
const router = useRouter();
const route = useRoute();
const { zjsection, tokens } = useStore();
const { id } = route.query;
const props = defineProps({
  sliceEvaluation: {
    type: Boolean,
    default: false,
  },
});
/* 预览图片 */
const showImagePreview = function (url?: string) {
  if (!url) {
    Toast.fail("图片路径不存在");
    return false;
  }
  ImagePreview({
    images: [url],
  });
};

//查看详情
const examineClick = (item: any) => {
  console.log(item);

  // if (!item.SliceUrl) {
  //   return Toast.fail("切片未上传");
  // }
  // zjsection.selectSectionFn(item);
  if (tokens.token) {
    router.push({
      path: "/examineDetails",
      query: { id: item.Id, caseId: id },
    });
  } else {
    router.push({ path: "/shareImage", query: { id: item.Id, caseId: id } });
  }
};
</script>
<style lang="scss" scoped>
.form-content {
  margin: 0 14px;
}

.case-slice-item {
  box-sizing: border-box;
  border-bottom: 1px solid #e6ecfb;
}
.case-slice-content {
  box-sizing: border-box;
  height: 80px;
  width: 100%;
  display: flex;
  align-items: center;

  .case-slice-thumbnail {
    .imageClass {
      display: flex;
      width: 51px;
      height: 51px;
    }
  }

  .case-slice-macro-img {
    .imageClass {
      display: flex;
      width: 51px;
      height: 51px;
      margin: 0 8px;
    }
  }

  .case-slice-text-info {
    width: 1px;
    flex: auto;
    font-size: 14px;
    font-weight: 400;
    color: #4d545f;
  }
}
.slice-name {
  width: 140px;
}

.slice-text {
  margin-top: 5px;
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}
.slice-texts {
  display: flex;
  align-items: center;
}
.examine {
  width: 61px;
  height: 32px;
  background: #f0e7f7;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 32px;
  color: #650faa;
  font-size: 12px;
}
.describe {
  font-size: 12px;
  font-weight: 600;
  color: #8b96a6;
  line-height: 14px;
  margin-bottom: 10px;
  word-break: break-all;
}

.sliceEvaluation {
  margin-left: 10px;
  width: 44px;
  height: 20px;
  background: #fff5ea;
  border-radius: 4px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #ff8743;
}
.sliceEvaluations {
  background: #f1f8e7;
  color: #6ab10e;
}
</style>
