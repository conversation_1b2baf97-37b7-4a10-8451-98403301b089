<template>
  <div>
    <div v-if="opinionList.length > 0" class="form-content">
      <div
        v-for="(item, index) in opinionList"
        :key="index"
        class="form-content-title"
      >
        <div class="form-content-time">
          <div class="form-content-time-title g-ellipsis">
            受邀专家：{{ item.ExpertName }}
          </div>
          <div class="form-content-time-date">{{ item.MsfContentsDate }}</div>
        </div>
        <div class="reason">
          <div class="reason-title">邀请理由：</div>
          <div class="reason-value">{{ item.Remark }}</div>
        </div>
        <div class="reason">
          <div class="reason-title">回复意见：</div>
          <div class="reason-value">{{ item.MsgContents }}</div>
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无邀请意见" />
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const { id } = route.query;
//获取邀请意见
const opinionList = ref<defs.ExpertInviteeDto[]>([]);
const getOpinionList = async (id: number) => {
  const res = await API.expertCaseManagementApi.getInvitationList.request({
    params: {
      Id: id,
      PageIndex: 1,
      PageSize: 50,
    },
  });
  opinionList.value = res.Data || [];
};
onMounted(() => {
  getOpinionList(Number(id));
});
</script>
<style lang="scss" scoped>
.form-content {
  margin: 0 14px;
}

.form-content-title {
  box-sizing: border-box;
  // height: 80px;
  padding: 8px 0;
  border-bottom: 1px solid #e6ecfb;
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.form-content div:last-child {
  border-bottom: none;
}

.form-content-title div:nth-child(2) {
  margin-bottom: 2px;
}
.form-content-time {
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.form-content-time-title {
  width: 200px;
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.form-content-time-date {
  font-size: 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
}

.reason {
  width: 100%;
  display: flex;
  line-height: 18px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
}

.reason-title {
  width: 65px;
}

.reason-value {
  flex: 1;
  word-break: break-all; //英文
  white-space: pre-wrap; //中文
}
</style>
