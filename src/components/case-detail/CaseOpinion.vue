<template>
  <div class="case-info-content">
    <div class="case-table">
      <div
        v-for="item in props.list"
        :key="item.id"
        :style="
          item.isActive
            ? 'background-color:#650FAA;color:#fff;font-weight:bold;border: 1px solid #650FAA'
            : ''
        "
        @click="tableClick(item)"
      >
        {{ item.title }}
      </div>
    </div>
    <div style="min-height: 300px">
      <component :is="tabComponent"></component>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  DefineComponent,
  ComponentOptionsMixin,
  VNodeProps,
  AllowedComponentProps,
  ComponentCustomProps,
  ExtractPropTypes,
} from "vue";

let tabComponent = ref();
const props = defineProps({
  list: {
    type: Array as () => {
      title: string;
      id: number;
      isActive: boolean;
      component: DefineComponent<
        {},
        {},
        {},
        {},
        {},
        ComponentOptionsMixin,
        ComponentOptionsMixin,
        {},
        string,
        VNodeProps & AllowedComponentProps & ComponentCustomProps,
        Readonly<ExtractPropTypes<{}>>,
        {}
      >;
    }[],
    default: () => [],
  },
});

watch(
  () => props.list,
  (newVal) => {
    tabComponent.value = newVal.find((item) => item.isActive)?.component;
  },
  { immediate: true }
);
const tableClick = (item: {
  isActive: boolean;
  component: DefineComponent<
    {},
    {},
    {},
    {},
    {},
    ComponentOptionsMixin,
    ComponentOptionsMixin,
    {},
    string,
    VNodeProps & AllowedComponentProps & ComponentCustomProps,
    Readonly<ExtractPropTypes<{}>>,
    {}
  >;
}) => {
  if (item.isActive) return;
  props.list.map((item) => {
    item.isActive = false;
  });
  item.isActive = true;
  tabComponent.value = item.component;
};
</script>
<style lang="scss" scoped>
.case-table {
  height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px 4px 0px 0px;
  & > div {
    flex: 1;
    height: 100%;
    text-align: center;
    line-height: 32px;
    font-size: 14px;
    font-weight: normal;
    color: #343840;
    border: 1px solid #e6ecfb;
  }
  & > div:nth-child(1) {
    border-radius: 4px 0 0 0;
  }
  & > div:nth-child(4) {
    border-radius: 0 4px 0 0;
  }
}
</style>
