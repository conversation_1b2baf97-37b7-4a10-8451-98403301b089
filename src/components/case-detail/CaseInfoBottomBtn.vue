<template>
  <div v-if="tokens.token" class="case-bottom-container">
    <van-button
      v-if="category === '6' && status === '7'"
      class="case-video"
      @click="retiredNumber(2)"
      color="#ff7272"
      :loading="sendBackLoading"
      >退回病例</van-button
    >
    <van-button
      v-if="[1, 3].includes(Number(category))"
      class="case-video case-videos-right"
      @click="gotoVideoPage()"
      color="#58bad1"
      :loading="videoLoading"
      >链接视频</van-button
    >
    <van-button
      v-if="[6, 7, 8].includes(Number(status)) && category === '1'"
      class="case-video case-videos"
      @click="retiredNumber(2)"
      color="#ff7272"
      :loading="sendBackLoading"
      >退回病例</van-button
    >
    <div
      v-if="[9].includes(Number(status)) && category === '1'"
      class="case-video case-videos"
      style="background-color: #ff7272"
      @click="withdrawReview"
    >
      撤回复核
    </div>
    <div
      v-if="category === '1' && status !== '9'"
      class="case-video case-bottom-left"
      style="background-color: #ffaf0c"
      @click="inviteClick"
    >
      邀请专家
    </div>
    <van-button
      v-if="category === '1' && status !== '9'"
      class="case-video case-bottom-left"
      @click="diagnoseClick"
      color="#650faa"
      :loading="diagnoseLoading"
      >诊断</van-button
    >
    <template v-if="category === '2'">
      <van-button
        class="case-diagnose case-bottom-left"
        @click="gotoVideoPage()"
        color="#58bad1"
        :loading="videoLoading"
        >链接视频</van-button
      >
      <div
        v-if="status !== '13'"
        class="case-diagnose case-bottom-left"
        style="background-color: #ff7272"
        @click="sendBack"
      >
        退回初审
      </div>

      <div
        v-if="status !== '13'"
        class="case-diagnose case-bottom-left"
        @click="signAndIssue"
      >
        同意签发
      </div>

      <div
        v-if="status !== '13'"
        class="case-diagnose case-bottom-left"
        @click="republishs"
      >
        复核
      </div>
    </template>
    <div
      v-else-if="category === '6' && status === '7'"
      class="case-diagnose case-bottom-left"
      @click="confirmingAppointment"
    >
      确认预约
    </div>
    <div
      v-else-if="category === '4' && status === '1'"
      class="case-diagnose case-bottom-left"
      @click="confirmReply"
    >
      回复
    </div>
    <van-button
      v-else-if="category === '4'"
      class="case-diagnose case-bottom-left"
      @click="gotoVideoPage()"
      color="#58bad1"
      :loading="videoLoading"
      >链接视频</van-button
    >
    <div
      v-else-if="category === '3' && status === '13'"
      class="case-diagnose case-bottom-left"
      style="background-color: #ff7272"
      @click="revocationClick"
    >
      撤回诊断
    </div>

    <!-- 确认预约弹窗 start -->
    <CaseOverlay
      v-model:show="show"
      overlayTitle="是否确认该冰冻预约？"
      @submit="submit"
    />
    <!-- 确认预约弹窗 end -->

    <!-- 退回初审弹窗 start -->
    <CaseOverlay
      v-model:show="sendBackShow"
      overlayTitle="是否退回初审？"
      @submit="sendBackClick"
    />
    <!-- 退回初审弹窗 end -->

    <!-- 同意签发弹窗 start -->
    <CaseOverlay
      v-model:show="signAndIssueShow"
      overlayTitle="是否同意签发？"
      @submit="signAndIssueClick"
    />
    <!-- 同意签发弹窗 end -->

    <!-- 撤回诊断弹窗 start -->
    <CaseOverlay
      v-model:show="revocationShow"
      overlayTitle="是否撤回诊断？"
      @submit="revocation"
    />
    <!-- 撤回诊断弹窗 end -->

    <!-- 撤回复核 start -->
    <CaseOverlay
      v-model:show="withdrawReviewShow"
      overlayTitle="是否撤回复核？"
      @submit="withdrawReviewClick"
    />
    <!-- 撤回复核 end -->
  </div>
</template>
<script lang="ts" setup>
import CaseOverlay from "../case-overlay/CaseOverlay.vue";
import { Toast } from "vant";
import useStore from "@/store";
import { Aes } from "@/utils/aesEncrypt";
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
import { getTeamUserList } from "@/api/zjEnd/regulates";
const { tokens } = useStore();
const props = defineProps({
  isShowIndex: {
    type: Number,
    default: 1,
  },
  status: {
    type: String,
    default: "",
  },
  pathologyNumber: {
    type: String,
  },
});
const emits = defineEmits(["confirmReplys", "republishClick"]);
const route = useRoute();
const router = useRouter();
const { id, status, category, siteId } = route.query;
//链接视频loading是否显示
const videoLoading = ref(false);
//退回病例loading是否显示
const sendBackLoading = ref(false);
//诊断loading是否显示
const diagnoseLoading = ref(false);
const show = ref(false);
// 退回初审
const sendBackShow = ref(false);
// 同意签发
const signAndIssueShow = ref(false);
// 撤回诊断
const revocationShow = ref(false);
//撤回复核
const withdrawReviewShow = ref(false);
const withdrawReview = () => {
  withdrawReviewShow.value = true;
};
const withdrawReviewClick = async () => {
  withdrawReviewShow.value = false;
  const res = await API.expertFunctionApi.postWithdrawalofreview.request({
    bodyParams: { Id: Number(id) },
  });
  if (res.Code === 200) {
    router.go(-1);
    Toast.success("撤回成功");
  }
};
//打开视频连接
const gotoVideoPage = async () => {
  videoLoading.value = true;
  const userInfo = getLocalStorage(LocalStorageName.user);
  const teamId = (
    await API.netEaseYunXinApi.postCreateTeam.request({
      bodyParams: {
        Tname: Aes.Encrypt(id as string),
        CaseId: Number(id),
        Msg: "Temp",
      },
    })
  ).Data; //创建im群聊

  await API.netEaseYunXinApi.postRoom.request({
    bodyParams: { ChannelName: id as string, CaseId: Number(id) },
  }); //创建音视频房间
  // await API.netEaseYunXinApi.postPullCaseTeam.request({
  //   bodyParams: {
  //     Id: Number(id),
  //     Str: userInfo.Id?.toString(),
  //   },
  // });
  router.push({
    path: "/caseVideo",
    query: {
      caseId: id,
      teamId: teamId,
      token: tokens.token,
    },
  });
  videoLoading.value = false;
  // plus.runtime.openURL(`https://rcp.sqray.com/webapp/#/caseVideo?caseId=${id}&token=${tokens.token}&teamId=${teamId}`);
};

//诊断
const diagnoseClick = async () => {
  diagnoseLoading.value = true;
  // try {
  // const res = await API.caseLockingApi.getExist.request({
  //   params: {
  //     Id: Number(id),
  //   },
  // });
  // if (res?.Data) {
  //   const res = await API.caseLockingApi.postAdd.request({
  //     bodyParams: {
  //       Id: Number(id),
  //     },
  //   });
  //   if (res.Code === 200) {
  router.push({
    path: "/examineDetails",
    query: {
      caseId: id,
      status: status,
      category: category,
      siteId: siteId,
    },
  });
  diagnoseLoading.value = false;
  //   }
  // }
  // } catch (error: any) {
  //   console.log(error);

  //   Toast.fail(error.data.message);
  //   diagnoseLoading.value = false;
  // }
};

//退回初审
const sendBack = () => {
  sendBackShow.value = true;
};
const sendBackClick = async () => {
  await API.expertReviewApi.postReview.request({
    bodyParams: {
      Id: Number(id),
      IsReturn: true,
    },
  });
  sendBackShow.value = false;
  router.go(-1);
  Toast.success("退回成功");
};

//同意签发
const signAndIssue = () => {
  signAndIssueShow.value = true;
};
const signAndIssueClick = async () => {
  await API.expertFunctionApi.putAgreeIssue.request({
    bodyParams: {
      Id: Number(id),
    },
  });
  signAndIssueShow.value = false;
  router.go(-1);
  Toast.success("签发成功");
};

//重新发布
const republishs = () => {
  emits("republishClick");
};

//退回预约 || 退回病例
const retiredNumber = async (type: number) => {
  sendBackLoading.value = true;
  if (type === 1) {
    router.push({
      path: "/sendBack",
      query: {
        caseId: id,
        status: status,
        category: category,
      },
    });
  } else {
    router.push({
      path: "/sendBack",
      query: {
        caseId: id,
        status: status,
        category: category,
        siteId: siteId,
      },
    });
  }
  sendBackLoading.value = false;
};

//确认预约
const confirmingAppointment = () => {
  show.value = true;
};
const submit = async () => {
  try {
    await API.expertFunctionApi.putFrozen.request({
      bodyParams: {
        Id: Number(id),
      },
    });
    show.value = false;
    router.go(-1);
    Toast.success("预约成功");
  } catch (error: any) {
    router.go(-1);
    Toast(error.data.message);
  }
};

//确认回复
const confirmReply = () => {
  emits("confirmReplys");
};

//撤回诊断
const revocationClick = () => {
  revocationShow.value = true;
};
const revocation = async () => {
  await API.expertFunctionApi.postWithdrawaDiagnosis.request({
    bodyParams: {
      Id: Number(id),
    },
  });
  revocationShow.value = false;
  router.go(-1);
  Toast.success("撤回成功");
};

//邀请专家
const inviteClick = () => {
  router.push({
    path: "/invitationReview",
    query: {
      caseId: id,
      siteId: siteId,
      inviteType: 1,
    },
  });
};
</script>

<style lang="scss" scoped>
//底部区域

.case-bottom-container {
  box-sizing: border-box;
  display: flex;
  padding: 13px 14px 0 14px;
  position: fixed;
  bottom: 0;
  z-index: 100;
  width: 100%;
  height: 88px;
  background: #fff;
  opacity: 1;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
}

.case-bottom-left {
  margin-left: 10px;
}

.case-video {
  flex: 1;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #58bad1;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-diagnose {
  flex: 1.5;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #650faa;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-center {
  margin-top: 9px;
  width: 1px;
  height: 40px;
  background-color: #e6ecfb;
}

// .case-videos {
//   // flex: none;
//   // width: 100px;
// }
.case-videos-right {
  margin-right: 10px;
}
:deep(.van-button--normal) {
  padding: 0;
}
</style>
