<template>
  <div>
    <div v-if="opinionList.length > 0" class="form-content">
      <div
        v-for="(item, index) in opinionList"
        :key="index"
        class="form-content-title"
      >
        <div class="form-content-time">
          <div class="form-content-time-title g-ellipsis">
            原病理号：{{ item.PathologyNumber }}
          </div>
        </div>
        <div class="reason">
          <span
            >{{ item.Name ? item.Name + " |" : "" }}
            {{ item.SexName ? item.SexName + " |" : "" }} {{ item.Age }}</span
          >
        </div>
        <div class="reason">
          <span
            >{{ item.CaseTypeName ? `${item.CaseTypeName} |` : "" }}
            {{ SampleLocationName(item.SampleLocationName as string) }} |
            {{ item.Inspection }}
          </span>
        </div>
        <div class="reason">
          {{ item.CreateTime }}
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无病史" />
  </div>
</template>

<script setup lang="ts">
import { SampleLocationName } from "@/utils/utils";
const route = useRoute();
const { id } = route.query;
const opinionList = ref<defs.CaseRenderingDto[]>([]);
const getOpinionList = async (id: number) => {
  const res = await API.allCommonApi.getMedicalhistorInfor.request({
    params: {
      Id: id,
      PageIndex: 1,
      PageSize: 50,
    },
  });
  opinionList.value = res.Data || [];
};
onMounted(() => {
  getOpinionList(Number(id));
});
</script>
<style lang="scss" scoped>
.form-content {
  margin: 0 6px;
  padding-top: 7px;
}

.form-content-title {
  box-sizing: border-box;
  padding: 8px 11px;
  width: 100%;
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  margin-bottom: 7px;
}

.form-content-time {
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
}

.form-content-time-title {
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.form-content-time-date {
  font-size: 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
}

.reason {
  width: 100%;
  display: flex;
  line-height: 18px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
}

.reason-title {
  width: 65px;
}

.reason-value {
  flex: 1;
  word-break: break-all; //英文
  white-space: pre-wrap; //中文
}
</style>
