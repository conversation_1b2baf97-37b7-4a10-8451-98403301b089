<template>
  <div class="feedBack">
    <div class="feedBack-title">{{ poops.backTitle }}</div>
    <van-cell-group inset>
      <van-field
        v-model="message"
        rows="3"
        label=""
        type="textarea"
        :maxlength="maxlength"
        autosize
        :readonly="poops.disabled"
        :placeholder="poops.backPlaceholder"
        @update:model-value="onBlur"
      />
    </van-cell-group>
  </div>
</template>
<script lang="ts" setup>
const poops = defineProps({
  backTitle: {
    type: String,
    default: "问题反馈",
  },
  backPlaceholder: {
    type: String,
    default: "请输入100字以内的问题反馈",
  },
  messages: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  maxlength: {
    type: Number,
    default: 100,
  },
});
const message = ref("");
watch(
  () => poops.messages,
  (val) => {
    message.value = val;
    emits("messageClick", message.value);
  },
  { deep: true, immediate: true }
);
message.value = poops.messages;
const emits = defineEmits(["messageClick"]);
const onBlur = () => {
  emits("messageClick", message.value);
};
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 14px 14px 14px;
  height: 100%;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.van-cell {
  padding: 0;
}

:deep(.van-field__control) {
  font-size: 12px;
  font-weight: 400;
  color: #4d545f;
}
</style>
