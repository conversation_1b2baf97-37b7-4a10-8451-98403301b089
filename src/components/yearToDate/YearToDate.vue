<template>
  <van-datetime-picker
    id="haha"
    ref="datetime"
    v-model="currentDate"
    type="date"
    title=""
    :min-date="minDate"
    :max-date="maxDate"
    :visible-item-count="3"
    :formatter="formatter"
    @confirm="onConfirm"
  >
    <template #columns-top>
      <div class="picker-title">选择日期</div>
    </template>
    <template #cancel
      ><div class="picker-btn" @click="onCancel">取消</div></template
    >
    <template #confirm><div class="picker-btn picker-btn1">确认</div></template>
  </van-datetime-picker>
</template>
<script lang="ts" setup>
import { formatDate } from "@/utils/means";
const currentDate = ref(new Date());
const minDate = new Date(2020, 0, 1);
const maxDate = new Date(2025, 10, 1);
const emits = defineEmits(["confirm", "cancel"]);
//确认
const onConfirm = (value: Date) => {
  console.log(value);
  emits("confirm", formatDate(value));
};
//取消
const onCancel = () => {
  emits("cancel");
};
const formatter = (type: any, val: any) => {
  if (type === "year") {
    return val + "年";
  }
  if (type === "month") {
    return val + "月";
  }
  if (type === "day") {
    return val + "日";
  }
  return val;
};
</script>
<style lang="scss" scoped>
.picker-title {
  height: 56px;
  line-height: 56px;
  margin-left: 23px;
  font-size: 16px;
  font-weight: bold;
  color: #343840;
}
.picker-bottom {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
.picker-btn {
  width: 105px;
  height: 36px;
  background: #f8f8f8;
  border-radius: 30px 30px 30px 30px;
  opacity: 1;
  text-align: center;
  line-height: 36px;
  font-size: 12px;
  font-weight: 400;
  color: #494949;
  // margin-left: 10px;
}
.picker-btn1 {
  background: #650faa;
  color: #fff;
}
:deep(.van-picker__toolbar) {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 64px;
  left: 0;
  width: 100%;
}
</style>
