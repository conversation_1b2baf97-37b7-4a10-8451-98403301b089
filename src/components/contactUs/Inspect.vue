<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="远程送检须知" />
    <!--顶部导航 end -->
    <div class="inspect-content">
      <div style="text-align: center">病理诊断业务须知</div>
      <div>
        <div class="inspect-titles">一、诊断服务简介</div>
        <div class="inspect-title">
          <div>1.服务范围</div>
          <div>为满足广大患者的病理远程诊断需要，现提供以下服务</div>
          <div>（1）国际远程病理诊断（咨询）服务；</div>
          <div>（2）远程组织病理诊断服务；</div>
          <div>（3）远程组织病理诊断（加急）服务；</div>
          <div>（4）远程病理疑难诊断（咨询）服务；</div>
          <div>（5）术中冰冻远程病理诊断（咨询）服务；</div>
        </div>
        <div class="inspect-title">
          <div>2.专家团队</div>
          <div>
            远程平台集聚了全国范围内病理亚专科专家，为疑难病例诊断提供优质的服务，诊断专家均为副高级以上职称，具有丰富的病理诊断经验，能够保证诊断的准确性、及时性。
          </div>
        </div>
        <div class="inspect-title">
          <div>3.技术保障</div>
          <div>
            提供国际及国内一线知名专业数字病理扫描设备、病理远程诊断平台、系统具有完备的使用功能。不仅能满足用户需求，还能保证整个业务的安全性及保密性。
          </div>
        </div>
        <div class="inspect-titles">二、诊断制度</div>
        <div class="inspect-title">
          <div>1.诊断流程</div>
        </div>
        <div class="inspect-system">
          病理科根据患者病情需要，安排申请病理远程诊断服务
        </div>
        <div class="inspect-system inspect-system1">
          <van-icon name="down" />
        </div>

        <div class="inspect-system">扫描已经制片完成的病理胶片</div>
        <div class="inspect-system inspect-system1">
          <van-icon name="down" />
        </div>
        <div class="inspect-system">
          在诊断平台中上传病人临床资料、数字病理切片、诊断辅助材料、选择诊断专家
        </div>
        <div class="inspect-system inspect-system1">
          <van-icon name="down" />
        </div>
        <div class="inspect-system">专家线上远程诊断、实时沟通</div>
        <div class="inspect-system inspect-system1">
          <van-icon name="down" />
        </div>
        <div class="inspect-system">专家出具病理诊断报告</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
const router = useRouter();
const route = useRoute();
const { type } = route.query;
const active = ref(0);
</script>
<style lang="scss" scoped>
.inspect-content {
  margin: 12px 14px;
  padding: 12px 14px 19px 14px;
  background-color: #fff;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
  letter-spacing: 1px;
  border-radius: 4px 4px 4px 4px;
}

.inspect-titles {
  margin-top: 8px;
}

.inspect-title {
  margin-top: 5px;
  line-height: 24px;
}

.inspect-system {
  margin-top: 6px;
  padding: 0 18px;
  text-align: center;
  line-height: 25px;
  border: 1px solid #414141;
}

.inspect-system1 {
  border: none;
  color: #000;
  font-size: 14px;
}
.software {
  font-size: 14px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #4d545f;
  line-height: 16px;
  margin-bottom: 24px;
  & > div:nth-child(2) {
    margin-top: 6px;
    font-size: 14px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #4d545f;
    line-height: 16px;
  }
}
</style>
