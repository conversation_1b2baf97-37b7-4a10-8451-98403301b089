<template>
  <div class="contact">
    <div class="contact-title">帮助与反馈</div>
    <!-- <div class="contact-bottom"> -->
    <div style="display: flex; justify-content: space-between">
      <div
        class="contact-bottom"
        v-for="item in list"
        :key="item.name"
        @click="router.push(item.path)"
      >
        <div class="contact-bottom-top">
          <iconpark-icon class="iconpark" :name="item.icon"></iconpark-icon>
        </div>
        <div class="contact-bottom-title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
const router = useRouter();
const userInfo = getLocalStorage(LocalStorageName.user);
const list = reactive([
  {
    name: "填写建议",
    icon: "rongqi1x-2",
    path: "/feedback",
  },
  {
    name: "送检须知",
    icon: "rongqi1x-3",
    path: "/inspect",
  },
  {
    name: "更新日志",
    icon: "rongqi1x-4",
    path: "/updateLog",
  },
]);
if (!userInfo?.JinFeng) {
  list.unshift({
    name: "关于我们",
    icon: "rongqi1x",
    path: "/aboutUs",
  });
}
</script>

<style lang="scss" scoped>
.contact {
  padding: 25px 20px 20px 20px;
  background-color: #f4f7fd;
}
.contact-title {
  font-size: 18px;
  font-weight: bold;
  color: #343840;
}
.contact-bottom {
  margin-top: 10px;
  padding: 12px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}
.contact-bottom-top {
  display: flex;
  justify-content: center;
}
.iconpark {
  font-size: 20px;
}
.contact-bottom-title {
  margin-top: 5px;
  font-size: 12px;
  font-weight: normal;
  color: #4d545f;
}
.van-divider {
  margin: 0 12px;
}
</style>
