<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="问题反馈" />
    <!--顶部导航 end -->
    <div class="feedBack">
      <div class="feedBack-title">问题反馈</div>
      <van-cell-group inset>
        <van-field
          v-model="message"
          rows="4"
          label=""
          type="textarea"
          maxlength="100"
          autosize
          :autofocus="true"
          show-word-limit
          placeholder="请输入100字以内的问题反馈"
        />
      </van-cell-group>
      <div class="phoneClass">
        <van-field
          v-model="phone"
          type="tel"
          label="联系方式"
          :maxlength="11"
          placeholder="请填写手机号码"
        />
      </div>
    </div>
    <div class="feedBack-btn" @click="feedBackClick">发送反馈</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Toast } from "vant";
import { CheckPhone } from "@/utils/utils";
const router = useRouter();
const message = ref("");
const phone = ref("");
const feedBackClick = async () => {
  if (!CheckPhone(phone.value)) {
    Toast("请输入正确的手机号码");
    return;
  }
  if (message.value.length > 0) {
    await API.userCommonApi.postSuggest.request({
      bodyParams: {
        Content: message.value,
        ContactDetails: phone.value,
      },
    });
    router.push({
      path: "/",
    });
    Toast.success("反馈成功");
  } else {
    Toast("请输入反馈内容");
  }
};
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 11px 16px 14px;
  // height: 172px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}

.phoneClass {
  margin: 0 14px;
  padding: 5px 0;
  border-top: 1px solid #e7edfb;
}
:deep(.van-field__label) {
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}
.van-cell {
  padding: 0 0 10px 0;
}

.van-field__body {
  height: 100%;
}

// :v-deep(textarea#van-field-1-input) {
//   height: 140px !important;
//   font-size: 12px;
//   font-weight: 400;
//   color: #b4bfd0;
// }

.feedBack-btn {
  margin: 0 49px 0 46px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
