<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="更新日志" />
    <!--顶部导航 end -->
    <div v-if="updateLogList.length > 0" class="updateLog">
      <div
        v-for="item in updateLogList"
        :key="item.Id"
        style="padding: 10px 14px"
      >
        <div class="updateLog-title">
          <div class="circle"></div>
          <div>更新版本：{{ item.Version }}</div>
        </div>
        <div class="updateLog-bottom">
          <div style="margin-top: 15px">更新时间：{{ item.CreateTime }}</div>
          <div style="margin-top: 12px">本次更新</div>
          <div style="white-space: pre-wrap">{{ item.Introduction }}</div>
          <div style="margin-top: 12px">体验优化</div>
          <div style="white-space: pre-wrap">{{ item.Description }}</div>
        </div>
      </div>
    </div>
    <div v-else class="empty">
      <van-empty description="暂无数据"> </van-empty>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";

//日志列表
const updateLogList = ref<defs.SysUpdateLog[]>([]);
//获取更新日志
const getUpdateLog = async () => {
  const res = await API.sysUpdateLogApi.getSysUpdateLogListAsync.request({});
  updateLogList.value = res.Data || [];
};
onMounted(() => {
  getUpdateLog();
});
</script>
<style lang="scss" scoped>
.updateLog {
  margin: 14px 11px 16px 14px;
  padding: 5px 0;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  font-size: 14px;
  font-weight: normal;
  color: #636363;
}
.updateLog-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: normal;
  color: #650faa;
}
.circle {
  margin-right: 10px;
  width: 13px;
  height: 13px;
  border-radius: 13px;
  background-color: #650faa;
}
.updateLog-bottom {
  margin-left: 6px;
  border-left: 1px solid #b1b1b1;
  padding-left: 17px;
}
.empty {
  margin-top: 80px;
}
</style>
