<template>
  <div class="docWrap">
    <!-- 预览文件的地方（用于渲染） -->
    <div class="fileClass" ref="file"></div>
  </div>
</template>
<script lang="ts" setup>

import axios from 'axios'
// 如果 docx-preview 版本比较新需要 es6 导出
import { ref } from 'vue'
const props = defineProps({
  url: {
    type: String,
    default: '',
  }
})
let docx = require('docx-preview');
const file = ref()
watch(
  () => props.url,
  (val) => {
    if (val) {
      console.log(val);
      nextTick(() => {
        goPreview(val)
      })

    }
  },
  {
    immediate: true,
    deep: true,
  }
)

// 预览
const goPreview = (val:string) => {
  axios({
    method: 'get',
    responseType: 'blob', // 因为是流文件，所以要指定blob类型
    url: val // 一个word下载文件的接口
  }).then(data => {
    console.log(data);

    docx.renderAsync(data, file.value, null, {
      className: 'text-docx', //class name/prefix for default and document style classes
      inWrapper: false, //enables rendering of wrapper around document content
      ignoreWidth: false, //disables rendering width of page
      ignoreHeight: false, //disables rendering height of page
      ignoreFonts: false, //disables fonts rendering
      breakPages: true, //enables page breaking on page breaks
      ignoreLastRenderedPageBreak: true, //disables page breaking on lastRenderedPageBreak elements
      experimental: true, //enables experimental features (tab stops calculation)
      trimXmlDeclaration: true, //if true, xml declaration will be removed from xml documents before parsing
      useBase64URL: false, //if true, images, fonts, etc. will be converted to base 64 URL, otherwise URL.createObjectURL is used
      useMathMLPolyfill: true, //includes MathML polyfills for chrome, edge, etc.
      debug: false //enables additional logging
    }) // 渲染到页面
  })
}
</script>
<style lang="scss" scoped>
.docWrap {
  width: 100%;
  height: 100%;

  .fileClass {
    width: 100%;
    height: 100%;
  }
}
</style>
