<template>
  <div class="imageClass">
    <van-image
      class="imageClasss"
      :lazy-load="true"
      :src="signerImgUrl || DefaultSection"
      @click="showImagePreview(signerImgUrl)"
      fit="contain"
    >
      <template v-slot:error><van-image :src="DefaultSection" /> </template
    ></van-image>
  </div>
</template>

<script lang="ts" setup>
import { ImagePreview } from "vant";
import DefaultSection from "@/assets/images/default-section.png";
import { Ref } from "vue";
const props = defineProps({
  imgUrl: {
    type: String,
    default: "",
  },
});
//签名后图片地址
const signerImgUrl: Ref<string | null> = ref(null);
const loadCaseImage = async () => {
  const res = await API.accountApi.getGetpresigned.request({
    params: {
      Str: props.imgUrl,
    },
  });
  signerImgUrl.value = res.Data?.Str as any;
};
//查看图片
const showImagePreview = (signerImgUrl: string | null) => {
  if (signerImgUrl) {
    ImagePreview({
      images: [signerImgUrl as string],
    });
  }
};
//挂载后
onMounted(() => {
  nextTick(() => {
    loadCaseImage();
  });
});
</script>
<style lang="scss" scoped>
.imageClass {
  height: 51px;
  width: 51px;
  border: 1px solid #e6ecfb;
}

.imageClasss {
  width: 100%;
  height: 100%;
}

.van-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  transform: none;
}
</style>
