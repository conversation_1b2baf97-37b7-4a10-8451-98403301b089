<template>
  <div class="search">
    <div class="search-container">
      <!-- 插槽 -->
      <slot name="search"></slot>
      <div class="van-search">
        <van-search
          v-model="inputValues"
          :placeholder="placeholder"
          class="input-search"
          @blur="accomplishClcik"
        >
          <template #left-icon>
            <div>
              <iconpark-icon
                class="iconpark"
                name="weibiaoti-1huaban1fuben7"
              ></iconpark-icon>
            </div>
          </template>
        </van-search>

        <div class="searchLine"></div>
        <div
          :class="['search-label-area', screenShow ? 'search-label-area1' : '']"
          @click="SearchClick"
        >
          <span>筛选</span>&nbsp;
          <van-icon v-if="screenShow" name="arrow-up" />
          <van-icon v-else name="arrow-down" />
        </div>
      </div>
    </div>
    <div v-if="screenShow" class="search-select">
      <div v-if="showtime" class="search-time">
        <div class="search-time-title">
          {{ category === 6 ? "申请时间" : "提交时间" }}
        </div>
        <div class="time-list">
          <van-field
            class="time-left"
            style="padding: 0 10px"
            v-model="startTime"
            right-icon="arrow-down"
            placeholder="请输入开始时间"
            :readonly="true"
            @click="startShow = true"
          />
          <div class="time-center"></div>
          <van-field
            class="time-left"
            style="padding: 0 10px"
            v-model="endTime"
            :readonly="true"
            right-icon="arrow-down"
            placeholder="请输入结束时间"
            @click="endShow = true"
          />
        </div>
      </div>
      <div class="search-time">
        <div v-if="props.caseTypeList.length > 0">
          <div class="search-time-title">病理类型</div>
          <div class="time-list" style="height: 150px; overflow-y: auto">
            <div
              v-for="(item, index) in props.caseTypeList"
              class="g-ellipsis"
              :class="[
                'time-left',
                'time-lefts',
                item.checked ? 'time-lefts-active' : '',
              ]"
              @click="caseTypeListClick(item)"
            >
              {{ item.Name }}
            </div>
          </div>
        </div>
        <div v-if="props.hospitalList.length > 0">
          <div class="search-time-title">送检医院</div>
          <div class="time-list" style="height: 150px; overflow-y: auto">
            <div
              v-for="(item, index) in props.hospitalList"
              class="g-ellipsis"
              :class="[
                'time-left',
                'time-lefts',
                item.checked ? 'time-lefts-active' : '',
              ]"
              @click="hospitalListClick(item)"
            >
              {{ item.Name }}
            </div>
          </div>
        </div>
        <div v-if="!showtime">
          <div class="search-time-title">地区</div>
          <div class="time-list">
            <van-field
              class="time-left time-lefts"
              v-model="province"
              right-icon="arrow-down"
              placeholder="请输入"
              :readonly="true"
              @click="cityShow = true"
            />
            <div class="time-center"></div>
            <van-field
              class="time-left time-lefts"
              v-model="citys"
              :readonly="true"
              right-icon="arrow-down"
              placeholder="请输入"
              @click="cityShow = true"
            />
          </div>
        </div>
        <div class="case-bottom-container">
          <div class="case-diagnose case-diagnose1" @click="resetClick">
            重置
          </div>
          <div class="case-center"></div>
          <div class="case-diagnose" @click="accomplishClcik">完成</div>
        </div>
      </div>
    </div>
    <van-overlay class="mask" :show="screenShow" @click="screenShow = false" />
    <!-- 开始时间 start -->
    <van-popup
      v-model:show="startShow"
      position="bottom"
      :style="{ height: '320px' }"
      :round="true"
      :close-on-click-overlay="false"
    >
      <YearToDate @confirm="onConfirm" @cancel="onCancel" />
    </van-popup>
    <!-- 开始时间 end -->

    <!-- 结束时间 start -->
    <van-popup
      v-model:show="endShow"
      position="bottom"
      :style="{ height: '320px' }"
      :round="true"
      :close-on-click-overlay="false"
    >
      <YearToDate @confirm="onConfirms" @cancel="onCancel" />
    </van-popup>
    <!-- 结束时间 end -->

    <!-- 选择城市 start -->
    <van-popup
      v-model:show="cityShow"
      position="bottom"
      :style="{ height: '330px' }"
      :round="true"
      :close-on-click-overlay="false"
    >
      <van-area
        :area-columns-placeholder="['请选择', '请选择']"
        :area-list="areaList"
        :columns-num="2"
        :visible-item-count="3"
        @cancel="onCancel"
        @confirm="cityConfirm"
      >
        <template #columns-top>
          <div class="picker-title">选择地区</div>
        </template>
        <template #cancel>
          <div class="picker-btn" @click="onCancel">取消</div>
        </template>
        <template #confirm>
          <div class="picker-btn picker-btn1">确认</div>
        </template>
      </van-area>
    </van-popup>
    <!-- 选择城市 end -->
  </div>
</template>

<script lang="ts" setup>
import YearToDate from "@/components/yearToDate/YearToDate.vue";
import { Toast } from "vant";
import { PropType } from "vue";
import { AdvancedSearch, WhereParams } from "@/utils/query";
const startTime = ref("");
const endTime = ref("");
const props = defineProps({
  placeholder: {
    type: String,
    default: "请搜索关键字",
  },
  selectTextArray: [],
  //医院数组
  hospitalList: {
    type: Array as PropType<Array<any>>,
    default: () => [],
  },
  //病例类型
  caseTypeList: {
    type: Array as PropType<Array<any>>,
    default: () => [],
  },
  showtime: {
    type: Boolean,
    default: true,
  },
  //城市
  areaList: {
    type: Object as PropType<any>,
    default: () => {},
  },
  category: {
    type: Number,
    default: 0,
  },
  /**
   * @param inquireType 查询参数
   * @param type  1：邀请专家查询
   *
   *
   */
  inquireType: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits([
  "hospitalListClick",
  "caseTypeListClick",
  "onSearchHandle",
]);

const inputValues = ref("");
const cityShow = ref(false);
//筛选
const screenShow = ref(false);
const SearchClick = () => {
  screenShow.value = !screenShow.value;
  maskShow.value = !maskShow.value;
};
//开始时间选择
const startShow = ref(false);
const onConfirm = (value: string) => {
  startTime.value = value;
  startShow.value = false;
};
const onCancel = () => {
  startShow.value = false;
  endShow.value = false;
  cityShow.value = false;
};
//结束时间选择
const endShow = ref(false);
const onConfirms = (value: string) => {
  endTime.value = value;
  endShow.value = false;
};

//重置
const resetClick = () => {
  startTime.value = "";
  endTime.value = "";
  inputValues.value = "";
  hospitalListId.value = "";
  caseTypeListId.value = "";
  province.value = "";
  citys.value = "";
  provinceId.value = "";
  cityId.value = "";

  emit("hospitalListClick");

  accomplishClcik();
};
//完成查询
const accomplishClcik = () => {
  const advancedSearch = new AdvancedSearch();

  if (hospitalListId.value) {
    advancedSearch.addParam(
      new WhereParams("SiteId", String(hospitalListId.value), 0, 0)
    );
  }
  if (caseTypeListId.value) {
    advancedSearch.addParam(
      new WhereParams("CaseTypeCode", caseTypeListId.value, 0, 0)
    );
  }

  if (props.inquireType === 1) {
    // 邀请专家查询
    if (inputValues.value) {
      advancedSearch.addParam(
        new WhereParams("ExpertName", inputValues.value, 1, 0)
      );
    }
    if (provinceId.value) {
      advancedSearch.addParam(
        new WhereParams("ProvinceName", Number(provinceId.value), 1, 0)
      );
    }
    if (cityId.value) {
      advancedSearch.addParam(
        new WhereParams("CityName", Number(cityId.value), 1, 0)
      );
    }
  } else if (props.inquireType === 2) {
    if (inputValues.value) {
      advancedSearch.addParam(
        new WhereParams("Number", inputValues.value, 1, 0)
      );
    }
  } else {
    if (inputValues.value) {
      advancedSearch.addParam(
        new WhereParams("PathologyNumber", inputValues.value, 1, 0)
      );
    }
  }
  if (inputValues.value && props.inquireType !== 1) {
    advancedSearch.addParam(new WhereParams("Name", inputValues.value, 1));
  }

  const advSearchValue = advancedSearch.getParams();
  const query = {
    where: "",
    AdvancedSearch: advSearchValue,
    StartingTime: startTime.value,
    EndTime: endTime.value,
  };
  emit("onSearchHandle", query);
  screenShow.value = false;
};

//点击医院
const hospitalListId = ref();
const hospitalListClick = (item: any) => {
  hospitalListId.value = item.Id;

  props.hospitalList.forEach((item: any) => {
    item.checked = false;
  });
  item.checked = !item.checked;
};
//点击病理类型
const caseTypeListId = ref();
const caseTypeListClick = (item: any) => {
  caseTypeListId.value = item.Code;
  props.caseTypeList.forEach((item: any) => {
    item.checked = false;
  });
  item.checked = !item.checked;
};
const maskShow = ref(false);

//城市选择
const province = ref("");
const citys = ref("");
//选中的城市Id
const provinceId = ref("");
const cityId = ref("");
const cityConfirm = (value: any) => {
  province.value = value[0].name;
  citys.value = value[1].name;
  provinceId.value = value[0].code;
  cityId.value = value[1].code;
  cityShow.value = false;
};
</script>
<style lang="scss" scoped>
.search {
  background-color: #f3f6fd;
}

.search-container {
  position: relative;
  width: 100%;
  // padding: 0 14px;
  z-index: 8;
  background-color: #f3f6fd;
}

.van-search {
  display: flex;
  width: 100%;
  // height: 32px;
  // margin: 11px 0;
  padding: 11px 14px;
  align-items: center;
  z-index: 8;
  background-color: #f3f6fd;
}

.iconpark {
  margin-top: 7px;
}

.input-search {
  width: 88%;
  padding: 0;
  line-height: 32px;
  display: inline-block;
  font-size: 14px;
  border-radius: 18px;
  overflow: hidden;
  // margin-left: 14px;
}

:deep(.van-field__left-icon .van-icon, .van-field__right-icon .van-icon) {
  font-size: 22px;
}

// :deep(.van-search) {
//   // padding: 11px 0;
// }
.search-label-area {
  width: 20%;
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.search-label-area1 {
  color: #650faa;
}

.searchLine {
  margin: 0 18px;
  width: 1px;
  height: 20px;
  background-color: #e6ecfb;
}

.search-select {
  background-color: #f3f6fd;
  position: absolute;
  bottom: 2;
  left: 0;
  width: 100%;
  z-index: 2;
}

.search-time {
  margin: 0 14px 12px 14px;
  padding: 12px 12px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.search-time-title {
  font-size: 14px;
  font-weight: bold;
  color: #494949;
}

.time-list {
  margin-top: 6px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-content: flex-start;
}

.time-center {
  margin-top: 16px;
  width: 11px;
  height: 0px;
  opacity: 1;
  border: 1px solid #ececec;
}

.time-left {
  display: flex;
  align-items: center;
  justify-content: center;
  justify-content: space-around;
  width: 145px;
  height: 32px;
  background: #f3f6fd;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #494949;
}

.time-lefts {
  margin-bottom: 12px;
}

.time-lefts-active {
  background-color: #650faa;
  color: #fff;
}

.case-bottom-container {
  display: flex;
  height: 48px;
  background: #ffffff;
  opacity: 1;
  border-top: 1px solid #e6ecfb;
}

.case-diagnose {
  flex: 1;
  font-size: 14px;
  font-weight: 400;
  color: #650faa;
  text-align: center;
  line-height: 58px;
}

.case-diagnose1 {
  color: #4d545f;
}

.case-center {
  margin-top: 9px;
  width: 1px;
  height: 40px;
  background-color: #e6ecfb;
}

.picker-title {
  height: 56px;
  line-height: 56px;
  margin-left: 23px;
  font-size: 16px;
  font-weight: bold;
  color: #343840;
}

.picker-btn {
  width: 105px;
  height: 36px;
  background: #f8f8f8;
  border-radius: 30px 30px 30px 30px;
  opacity: 1;
  text-align: center;
  line-height: 36px;
  font-size: 12px;
  font-weight: 400;
  color: #494949;
  // margin-left: 10px;
}

.picker-btn1 {
  background: #650faa;
  color: #fff;
}

:deep(.van-picker__toolbar) {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 64px;
  left: 0;
  width: 100%;
}
</style>
