<template>
  <div class="case-info-content">
    <div class="form-title">
      <div></div>
      <div>专家信息</div>
    </div>
    <div v-if="props.expertInfo" class="form-content">
      <div class="form-contents">
        <van-image class="vanImage" :src=props.expertInfo?.HeadPicUrl :show-loading="false" :show-error="false" />
        <div class=" invitation">
          <div>{{ props.expertInfo?.ExpertName }}</div>
          <div>{{ props.expertInfo?.ExpertleveName }} | {{ props.expertInfo?.CityName }} |
            {{ props.expertInfo?.ExpertTypeName }}</div>
        </div>
      </div>
      <div class="form-content-line"></div>
      <div class="form-content-title">
        <div>个人简介：{{ props.expertInfo?.BriefIntroduction }}</div>
      </div>
    </div>
    <van-empty v-else description="暂无专家信息" />
  </div>
</template>

<script lang="ts" setup>import { PropType } from 'vue';

const props = defineProps({
  expertInfo: {
    type: Object as PropType<defs.SiteExpertDto>,
    default: () => { }
  },
})
</script>
<style lang="scss" scoped>
.form-content {
  margin: 0 14px;
  padding: 10px 0;
}


.form-content div:last-child {
  border-bottom: none;
}



.form-size {
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8B96A6;
}

.vanImage {
  width: 37px;
  height: 37px;
  min-width: 37px;
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #E2E2E2;
  margin-right: 10px;
}

.invitation {
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4D545F;

  & div:nth-child(2) {
    margin-top: 5px;
    font-size: 12px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #8B96A6;
  }
}

.form-contents {
  box-sizing: border-box;
  display: flex;
  align-items: center;

}

.form-content-line {
  margin-top: 5px;
  border-top: 1px solid #E6ECFB;
}

.form-content-title {
  box-sizing: border-box;
  margin-top: 10px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8B96A6;
}
</style>
