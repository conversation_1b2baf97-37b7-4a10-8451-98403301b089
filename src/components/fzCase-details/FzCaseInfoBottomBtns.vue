<template>
  <div class="case-bottom-container">
    <template v-if="selectColor.selectNav1 === 1">
      <div
        class="case-video"
        style="background-color: #ff7272"
        @click="sendBacks"
      >
        退回站点
      </div>
      <div
        v-if="![5, 9, 13].includes(Number(status))"
        class="case-diagnose case-bottom-left"
        @click="allocation"
      >
        分配
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { Toast } from "vant";
import useStore from "@/store";
import { Aes } from "@/utils/aesEncrypt";
const { selectColor } = useStore();
const props = defineProps({
  pathologyNumber: {
    type: String,
  },
});
const route = useRoute();
const router = useRouter();
const { id, status, category, siteId } = route.query;
const reviewerShow = ref(false);
// 退回病例
const sendBack = () => {
  router.push({
    path: "/sendBack",
    query: {
      id: id,
    },
  });
};
// 分配
const allocation = () => {
  router.push({
    path: "/allocation",
    query: {
      id: id,
      siteId: siteId,
      isFrozen: 1,
    },
  });
};
// 操作日志
const gotoLog = () => {
  router.push({
    path: "/operationLog",
    query: {
      id: id,
    },
  });
};
// 查看原因
const checkCause = (type: number) => {
  if (type === 1) {
    router.push({
      path: "/checkCause",
      query: {
        id: id,
      },
    });
  } else {
    router.push({
      path: "/checkCause",
      query: {
        id: id,
        type: 2,
      },
    });
  }
};
// 撤回
const withdraw = () => {
  reviewerShow.value = true;
};
// 撤回提交
const submit = async () => {
  await API.triageFunctionApi.putAgreeWithdraw.request({
    bodyParams: {
      Id: Number(id),
      IsFrozen: false,
    },
  });
  router.go(-1);
};
// 退回站点
const sendBacks = () => {
  router.push({
    path: "/sendBack",
    query: {
      id: id,
      type: 1,
    },
  });
};
</script>

<style lang="scss" scoped>
//底部区域

.case-bottom-container {
  box-sizing: border-box;
  display: flex;
  padding: 13px 14px 0 14px;
  position: fixed;
  bottom: 0;
  z-index: 100;
  width: 100%;
  height: 88px;
  background: #fff;
  opacity: 1;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
}

.case-bottom-left {
  margin-left: 10px;
}

.case-video {
  flex: 1;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #58bad1;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-diagnose {
  flex: 1.5;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #650faa;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-center {
  margin-top: 9px;
  width: 1px;
  height: 40px;
  background-color: #e6ecfb;
}

// .case-videos {
//   // flex: none;
//   // width: 100px;
// }
.case-videos-right {
  margin-right: 10px;
}
</style>
