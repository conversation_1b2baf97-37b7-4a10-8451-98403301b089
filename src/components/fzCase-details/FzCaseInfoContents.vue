<template>
  <div class="case-info-content">
    <div class="form-title">
      <div></div>
      <div>预约信息</div>
    </div>
    <div class="case-info-details">
      <div class="form-content">
        <div class="form-content-title">预约编号</div>
        <div class="form-content-value">
          {{ props.data?.Number || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">患者姓名</div>
        <div class="form-content-value">{{ props.data?.Name || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">性别</div>
        <div class="form-content-value">{{ props.data?.SexName || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">年龄</div>
        <div class="form-content-value">{{ props.data?.Age || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">预约时间</div>
        <div class="form-content-value">
          {{ props.data?.AppointmentTime || "-" }}
        </div>
      </div>

      <div class="form-content">
        <div class="form-content-title">手术部位</div>
        <div class="form-content-value">
          {{ SampleLocationName(props.data?.SampleLocationName) || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">临床医生</div>
        <div class="form-content-value">
          {{ props.data?.Doctor || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">联系电话</div>
        <div class="form-content-value">
          {{ props.data?.UserPhone || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">备注</div>
        <div class="form-content-value">
          {{ props.data?.Remark || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">临床病例</div>
        <div class="form-content-value">
          {{ props.data?.ClinicalMedicalRecords || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">手术所见</div>
        <div class="form-content-value">
          {{ props.data?.SurgicalFindings || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">临床诊断</div>
        <div class="form-content-value">
          {{ props.data?.ClinicalDiagnosis || "-" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from "vue";
import { SampleLocationName } from "@/utils/utils";
const route = useRoute();
const props = defineProps({
  data: {
    type: Object as PropType<any>,
    require: true,
  },
});
</script>
<style lang="scss" scoped>
.case-info-content {
  padding-bottom: 12px;
  border-radius: 4px 4px 4px 4px;
}

.form-title {
  border-bottom: none;
}

.case-info-details {
  padding: 0 14px;
}

.form-content {
  box-sizing: content-box;
  display: flex;
  align-items: center;
  line-height: 20px;
  border: 1px solid #e6ecfb;
  border-bottom: none;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}

.case-info-details > div:last-child {
  border-bottom: 1px solid #e6ecfb;
}

.form-content-title {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 7px;
  width: 84px;
}

.form-content-value {
  padding: 8px 0;
  display: flex;
  align-items: center;
  width: 236px;
  padding-left: 7px;
  font-weight: 400;
  border-left: 1px solid #e6ecfb;
  word-break: break-all; //英文
  white-space: pre-wrap; //中文
}

.van-cell {
  padding: 0;
}
</style>
