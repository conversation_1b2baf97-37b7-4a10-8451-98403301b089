<template>
  <van-popup
    v-model:show="zjsection.versionsShow"
    :close-on-click-overlay="false"
    round
  >
    <div class="versionsShow">
      <div class="versionsShow-title">
        建议使用安卓8.0或者ios15及以上版本打开，以获取更好的使用体验，谢谢
      </div>
      <div class="versionsShow-btn" @click="zjsection.versionsShow = false">
        知道了
      </div>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import useStore from "@/store";
const { zjsection } = useStore();
</script>
<style lang="scss" scoped>
.versionsShow {
  width: 320px;
  background: #ffffff;
}
.versionsShow-title {
  text-align: center;
  padding: 45px 24px 24px 24px;
  font-size: 16px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #343840;
  line-height: 19px;
  border-bottom: 1px solid #e5e5e5;
}
.versionsShow-btn {
  font-size: 16px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #650faa;
  text-align: center;
  line-height: 56px;
}
</style>
