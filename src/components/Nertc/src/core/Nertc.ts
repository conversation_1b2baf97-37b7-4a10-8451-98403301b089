import SDK from "nertc-web-sdk";
import { computed, ComputedRef, reactive } from "vue";
import { PubSub } from "../utils/PubSub";
import { NertcMember } from "../types/member";
import {
  NertcClient,
  NertcEvent,
  NertcInitOptions,
  NertcPlayStreamOptions,
  NertcStream,
} from "../types/nertc";

export default class Nertc {
  /** 客户端实例 */
  private clientInstance: NertcClient | null;

  /** 发布/订阅实例 */
  private pubsubInstance = new PubSub<NertcEvent>();

  /** 音视频流列表 */
  private streams = reactive<Record<string, NertcStream>>({});

  /** 成员列表 */
  private members = reactive<Record<string, NertcMember>>({});

  /** 元素列表 */
  private elements: Record<string, HTMLElement> = {};

  /** 主音视频流 */
  private mainStream: NertcStream | null;

  /** 主音视频元素 */
  private mainElement: HTMLElement | null;

  /** 本地音视频流uid */
  private get localUid() {
    return (this.clientInstance?.getUid() as string) || "";
  }

  /** 本地音视频流元素 */
  private get localElement(): HTMLElement | undefined {
    return this.elements[this.localUid];
  }

  /** 本地音视频流 */
  get localStream(): NertcStream | undefined {
    return this.streams[this.localUid];
  }

  /** 渲染成员列表 */
  get renderMembers(): ComputedRef<NertcMember[]> {
    console.log(this.members, "this.members123");

    return computed(() =>
      Object.values(this.members).sort((a, b) => a.time - b.time)
    );
  }

  /**
   * @description 初始化
   * @param {NertcInitOptions} options
   */
  init(options: NertcInitOptions) {
    const { appkey, token, channelName, account } = options;
    // 初始化客户端
    this.clientInstance = SDK.createClient({ appkey, debug: false });
    // 初始化事件
    this.initEvents();
    // 加入房间
    this.joinChannel(account, token, channelName);
  }

  /**
   * @description 更新成员
   * @param {NertcMember} member
   */
  updateMember(uid: string, member: NertcMember) {
    this.members[uid] = member;
  }

  /**
   * @description 删除成员
   * @param {string} uid
   */
  deleteMember(uid: string) {
    delete this.members[uid];
    delete this.streams[uid];
    delete this.elements[uid];
    this.pubsubInstance.emit(NertcEvent.PEER_LEAVE, uid);
  }
  /**
   * @description 添加元素
   * @param {string} uid
   * @param {HTMLElement} el
   */
  addElement(uid: string, el: HTMLElement) {
    this.elements[uid] = el;
  }

  /**
   * @description 设置主音视频元素
   * @param {HTMLElement} el
   */
  setMainElement(el: HTMLElement) {
    this.mainElement = el;
  }

  /**
   * @description 切换播放主音视频
   * @param {string} uid
   */
  async cutPlayMainStream(uid: string) {
    const stream = this.streams[uid];

    if (!stream) {
      console.log("切换主音视频播放失败，因找不到该成员的音视频流");
      return;
    }

    if (this.mainStream && this.mainStream.getId() === stream.getId()) {
      console.log("切换主音视频播放失败，因该音视频已经在主视频播放中了");
      return;
    }

    if (!this.mainElement) {
      console.log("切换主音视频播放失败，因主音视频元素不存在");
      return;
    }

    // 还原小窗口播放
    if (this.mainStream) {
      // 停止音视频流
      this.mainStream.stop();
      // 播放音视频流
      this.playStream(
        this.elements[this.mainStream.getId() as string],
        this.mainStream
      );
    }

    // 停止音视频流
    stream.stop();
    // 播放音视频流
    this.playStream(this.mainElement, stream);
    // 主视频流赋值
    this.mainStream = stream;
    // 发布播放主视频流事件
    this.pubsubInstance.emit(NertcEvent.OPEN_MAIN_VIDEO, this.members[uid]);
  }

  /**
   * @description 关闭主音视频
   */
  closeMainVideo(member: NertcMember) {
    if (!this.mainStream) {
      console.log("关闭主视频流播放失败，因该主视频流不存在");
      return;
    }

    const uid = this.mainStream.getId() as string;
    // 停止音视频流
    this.mainStream.stop();
    // 播放音视频流
    this.playStream(this.elements[uid], this.mainStream);
    // 清空音视频流
    this.mainStream = null;
    // 出发关闭主视频流事件
    this.pubsubInstance.emit(NertcEvent.CLOSE_MAIN_VIDEO, member);
  }

  /**
   * @description 监听事件
   * @param {NertcEvent} event
   * @param {function} handler
   */
  on<T extends NertcEvent>(event: T, handler: (...any: any[]) => void): void {
    this.pubsubInstance.on(event, handler);
  }

  /**
   * @description 取消事件监听
   * @param {NertcEvent} event
   * @param {function} handler
   */
  off<T extends NertcEvent>(event: T, handler: (...any: any[]) => void) {
    this.pubsubInstance.off(event, handler);
  }

  /**
   * @description 打开/关闭本地视频流
   */
  async openOrCloseLocalVideo() {
    if (!this.localStream) {
      console.log("打开/关闭本地视频失败，因本地视频流不存在");
      return;
    }

    try {
      if (this.localStream.hasVideo()) {
        // 关闭本地视频流
        await this.localStream.close({ type: "video" });
      } else {
        const devices = await SDK.getDevices();
        const videoDevice = devices.video[0];
        // 打开本地视频流
        await this.localStream.open({
          type: "video",
          deviceId: videoDevice.deviceId,
        });
        if (this.mainStream && this.mainStream?.getId() === this.localUid) {
          this.playStream(this.mainElement as HTMLElement, this.localStream, {
            renderMode: { cut: true },
          });
        } else {
          this.playStream(this.localElement as HTMLElement, this.localStream, {
            renderMode: { cut: true },
          });
        }
      }
      // 发布打开/关闭本地视频流事件
      this.pubsubInstance.emit(
        NertcEvent.OPEN_OR_CLOSE_LOCAL_VIDEO,
        this.localStream.hasVideo()
      );
    } catch (e) {
      console.log("打开/关闭本地视频流失败", e);
    }
  }

  /**
   * @description 打开/关闭本地音频流
   */
  async openOrCloseLocalAudio() {
    if (!this.localStream) {
      console.log("打开/关闭本地音频失败，因本地音频流不存在");
      return;
    }

    try {
      if (this.localStream.hasAudio()) {
        // 关闭本地音频流
        await this.localStream.close({ type: "audio" });
      } else {
        const devices = await SDK.getDevices();
        const audioDevice = devices.audioIn[0];
        // 打开本地音频流
        await this.localStream.open({
          type: "audio",
          deviceId: audioDevice.deviceId,
        });
      }
      // 发布打开/关闭本地音频流事件
      this.pubsubInstance.emit(
        NertcEvent.OPEN_OR_CLOSE_LOCAL_AUDIO,
        this.localStream.hasAudio()
      );
    } catch (e) {
      console.log("打开/关闭本地音频流失败", e);
    }
  }

  /**
   * @description 打开/关闭共享屏幕流
   */
  async openOrCloseLocalScreen() {
    if (!this.localStream) {
      console.log("打开/关闭共享屏幕流失败，因本地共享屏幕流不存在");
      return;
    }

    try {
      if (this.localStream.hasScreen()) {
        // 关闭本地共享屏幕流
        await this.localStream.close({ type: "screen" });
      } else {
        this.localStream.setScreenProfile({
          resolution: SDK.VIDEO_QUALITY.VIDEO_QUALITY_1080p,
          frameRate: SDK.VIDEO_FRAME_RATE.CHAT_VIDEO_FRAME_RATE_15,
        });
        // 打开本地共享屏幕流
        await this.localStream.open({ type: "screen" });
      }

      // 打开/关闭本地视频流
      await this.openOrCloseLocalVideo();

      if (this.mainStream && this.mainStream.getId()) {
        this.playStream(this.mainElement as HTMLElement, this.localStream, {
          renderMode: { cut: this.localStream.hasScreen() },
        });
      } else {
        this.playStream(this.localElement as HTMLElement, this.localStream, {
          renderMode: { cut: this.localStream.hasScreen() },
        });
      }

      // 发布打开/关闭本地共享屏幕流事件
      this.pubsubInstance.emit(
        NertcEvent.OPEN_OR_CLOSE_LOCAL_SCREEN,
        this.localStream.hasScreen()
      );
    } catch (e) {
      console.log("打开/关闭共享屏幕流", e);
    }
  }

  /**
   * @description 播放音视频流
   * @param {HTMLElement} el
   * @param {NertcStream} stream
   * @param {NertcPlayStreamOptions} options
   */
  private async playStream(
    el: HTMLElement,
    stream: NertcStream,
    options?: NertcPlayStreamOptions
  ) {
    const uid = stream.getId() as string;
    console.log(`播放音视频流，uid: ${uid}`, el, stream, options);

    if (!el) {
      console.log(`播放音视频失败，因该${uid}播放元素不存在`);
      return;
    }

    const { clientWidth: width, clientHeight: height } = el;
    const defRenderMode = { width, height, cut: false };
    const mergeRenderMode = Object.assign(defRenderMode, options?.renderMode);
    console.log("播放音视频流，渲染模式", mergeRenderMode);

    if (uid === this.localUid) {
      // 设置本地视频画布
      stream.setLocalRenderMode(mergeRenderMode, options?.mediaType);
    } else {
      // 设置远程视频画布
      stream.setRemoteRenderMode(mergeRenderMode, options?.mediaType);
    }

    await stream.play(el, options?.playOptions);
  }

  /**
   * @description 加入房间
   * @param {string} uid
   * @param {string} token
   * @param {string} channelName
   */
  private async joinChannel(uid: string, token: string, channelName: string) {
    console.log("加入房间", uid, token, channelName);

    try {
      // 触发加入房间事件
      this.onPeerOnline({ uid });
      // this.clientInstance?.startProxyServer();
      // 加入房间
      console.log(1);

      await this.clientInstance?.join({ channelName, uid, token });
      console.log(2);
      // 初始化本地音视频流
      await this.initLocalStream();
      console.log(3);
      // 发布本地音视频流
      await this.publishLocalStream();
    } catch (e) {
      console.log("加入房间错误", e);
    }
  }

  /**
   * @description 发布本地音视频流
   */
  private async publishLocalStream() {
    if (!this.localStream) {
      console.log("本地音视频流不存在");
      return;
    }

    // 发布本地音视频流
    await this.clientInstance?.publish(this.localStream);
  }

  /**
   * @description 初始化本地音视频流
   */
  private async initLocalStream() {
    try {
      const localStream = await this.createLocalStream();
      // 保存本地音视频流
      this.streams[this.localUid] = localStream;
      // 初始化本地音视频流
      await localStream.init();
      // 播放本地音视频流
      await this.playStream(this.localElement as HTMLElement, localStream);
      // 发布打开/关闭本地视频流事件
      this.pubsubInstance.emit(NertcEvent.OPEN_OR_CLOSE_LOCAL_VIDEO, true);
      // 发布打开/关闭本地音频流事件
      this.pubsubInstance.emit(NertcEvent.OPEN_OR_CLOSE_LOCAL_AUDIO, true);
    } catch (e) {
      console.log("本地音视频流初始化失败", e);
    }
  }

  /**
   * @description 创建本地音视频流
   */
  private async createLocalStream() {
    const devices = await SDK.getDevices();
    const audioDevice = devices.audioIn[0];
    const videoDevice = devices.video[0];

    // 本地音视频流
    const localeStream = SDK.createStream({
      uid: this.localUid,
      audio: true,
      video: true,
      screen: false,
      microphoneId: audioDevice.deviceId,
      cameraId: videoDevice.deviceId,
      client: this.clientInstance as NertcClient,
    }) as NertcStream;

    // 设置本地视频质量
    localeStream.setVideoProfile({
      // 设置本地视频分辨率
      resolution: SDK.VIDEO_QUALITY.VIDEO_QUALITY_1080p,
      // 设置本地视频帧率
      frameRate: SDK.VIDEO_FRAME_RATE.CHAT_VIDEO_FRAME_RATE_15,
    });

    // 设置本地音频质量
    localeStream.setAudioProfile("speech_low_quality");

    return localeStream;
  }

  /**
   * @description 离开房间
   */
  leaving() {
    this.clientInstance?.leave();
    this.localStream?.destroy();
    this.mainStream?.destroy();
    this.pubsubInstance.clear();
    this.clientInstance = null;
    this.streams = reactive({});
    this.members = reactive({});
    this.elements = {};
    this.mainStream = null;
    this.mainElement = null;
  }

  /**
   * @description 初始化事件
   */
  private initEvents() {
    this.clientInstance?.on(
      "stopScreenSharing",
      this.onStopScreenSharing.bind(this)
    );
    this.clientInstance?.on("peer-online", this.onPeerOnline.bind(this));
    this.clientInstance?.on("peer-leave", this.onPeerLeave.bind(this));
    this.clientInstance?.on("stream-added", this.onStreamAdded.bind(this));
    this.clientInstance?.on("stream-removed", this.onStreamRemoved.bind(this));
    this.clientInstance?.on(
      "stream-subscribed",
      this.onStreamSubscribed.bind(this)
    );
  }

  /**
   * @description 发起订阅
   * @param {NertcStream} stream
   */
  private async subscribe(stream: NertcStream) {
    try {
      stream.setSubscribeConfig({ audio: true, video: true, screen: true });
      await this.clientInstance?.subscribe(stream);
    } catch (e) {
      console.log("发起订阅对端失败", e);
    }
  }

  /**
   * @description 监听停止屏幕共享
   */
  private async onStopScreenSharing() {
    console.log("停止屏幕共享");
    this.openOrCloseLocalScreen();
  }

  /**
   * @description 监听加入房间
   * @param {any} evt
   */
  private onPeerOnline(evt: any) {
    console.log("加入房间", evt);
    // 默认成员
    const member = {
      uid: evt.uid,
      nickname: evt.uid,
      avatar: "",
      time: Date.now(),
    };
    // 添加成员
    this.members[evt.uid] = member;
    console.log("成员列表", this.members);

    // 发布加入房间事件
    this.pubsubInstance.emit(NertcEvent.JOIN_CHANNEL, evt);
  }

  /**
   * @description 监听离开房间
   * @param {any} evt
   */
  private onPeerLeave(evt: any) {
    console.log("离开房间", evt);
    delete this.streams[evt.uid];
    delete this.members[evt.uid];
    delete this.elements[evt.uid];
    // 发布离开房间事件
    this.pubsubInstance.emit(NertcEvent.PEER_LEAVE, evt);
  }

  /**
   * @description 监听远端用户发布音视频流
   * @param {any} evt
   */
  private async onStreamAdded(evt: any) {
    console.log("远端用户发布音视频流", evt);
    // 发起订阅
    this.subscribe(evt.stream);
  }

  /**
   * @description 监听用户停止发布音视频流
   * @param {any} evt
   */
  private onStreamRemoved(evt: any) {
    console.log("用户停止发布音视频流", evt);
    const mediaType = evt.mediaType;
    const stream = evt.stream;
    const uid = stream.getId();

    // 替换音视频流
    this.streams[uid] = stream;
    // 停止音视频播放
    this.streams[uid].stop(mediaType);
  }

  /**
   * @description 监听订阅远端音视频流
   * @param {any} evt
   */
  private async onStreamSubscribed(evt: any) {
    console.log("订阅远端音视频流", evt);
    const stream = evt.stream;
    const uid = stream.getId();

    if (this.mainStream && this.mainStream.getId() === uid) {
      // 播放音视频流
      this.playStream(this.mainElement as HTMLElement, stream, {
        mediaType: stream.mediaType,
      });
    } else {
      // 播放音视频流
      this.playStream(this.elements[uid], stream, {
        mediaType: stream.mediaType,
      });
    }

    // 保存/替换音视频流
    this.streams[uid] = stream;
  }
}
