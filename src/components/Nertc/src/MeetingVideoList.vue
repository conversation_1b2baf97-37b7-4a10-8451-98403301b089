<!--
 * @description 会议音视频列表
-->

<script setup lang="ts">
import VideoCard from './components/VideoCard.vue'
import { useNertc } from './hooks/useNertc'
const props = withDefaults(defineProps<{
  /** 是否显示放大/缩小按钮 */
  zoom: boolean
}>(), {
  zoom: false
})

const nertc = useNertc()

</script>

<template>
  <div class="meeting-video-list">
    <div class="meeting-video-list-inner">
      <VideoCard v-for="member in nertc.renderMembers.value" :key="member.uid" :member="member" :zoom="props.zoom" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.meeting-video-list {
  box-sizing: border-box;
  margin: 8px 14px 22px 14px;
  height: 128px;
  background: #fff;
  border-radius: 4px;

  .meeting-video-list-inner {
    box-sizing: border-box;
    width: 100%;
    height: 128px;
    display: flex;
    overflow: auto;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 12px 12px;
  }
}
</style>
