
/** 客户端 */
import type { Client } from 'nertc-web-sdk/types/client'
export type NertcClient = Client

/** 音视频流 */
import type { Stream  } from 'nertc-web-sdk/types/stream'
export type NertcStream = Stream

/** 视频画布设置 */
import { RenderMode } from 'nertc-web-sdk/types/types'
export type NertcRenderMode = Partial<RenderMode>

/** Nertc 初始化配置 */
export interface NertcInitOptions {
  /** 在云信管理后台查看应用的 appKey */
  appkey: string
  /** 帐号, 应用内唯一 */
  account: string
  /** 帐号的 token, 用于建立连接 */
  token: string
  /** 房间名 */
  channelName: string
}

/** Nertc 事件 */
export const enum NertcEvent {
  /** 离开房间 */
  PEER_LEAVE = 'peerLeave',
  /** 打开主音视频 */
  OPEN_MAIN_VIDEO = 'openMainVideo',
  /** 关闭主音视频 */
  CLOSE_MAIN_VIDEO = 'closeMainVideo',
  /** 有人加入房间 */
  JOIN_CHANNEL = 'joinChannel',
  /** 打开/关闭本地视频流 */
  OPEN_OR_CLOSE_LOCAL_VIDEO = 'openOrCloseLocalVideo',
  /** 打开/关闭本地音频流 */
  OPEN_OR_CLOSE_LOCAL_AUDIO = 'openOrCloseLocalAudio',
  /** 打开/关闭本地共享屏幕流 */
  OPEN_OR_CLOSE_LOCAL_SCREEN = 'openOrCloseLocalScreen',
}

/** 主音视频 */
export interface NertcMainVideo {
  /** 元素 */
  el: HTMLElement
  /** 主音视频流 */
  stream: NertcStream
}

/** 播放媒体配置 */
export interface NertcPlayOptions {
  /** 播放音频 */
  audio?: boolean
  /** 播放视频 */
  video?: boolean
  /** 播放屏幕共享 */
  screen?: boolean
}

export interface NertcPlayStreamOptions {
  /** 设置视频画布 */
  renderMode?: NertcRenderMode
  /** 设置媒体类型 */
  mediaType?: 'screen' | 'video' | undefined
  /** 播放媒体配置 */
  playOptions?: NertcPlayOptions
}
