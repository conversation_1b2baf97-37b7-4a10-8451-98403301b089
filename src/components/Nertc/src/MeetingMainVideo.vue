<!--
 * @description 会议主音视频
-->

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from "vue";
import { useNertc } from "./hooks/useNertc";
import { NertcEvent } from "./types/nertc";
import { NertcMember } from "./types/member";
import useStore from "@/store";
const nertc = useNertc();
const { selectColor } = useStore();
// 主音视频元素
const meetingMainVideoRef = ref();

// 主成员
const memberMain = ref<NertcMember | null>(null);

// 是否显示主音视频
const isShowMainVideo = ref(false);

// 处理主音视频切换
const onCutMainVideoHandler = (member: NertcMember) => {
  isShowMainVideo.value = true;
  memberMain.value = member;
};

// 处理主音视频关闭
const onCloseMainVideoHandler = () => {
  isShowMainVideo.value = false;
  memberMain.value = null;
};

// 关闭主音视频
const onCloseMainVideo = () => {
  selectColor.isMainVideoPlayFn(false);
  nertc.closeMainVideo(memberMain.value as NertcMember);
  isShowMainVideo.value = false;
};

// 监听主音视频切换
nertc.on(NertcEvent.OPEN_MAIN_VIDEO, onCutMainVideoHandler);
// 监听主音视频关闭
nertc.on(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);

onMounted(() => {
  // 设置主视频元素
  nertc.setMainElement(meetingMainVideoRef.value);
  //旋转90度，填满屏幕
  meetingMainVideoRef.value.style.transform = "rotate(90deg)";
  meetingMainVideoRef.value.style.width = "calc(100vh)";
  meetingMainVideoRef.value.style.height = "calc(100vw)";
  meetingMainVideoRef.value.style.transformOrigin = "50vw 50vw";
});

onUnmounted(() => {
  nertc.off(NertcEvent.OPEN_MAIN_VIDEO, onCutMainVideoHandler);
  nertc.off(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);
});
</script>

<template>
  <div class="meeting-main-video" ref="meetingMainVideoRef">
    <!-- 缩小图标 -->
    <van-icon
      name="enlarge"
      @click="onCloseMainVideo"
      class="enlargeClass"
      color="#fff"
      size="30"
    />
  </div>
</template>

<style lang="scss" scoped>
.meeting-main-video {
  position: relative;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  width: 100%;
  height: 100%;

  :deep(.nertc-video-container-local) {
    width: 100% !important;
    height: 100% !important;

    video {
      max-width: 100%;
    }
  }

  :deep(.nertc-video-container-remote) {
    width: 100% !important;
    height: 100% !important;

    video {
      max-width: 100%;
    }
  }
}

.enlargeClass {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 15;
}
</style>
