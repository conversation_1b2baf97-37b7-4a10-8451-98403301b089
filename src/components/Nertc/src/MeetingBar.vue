<!--
 * @description 会议导航栏
-->

<script setup lang="ts">
import { computed, onUnmounted, ref } from 'vue'
import { useNertc } from './hooks/useNertc'
import { NertcEvent } from './types/nertc'
import { usePropVModel } from './hooks/usePropVModel'
import { NertcBarChatroomTabName } from './types/bar'

const props = withDefaults(defineProps<{
  /**
   * 聊天室选项卡的名称
   */
  chatroomTabName?: NertcBarChatroomTabName
}>(), {
  chatroomTabName: NertcBarChatroomTabName.MESSAGE_LIST
})

const emit = defineEmits<{
  /** 结束视频链接 */
  (event: 'leaving'): void
  /** 复制邀请链接 */
  (event: 'invitation', toClipboard: (text: string) => Promise<unknown>): void
  (event: 'update:chatroomTabName', tagName: NertcBarChatroomTabName): void
  (event: 'update:memberShow', memberShow: boolean): void
}>()

const nertc = useNertc()
console.log(nertc, 1231323);


// 音频是否激活
const isAudioActive = ref(true)

// 视频是否激活
const isVideoActive = ref(true)

// 共享屏幕是否激活
const isScreenActive = ref(false)

// 成员列表是否激活
const isMemberListActive = computed(() => props.chatroomTabName === NertcBarChatroomTabName.MEMBER_LIST)

// 聊天是否激活
const isChatActive = computed(() => props.chatroomTabName === NertcBarChatroomTabName.MESSAGE_LIST)

// 聊天室选项卡的名称（双向绑定）
const chatroomTabNameVModel = usePropVModel(props, 'chatroomTabName')

// 改变聊天室选项卡名称
const onChangeChatroomTabName = (tagName: NertcBarChatroomTabName) => {
  chatroomTabNameVModel.value = tagName
}


// 处理打开/关闭本地视频流
const onOpenOrCloseLocalVideoHandler = (evt: boolean) => {
  isVideoActive.value = evt
}

// 处理打开/关闭本地音频流
const onOpenOrCloseLocalAudioHandler = (evt: boolean) => {
  isAudioActive.value = evt
}

// 处理打开/关闭本地共享屏幕流
const onOpenOrCloseLocalScreenHandler = (evt: boolean) => {
  isScreenActive.value = evt
}

// 监听打开/关闭本地视频流
nertc.on(NertcEvent.OPEN_OR_CLOSE_LOCAL_VIDEO, onOpenOrCloseLocalVideoHandler)
// 监听打开/关闭本地音频流
nertc.on(NertcEvent.OPEN_OR_CLOSE_LOCAL_AUDIO, onOpenOrCloseLocalAudioHandler)
// 监听打开/关闭本地共享屏幕流
nertc.on(NertcEvent.OPEN_OR_CLOSE_LOCAL_SCREEN, onOpenOrCloseLocalScreenHandler)
const member = ref(false);
function memberClick() {
  member.value = !member.value;
  emit("update:memberShow", member.value);
}
onUnmounted(() => {
  nertc.off(NertcEvent.OPEN_OR_CLOSE_LOCAL_VIDEO, onOpenOrCloseLocalVideoHandler)
  nertc.off(NertcEvent.OPEN_OR_CLOSE_LOCAL_AUDIO, onOpenOrCloseLocalAudioHandler)
  nertc.on(NertcEvent.OPEN_OR_CLOSE_LOCAL_SCREEN, onOpenOrCloseLocalScreenHandler)
})
</script>

<template>
  <ul class="bar">
    <li class="bar-item" @click="nertc.openOrCloseLocalAudio()">
      <div class="iconparkClass">
        <iconpark-icon v-if="!isAudioActive" name="ziyuan10"></iconpark-icon>
        <iconpark-icon v-else color="#000" name="ziyuan6"></iconpark-icon>
      </div>
      <span class="label">{{ !isAudioActive ? "解除静音" : "关闭声音" }}</span>
    </li>
    <li class="bar-item" @click="nertc.openOrCloseLocalVideo()">
      <div class="iconparkClass">
        <iconpark-icon v-if="!isVideoActive" name="ziyuan7"></iconpark-icon>
        <iconpark-icon v-else name="ziyuan11"></iconpark-icon>
      </div>
      <span class="label">{{ !isVideoActive ? "开启视频" : "关闭视频" }}</span>
    </li>
    <li class="bar-item" @click="memberClick">
      <div class="iconparkClass">
        <iconpark-icon name="ziyuan14"></iconpark-icon>
      </div>
      <span class="label">成员管理</span>
    </li>
    <li class="bar-item" @click="emit('leaving')">
      <div class="iconparkClass">
        <iconpark-icon name="ziyuan9"></iconpark-icon>
      </div>
      <span class="label">结束视频</span>
    </li>
  </ul>
  <!-- <ElCard class="meeting-bar">
    <BarItem v-if="isAudioActive" label="关闭声音" icon="icon-a-ziyuan1" @click="nertc.openOrCloseLocalAudio()" />
    <BarItem v-else label="解除静音" svg icon="icon-Frame-1" @click="nertc.openOrCloseLocalAudio()" />

    <BarItem v-if="isVideoActive" label="关闭视频" icon="icon-a-ziyuan11" @click="nertc.openOrCloseLocalVideo()" />
    <BarItem v-else label="开启视频" svg icon="icon-Frame2" @click="nertc.openOrCloseLocalVideo()" />

    <ElDivider class="meeting-bar-divider" direction="vertical" />

    <BarItem label="共享屏幕" icon="icon-a-ziyuan10" :active="isScreenActive" @click="nertc.openOrCloseLocalScreen()" />
    <BarItem label="邀请" icon="icon-a-ziyuan16" @click="onInvite" />
    <BarItem label="开启白板" icon="icon-a-ziyuan16" @click="initWhiteBoard" />
    <BarItem label="关闭白板" icon="icon-a-ziyuan16" @click="closeWhiteBoard" />

    <ElDivider class="meeting-bar-divider" direction="vertical" />

    <BarItem label="结束视频链接" icon="icon-a-ziyuan11" @click="emit('leaving')" />
  </ElCard> -->
</template>

<style lang="scss" scoped>
.bar {
  margin: 0 14px;
  height: 78px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 4px 4px 4px 4px;

  .bar-item {
    list-style: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    // text-align: center;
    .iconfont {
      color: #636363;
      font-size: 20px;
      display: inline-block;
      margin-bottom: 3px;
    }

    .icon {
      font-size: 20px;
      margin-bottom: 3px;
    }

    .label {
      margin-top: 3px;
      color: #636363;
      font-size: 12px;
    }

    &:hover {
      cursor: pointer;
    }

    &.active {

      .label,
      .iconfont {
        color: #650faa;
      }
    }
  }

  .iconparkClass {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 28px;
    height: 28px;
    background: #E6ECFB;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
  }

  .divider {
    display: inline-block;
    height: 42px;
    width: 1px;
    background-color: #eaeaea;
    // &::marker {
    //   display: none;
    // }
  }
}

iconpark-icon {
  font-size: 16px;
}
</style>
