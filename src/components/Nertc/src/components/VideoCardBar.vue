<!--
 * @description 音视频导航
-->

<script setup lang="ts">
import { onUnmounted, PropType, ref } from "vue";
import { useNertc } from "../hooks/useNertc";
import { NertcEvent } from "../types/nertc";
import { NertcMember } from "../types/member";
import useStore from "@/store";
const props = defineProps({
  zoom: {
    type: Boolean,
    default: false,
  },
  member: {
    type: Object as PropType<NertcMember>,
    default: () => ({}),
  },
});
const { selectColor } = useStore();
const nertc = useNertc();

// 是否主视频播放
const isMainPlay = ref(false);

// 切换主音视频
const onCutMainVideo = async () => {
  selectColor.isMainVideoPlayFn(true);
  setTimeout(async () => {
    await nertc.cutPlayMainStream(props.member.uid);
  }, 500);
};

// 关闭主音视频
const onCloseMainVideo = () => {
  selectColor.isMainVideoPlayFn(false);
  nertc.closeMainVideo(props.member);
};

// 处理主音视频打开
const onOpenMainVideoHandler = (evt: NertcMember) => {
  isMainPlay.value = evt.uid === props.member.uid;
};

// 处理主音视频关闭
const onCloseMainVideoHandler = (evt: NertcMember) => {
  isMainPlay.value = false;
};

// 监听关闭主音视频
nertc.on(NertcEvent.OPEN_MAIN_VIDEO, onOpenMainVideoHandler);
// 监听打开主音视频
nertc.on(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);

onUnmounted(() => {
  nertc.off(NertcEvent.OPEN_MAIN_VIDEO, onOpenMainVideoHandler);
  nertc.off(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);
});
</script>

<template>
  <div class="video-card-bar">
    <div class="video-card-nickname">
      {{ props.member.nickname }}
    </div>
    <div class="video-card-actions">
      <div
        v-if="props.zoom && isMainPlay"
        class="video-card-icon"
        aria-hidden="true"
      >
        <van-icon name="enlarge" @click="onCloseMainVideo" />
      </div>
      <div
        v-if="props.zoom && !isMainPlay"
        class="video-card-icon"
        aria-hidden="true"
      >
        <van-icon name="enlarge" @click="onCutMainVideo" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-card-bar {
  height: 20px;
  display: flex;
  padding: 0 5px;
  align-items: center;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  justify-content: space-between;
  z-index: 6;
  box-sizing: border-box;

  .video-card-actions {
    display: flex;
    justify-content: space-between;
    padding-left: 7px;
  }

  .video-card-nickname {
    color: #fff !important;
    border: none !important;
    height: 20px;
    font-size: 12px;
    max-width: 80px;
    padding: 0 4px;
    line-height: 20px;
  }

  .video-card-icon {
    cursor: pointer;
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
}
</style>
