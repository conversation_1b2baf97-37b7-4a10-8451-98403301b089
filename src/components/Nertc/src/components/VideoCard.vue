<!--
 * @description 音视频卡片
-->

<script setup lang="ts">
import VideoCardBar from './VideoCardBar.vue'
import { computed } from 'vue'
import { useNertc } from '../hooks/useNertc'
import { NertcMember } from '../types/member';
const props = defineProps<{
  /** 成员 */
  member: NertcMember
}>()
const nertc = useNertc()

// 设置音视频元素
const setElementRef = (el: any) => {
  nertc.addElement(props.member.uid, el)
}
</script>

<template>
  <div class="video-card" shadow="hover">
    <div class="video-card-stream" :ref="setElementRef"></div>
    <van-image class="video-card-image" :src="props.member.avatar" fit="cover" :show-loading="false"
      :show-error="false" />
    <VideoCardBar v-bind="$attrs" :member="props.member" />
  </div>
</template>

<style lang="scss" scoped>
.video-card {
  margin-bottom: 12px;
  width: 154px;
  height: 100px;
  position: relative;
  background-color: #000;
  background-size: cover !important;
  background-position: center;

  :deep(.nertc-video-container) {
    background: #000;
  }

  :deep(.el-card__body) {
    padding: 0 !important;
  }

  :deep(.el-card__body),
  .video-card-stream {
    width: 100%;
    height: 100%;
    z-index: 5;
  }

  :deep(video) {
    width: 100%;
    height: 100%;
    z-index: 5;
  }
}

.video-card-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 42px;
  height: 42px;
  border: 1px solid #E6ECFB;
  border-radius: 42px;
}
</style>
