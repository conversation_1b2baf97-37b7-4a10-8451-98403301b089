<!--
 * @description 成员提供者
-->

<script lang="ts">
import { useContext } from '../hooks/useContext'
import { defineComponent, Ref, toRefs } from 'vue'
import { NertcMember } from '../types/member'

const [MemberProvider, useMemberContext] = useContext<{
  member: Ref<NertcMember>
}>()

export { useMemberContext }

export default defineComponent({
  name: "MemberProvider"
})
</script>

<script setup lang="ts">

const props = defineProps<{
  member: NertcMember
}>()

MemberProvider(toRefs(props))
</script>

<template>
  <slot v-bind="$attrs"></slot>
</template>

<style lang="scss" scoped></style>
