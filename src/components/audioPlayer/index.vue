<script setup lang="ts">
// 接收音频 URL 作为 props
const props = defineProps({
  src: {
    type: String,
    required: true,
  },
});

// 定义 emits 事件，通知父组件
const emit = defineEmits(["play", "pause", "stop", "progress"]);

// 音频对象
const audio = ref(new Audio());
const isPlaying = ref(false);
const progress = ref(0);
const duration = ref(0);

// 监听音频 URL 变化，重新加载音频
watch(
  () => props.src,
  (newSrc) => {
    if (audio.value) {
      audio.value.src = newSrc;
      audio.value.load();
      isPlaying.value = false;
      progress.value = 0;
      duration.value = 0;
    }
  },
  { immediate: true }
);

// 更新播放进度
const updateProgress = () => {
  if (audio.value.duration) {
    progress.value = (audio.value.currentTime / audio.value.duration) * 100;
    emit("progress", progress.value);
  }
};

// 监听音频加载完成，获取时长
const updateDuration = () => {
  duration.value = audio.value.duration;
};

// 播放音频
const playAudio = () => {
  audio.value.play();
  isPlaying.value = true;
  emit("play");
};

// 暂停音频
const pauseAudio = () => {
  audio.value.pause();
  isPlaying.value = false;
  emit("pause");
};

// 停止音频
const stopAudio = () => {
  audio.value.pause();
  audio.value.currentTime = 0;
  isPlaying.value = false;
  emit("stop");
};

// 拖动进度条调整播放进度
const changeProgress = (event: any) => {
  const newTime = (event.target.value / 100) * audio.value.duration;
  audio.value.currentTime = newTime;
};
const formatTime = (time: number): string => {
  const hours = Math.floor(time / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = Math.floor(time % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }
};
// 监听音频事件
onMounted(() => {
  audio.value.addEventListener("timeupdate", updateProgress);
  audio.value.addEventListener("loadedmetadata", updateDuration);
  audio.value.addEventListener("ended", () => {
    isPlaying.value = false;
    emit("stop");
  });
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  audio.value.removeEventListener("timeupdate", updateProgress);
  audio.value.removeEventListener("loadedmetadata", updateDuration);
});
</script>

<template>
  <div class="audio-player">
    <div class="controls">
      <button @click="playAudio" v-if="!isPlaying">▶ 播放</button>
      <button @click="pauseAudio" v-if="isPlaying">⏸ 暂停</button>
      <button @click="stopAudio">⏹ 停止</button>
    </div>

    <input
      type="range"
      min="0"
      max="100"
      v-model="progress"
      @input="changeProgress"
    />

    <p>
      {{ formatTime((progress * duration) / 100) }} / {{ formatTime(duration) }}
    </p>
  </div>
</template>

<style lang="scss" scoped>
.audio-player {
  text-align: center;
  width: 300px;
  margin: auto;
}
.controls button {
  margin: 5px;
  padding: 5px 10px;
  font-size: 16px;
}
input[type="range"] {
  width: 100%;
}
</style>
