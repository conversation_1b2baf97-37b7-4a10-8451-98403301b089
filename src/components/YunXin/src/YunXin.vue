<!--
 * @description 云信音视频
-->

<script setup lang="ts">
import { ref } from 'vue';
import { NMeetingBar, NMeetingVideoList, NMeetingMainVideo } from '../../Nertc'
import { NChatroom, NCSendMessage, NCmember } from '../../Nim'
import useStore from '@/store'
const props = withDefaults(defineProps<{
  /** 是否显示放大/缩小按钮 */
  zoom: boolean
}>(), {
  zoom: false
})

const emit = defineEmits<{
  /** 结束视频链接 */
  (event: 'leaving'): void
  /** 复制邀请链接 */
  (event: 'invitation', toClipboard: (text: string) => Promise<unknown>): void
}>()
const activePage = ref(false); //聊天界面
const memberShow = ref(false); //成员列表
const { selectColor } = useStore()
const messageListHeight = ref('442px'); //聊天室高度

watch(
  () => activePage.value,
  (n) => {
    if (n) {
      messageListHeight.value = '364px';
    } else {
      messageListHeight.value = '400px';
    }

  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="yun-xin">
    <NMeetingMainVideo v-show="selectColor.isMainVideoPlay" class="meetingMain" />
    <NMeetingVideoList :zoom="props.zoom" />
    <NChatroom />
    <!-- 发送消息框 -->
    <NCSendMessage ref="sendMessageRef" v-model:activePage="activePage" />
    <NMeetingBar v-if="activePage && !memberShow" v-model:memberShow="memberShow" @leaving="emit('leaving')" />
    <!-- 成员管理 -->
    <NCmember v-if="memberShow" v-model:memberShow="memberShow" @invitation="emit('invitation', $event)" />
  </div>
</template>

<style lang="scss" scoped>
.yun-xin {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
}

.meetingMain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 11;
}
</style>
