<!--
 * @description 云信音视频
-->

<script setup lang="ts">
import { ref, computed } from "vue";
import { NMeetingBar, NMeetingVideoList, NMeetingMainVideo } from "../../Nertc";
import { NChatroom, NCSendMessage, NCmember } from "../../Nim";
import useStore from "@/store";
const props = withDefaults(
  defineProps<{
    /** 是否显示放大/缩小按钮 */
    zoom: boolean;
  }>(),
  {
    zoom: false,
  }
);

const emit = defineEmits<{
  /** 结束视频链接 */
  (event: "leaving"): void;
  /** 复制邀请链接 */
  (event: "invitation", toClipboard: (text: string) => Promise<unknown>): void;
}>();
const activePage = ref(false); //聊天界面
const memberShow = ref(false); //成员列表
const { selectColor } = useStore();

// 计算聊天室高度 - 在新的布局结构中，聊天室占据主内容区域的剩余空间
const chatRoomHeight = computed(() => {
  // 聊天室高度 = 主内容区域高度 - 视频列表高度
  // 由于主内容区域会根据底部区域的高度自动调整，这里使用flex布局
  return "100%";
});
</script>

<template>
  <div class="yun-xin">
    <NMeetingMainVideo
      v-show="selectColor.isMainVideoPlay"
      class="meetingMain"
    />
    <!-- 主要内容区域 -->
    <div class="main-content">
      <NMeetingVideoList :zoom="props.zoom" />
      <NChatroom :height="chatRoomHeight" />
    </div>

    <!-- 底部区域 - 输入框和功能菜单 -->
    <div class="bottom-area">
      <!-- 发送消息框 -->
      <NCSendMessage ref="sendMessageRef" v-model:activePage="activePage" />
      <!-- 功能菜单 -->
      <NMeetingBar
        v-if="activePage && !memberShow"
        v-model:memberShow="memberShow"
        @leaving="emit('leaving')"
      />
    </div>

    <!-- 成员管理 -->
    <NCmember
      v-if="memberShow"
      v-model:memberShow="memberShow"
      @invitation="emit('invitation', $event)"
    />
  </div>
</template>

<style lang="scss" scoped>
.yun-xin {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.bottom-area {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.meetingMain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 11;
}
</style>
