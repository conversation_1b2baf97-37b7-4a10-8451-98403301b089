<template>
  <div class="box">
    <div class="The-main-body">
      <div v-if="props.flag" class="body">
        <van-checkbox
          v-model="checkAll"
          icon-size="14px"
          shape="square"
          @change="handleCheckAllChange"
          @click="handleCheckAllChanges"
        >
          {{ props.body }}
          <template #icon="props">
            <div class="div-icon">{{ props.checked ? "√" : "" }}</div>
          </template>
        </van-checkbox>
      </div>
      <div v-else class="body">
        <span>{{ props.body }}</span>
      </div>
      <span class="interval" v-if="list.length">-</span>
      <div class="Bifurcate">
        <span v-for="(itme, index) in list" :key="index"></span>
      </div>
      <div class="content">
        <van-checkbox-group
          v-model="checkedCitiess"
          @change="handleCheckedCitiesChange"
        >
          <van-checkbox
            v-for="city in list"
            icon-size="14px"
            shape="square"
            key="city"
            :name="city"
            size="large"
            >{{ city }}
            <template #icon="props">
              <div class="div-icon">{{ props.checked ? "√" : "" }}</div>
            </template>
          </van-checkbox>
        </van-checkbox-group>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  body: {
    type: String,
    default: "",
  },
  list: {
    type: Array,
    default: () => [],
  },
  flag: {
    type: Boolean,
    default: true,
  },
  checkAll: {
    type: Boolean,
    default: false,
  },
  checkedCitiess: {
    type: Array,
    default: () => [],
  },
});
watch(
  () => props.checkAll,
  (val) => {
    nextTick(() => {
      checkAll.value = val;
    });
  },
  { immediate: true }
);
watch(
  () => props.checkedCitiess,
  (val) => {
    nextTick(() => {
      checkedCitiess.value = val;
    });
  },
  { immediate: true }
);
const checkAll = ref();
const emits = defineEmits([
  "update:checkedCitiess",
  "update:checkAll",
  "sayHi",
  "sayHis",
]);
const handleCheckAllChange = (value: boolean) => {
  emits("update:checkAll", value);
};
const handleCheckAllChanges = () => {
  emits("sayHis", checkAll.value);
};
const checkedCitiess = ref();
const handleCheckedCitiesChange = (value: any) => {
  emits("update:checkedCitiess", value);
  emits("sayHi", value);
};
</script>
<style scoped lang="scss">
.The-main-body {
  display: flex;
  font-size: 14px;

  .interval {
    display: flex;
    align-items: center;
    color: #aaaaaa;
    font-size: 12px;
  }

  .body {
    display: inline-block;
    align-self: center;
    text-align: center;
    font-size: 12px;
    margin-right: 10px;

    > .label {
      display: inline-block;
      padding-bottom: 4px;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    font-size: 14px;
  }

  .Bifurcate {
    display: flex;
    flex-direction: column;
    align-content: space-between;
    margin-top: 10px;

    span {
      width: 16px;
      height: 23px;
      border-top: 1px solid #aaaaaa;
      border-left: 1px solid #aaaaaa;
      box-sizing: border-box;

      &:last-child {
        border-left: none;
        height: 0px;
      }
    }
  }
}

// .van-checkbox__icon.van-checkbox__icon--square.van-checkbox__icon--checked {

//     border: 1px solid #EAEAEA;
// }

.div-icon {
  width: 14px;
  height: 14px;
  border: 1px solid #eaeaea;
  border-radius: 2px 2px 2px 2px;
  color: #650faa;
  text-align: center;
  font-size: 14px;
  line-height: 14px;
}

:deep(.van-checkbox__label) {
  margin-top: 2px;
}

:deep(.van-checkbox__icon) {
  height: 14px;
}
</style>
