<template>
  <div class="upload">
    <div>{{ props.uploaderName }}</div>
    <div class="uploadClass">
      <template v-for="index in 4" :key="index">
        <!-- GEC 模式只显示第一张图 -->
        <div v-if="!TCT || index === 1">
          <div class="imageClass">
            <van-image
              v-if="getImageSrc(index - 1)"
              fit="contain"
              class="upload-file"
              :src="getImageSrc(index - 1)"
              :show-loading="false"
              :show-error="false"
              @click="showImagePreview(getImageSrc(index - 1))"
            />
            <div
              v-else
              class="upload-file"
              :style="{
                marginBottom:
                  props.TCT || index === 3 || index === 4 ? '0px' : '12px',
              }"
            >
              <van-image :src="Image" />
            </div>

            <button
              v-if="getImageSrc(index - 1)"
              type="button"
              class="btn-delete"
              @click="handleDeleteScreenShot(index - 1)"
            >
              删除
            </button>
          </div>

          <van-field
            v-if="imageList[index - 1]"
            v-model="imageList[index - 1].Remark"
            :maxlength="10"
            class="remark-field"
            :style="{
              marginBottom:
                props.TCT || index === 3 || index === 4 ? '0px' : '12px',
            }"
            placeholder="请输入（限10字内）"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, inject } from "vue";
import { ImagePreview } from "vant";
import { ScreenShot } from "@/hooks/useScreenshot";
import Image from "@/assets/images/image.png";

const props = defineProps({
  uploaderName: { type: String, default: "报告附图" },
  TCT: { type: Boolean, default: false },
  ExpansionInformation: { type: Object, default: () => ({}) },
});
const emits = defineEmits(["deleteScreenShots"]);

const uploadScreenshot = inject("uploadScreenshot") as (
  src: string,
  objectUrl: string
) => Promise<void>;
const isBase64 = inject("isBase64") as (str: string) => boolean;
const imageList = inject("screenshots") as ScreenShot[];
const deleteScreenshot = inject("deleteScreenshot") as (index: number) => void;

defineExpose({ imageList, handleUpload });

/* 获取图片展示优先级 */
function getImageSrc(index: number): string | undefined {
  return imageList[index]?.Url;
}

/* 预览图片 */
function showImagePreview(url?: string) {
  if (url) {
    ImagePreview({ images: [url] });
  }
}

/* 删除图片 */
function handleDeleteScreenShot(index: number) {
  deleteScreenshot(index);
  emits("deleteScreenShots", index);
}

/* 上传图片 */
async function handleUpload() {
  if (imageList && imageList.length) {
    await Promise.all(
      imageList.map(async (item) => {
        if (isBase64(item.Url)) {
          await uploadScreenshot(item.Url, item.ImageUrl);
        }
      })
    );
  }
}
/* 获取附图地址 */
watch(
  () => props.ExpansionInformation,
  async (val) => {
    console.log("+++++++++++++");

    if (!val || !Array.isArray(val)) return;

    // 清空
    imageList.splice(0, imageList.length);
    for (const item of val) {
      const { ImageUrl, Remark } = item;

      if (ImageUrl) {
        const res = await API.accountApi.getGetpresigned.request({
          params: { Str: ImageUrl },
        });
        const url = res.Data?.Str || null;
        imageList.push({
          Url: url,
          ImageUrl,
          Remark,
        });
      }
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.upload {
  margin: 12px 14px 12px;
  padding: 12px;
  background-color: #fff;
  font-size: 14px;
  font-weight: bold;
  color: #636363;
  border-radius: 4px;
}

.uploadClass {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 8px;
}

.imageClass {
  position: relative;
}

.upload-file {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 154px;
  height: 86px;
  border: 1px solid #e6ecfb;
  background-color: #f3f6fd;
  margin-bottom: 8px;
}

.btn-delete {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 24px;
  line-height: 24px;
  border: none;
  background: rgba(0, 0, 0, 0.4);
  color: white;
}

.iconUpload {
  font-size: 30px;
  color: #e6ecfb;
}

.remark-field {
  width: 155px;
  border: 1px solid #e6ecfb;
  padding: 8px 10px;
  margin-bottom: 16px;
}
</style>
