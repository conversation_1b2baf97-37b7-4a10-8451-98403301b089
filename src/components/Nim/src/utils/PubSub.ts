/**
 * @description 发布/订阅
 */

interface EventItem<T> {
  event: T
  handler: (...any: any[]) => void
}

export class PubSub<T = string> {
  /**
   * @description 事件集合
   */
  private events: EventItem<T>[] = []

  /**
   * @description 发布
   * @param {T} event
   * @param {array} args
   */
  emit(event: T, ...args: any[]) {
    this.events.forEach((e) => {
      if (e.event === event) {
        e.handler(...args)
      }
    })
  }

  /**
   * @description 订阅
   * @param {TextDecoderCommon} event
   * @param {function} handler
   */
  on(event: T, handler: (...any: any[]) => void) {
    if (!this.events.some((e) => e.event === event && e.handler === handler)) {
      const eventItem: EventItem<T> = { event, handler }
      this.events.push(eventItem)
    }
  }

  /**
   * @description 删除订阅
   * @param {T} event
   * @param {function} handler
   */
  off(event: T, handler: (...any: any[]) => void) {
    const index = this.events.findIndex((e) => e.event === event && e.handler === handler)
    if (index >= 0) {
      this.events.splice(index, 1)
    }
  }

  /**
   * @description 清空订阅
   */
   clear() {
    this.events = []
  }
}
