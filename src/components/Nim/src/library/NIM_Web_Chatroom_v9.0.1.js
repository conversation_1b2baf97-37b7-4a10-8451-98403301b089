!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Chatroom=t():e.<PERSON><PERSON>om=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=614)}([function(e,t,n){var r=n(113),o=n(114),i=n(80),a=n(60),s=n(61),c=n(62),u=n(63),l=n(50),p=n(64),m=n(3),f=n(6),d=n(17),g=n(9),h=n(115),y=n(77),v=n(105),b=n(30),T=n(10),S=n(8),x=n(12),k=n(119),M=n(107),w=n(25),_=n(19),C=n(331),P=n(4),I=n(28);function A(e,t){var n=g(e);if(a){var r=a(e);t&&(r=_(r).call(r,(function(t){return s(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e,t){var n=void 0!==o&&i(e)||e["@@iterator"];if(!n){if(b(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return O(e,t);var o=d(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return r(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return O(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var a=0,s=function(){};return{s:s,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c,u=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return u=e.done,e},e:function(e){l=!0,c=e},f:function(){try{u||null==n.return||n.return()}finally{if(l)throw c}}}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var j=n(191),N=n(148);function L(e){"object"===I(e)?(this.callFunc=e.callFunc||null,this.message=e.message||"UNKNOW ERROR"):this.message=e,this.time=new Date,this.timetag=+this.time}n(367);var R,D,F=n(43),U=F.getGlobal(),B=/\s+/;F.deduplicate=function(e){var t=[];return m(e).call(e,(function(e){-1===f(t).call(t,e)&&t.push(e)})),t},F.capFirstLetter=function(e){return e?d(e=""+e).call(e,0,1).toUpperCase()+d(e).call(e,1):""},F.guid=(R=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return R()+R()+R()+R()+R()+R()+R()+R()}),F.extend=function(e,t,n){for(var r in t)void 0!==e[r]&&!0!==n||(e[r]=t[r])},F.filterObj=function(e,t){var n={};return F.isString(t)&&(t=t.split(B)),m(t).call(t,(function(t){e.hasOwnProperty(t)&&(n[t]=e[t])})),n},F.copy=function(e,t){var n;return t=t||{},e?(m(n=g(e)).call(n,(function(n){F.exist(e[n])&&(t[n]=e[n])})),t):t},F.copyWithNull=function(e,t){var n;return t=t||{},e?(m(n=g(e)).call(n,(function(n){(F.exist(e[n])||F.isnull(e[n]))&&(t[n]=e[n])})),t):t},F.findObjIndexInArray=function(e,t){e=e||[];var n=t.keyPath||"id",r=-1;return h(e).call(e,(function(e,o){if(N(e,n)===t.value)return r=o,!0})),r},F.findObjInArray=function(e,t){var n=F.findObjIndexInArray(e,t);return-1===n?null:e[n]},F.mergeObjArray=function(){var e=[],t=d([]).call(arguments,0,-1),n=arguments[arguments.length-1];F.isArray(n)&&(t.push(n),n={});var r,o=n.keyPath=n.keyPath||"id";for(n.sortPath=n.sortPath||o;!e.length&&t.length;)e=t.shift()||[],e=d(e).call(e,0);return m(t).call(t,(function(t){t&&m(t).call(t,(function(t){-1!==(r=F.findObjIndexInArray(e,{keyPath:o,value:N(t,o)}))?e[r]=F.merge({},e[r],t):e.push(t)}))})),n.notSort||(e=F.sortObjArray(e,n)),e},F.cutObjArray=function(e){var t=d(e).call(e,0),n=arguments.length,r=d([]).call(arguments,1,n-1),o=arguments[n-1];F.isObject(o)||(r.push(o),o={});var i,a=o.keyPath=o.keyPath||"id";return m(r).call(r,(function(e){F.isArray(e)||(e=[e]),m(e).call(e,(function(e){e&&(o.value=N(e,a),-1!==(i=F.findObjIndexInArray(t,o))&&y(t).call(t,i,1))}))})),t},F.sortObjArray=function(e,t){var n=(t=t||{}).sortPath||"id";j.insensitive=!!t.insensitive;var r,o,i,a=!!t.desc;return i=F.isFunction(t.compare)?t.compare:function(e,t){return r=N(e,n),o=N(t,n),a?j(o,r):j(r,o)},v(e).call(e,i)},F.emptyFunc=function(){},F.isEmptyFunc=function(e){return e===F.emptyFunc},F.notEmptyFunc=function(e){return e!==F.emptyFunc},F.splice=function(e,t,n){return y([]).call(e,t,n)},F.reshape2d=function(e,t){if(b(e)){F.verifyParamType("type",t,"number","util::reshape2d");var n=e.length;if(n<=t)return[e];for(var r=Math.ceil(n/t),o=[],i=0;i<r;i++)o.push(d(e).call(e,i*t,(i+1)*t));return o}return e},F.flatten2d=function(e){if(b(e)){var t=[];return m(e).call(e,(function(e){t=T(t).call(t,e)})),t}return e},F.dropArrayDuplicates=function(e){if(b(e)){for(var t={},n=[];e.length>0;){t[e.shift()]=!0}for(var r in t)!0===t[r]&&n.push(r);return n}return e},F.onError=function(e){throw new L(e)},F.verifyParamPresent=function(e,t,n,r){n=n||"";var o=!1;switch(F.typeOf(t)){case"undefined":case"null":o=!0;break;case"string":""===t&&(o=!0);break;case"StrStrMap":case"object":g(t).length||(o=!0);break;case"array":t.length?h(t).call(t,(function(e){if(F.notexist(e))return o=!0,!0})):o=!0}o&&F.onParamAbsent(n+e,r)},F.onParamAbsent=function(e,t){F.onParamError("缺少参数 ".concat(e,", 请确保参数不是 空字符串、空对象、空数组、null或undefined, 或数组的内容不是 null/undefined"),t)},F.verifyParamAbsent=function(e,t,n,r){n=n||"",void 0!==t&&F.onParamPresent(n+e,r)},F.onParamPresent=function(e,t){F.onParamError("多余的参数 ".concat(e),t)},F.verifyParamType=function(e,t,n,r){var o=F.typeOf(t).toLowerCase();F.isArray(n)||(n=[n]),n=S(n).call(n,(function(e){return e.toLowerCase()}));var i=!0;switch(-1===f(n).call(n,o)&&(i=!1),o){case"number":isNaN(t)&&(i=!1);break;case"string":"numeric or numeric string"===n.join("")&&(i=!!/^[0-9]+$/.test(t))}i||F.onParamInvalidType(e,n,"",r)},F.onParamInvalidType=function(e,t,n,r){n=n||"",t=F.isArray(t)?(t=S(t).call(t,(function(e){return'"'+e+'"'}))).join(", "):'"'+t+'"',F.onParamError('参数"'+n+e+'"类型错误, 合法的类型包括: ['+t+"]",r)},F.verifyParamValid=function(e,t,n,r){F.isArray(n)||(n=[n]),-1===f(n).call(n,t)&&F.onParamInvalidValue(e,n,r)},F.onParamInvalidValue=function(e,t,n){var r;F.isArray(t)||(t=[t]),t=S(t).call(t,(function(e){return'"'+e+'"'})),F.isArray(t)&&(t=t.join(", ")),F.onParamError(T(r="参数 ".concat(e,"值错误, 合法的值包括: [")).call(r,x(t),"]"),n)},F.verifyParamMin=function(e,t,n,r){t<n&&F.onParamError("参数"+e+"的值不能小于"+n,r)},F.verifyParamMax=function(e,t,n,r){t>n&&F.onParamError("参数"+e+"的值不能大于"+n,r)},F.verifyArrayMax=function(e,t,n,r){t.length>n&&F.onParamError("参数"+e+"的长度不能大于"+n,r)},F.verifyEmail=(D=/^\S+@\S+$/,function(e,t,n){D.test(t)||F.onParamError("参数"+e+"邮箱格式错误, 合法格式必须包含@符号, @符号前后至少要各有一个字符",n)}),F.verifyTel=function(){var e=/^[+\-()\d]+$/;return function(t,n,r){e.test(n)||F.onParamError("参数"+t+"电话号码格式错误, 合法字符包括+、-、英文括号和数字",r)}}(),F.verifyBirth=function(){var e=/^(\d{4})-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$/;return function(t,n,r){e.test(n)||F.onParamError("参数"+t+'生日格式错误, 合法为"yyyy-MM-dd"',r)}}(),F.onParamError=function(e,t){F.onError({message:e,callFunc:t})},F.verifyOptions=function(e,t,n,r,o){if(e=e||{},t&&(F.isString(t)&&(t=t.split(B)),F.isArray(t))){"boolean"!=typeof n&&(o=n||null,n=!0,r="");var i=n?F.verifyParamPresent:F.verifyParamAbsent;m(t).call(t,(function(t){i.call(F,t,e[t],r,o)}))}return e},F.verifyParamAtLeastPresentOne=function(e,t,n){t&&(F.isString(t)&&(t=t.split(B)),F.isArray(t)&&(h(t).call(t,(function(t){return F.exist(e[t])}))||F.onParamError("以下参数["+t.join(", ")+"]至少需要传入一个",n)))},F.verifyParamPresentJustOne=function(e,t,n){t&&(F.isString(t)&&(t=t.split(B)),F.isArray(t)&&1!==k(t).call(t,(function(t,n){return F.exist(e[n])&&t++,t}),0)&&F.onParamError("以下参数["+t.join(", ")+"]必须且只能传入一个",n))},F.verifyBooleanWithDefault=function(e,t,n,r,o){F.undef(n)&&(n=!0),B.test(t)&&(t=t.split(B)),F.isArray(t)?m(t).call(t,(function(t){F.verifyBooleanWithDefault(e,t,n,r,o)})):void 0===e[t]?e[t]=n:F.isBoolean(e[t])||F.onParamInvalidType(t,"boolean",r,o)},F.verifyFileInput=function(e,t){var n;(F.verifyParamPresent("fileInput",e,"",t),F.isString(e)&&((e="undefined"==typeof document?void 0:document.getElementById(e))||F.onParamError("找不到要上传的文件对应的input, 请检查fileInput id ".concat(e),t)),e.tagName&&"input"===e.tagName.toLowerCase()&&"file"===e.type.toLowerCase())||F.onParamError(T(n="请提供正确的 fileInput, 必须为 file 类型的 input 节点 tagname:".concat(e.tagName,", filetype:")).call(n,e.type),t);return e},F.verifyFileType=function(e,t){F.verifyParamValid("type",e,F.validFileTypes,t)},F.verifyCallback=function(e,t,n){B.test(t)&&(t=t.split(B)),F.isArray(t)?m(t).call(t,(function(t){F.verifyCallback(e,t,n)})):e[t]?F.isFunction(e[t])||F.onParamInvalidType(t,"function","",n):e[t]=F.emptyFunc},F.verifyFileUploadCallback=function(e,t){F.verifyCallback(e,"uploadprogress uploaddone uploaderror uploadcancel",t)},F.validFileTypes=["image","audio","video","file"],F.validFileExts={image:["bmp","gif","jpg","jpeg","jng","png","webp"],audio:["mp3","wav","aac","wma","wmv","amr","mp2","flac","vorbis","ac3"],video:["mp4","rm","rmvb","wmv","avi","mpg","mpeg","mov"]},F.filterFiles=function(e,t){var n,r,o="file"===(t=t.toLowerCase()),i=[];return m([]).call(e,(function(e){var a,s;if(o)i.push(e);else if(n=d(a=e.name).call(a,M(s=e.name).call(s,".")+1),(r=e.type.split("/"))[0]&&r[1]){var c,u=!1;if(r[0].toLowerCase()===t)u=!0;else u=-1!==f(c=F.validFileExts[t]).call(c,n);u&&i.push(e)}})),i};var q,H,z=F.supportFormData=F.notundef(U.FormData);F.getFileName=function(e){return e=F.verifyFileInput(e),z?e.files[0].name:d(t=e.value).call(t,M(n=e.value).call(n,"\\")+1);var t,n},F.getFileInfo=(q={ppt:1,pptx:2,pdf:3,doc:6,docx:7},function(e){var t={};if(!(e=F.verifyFileInput(e)).files)return t;var n=e.files[0];return z&&(t.name=n.name,t.size=n.size,t.type=n.name.match(/\.(\w+)$/),t.type=t.type&&t.type[1].toLowerCase(),t.transcodeType=q[t.type]||0),t}),F.sizeText=(H=["B","KB","MB","GB","TB","PB","EB","ZB","BB"],function(e){var t,n=0;do{t=(e=Math.floor(100*e)/100)+H[n],e/=1024,n++}while(e>1);return t}),F.promises2cmds=function(e){return S(e).call(e,(function(e){return e.cmd}))},F.objs2accounts=function(e){return S(e).call(e,(function(e){return e.account}))},F.teams2ids=function(e){return S(e).call(e,(function(e){return e.teamId}))},F.objs2ids=function(e){return S(e).call(e,(function(e){return e.id}))},F.getMaxUpdateTime=function(e){var t=S(e).call(e,(function(e){return+e.updateTime}));return Math.max.apply(Math,t)},F.genCheckUniqueFunc=function(e,t){return e=e||"id",t=t||1e3,function(t){this.uniqueSet=this.uniqueSet||{},this.uniqueSet[e]=this.uniqueSet[e]||{};var n=this.uniqueSet[e],r=t[e];return!n[r]&&(n[r]=!0,!0)}},F.fillPropertyWithDefault=function(e,t,n){return!!F.undef(e[t])&&(e[t]=n,!0)},F.throttle=function(e,t,n,r){var o,i,a=null,s=0;n||(n={});var c=function(){s=!1===n.leading?0:(new Date).getTime(),a=null,e.apply(o,i),a||(o=i=null)};return function(){var u=(new Date).getTime();s||!1!==n.leading||(s=u);var l=t-(u-s);return o=this,i=arguments,l<=0||l>t?(clearTimeout(a),a=null,s=u,e.apply(o,i),a||(o=i=null)):a||!1===n.trailing?a&&r&&r.apply(o,i):(a=w(c,l),r&&r.apply(o,i)),a}},F.get=function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,o=t.replace(/\[(\d+)\]/g,".$1").split("."),i=e,a=E(o);try{for(a.s();!(n=a.n()).done;){var s=n.value;if(void 0===(i=Object(i)[s]))return r}}catch(e){a.e(e)}finally{a.f()}return i},F.pickAsString=function(e,t){var n;return e?S(n=_(t).call(t,(function(t){return void 0!==e[t]}))).call(n,(function(t){var n;return T(n="".concat(t,"=")).call(n,e[t])})).join(","):""},F.omitAsString=function(e,t){var n,r;if(!e)return"";var o={};return m(t).call(t,(function(e){o[e]=!0})),S(n=_(r=g(e)).call(r,(function(e){return!o[e]}))).call(n,(function(t){var n;return T(n="".concat(t,"=")).call(n,e[t])})).join(",")};var W={session:function(e){var t=function(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?m(n=A(Object(o),!0)).call(n,(function(t){p(e,t,o[t])})):c?u(e,c(o)):m(r=A(Object(o))).call(r,(function(t){l(e,t,s(o,t))}))}return e}({},e),n=["topCustom","localCustom","extra"];return m(n).call(n,(function(e){t[e]&&(t[e]="***")})),t.lastMsg&&(t.lastMsg={idServer:t.lastMsg.idServer,idClient:t.lastMsg.idClient}),t}};F.secureOutput=function(e,t){b(t)||(t=[t]);var n=W[e];return 1===(t=S(t).call(t,(function(e){return n(e)}))).length?t[0]:t};var G={string:function(e,t,n){var r=n.required,o=n.allowEmpty,i=e[t];return!1===r&&void 0===i||"string"==typeof i&&!(!o&&""===i)},number:function(e,t,n){var r=n.required,o=n.min,i=e[t];return!1===r&&void 0===i||"number"==typeof i&&!(i<o)},enum:function(e,t,n){var r=n.required,o=C(n),i=e[t];return!1===r&&void 0===i||f(o).call(o,i)>-1},array:function(e,t,n){var r=n.required,o=e[t];return!1===r&&void 0===o||!!b(o)}};F.validate=function(e,t,n){var r,o={};return m(r=g(e)).call(r,(function(r){var i,a=e[r].type,s=G[a];s&&!s(t,r,e[r])?F.onError({message:T(i="Error in parameter verification, ".concat(r," expected value is ")).call(i,x(e[r])),callFunc:n}):o[r]=t[r]})),o},F.asyncPool=function(e,t,n){if("number"!=typeof e||"function"!=typeof n||!t||!t.length)return P.resolve();var r=0,o=[],i=[];return function a(){if(r===t.length)return P.resolve();var s=t[r++],c=P.resolve().then((function(){return n(s,t)}));o.push(c);var u=P.resolve();if(e<=t.length){var l=c.then((function(){return y(i).call(i,f(i).call(i,l),1)}));i.push(l),i.length>=e&&(u=P.race(i))}return u.then((function(){return a()}))}().then((function(){return P.all(o)}))},F.getIdArray=function(e){if(!e||"string"!=typeof e)throw new Error("id type error");var t=f(e).call(e,"-");if(t<0||t===e.length-1)throw new Error("id format error");return[d(e).call(e,0,t),d(e).call(e,t+1)]},e.exports=F},function(e,t,n){(function(t){var n=function(e){return e&&e.Math==Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t&&t)||function(){return this}()||Function("return this")()}).call(this,n(49))},function(e,t,n){"use strict";var r=n(1),o=n(56),i=n(7),a=n(21),s=n(67).f,c=n(160),u=n(13),l=n(70),p=n(46),m=n(26),f=function(e){var t=function(n,r,i){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(n);case 2:return new e(n,r)}return new e(n,r,i)}return o(e,this,arguments)};return t.prototype=e.prototype,t};e.exports=function(e,t){var n,o,d,g,h,y,v,b,T=e.target,S=e.global,x=e.stat,k=e.proto,M=S?r:x?r[T]:(r[T]||{}).prototype,w=S?u:u[T]||p(u,T,{})[T],_=w.prototype;for(d in t)n=!c(S?d:T+(x?".":"#")+d,e.forced)&&M&&m(M,d),h=w[d],n&&(y=e.noTargetGet?(b=s(M,d))&&b.value:M[d]),g=n&&y?y:t[d],n&&typeof h==typeof g||(v=e.bind&&n?l(g,r):e.wrap&&n?f(g):k&&a(g)?i(g):g,(e.sham||g&&g.sham||h&&h.sham)&&p(v,"sham",!0),p(w,d,v),k&&(m(u,o=T+"Prototype")||p(u,o,{}),p(u[o],d,g),e.real&&_&&!_[d]&&p(_,d,g)))}},function(e,t,n){e.exports=n(281)},function(e,t,n){e.exports=n(208)},function(e,t,n){e.exports=n(430)},function(e,t,n){e.exports=n(157)},function(e,t,n){var r=n(83),o=Function.prototype,i=o.bind,a=o.call,s=r&&i.bind(a,a);e.exports=r?function(e){return e&&s(e)}:function(e){return e&&function(){return a.apply(e,arguments)}}},function(e,t,n){e.exports=n(309)},function(e,t,n){e.exports=n(182)},function(e,t,n){e.exports=n(231)},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t,n){e.exports=n(313)},function(e,t){e.exports={}},function(e,t,n){e.exports=n(378)},function(e,t,n){var r=n(6),o=n(10),i=(n(0),!1);try{"function"==typeof localStorage.setItem&&"function"==typeof localStorage.getItem&&(localStorage.setItem("nim_localstorage_exist_test","1"),i="1"===localStorage.getItem("nim_localstorage_exist_test"),localStorage.removeItem("nim_localstorage_exist_test"))}catch(e){i=!1}var a={nodeEnv:"production",info:{hash:"3f438a27d610a873eb0849d07ca3dfa26a209f1d",shortHash:"3f438a2",version:"9.0.1",sdkVersion:"90001",sdkHumanVersion:"9.0.1",protocolVersion:1},lbsUrl:"https://lbs.netease.im/lbs/webconf.jsp",roomserver:"roomserver.netease.im",connectTimeout:8e3,xhrTimeout:8e3,socketTimeout:8e3,reconnectionDelay:1600,reconnectionDelayMax:8e3,reconnectionJitter:.01,reconnectiontimer:null,heartbeatInterval:3e4,cmdTimeout:8e3,hbCmdTimeout:5e3,defaultReportUrl:"https://dr.netease.im/1.gif",isWeixinApp:!1,isNodejs:!1,isRN:!1,ipVersion:0,PUSHTOKEN:"",PUSHCONFIG:{},CLIENTTYPE:16,PushPermissionAsked:!1,iosPushConfig:null,androidPushConfig:null,netDetectAddr:"https://roomserver-dev.netease.im/v1/sdk/detect/local",optionDefaultLinkUrl:"",defaultLinkUrl:"weblink.netease.im",ipv6DefaultLinkUrl:"weblink.netease.im",optionIpv6DefaultLinkUrl:"",wxDefaultLinkUrl:"wlnimsc0.netease.im",serverNosConfig:i?{cdnDomain:localStorage.getItem("nim_cdn_domain")||"",objectPrefix:localStorage.getItem("nim_object_prefix")||""}:{},hasLocalStorage:i,getDefaultLinkUrl:function(e){var t,n;1===a.ipVersion?(t=a.optionIpv6DefaultLinkUrl,n=a.ipv6DefaultLinkUrl):(t=a.optionDefaultLinkUrl,n=a.defaultLinkUrl);var o=t||(a.isWeixinApp?a.wxDefaultLinkUrl:n);if(!o)return!1;var i=e?"https":"http",s=e?"443":"80",c=o;return-1===r(o).call(o,"http")&&(c=i+"://"+c),-1===r(o).call(o,":")&&(c=c+":"+s),c},ipProbeAddr:{ipv4:"https://detect4.netease.im/test/",ipv6:"https://detect6.netease.im/test/"},formatSocketUrl:function(e){var t=e.url,n=e.secure?"https":"http";return-1===r(t).call(t,"http")?n+"://"+t:t},uploadUrl:"https://nos.netease.com",chunkUploadUrl:"https://wanproxy-web.127.net",commonMaxSize:104857600,chunkSize:4194304,chunkMaxSize:4194304e4,replaceUrl:"https://{bucket}-nosdn.netease.im/{object}",downloadHost:"nos.netease.com",downloadHostList:["nos.netease.com"],downloadUrl:"https://{bucket}-nosdn.netease.im/{object}",nosCdnEnable:!0,httpsEnabled:!1,threshold:0,nosLbsUrls:["http://wanproxy.127.net/lbs","http://wanproxy-bj.127.net/lbs","http://wanproxy-hz.127.net/lbs","http://wanproxy-oversea.127.net/lbs"],genUploadUrl:function(e){return a.uploadUrl+"/"+e},genChunkUploadUrl:function(e){return a.chunkUploadUrl?a.chunkUploadUrl+"/"+e.bucket+"/"+e.objectName:""},genDownloadUrl:function(e,t,n){var r,i,s,c,u,l=e.bucket,p=(e.tag,e.expireSec),m=+new Date,f=p?"&survivalTime=".concat(p):"";if(n)return o(s=o(c=o(u="https://".concat(n,"/")).call(u,t,"?createTime=")).call(c,m)).call(s,f);var d=o(r=o(i="".concat(a.replaceUrl,"?createTime=")).call(i,m)).call(r,f);return(d=a.genNosProtocolUrl(d)).replace("{bucket}",l).replace("{object}",t)},genFileUrl:function(e){var t=e.bucket,n=e.objectName;return a.genNosProtocolUrl(a.replaceUrl).replace("{bucket}",t).replace("{object}",n)},genNosProtocolUrl:function(e){return/^http/.test(e)?a.httpsEnabled&&0!==r(e).call(e,"https://")&&(e=e.replace("http","https")):e=a.httpsEnabled?"https://".concat(e):"http://".concat(e),e}};e.exports=a},function(e,t,n){var r=n(7);e.exports=r({}.isPrototypeOf)},function(e,t,n){e.exports=n(181)},function(e,t,n){var r=n(1),o=n(124),i=n(26),a=n(126),s=n(122),c=n(158),u=o("wks"),l=r.Symbol,p=l&&l.for,m=c?l:l&&l.withoutSetter||a;e.exports=function(e){if(!i(u,e)||!s&&"string"!=typeof u[e]){var t="Symbol."+e;s&&i(l,e)?u[e]=l[e]:u[e]=c&&p?p(t):m(t)}return u[e]}},function(e,t,n){e.exports=n(327)},function(e,t,n){var r=n(13),o=n(26),i=n(138),a=n(40).f;e.exports=function(e){var t=r.Symbol||(r.Symbol={});o(t,e)||a(t,e,{value:i.f(e)})}},function(e,t){e.exports=function(e){return"function"==typeof e}},function(e,t,n){var r=n(13);e.exports=function(e){return r[e+"Prototype"]}},function(e,t,n){e.exports=n(411)},function(e,t,n){var r=n(11);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(e,t,n){e.exports=n(326)},function(e,t,n){var r=n(7),o=n(37),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},function(e,t,n){var r=n(21);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},function(e,t,n){var r=n(139),o=n(362);function i(t){return"function"==typeof r&&"symbol"==typeof o?(e.exports=i=function(e){return typeof e},e.exports.default=e.exports,e.exports.__esModule=!0):(e.exports=i=function(e){return e&&"function"==typeof r&&e.constructor===r&&e!==r.prototype?"symbol":typeof e},e.exports.default=e.exports,e.exports.__esModule=!0),i(t)}e.exports=i,e.exports.default=e.exports,e.exports.__esModule=!0},,function(e,t,n){e.exports=n(183)},function(e,t,n){var r=n(83),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},,function(e,t,n){var r=n(14),o=n(3),i=n(28);function a(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this;r.message=e||n.message||"","object"===i(t)?(r.event=t,r.code="Other_Error"):void 0!==t&&(r.code=t),r.timetag=+new Date,void 0!==n&&(r.event=n),r.event&&(r.callFunc=r.event.callFunc||null,delete r.event.callFunc)}a.prototype=r(Error.prototype),a.prototype.name="NIMError";var s={201:"客户端版本不对, 需升级sdk",302:"用户名或密码错误, 请检查appKey和token是否有效, account和token是否匹配",403:"非法操作或没有权限",404:"对象(用户/群/聊天室)不存在",405:"参数长度过长",408:"客户端请求超时",414:"参数错误",415:"服务不可用/没有聊天室服务器可分配",416:"频率控制",417:"重复操作",422:"帐号被禁用",500:"服务器内部错误",501:"数据库操作失败",503:"服务器繁忙",508:"删除有效期过了",509:"已失效",7101:"被拉黑",700:"批量操作部分失败",801:"群人数达到上限",802:"没有权限",803:"群不存在或未发生变化",804:"用户不在群里面",805:"群类型不匹配",806:"创建群数量达到限制",807:"群成员状态不对",809:"已经在群里",811:"强推列表中帐号数量超限",812:"群被禁言",813:"因群数量限制，部分拉人成功",814:"禁止使用群组消息已读服务",815:"群管理员人数上限",816:"批量操作部分失败",997:"协议已失效",998:"解包错误",999:"打包错误",9102:"通道失效",9103:"已经在其他端接听/拒绝过这通电话",11001:"对方离线, 通话不可送达",13002:"聊天室状态异常",13003:"在黑名单中",13004:"在禁言名单中",13006:"聊天室处于整体禁言状态,只有管理员能发言",Connect_Failed:"无法建立连接, 请确保能 ping/telnet 到云信服务器; 如果是IE8/9, 请确保项目部署在 HTTPS 环境下",Error_Internet_Disconnected:"网断了",Error_Connection_is_not_Established:"连接未建立",Error_Connection_Socket_State_not_Match:"socket状态不对",Error_Timeout:"超时",Param_Error:"参数错误",No_File_Selected:"请选择文件",Wrong_File_Type:"文件类型错误",File_Too_Large:"文件过大",Cross_Origin_Iframe:"不能获取跨域Iframe的内容",Not_Support:"不支持",NO_DB:"无数据库",DB:"数据库错误",Still_In_Team:"还在群里",Session_Exist:"会话已存在",Session_Not_Exist:"会话不存在",Error_Unknown:"未知错误",Operation_Canceled:"操作取消"},c=[200,406,808,810];o(c).call(c,(function(e){s[e]=null})),a.genError=function(e){var t=s[e];return void 0===t&&(t="操作失败"),null===t?null:new a(t,e)},a.multiInstance=function(e){return new a("不允许初始化多个实例","Not_Allow_Multi_Instance",e)},a.newNetworkError=function(e){var t="Error_Internet_Disconnected";return new a(s[t],t,e)},a.newConnectError=function(e){var t="Connect_Failed";return new a(s[t]||null,t,e)},a.newConnectionError=function(e){var t="Error_Connection_is_not_Established";return new a(s[t],t,e)},a.newSocketStateError=function(e){var t="Error_Connection_Socket_State_not_Match";return new a(s[t],t,e)},a.newTimeoutError=function(e){var t="Error_Timeout";return new a(s[t],t,e)},a.newFrequencyControlError=function(e){var t=new a(s[416],416,e);return t.from="local",t},a.newParamError=function(e,t){return new a(e||s.Param_Error,"Param_Error",t)},a.newNoFileError=function(e,t){var n="No_File_Selected";return new a(e||s[n],n,t)},a.newWrongFileTypeError=function(e,t){var n="Wrong_File_Type";return new a(e||s[n],n,t)},a.newFileTooLargeError=function(e,t){var n="File_Too_Large";return new a(e||s[n],n,t)},a.newCORSIframeError=function(e){var t="Cross_Origin_Iframe";return new a(s[t],t,e)},a.newSupportError=function(e,t,n){return new a("不支持"+e,"Not_Support_"+t,n)},a.newSupportDBError=function(e){return a.newSupportError("数据库","DB",e)},a.noDBError=function(e){return new a(s.NO_DB,"NO_DB",e)},a.newDBError=function(e){return new a(s.DB,"DB",e)},a.newUnknownError=function(e){var t="Error_Unknown";return new a(s[t],t,e)},a.stillInTeamError=function(e){var t="Still_In_Team";return new a(s[t],t,e)},a.sessionExist=function(e){var t="Session_Exist";return new a(s[t],t,e)},a.sessionNotExist=function(e){var t="Session_Not_Exist";return new a(s[t],t,e)},a.cancel=function(e){var t="Operation_Canceled";return new a(s[t],t,e)},a.customError=function(e,t){e=e||"Other_Error";var n="";return(t=t||{}).message||(n=s[e]||e),"object"!==i(e)?new a(n,e,t):new a(n,"Other_Error",void 0===t?e:t)},e.exports=a},function(e,t,n){var r=n(1),o=n(27),i=r.String,a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not an object")}},function(e,t,n){var r=n(95),o=n(84);e.exports=function(e){return r(o(e))}},function(e,t,n){var r=n(13),o=n(1),i=n(21),a=function(e){return i(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?a(r[e])||a(o[e]):r[e]&&r[e][t]||o[e]&&o[e][t]}},function(e,t,n){var r=n(1),o=n(84),i=r.Object;e.exports=function(e){return i(o(e))}},function(e,t,n){var r=n(230);e.exports=function(e){return r(e.length)}},function(e,t,n){var r=n(1),o=n(21),i=n(97),a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not a function")}},function(e,t,n){var r=n(1),o=n(24),i=n(159),a=n(161),s=n(34),c=n(96),u=r.TypeError,l=Object.defineProperty,p=Object.getOwnPropertyDescriptor;t.f=o?a?function(e,t,n){if(s(e),t=c(t),s(n),"function"==typeof e&&"prototype"===t&&"value"in n&&"writable"in n&&!n.writable){var r=p(e,t);r&&r.writable&&(e[t]=n.value,n={configurable:"configurable"in n?n.configurable:r.configurable,enumerable:"enumerable"in n?n.enumerable:r.enumerable,writable:!1})}return l(e,t,n)}:l:function(e,t,n){if(s(e),t=c(t),s(n),i)try{return l(e,t,n)}catch(e){}if("get"in n||"set"in n)throw u("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var r=n(0),o={init:function(){o.deviceId=r.guid()}};o.init(),o.clientTypeMap={1:"Android",2:"iOS",4:"PC",8:"WindowsPhone",16:"Web",32:"Server",64:"Mac"},o.db={open:function(){}},o.rnfs=null,e.exports=o},function(e,t,n){var r=n(1),o=n(47),i=r.String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return i(e)}},function(e,t,n){(function(t){var r=n(113),o=n(114),i=n(80),a=n(30),s=n(60),c=n(61),u=n(62),l=n(63),p=n(50),m=n(149),f=n(64),d=n(28),g=n(17),h=n(25),y=n(6),v=n(107),b=n(12),T=n(3),S=n(9),x=n(105),k=n(8),M=n(19);function w(e,t){var n=S(e);if(s){var r=s(e);t&&(r=M(r).call(r,(function(t){return c(e,t).enumerable}))),n.push.apply(n,r)}return n}function _(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?T(n=w(Object(o),!0)).call(n,(function(t){f(e,t,o[t])})):u?l(e,u(o)):T(r=w(Object(o))).call(r,(function(t){p(e,t,c(o,t))}))}return e}function C(e,t){var n=void 0!==o&&i(e)||e["@@iterator"];if(!n){if(a(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return P(e,t);var o=g(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return r(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return P(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var s=0,c=function(){};return{s:c,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:c}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,l=!0,p=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){p=!0,u=e},f:function(){try{l||null==n.return||n.return()}finally{if(p)throw u}}}}function P(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function I(){return"undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{}}function A(e,t){t=t||2;for(var n=""+e;n.length<t;)n="0"+n;return n}function E(e){return""+e.getFullYear()}function O(e){return A(e.getMonth()+1)}function j(e){return A(e.getDate())}function N(e){return A(e.getHours())}function L(e){return A(e.getMinutes())}function R(e){return A(e.getSeconds())}function D(e){return A(e.getMilliseconds(),3)}var F,U,B=(F=/yyyy|MM|dd|hh|mm|ss|SSS/g,U={yyyy:E,MM:O,dd:j,hh:N,mm:L,ss:R,SSS:D},function(e,t){return e=new Date(e),isNaN(+e)?"invalid date":(t=t||"yyyy-MM-dd").replace(F,(function(t){return U[t](e)}))});function q(e){var t;return g(t=Object.prototype.toString.call(e)).call(t,8,-1)}function H(e){return q(e).toLowerCase()}function z(e){return"string"===H(e)}function W(e){return"number"===H(e)}function G(e){return"array"===H(e)}function X(e){return"function"==typeof e}function K(e){return"date"===H(e)}function $(e){return null===e}function J(e){return null!==e}function V(e){return void 0===e}function Q(e){return void 0!==e}function Y(e){return Q(e)&&J(e)}function Z(e){return V(e)||$(e)}function ee(e){return Y(e)&&"object"===H(e)}var te=function(e,t,n){e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent&&e.attachEvent("on"+t,n)},ne=te,re=function(e,t,n){e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent&&e.detachEvent("on"+t,n)},oe=re;function ie(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},n=arguments.length>2?arguments[2]:void 0;for(var r in e)e.hasOwnProperty(r)&&t.call(n,r,e[r])}function ae(e,t){ie(t,(function(t,n){e[t]=n}))}var se,ce=(se=0,function(){return""+se++});function ue(e){return z(e)&&0===y(e).call(e,"{")&&v(e).call(e,"}")===e.length-1}function le(e,t,n){if(!e)return"";var r=[];return ie(e,(function(e,t){X(t)||(K(t)?t=t.getTime():G(t)?t=t.join(","):ee(t)&&(t=b(t)),n&&(t=encodeURIComponent(t)),r.push(encodeURIComponent(e)+"="+t))})),r.join(t||",")}var pe=function(){var e=/^([\w]+?:\/\/.*?(?=\/|$))/i;return function(t){return e.test(t||"")?RegExp.$1.toLowerCase():""}}();function me(e){var t=I();return e.tagName&&"INPUT"===e.tagName.toUpperCase()||t.Blob&&e instanceof t.Blob}e.exports={o:{},emptyObj:{},f:function(){},emptyFunc:function(){},regBlank:/\s+/gi,regWhiteSpace:/\s+/gi,getGlobal:I,detectCSSFeature:function(e){var t=!1,n="Webkit Moz ms O".split(" ");if("undefined"!=typeof document){var r=document.createElement("div"),o=null;if(e=e.toLowerCase(),void 0!==r.style[e]&&(t=!0),!1===t){o=e.charAt(0).toUpperCase()+e.substr(1);for(var i=0;i<n.length;i++)if(void 0!==r.style[n[i]+o]){t=!0;break}}return t}},fix:A,getYearStr:E,getMonthStr:O,getDayStr:j,getHourStr:N,getMinuteStr:L,getSecondStr:R,getMillisecondStr:D,format:B,dateFromDateTimeLocal:function(e){return e=""+e,new Date(e.replace(/-/g,"/").replace("T"," "))},getClass:q,typeOf:H,isString:z,isNumber:W,isInt:function(e){return W(e)&&e%1==0},isBoolean:function(e){return"boolean"===H(e)},isArray:G,isFunction:X,isDate:K,isRegExp:function(e){return"regexp"===H(e)},isError:function(e){return"error"===H(e)},isnull:$,notnull:J,undef:V,notundef:Q,exist:Y,notexist:Z,isObject:ee,isEmpty:function(e){return Z(e)||(z(e)||G(e))&&0===e.length},containsNode:function(e,t){if(e===t)return!0;for(;t.parentNode;){if(t.parentNode===e)return!0;t=t.parentNode}return!1},calcHeight:function(e){var t=e.parentNode||("undefined"==typeof document?null:document.body);if(!t)return 0;(e=e.cloneNode(!0)).style.display="block",e.style.opacity=0,e.style.height="auto",t.appendChild(e);var n=e.offsetHeight;return t.removeChild(e),n},remove:function(e){e.parentNode&&e.parentNode.removeChild(e)},dataset:function(e,t,n){if(!Y(n))return e.getAttribute("data-"+t);e.setAttribute("data-"+t,n)},addEventListener:te,on:ne,removeEventListener:re,off:oe,target:function(e){return e.target||e.srcElement},createIframe:function(e){if("undefined"!=typeof document){var t;if((e=e||{}).name)try{(t=document.createElement('<iframe name="'+e.name+'"></iframe>')).frameBorder=0}catch(n){(t=document.createElement("iframe")).name=e.name}else t=document.createElement("iframe");e.visible||(t.style.display="none"),X(e.onload)&&ne(t,"load",(function n(r){t.src&&(e.multi||oe(t,"load",n),e.onload(r))})),(e.parent||document.body).appendChild(t);var n=e.src||"about:blank";return h((function(){t.src=n}),0),t}},html2node:function(e){if("undefined"!=typeof document){var t=document.createElement("div");t.innerHTML=e;var n,r,o=[];if(t.children)for(n=0,r=t.children.length;n<r;n++)o.push(t.children[n]);else for(n=0,r=t.childNodes.length;n<r;n++){var i=t.childNodes[n];1===i.nodeType&&o.push(i)}return o.length>1?t:o[0]}},scrollTop:function(e){return"undefined"!=typeof document&&Y(e)&&(document.documentElement.scrollTop=document.body.scrollTop=e),window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},forOwn:ie,mixin:ae,uniqueID:ce,isJSON:ue,parseJSON:function e(t){try{ue(t)&&(t=JSON.parse(t)),ee(t)&&ie(t,(function(n,r){switch(H(r)){case"string":case"object":t[n]=e(r)}}))}catch(e){}return t},simpleClone:function(e){var t=[],n=b(e,(function(e,n){if("object"===d(n)&&null!==n){if(-1!==y(t).call(t,n))return;t.push(n)}return n}));return JSON.parse(n)},merge:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return T(n).call(n,(function(t){ae(e,t)})),e},fillUndef:function(e,t){return ie(t,(function(t,n){V(e[t])&&(e[t]=n)})),e},checkWithDefault:function(e,t,n){var r=e[t]||e[t.toLowerCase()];return Z(r)&&(r=n,e[t]=r),r},fetch:function(e,t){return ie(e,(function(n,r){Y(t[n])&&(e[n]=t[n])})),e},string2object:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",",r={};return T(e=t.split(n)).call(e,(function(e){var t=e.split("="),n=t.shift();n&&(r[decodeURIComponent(n)]=decodeURIComponent(t.join("=")))})),r},object2string:le,genUrlSep:function(e){return y(e).call(e,"?")<0?"?":"&"},object2query:function(e){return le(e,"&",!0)},url2origin:pe,isFileInput:me,getKeys:function(e,t){var n=S(e);return t&&x(n).call(n,(function(t,n){var r=me(e[t]);return r===me(e[n])?0:r?1:-1})),n},_get:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,o=t.replace(/\[(\d+)\]/g,".$1").split("."),i=e,a=C(o);try{for(a.s();!(n=a.n()).done;){var s=n.value;if(void 0===(i=Object(i)[s]))return r}}catch(e){a.e(e)}finally{a.f()}return i},getLastSeveralItems:function(e,t){for(var n=e.length,r=[],o=function(t){var n=e[t],o=k(n).call(n,(function(e){return _(_({},e),{},{flag:"sort_flag_".concat(t)})}));r.push.apply(r,m(o))},i=0;i<n;i++)o(i);x(r).call(r,(function(e,t){return e.startTime-t.startTime}));for(var a=[],s=function(e){var t;a[e]=k(t=M(r).call(r,(function(t){return t.flag==="sort_flag_".concat(e)}))).call(t,(function(e){return _(_({},e),{},{flag:void 0})}))},c=0;c<n;c++)s(c);return a}}}).call(this,n(49))},function(e,t,n){var r=n(28),o=n(14),i=n(3),a=n(381),s=n(6),c=n(4),u=n(385),l=n(0),p=l.notundef,m=n(196),f=n(15),d=n(54);function g(){}var h={};g.getInstance=function(e){e=v(e),l.verifyOptions(e,"account","api::Base.getInstance");var t=this.genInstanceName(e),n=h[t];return n?g.updateInstance(n,e):n=h[t]=new this(e),n},g.updateInstance=function(e,t){e.setOptions(t),e.connect()};var y=g.fn=g.prototype=o(new u),v=function(e){return e.nosSurvivalTime?(l.verifyParamType("nosSurvivalTime",e.nosSurvivalTime,"number","api::Base.getInstance"),l.verifyParamMin("nosSurvivalTime",e.nosSurvivalTime,86400,"api::Base.getInstance")):e.nosSurvivalTime=1/0,e};y.updatePrivateConf=function(e){if(e&&"object"===r(e.privateConf)){var t=e.privateConf;"string"==typeof t.lbs_web&&(e.lbsUrl=t.lbs_web),"boolean"==typeof t.link_ssl_web&&(e.secure=t.link_ssl_web),"boolean"==typeof t.https_enabled&&(e.httpsEnabled=t.https_enabled),e.uploadUrl=t.nos_uploader_web?t.nos_uploader_web:null,e.chunkUploadUrl=t.nos_uploader_web?t.nos_uploader_web:null,e.replaceUrl=t.nos_downloader?t.nos_downloader:null,e.downloadUrl=t.nos_accelerate?t.nos_accelerate:null,e.downloadHost=t.nos_accelerate_host?t.nos_accelerate_host:null,e.downloadHostList=t.nos_accelerate_host_list||[],e.downloadHost&&e.downloadHostList.push(e.downloadHost),e.nosCdnEnable=!1!==t.nos_cdn_enable,e.ntServerAddress=t.nt_server||null,e.kibanaServer=t.kibana_server,e.statisticServer=t.statistic_server,e.reportGlobalServer=t.report_global_server,e.ipVersion=t.ip_protocol_version,e.defaultLink=t.link_web||e.defaultLink,e.ipv6DefaultLink=t.link_ipv6_web||e.ipv6DefaultLink,"string"==typeof t.nos_lbs?e.nosLbsUrls=[t.nos_lbs]:e.nosLbsUrls=[]}return null===e.ntServerAddress||""===e.ntServerAddress?f.ntServerAddress=null:f.ntServerAddress=e.ntServerAddress||f.defaultReportUrl,f.uploadUrl=e.uploadUrl||f.uploadUrl,f.chunkUploadUrl=e.chunkUploadUrl||f.chunkUploadUrl,f.downloadUrl=e.downloadUrl||f.downloadUrl,f.downloadHost=e.downloadHost||f.downloadHost,f.downloadHostList=e.downloadHostList&&e.downloadHostList.length>0?e.downloadHostList:f.downloadHostList,f.nosCdnEnable=!1!==e.nosCdnEnable,f.replaceUrl=e.replaceUrl||f.replaceUrl,f.httpsEnabled=e.httpsEnabled||f.httpsEnabled,e.probe_ipv4_url&&(f.ipProbeAddr.ipv4=e.probe_ipv4_url),e.probe_ipv6_url&&(f.ipProbeAddr.ipv6=e.probe_ipv6_url),e},y.init=function(e){l.verifyOptions(e,"account","api::Base.init"),e=this.updatePrivateConf(e),l.verifyBooleanWithDefault(e,"exifOrientation",!0,"","api::Base.init"),e.lbsBackup=void 0===e.lbsBackup||e.lbsBackup;var t=this.account=e.account=e.account+"",n=e.constructor.genInstanceName(e),r=h[n];if(e._disableSingleton&&(r=null),r)return g.updateInstance(r,e),r;this.name=n,h[n]=this,this.logger=e.logger=new m({debug:e.debug,logFunc:e.logFunc,prefix:this.subType,dbLog:!1!==e.dbLog,account:e.account,expire:e.expire}),e.api=this;var o=this.protocol=new e.Protocol(e);return o.name="Protocol-"+n,o.account=t,o.api=this,o.message=this.message=new e.Message({account:t}),this.options=l.copy(e),this},y.destroy=function(e){var t=this,n=this;if(!n.__beginDestroy){var r;n.__beginDestroy=!0,e=e||{};var o,c=this.name;if(this.logger.warn("destroy::start"),!c)return this.logger&&this.logger.warn&&this.logger.warn("destroy::no instanceName"),void(n.__beginDestroy=void 0);if(this.protocol&&(r=this.protocol.connectTimer),this.protocol&&this.protocol.resetPush&&this.protocol.resetPush(),this.eventNames&&"function"==typeof this.eventNames)i(o=this.eventNames()).call(o,(function(e){delete t._events[e]}));this.disconnect({done:function(t){var o,u;n.logger.warn("ApiBase::destroy: instance destroyed ..."),n.__beginDestroy=void 0,d.destroy(),i(o=a(n.options)).call(o,(function(e){0===s(e).call(e,"on")&&(n.options[e]=function(){},n.protocol.options[e]=function(){},n.protocol.api.options[e]=function(){})})),n.logger.setLogDisabled(),i(u=a(n)).call(u,(function(e){n[e]=void 0})),h&&(h[c]=null,clearTimeout(r)),e.done instanceof Function&&e.done(t)}})}},y.setOptions=function(e){this.protocol.setOptions(e)},y.processCallback=function(e,t){b(e,t)},y.processCallbackPromise=function(e,t){return new c((function(n,r){b(e,t,!0,n,r)}))};var b=function(e,t,n,r,o){var i="api::processCallback";n&&(i="api::processCallbackPromise"),l.verifyCallback(e,"done",i),e.callback=function(a,s,c){var u=e.callback.options;if(s=s||u,t&&(s=u),l.isFunction(e.cbaop)){var m=e.cbaop(a,s);p(m)&&(s=m)}var f=e.done;l.isObject(s)&&(delete s.done,delete s.cb,delete s.callback),n?a?o({message:"生成接口回调错误",callFunc:i,event:a}):r(s):f(a,s,c)},e.callback.options=l.copy(e)};y.processPs=function(e){l.notexist(e.ps)&&(e.ps=""),l.verifyArrayMax("ps",e.ps,5e3)},y.processCustom=function(e){l.notexist(e.custom)&&(e.custom="")},y.sendCmd=function(){this.protocol.sendCmd.apply(this.protocol,arguments)},y.sendCmdWithResp=function(e,t,n){this.sendCmd(e,t,(function(e,t,r){l.isFunction(n)&&(e?n(e,t):n(null,r))}))},y.cbAndSendCmd=function(e,t){var n=this.processCallbackPromise(t);return this.sendCmd(e,t),n},y.sendCmdUsePromise=function(e,t){var n=this;return new c((function(r,o){n.sendCmd(e,t,(function(e,t,n){if(e)o(e);else{var i=l.merge({},t,n);r(i)}}))}))},g.use=function(e,t){e&&e.install&&l.isFunction(e.install)&&e.install(this,t)},g.rmAllInstances=function(){for(var e in h)h[e].destroy();h={}},y.logout=function(e){e=e||{},this.protocol.shouldReconnect=!1,this.protocol.doLogout=!0,this.processCallback(e),this.sendCmd("logout",e,e.callback)},e.exports=g,n(428),n(429),n(436),n(437),n(447),n(451),n(452)},function(e,t,n){var r=n(36);e.exports=r("navigator","userAgent")||""},function(e,t,n){var r=n(24),o=n(40),i=n(57);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var r=n(1),o=n(130),i=n(21),a=n(68),s=n(18)("toStringTag"),c=r.Object,u="Arguments"==a(function(){return arguments}());e.exports=o?a:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=c(e),s))?n:u?a(t):"Object"==(r=a(t))&&i(t.callee)?"Arguments":r}},function(e,t,n){e.exports=n(453)},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){e.exports=n(180)},function(e,t,n){e.exports=n(391)},,function(e,t,n){(function(t){var r=n(17),o=n(6),i=n(386),a=n(51),s=n(28);var c=function(){"use strict";var e="object"===(void 0===t?"undefined":s(t))?t:window,n=Math.pow(2,53)-1,c=/\bOpera/,u=Object.prototype,l=u.hasOwnProperty,p=u.toString;function m(e){return(e=String(e)).charAt(0).toUpperCase()+r(e).call(e,1)}function f(e){return e=v(e),/^(?:webOS|i(?:OS|P))/.test(e)?e:m(e)}function d(e,t){for(var n in e)l.call(e,n)&&t(e[n],n,e)}function g(e){var t;return null==e?m(e):r(t=p.call(e)).call(t,8,-1)}function h(e){return String(e).replace(/([ -])(?!$)/g,"$1?")}function y(e,t){var r=null;return function(e,t){var r=-1,o=e?e.length:0;if("number"==typeof o&&o>-1&&o<=n)for(;++r<o;)t(e[r],r,e);else d(e,t)}(e,(function(n,o){r=t(r,n,o,e)})),r}function v(e){return String(e).replace(/^ +| +$/g,"")}return function t(n){var u=e,l=n&&"object"===s(n)&&"String"!=g(n);l&&(u=n,n=null);var m=u.navigator||{},b=m.userAgent||"";n||(n=b);var T,S,x,k,M,w=l?!!m.likeChrome:/\bChrome\b/.test(n)&&!/internal|\n/i.test(p.toString()),_=l?"Object":"ScriptBridgingProxyObject",C=l?"Object":"Environment",P=l&&u.java?"JavaPackage":g(u.java),I=l?"Object":"RuntimeObject",A=/\bJava/.test(P)&&u.java,E=A&&g(u.environment)==C,O=A?"a":"α",j=A?"b":"β",N=u.document||{},L=u.operamini||u.opera,R=c.test(R=l&&L?L["[[Class]]"]:g(L))?R:L=null,D=n,F=[],U=null,B=n==b,q=B&&L&&"function"==typeof L.version&&L.version(),H=y([{label:"EdgeHTML",pattern:"Edge"},"Trident",{label:"WebKit",pattern:"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"],(function(e,t){return e||RegExp("\\b"+(t.pattern||h(t))+"\\b","i").exec(n)&&(t.label||t)})),z=function(e){return y(e,(function(e,t){return e||RegExp("\\b"+(t.pattern||h(t))+"\\b","i").exec(n)&&(t.label||t)}))}(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{label:"Microsoft Edge",pattern:"Edge"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{label:"Samsung Internet",pattern:"SamsungBrowser"},"SeaMonkey",{label:"Silk",pattern:"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{label:"SRWare Iron",pattern:"Iron"},"Sunrise","Swiftfox","Waterfox","WebPositive","Opera Mini",{label:"Opera Mini",pattern:"OPiOS"},"Opera",{label:"Opera",pattern:"OPR"},"Chrome",{label:"Chrome",pattern:"(?:HeadlessChrome)"},{label:"Chrome Mobile",pattern:"(?:CriOS|CrMo)"},{label:"Firefox",pattern:"(?:Firefox|Minefield)"},{label:"Firefox for iOS",pattern:"FxiOS"},{label:"IE",pattern:"IEMobile"},{label:"IE",pattern:"MSIE"},"Safari"]),W=K([{label:"BlackBerry",pattern:"BB10"},"BlackBerry",{label:"Galaxy S",pattern:"GT-I9000"},{label:"Galaxy S2",pattern:"GT-I9100"},{label:"Galaxy S3",pattern:"GT-I9300"},{label:"Galaxy S4",pattern:"GT-I9500"},{label:"Galaxy S5",pattern:"SM-G900"},{label:"Galaxy S6",pattern:"SM-G920"},{label:"Galaxy S6 Edge",pattern:"SM-G925"},{label:"Galaxy S7",pattern:"SM-G930"},{label:"Galaxy S7 Edge",pattern:"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{label:"Kindle Fire",pattern:"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{label:"Wii U",pattern:"WiiU"},"Wii","Xbox One",{label:"Xbox 360",pattern:"Xbox"},"Xoom"]),G=function(e){return y(e,(function(e,t,r){return e||(t[W]||t[/^[a-z]+(?: +[a-z]+\b)*/i.exec(W)]||RegExp("\\b"+h(r)+"(?:\\b|\\w*\\d)","i").exec(n))&&r}))}({Apple:{iPad:1,iPhone:1,iPod:1},Archos:{},Amazon:{Kindle:1,"Kindle Fire":1},Asus:{Transformer:1},"Barnes & Noble":{Nook:1},BlackBerry:{PlayBook:1},Google:{"Google TV":1,Nexus:1},HP:{TouchPad:1},HTC:{},LG:{},Microsoft:{Xbox:1,"Xbox One":1},Motorola:{Xoom:1},Nintendo:{"Wii U":1,Wii:1},Nokia:{Lumia:1},Samsung:{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},Sony:{PlayStation:1,"PlayStation Vita":1}}),X=function(e){return y(e,(function(e,t){var r=t.pattern||h(t);return!e&&(e=RegExp("\\b"+r+"(?:/[\\d.]+|[ \\w.]*)","i").exec(n))&&(e=function(e,t,n){var r={"10.0":"10",6.4:"10 Technical Preview",6.3:"8.1",6.2:"8",6.1:"Server 2008 R2 / 7","6.0":"Server 2008 / Vista",5.2:"Server 2003 / XP 64-bit",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"};return t&&n&&/^Win/i.test(e)&&!/^Windows Phone /i.test(e)&&(r=r[/[\d.]+$/.exec(e)])&&(e="Windows "+r),e=String(e),t&&n&&(e=e.replace(RegExp(t,"i"),n)),e=f(e.replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])}(e,r,t.label||t)),e}))}(["Windows Phone","Android","CentOS",{label:"Chrome OS",pattern:"CrOS"},"Debian","Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "]);function K(e){return y(e,(function(e,t){var r=t.pattern||h(t);return!e&&(e=RegExp("\\b"+r+" *\\d+[.\\w_]*","i").exec(n)||RegExp("\\b"+r+" *\\w+-[\\w]*","i").exec(n)||RegExp("\\b"+r+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(n))&&((e=String(t.label&&!RegExp(r,"i").test(t.label)?t.label:e).split("/"))[1]&&!/[\d.]+/.test(e[0])&&(e[0]+=" "+e[1]),t=t.label||t,e=f(e[0].replace(RegExp(r,"i"),t).replace(RegExp("; *(?:"+t+"[_-])?","i")," ").replace(RegExp("("+t+")[-_.]?(\\w)","i"),"$1 $2"))),e}))}if(H&&(H=[H]),G&&!W&&(W=K([G])),(T=/\bGoogle TV\b/.exec(W))&&(W=T[0]),/\bSimulator\b/i.test(n)&&(W=(W?W+" ":"")+"Simulator"),"Opera Mini"==z&&/\bOPiOS\b/.test(n)&&F.push("running in Turbo/Uncompressed mode"),"IE"==z&&/\blike iPhone OS\b/.test(n)?(G=(T=t(n.replace(/like iPhone OS/,""))).manufacturer,W=T.product):/^iP/.test(W)?(z||(z="Safari"),X="iOS"+((T=/ OS ([\d_]+)/i.exec(n))?" "+T[1].replace(/_/g,"."):"")):"Konqueror"!=z||/buntu/i.test(X)?G&&"Google"!=G&&(/Chrome/.test(z)&&!/\bMobile Safari\b/i.test(n)||/\bVita\b/.test(W))||/\bAndroid\b/.test(X)&&/^Chrome/.test(z)&&/\bVersion\//i.test(n)?(z="Android Browser",X=/\bAndroid\b/.test(X)?X:"Android"):"Silk"==z?(/\bMobi/i.test(n)||(X="Android",F.unshift("desktop mode")),/Accelerated *= *true/i.test(n)&&F.unshift("accelerated")):"PaleMoon"==z&&(T=/\bFirefox\/([\d.]+)\b/.exec(n))?F.push("identifying as Firefox "+T[1]):"Firefox"==z&&(T=/\b(Mobile|Tablet|TV)\b/i.exec(n))?(X||(X="Firefox OS"),W||(W=T[1])):!z||(T=!/\bMinefield\b/i.test(n)&&/\b(?:Firefox|Safari)\b/.exec(z))?(z&&!W&&/[\/,]|^[^(]+?\)/.test(r(n).call(n,o(n).call(n,T+"/")+8))&&(z=null),(T=W||G||X)&&(W||G||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(X))&&(z=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(X)?X:T)+" Browser")):"Electron"==z&&(T=(/\bChrome\/([\d.]+)\b/.exec(n)||0)[1])&&F.push("Chromium "+T):X="Kubuntu",q||(q=y(["(?:Cloud9|CriOS|CrMo|Edge|FxiOS|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$))","Version","HeadlessChrome",h(z),"(?:Firefox|Minefield|NetFront)"],(function(e,t){return e||(RegExp(t+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(n)||0)[1]||null}))),(T=("iCab"==H&&i(q)>3?"WebKit":/\bOpera\b/.test(z)&&(/\bOPR\b/.test(n)?"Blink":"Presto"))||/\b(?:Midori|Nook|Safari)\b/i.test(n)&&!/^(?:Trident|EdgeHTML)$/.test(H)&&"WebKit"||!H&&/\bMSIE\b/i.test(n)&&("Mac OS"==X?"Tasman":"Trident")||"WebKit"==H&&/\bPlayStation\b(?! Vita\b)/i.test(z)&&"NetFront")&&(H=[T]),"IE"==z&&(T=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(n)||0)[1])?(z+=" Mobile",X="Windows Phone "+(/\+$/.test(T)?T:T+".x"),F.unshift("desktop mode")):/\bWPDesktop\b/i.test(n)?(z="IE Mobile",X="Windows Phone 8.x",F.unshift("desktop mode"),q||(q=(/\brv:([\d.]+)/.exec(n)||0)[1])):"IE"!=z&&"Trident"==H&&(T=/\brv:([\d.]+)/.exec(n))&&(z&&F.push("identifying as "+z+(q?" "+q:"")),z="IE",q=T[1]),B){if(k="global",M=null!=(x=u)?s(x[k]):"number",/^(?:boolean|number|string|undefined)$/.test(M)||"object"==M&&!x[k])g(T=u.runtime)==_?(z="Adobe AIR",X=T.flash.system.Capabilities.os):g(T=u.phantom)==I?(z="PhantomJS",q=(T=T.version||null)&&T.major+"."+T.minor+"."+T.patch):"number"==typeof N.documentMode&&(T=/\bTrident\/(\d+)/i.exec(n))?(q=[q,N.documentMode],(T=+T[1]+4)!=q[1]&&(F.push("IE "+q[1]+" mode"),H&&(H[1]=""),q[1]=T),q="IE"==z?String(q[1].toFixed(1)):q[0]):"number"==typeof N.documentMode&&/^(?:Chrome|Firefox)\b/.test(z)&&(F.push("masking as "+z+" "+q),z="IE",q="11.0",H=["Trident"],X="Windows");else if(A&&(D=(T=A.lang.System).getProperty("os.arch"),X=X||T.getProperty("os.name")+" "+T.getProperty("os.version")),E){try{q=u.require("ringo/engine").version.join("."),z="RingoJS"}catch(e){(T=u.system)&&T.global.system==u.system&&(z="Narwhal",X||(X=T[0].os||null))}z||(z="Rhino")}else"object"===s(u.process)&&!u.process.browser&&(T=u.process)&&("object"===s(T.versions)&&("string"==typeof T.versions.electron?(F.push("Node "+T.versions.node),z="Electron",q=T.versions.electron):"string"==typeof T.versions.nw&&(F.push("Chromium "+q,"Node "+T.versions.node),z="NW.js",q=T.versions.nw)),z||(z="Node.js",D=T.arch,X=T.platform,q=(q=/[\d.]+/.exec(T.version))?q[0]:null));X=X&&f(X)}if(q&&(T=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(q)||/(?:alpha|beta)(?: ?\d)?/i.exec(n+";"+(B&&m.appMinorVersion))||/\bMinefield\b/i.test(n)&&"a")&&(U=/b/i.test(T)?"beta":"alpha",q=q.replace(RegExp(T+"\\+?$"),"")+("beta"==U?j:O)+(/\d+\+?/.exec(T)||"")),"Fennec"==z||"Firefox"==z&&/\b(?:Android|Firefox OS)\b/.test(X))z="Firefox Mobile";else if("Maxthon"==z&&q)q=q.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(W))"Xbox 360"==W&&(X=null),"Xbox 360"==W&&/\bIEMobile\b/.test(n)&&F.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(z)&&(!z||W||/Browser|Mobi/.test(z))||"Windows CE"!=X&&!/Mobi/i.test(n))if("IE"==z&&B)try{null===u.external&&F.unshift("platform preview")}catch(e){F.unshift("embedded")}else(/\bBlackBerry\b/.test(W)||/\bBB10\b/.test(n))&&(T=(RegExp(W.replace(/ +/g," *")+"/([.\\d]+)","i").exec(n)||0)[1]||q)?(X=((T=[T,/BB10/.test(n)])[1]?(W=null,G="BlackBerry"):"Device Software")+" "+T[0],q=null):this!=d&&"Wii"!=W&&(B&&L||/Opera/.test(z)&&/\b(?:MSIE|Firefox)\b/i.test(n)||"Firefox"==z&&/\bOS X (?:\d+\.){2,}/.test(X)||"IE"==z&&(X&&!/^Win/.test(X)&&q>5.5||/\bWindows XP\b/.test(X)&&q>8||8==q&&!/\bTrident\b/.test(n)))&&!c.test(T=t.call(d,n.replace(c,"")+";"))&&T.name&&(T="ing as "+T.name+((T=T.version)?" "+T:""),c.test(z)?(/\bIE\b/.test(T)&&"Mac OS"==X&&(X=null),T="identify"+T):(T="mask"+T,z=R?f(R.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(T)&&(X=null),B||(q=null)),H=["Presto"],F.push(T));else z+=" Mobile";if(T=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(n)||0)[1]){var $,J;if(T=[i(T.replace(/\.(\d)$/,".0$1")),T],"Safari"==z&&"+"==r($=T[1]).call($,-1))z="WebKit Nightly",U="alpha",q=r(J=T[1]).call(J,0,-1);else q!=T[1]&&q!=(T[2]=(/\bSafari\/([\d.]+\+?)/i.exec(n)||0)[1])||(q=null);T[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(n)||0)[1],537.36==T[0]&&537.36==T[2]&&i(T[1])>=28&&"WebKit"==H&&(H=["Blink"]),B&&(w||T[1])?(H&&(H[1]="like Chrome"),T=T[1]||((T=T[0])<530?1:T<532?2:T<532.05?3:T<533?4:T<534.03?5:T<534.07?6:T<534.1?7:T<534.13?8:T<534.16?9:T<534.24?10:T<534.3?11:T<535.01?12:T<535.02?"13+":T<535.07?15:T<535.11?16:T<535.19?17:T<536.05?18:T<536.1?19:T<537.01?20:T<537.11?"21+":T<537.13?23:T<537.18?24:T<537.24?25:T<537.36?26:"Blink"!=H?"27":"28")):(H&&(H[1]="like Safari"),T=(T=T[0])<400?1:T<500?2:T<526?3:T<533?4:T<534?"4+":T<535?5:T<537?6:T<538?7:T<601?8:"8"),H&&(H[1]+=" "+(T+="number"==typeof T?".x":/[.+]/.test(T)?"":"+")),"Safari"==z&&(!q||a(q)>45)&&(q=T)}"Opera"==z&&(T=/\bzbov|zvav$/.exec(X))?(z+=" ",F.unshift("desktop mode"),"zvav"==T?(z+="Mini",q=null):z+="Mobile",X=X.replace(RegExp(" *"+T+"$"),"")):"Safari"==z&&/\bChrome\b/.exec(H&&H[1])&&(F.unshift("desktop mode"),z="Chrome Mobile",q=null,/\bOS X\b/.test(X)?(G="Apple",X="iOS 4.3+"):X=null),q&&0==o(q).call(q,T=/[\d.]+$/.exec(X))&&o(n).call(n,"/"+T+"-")>-1&&(X=v(X.replace(T,""))),H&&!/\b(?:Avant|Nook)\b/.test(z)&&(/Browser|Lunascape|Maxthon/.test(z)||"Safari"!=z&&/^iOS/.test(X)&&/\bSafari\b/.test(H[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|Web)/.test(z)&&H[1])&&(T=H[H.length-1])&&F.push(T),F.length&&(F=["("+F.join("; ")+")"]),G&&W&&o(W).call(W,G)<0&&F.push("on "+G),W&&F.push((/^on /.test(F[F.length-1])?"":"on ")+W),X&&(T=/ ([\d.+]+)$/.exec(X),S=T&&"/"==X.charAt(X.length-T[0].length-1),X={architecture:32,family:T&&!S?X.replace(T[0],""):X,version:T?T[1]:null,toString:function(){var e=this.version;return this.family+(e&&!S?" "+e:"")+(64==this.architecture?" 64-bit":"")}}),(T=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(D))&&!/\bi686\b/i.test(D)?(X&&(X.architecture=64,X.family=X.family.replace(RegExp(" *"+T),"")),z&&(/\bWOW64\b/i.test(n)||B&&/\w(?:86|32)$/.test(m.cpuClass||m.platform)&&!/\bWin64; x64\b/i.test(n))&&F.unshift("32-bit")):X&&/^OS X/.test(X.family)&&"Chrome"==z&&i(q)>=39&&(X.architecture=64),n||(n=null);var V={};return V.description=n,V.layout=H&&H[0],V.manufacturer=G,V.name=z,V.prerelease=U,V.product=W,V.ua=n,V.version=z&&q,V.os=X||{architecture:null,family:null,version:null,toString:function(){return"null"}},V.parse=t,V.toString=function(){return this.description||""},V.version&&F.unshift(q),V.name&&F.unshift(z),X&&z&&(X!=String(X).split(" ")[0]||X!=z.split(" ")[0]&&!W)&&F.push(W?"("+X+")":"on "+X),F.length&&(V.description=F.join(" ")),V}()}();e.exports=c}).call(this,n(49))},function(e,t,n){var r,o=n(149),i=n(150),a=n(120),s=n(3),c=n(9),u=n(12),l=n(10),p=n(94),m=n(30),f=n(119),d=n(19),g=n(23),h=["operation_type","code","target"],y=["operation_type","code","target"],v=n(55),b=n(53),T=n(0),S=n(41),x=n(15),k=n(43),M="https://statistic.live.126.net/statics/report/common/form",w="nimErrEvent",_={sdktype:"IM",platform:"Web",deviceId:S.deviceId,sdk_ver:x.info.version,manufactor:b.manufacturer,env:"online"},C={login:"nimLoginErrEvent",nos:"nimNosErrEvent"},P={login:null,nos:null},I={maxInterval:30,maxSize:100,minInterval:10,maxDelay:100,turnOn:!1},A=null,E=!1,O={reportErrEventUrl:M,localKey:w,reportErrEvent:function(e){try{var t,n=localStorage.getItem(w);if(!n)return;n=JSON.parse(n);var r=[];s(t=c(n)).call(t,(function(e){r.push(n[e])}));var o={app_key:e.appKey,sdk_ver:e.sdk_ver,platform:"Web",os_ver:b.os.family+" "+b.os.version,manufacturer:b.manufacturer,model:b.name};v(M,{method:"POST",timeout:2e3,headers:{"Content-Type":"application/json"},data:u({common:{device_id:e.deviceId,sdk_type:"IM"},event:{logReport:r,deviceinfo:o}}),onload:function(){localStorage.removeItem(w)},onerror:function(e){}})}catch(e){}},saveErrEvent:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e.code&&e.module)try{var t=localStorage.getItem(w)||"{}";t=JSON.parse(t);var n=e.code+e.module+e.accid;t[n]?t[n].count++:t[n]={errorCode:e.code,module:e.module,accid:e.accid,timestamp:(new Date).getTime(),count:1},localStorage.setItem(w,u(t))}catch(e){}},initUniErrReport:function(){var e,t,n,r,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};_.appkey=o.appKey,v(l(e=l(t=l(n=l(r="".concat("https://statistic.live.126.net","/dispatcher/req?deviceId=").concat(_.deviceId,"&sdktype=")).call(r,_.sdktype,"&sdkVer=")).call(n,_.sdk_ver,"&platform=")).call(t,_.platform,"&appkey=")).call(e,_.appkey),{method:"get",onload:function(e){var t=null;try{t=JSON.parse(e)}catch(e){}t&&t.code&&200===t.code&&((I=t.data).maxInterval=I.maxInterval>1e4?1e4:I.maxInterval,I.maxInterval=I.maxInterval<10?10:I.maxInterval,I.maxSize=I.maxSize>1e3?1e3:I.maxSize,I.minInterval=I.minInterval<2?2:I.minInterval,I.maxDelay=I.maxDelay>600?600:I.maxDelay,200===t.code&&(I.turnOn=!0,A=p((function(){O.checkUniErrCache(!0)}),1e3*I.maxInterval)))},onerror:function(e){}})},startUniErrCache:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(P.hasOwnProperty(e)&&!P[e]&&t.user_id&&t.action){var n=(new Date).valueOf();P[e]={user_id:t.user_id,action:t.action,start_time:n,extension:[]}}},updateUniErrCache:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(P.hasOwnProperty(e)&&P[e]&&P[e].extension&&m(P[e].extension)){var r=n.operation_type,o=n.code,i=n.target,s=a(n,h),c=f(t=P[e].extension).call(t,(function(e,t){return e+t.duration}),0),l=(new Date).getTime()-c-P[e].start_time;n.error&&n.error.code&&(o=n.error.code),P[e].extension.push({operation_type:r,code:o,succeed:!1,target:i,duration:l,description:u(s)})}},updateUniSuccCache:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(P.hasOwnProperty(e)&&P[e]&&P[e].extension&&m(P[e].extension)){var r=n.operation_type,o=(n.code,n.target),i=a(n,y),s=f(t=P[e].extension).call(t,(function(e,t){return e+t.duration}),0),c=(new Date).getTime()-s-P[e].start_time;P[e].extension.push({operation_type:r,code:200,succeed:!0,target:o,duration:c,description:u(i)})}},concludeUniErrCache:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(P.hasOwnProperty(e)&&P[e])if("nos"!==e||0!==t){var n=(new Date).valueOf();P[e].succeed=!t,P[e].duration=n-P[e].start_time;var r=T.copy(P[e]);P[e]=null;try{var o=localStorage.getItem(C[e])||"[]",i=JSON.parse(o);i.push(r),localStorage.setItem(C[e],u(i)),I.turnOn&&O.checkUniErrCache()}catch(e){}}else P[e]=null},checkUniErrCache:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!E){var t=(new Date).valueOf();try{var n=localStorage.getItem(C.login)||"[]",r=localStorage.getItem(C.nos)||"[]",a=JSON.parse(n),s=JSON.parse(r),c=d(a).call(a,(function(e){return t-e.start_time<1e3*I.maxDelay})),l=d(s).call(s,(function(e){return t-e.start_time<1e3*I.maxDelay})),m=k.getLastSeveralItems([c,l],I.maxSize),f=i(m,2),g=f[0],h=f[1];if(g.length+h.length===I.maxSize||e){if(g.length+h.length===0)return;var y=_.appkey,b=_.platform,T=_.sdk_ver,S=_.manufactor,x=_.env,w={common:{app_key:y,platform:b,sdk_ver:T,manufactor:S,env:x},event:{login:o(g),nos:o(h)}};v(M,{method:"POST",timeout:2e3,headers:{"Content-Type":"application/json",sdktype:_.sdktype},data:u(w),onload:function(){for(var e in C)if(Object.hasOwnProperty.call(C,e)){var t=C[e];localStorage.setItem(t,"[]")}clearInterval(A),A=p((function(){O.checkUniErrCache(!0)}),1e3*I.maxInterval),E=!1},onerror:function(){E=!1}}),E=!0}}catch(e){E=!1}}},pause:function(){_.turnOn=!1},restore:function(){_.turnOn=!0},destroy:function(){A&&clearInterval(A),O.concludeUniErrCache("login",1),O.concludeUniErrCache("nos",1),_.turnOn=!1,E=!1}};O.sendBeacon=navigator&&navigator.sendBeacon&&g(r=navigator.sendBeacon).call(r,navigator)||function(e,t){var n=new XMLHttpRequest;n.open("POST",e,!0),n.send(t)},e.exports=O},function(e,t,n){var r=n(110),o=n(425),i=n(426),a=n(427);r.json=o,r.upload=i,r.chunkUpload=a,e.exports=r},function(e,t,n){var r=n(83),o=Function.prototype,i=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(i):function(){return a.apply(i,arguments)})},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t){e.exports=!0},function(e,t,n){n(76);var r=n(262),o=n(1),i=n(47),a=n(46),s=n(75),c=n(18)("toStringTag");for(var u in r){var l=o[u],p=l&&l.prototype;p&&i(p)!==c&&a(p,c,u),s[u]=s.Array}},function(e,t,n){e.exports=n(178)},function(e,t,n){e.exports=n(268)},function(e,t,n){e.exports=n(271)},function(e,t,n){e.exports=n(274)},function(e,t,n){var r=n(207);e.exports=function(e,t,n){return t in e?r(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(78),o=n(30),i=n(9),a=n(6),s=n(77),c=n(3),u=n(12),l=n(25),p=n(152),m=n(10),f=n(23),d=n(0),g=n(15),h=n(54),y=n(33);function v(e){d.undef(e.secure)&&(e.secure=!0),this.options=d.copy(e),this.keepNosSafeUrl=this.options.keepNosSafeUrl||!1;var t=e.defaultLink||e.defaultLinkUrl;d.notundef(t)&&d.isString(t)&&(g.optionDefaultLinkUrl=r(t).call(t)),d.notundef(e.ipv6DefaultLink)&&d.isString(e.ipv6DefaultLink)&&(g.optionIpv6DefaultLinkUrl=e.ipv6DefaultLink),"number"==typeof e.heartbeatInterval&&(g.heartbeatInterval=e.heartbeatInterval),o(e.nosLbsUrls)&&(g.nosLbsUrls=e.nosLbsUrls),this.init(),this.connect()}var b=v.fn=v.prototype;b.init=function(){this.logger=this.options.logger,this.getNosOriginUrlReqNum=0,this.checkNosReqNum=0,this.cmdTaskArray=[],this.timerMap={},this.cmdCallbackMap={},this.cmdContentMap={},this.initConnect(),this.reset()},b.reset=function(){this.resetConnect()},b.setOptions=function(e){var t=this.options,n=i(t),r=a(n).call(n,"account");this.logger.info("setOptions::",d.omitAsString(e,["appKey","token","loginExt","customTag"])),-1!==r&&s(n).call(n,r,1),e=d.filterObj(e,n),this.options=d.merge(t,e),this.reset()},b.sendCmd=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=this;r.heartbeat();var o,a=e,s=(e=r.parser.createCmd(e,t)).SER;t=t||{},r.cmdContentMap[s]=t,t.single&&(delete t.single,1===(o=i(t)).length&&(r.cmdContentMap[s]=t[o[0]])),t.NOTSTORE&&(o=t.NOTSTORE.split(" "),c(o).call(o,(function(e){delete t[e]})),delete t.NOTSTORE),(n=n||t.callback)&&(r.cmdCallbackMap[s]=n),r.cmdTaskArray.push({cmdName:a,cmd:u(e)}),r.startCmdTaskTimer()},b.startCmdTaskTimer=function(){var e=this;e.cmdTaskTimer||(e.cmdTaskTimer=l((function(){var t=e.cmdTaskArray.shift();e.cmdTaskTimer=null,t&&e.executeCmdTask(t),e.cmdTaskArray.length&&e.startCmdTaskTimer()}),0))},b.executeCmdTask=function(e){var t=e.cmdName,n=e.cmd,r=(n=JSON.parse(n)).SER;this.isFrequencyControlled(t)?(this.logger.warn("protocol::executeCmdTask: ".concat(t," hit freq control")),this.markCallbackInvalid(r,y.newFrequencyControlError({callFunc:"protocol::executeCmdTask",message:"".concat(t," hit freq control")}))):this.hasLogin?("heartbeat"!==t&&this.logger.log("protocol::sendCmd: ".concat(t)),this.doSendCmd(n)):"login"===t&&this.isConnected()?(this.logger.info("protocol::sendCmd: ".concat(t)),this.doSendCmd(n)):(this.logger.warn("protocol::executeCmdTask: ".concat(t," not connected or login")),this.markCallbackInvalid(r,y.newSocketStateError({callFunc:"protocol::executeCmdTask",message:"".concat(t," not connected or not login")})))},b.isFrequencyControlled=function(e){var t=this.frequencyControlMap&&this.frequencyControlMap[e];if(t){if(p()<t.from+t.duration)return!0;delete this.frequencyControlMap[e]}},b.doSendCmd=function(e){var t=this,n=e.SER,r=0===n?g.hbCmdTimeout:g.cmdTimeout;function o(){t.markCallbackInvalid(n,y.newSocketStateError({callFunc:"protocol::doSendCmd",message:"ser ".concat(n," socketSendJson Error")})),t.onDisconnect("protocol::doSendCmd:socketSendJson")}t.timerMap[n]=l((function(){t.markCallbackInvalid(n,y.newTimeoutError({callFunc:"protocol::doSendCmd",message:"ser ".concat(n," Timeout Error")}))}),r);try{t.socket&&t.socket.send?t.socket.send(u(e)):o()}catch(e){o()}},b.getObjWithSer=function(e){var t=this.cmdContentMap[e];return t&&!t.isImSyncDataObj&&delete this.cmdContentMap[e],t&&d.copy(t)},b.getCallbackWithSer=function(e){var t=this.cmdCallbackMap[e];return t&&!t.isImSyncDataCb&&delete this.cmdCallbackMap[e],t},b.getTimerWithSer=function(e){var t=this.timerMap[e];return delete this.timerMap[e],t},b.clearTimerWithSer=function(e){var t=this.getTimerWithSer(e);t&&clearTimeout(t)},b.markCallbackInvalid=function(e,t){this.getObjWithSer(e),this.clearTimerWithSer(e);var n=this.getCallbackWithSer(e);if(n){var r=n.options;l((function(){n(t||y.newUnknownError({ser:e}),r)}),0)}},b.markAllCallbackInvalid=function(e){var t,n=this;c(t=i(this.cmdCallbackMap)).call(t,(function(t){n.markCallbackInvalid(t,e)})),n.cmdTaskArray=[]},b.getPacketObj=function(e){var t=null;if(e&&e.raw){var n=e.raw.ser;d.notundef(n)&&(t=this.getObjWithSer(n))}return t},b.callPacketAckCallback=function(e){var t=this;if(e&&e.raw){var n=e.raw.ser;if(d.notundef(n)){t.clearTimerWithSer(n);var r=t.getCallbackWithSer(n);if(r){var o;if(r.originUrl&&e.obj&&e.obj.file)if(e.obj.file._url_safe=e.obj.file.url,e.obj.file.url=r.originUrl,"audio"===e.obj.type)e.obj.file.mp3Url=r.originUrl+(~a(o=r.originUrl).call(o,"?")?"&":"?")+"audioTrans&type=mp3";e.promise?e.promise.then((function(){r(e.error,e.obj)}),(function(o){var i;o.callFunc="protocol::callPacketAckCallback",o.message=m(i="Resp Promoise Error: cmd: ".concat(e.cmd,", ser: ")).call(i,n);var a=y.customError("CALLBACK_ACK_ERR",o);t.logger.error("protocol::callPacketAckCallback: promise error ".concat(u(o))),r(a,e.obj,e.content)})):r(e.error,e.obj,e.content)}}}},b.onMessage=function(e){var t=this;t.heartbeat(),t.parser.parseResponse(e).then((function(e){if(e.notFound&&t.logger.warn("protocol::onMessage: packet not found ".concat(u(e))),e.error){var n,r;e.error.message=m(n="".concat(e.cmd," error: ")).call(n,e.error.message),t.logger.error(m(r="protocol::onMessage: packet error ".concat(u(e.error),"，raw cmd ")).call(r,e.rawStr));var o=e.raw||{};408!==o.code&&415!==o.code&&500!==o.code||h.saveErrEvent({code:o.code,module:e.cmd,accid:t.account})}e.frequencyControlDuration&&(t.logger.error("protocol::onMessage: server freq control ".concat(u(e.cmd))),t.frequencyControlMap=t.frequencyControlMap||{},t.frequencyControlMap[e.cmd]={from:+new Date,duration:e.frequencyControlDuration}),e.obj=t.getPacketObj(e),"heartbeat"!==e.cmd&&t.logger.log("protocol::recvCmd: ".concat(e.cmd));var i="process"+d.capFirstLetter(e.service);if(t[i])if("syncDone"===e.cmd){if(t.cmdCallbackMap[e.raw.ser]&&t.cmdCallbackMap[e.raw.ser].isImSyncDataCb){var a;t.cmdCallbackMap[e.raw.ser].isImSyncDataCb=!1;var s=f(a=function(e,t){this.checkNosReqNum++,this.getNosOriginUrlReqNum<=0||this.checkNosReqNum>=20?this[e](t):l(s,300)}).call(a,t,i,e);l((function(){s.call(t,i,e)}),10)}}else t[i](e);else t.logger.warn("protocol::onMessage: ".concat(i," not found"));t.callPacketAckCallback(e)}))},b.onMiscError=function(e,t,n){t&&this.notifyError(e,t,n)},b.onCustomError=function(e,t){var n=y.customError(e,t),r=t.message||"未知错误";this.onMiscError(r,n)},b.notifyError=function(e,t,n){var r;this.isConnected()&&(this.logger.error(m(r="".concat(e||""," ")).call(r,this.name),t,n),this.options.onerror(t,n))},b.emitAPI=function(e){var t=e.type,n=e.obj;this.api.emit(t,n)},e.exports=v,n(460),n(463),n(464),n(465),n(466)},function(e,t,n){(function(t){var r;e.exports=(r=r||function(e,r){var o;if("undefined"!=typeof window&&window.crypto&&(o=window.crypto),"undefined"!=typeof self&&self.crypto&&(o=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(o=globalThis.crypto),!o&&"undefined"!=typeof window&&window.msCrypto&&(o=window.msCrypto),!o&&void 0!==t&&t.crypto&&(o=t.crypto),!o)try{o=n(592)}catch(e){}var i=function(){if(o){if("function"==typeof o.getRandomValues)try{return o.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof o.randomBytes)try{return o.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},a=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},c=s.lib={},u=c.Base={extend:function(e){var t=a(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=c.WordArray=u.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||m).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var a=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(var s=0;s<o;s+=4)t[r+s>>>2]=n[s>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=u.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(i());return new l.init(t,e)}}),p=s.enc={},m=p.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new l.init(n,t/2)}},f=p.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new l.init(n,t)}},d=p.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},g=c.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,o=r.words,i=r.sigBytes,a=this.blockSize,s=i/(4*a),c=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,u=e.min(4*c,i);if(c){for(var p=0;p<c;p+=a)this._doProcessBlock(o,p);n=o.splice(0,c),r.sigBytes-=u}return new l.init(n,u)},clone:function(){var e=u.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),h=(c.Hasher=g.extend({cfg:u.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){g.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}}),s.algo={});return s}(Math),r)}).call(this,n(49))},function(e,t,n){var r=n(24),o=n(31),i=n(112),a=n(57),s=n(35),c=n(96),u=n(26),l=n(159),p=Object.getOwnPropertyDescriptor;t.f=r?p:function(e,t){if(e=s(e),t=c(t),l)try{return p(e,t)}catch(e){}if(u(e,t))return a(!o(i.f,e,t),e[t])}},function(e,t,n){var r=n(7),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},function(e,t,n){var r,o,i=n(1),a=n(45),s=i.process,c=i.Deno,u=s&&s.versions||c&&c.version,l=u&&u.v8;l&&(o=(r=l.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(o=+r[1]),e.exports=o},function(e,t,n){var r=n(7),o=n(39),i=n(83),a=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?a(e,t):function(){return e.apply(t,arguments)}}},function(e,t,n){"use strict";var r=n(11);e.exports=function(e,t){var n=[][e];return!!n&&r((function(){n.call(null,t||function(){return 1},1)}))}},function(e,t,n){var r=n(68);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){"use strict";var r=n(96),o=n(40),i=n(57);e.exports=function(e,t,n){var a=r(t);a in e?o.f(e,a,i(0,n)):e[a]=n}},function(e,t,n){var r=n(46);e.exports=function(e,t,n,o){o&&o.enumerable?e[t]=n:r(e,t,n)}},function(e,t){e.exports={}},function(e,t,n){"use strict";var r=n(35),o=n(147),i=n(75),a=n(101),s=n(40).f,c=n(164),u=n(58),l=n(24),p=a.set,m=a.getterFor("Array Iterator");e.exports=c(Array,"Array",(function(e,t){p(this,{type:"Array Iterator",target:r(e),index:0,kind:t})}),(function(){var e=m(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");var f=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!u&&l&&"values"!==f.name)try{s(f,"name",{value:"values"})}catch(e){}},function(e,t,n){e.exports=n(295)},function(e,t,n){e.exports=n(419)},function(e,t,n){var r=n(70),o=n(7),i=n(95),a=n(37),s=n(38),c=n(129),u=o([].push),l=function(e){var t=1==e,n=2==e,o=3==e,l=4==e,p=6==e,m=7==e,f=5==e||p;return function(d,g,h,y){for(var v,b,T=a(d),S=i(T),x=r(g,h),k=s(S),M=0,w=y||c,_=t?w(d,k):n||m?w(d,0):void 0;k>M;M++)if((f||M in S)&&(b=x(v=S[M],M,T),e))if(t)_[M]=b;else if(b)switch(e){case 3:return!0;case 5:return v;case 6:return M;case 2:u(_,v)}else switch(e){case 4:return!1;case 7:u(_,v)}return p?-1:o||l?l:_}};e.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterReject:l(7)}},function(e,t,n){e.exports=n(263)},,function(e,t,n){var r=n(113),o=n(114),i=n(80),a=n(30),s=n(6),c=n(17),u=n(3),l=n(10),p=n(213),m=n(19);function f(e,t){var n=void 0!==o&&i(e)||e["@@iterator"];if(!n){if(a(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return d(e,t);var o=c(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return r(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return d(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var s=0,u=function(){};return{s:u,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,p=!0,m=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return p=e.done,e},e:function(e){m=!0,l=e},f:function(){try{p||null==n.return||n.return()}finally{if(m)throw l}}}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g=n(15),h={genUrlSep:function(e){return-1===s(e=""+e).call(e,"?")?"?imageView&":"&"},urlQuery2Object:function(e){if("[object String]"!==Object.prototype.toString.call(e)||""===e)return{};var t=s(e).call(e,"?");if(-1!==t){var n=c(e).call(e,t+1).split("&"),r={};return u(n).call(n,(function(e){if(~s(e).call(e,"=")){var t=e.split("=");r[t[0]]=decodeURIComponent(t[1])}else r[e]=""})),r}},url2object:function(e){"[object String]"!==Object.prototype.toString.call(e)&&(e="");var t=s(e=e||"").call(e,"https")>=0?"https://":"http://",n=e.replace(t,"");s(n).call(n,"?")>=0&&(n=n.substring(0,s(n).call(n,"?")));var r=n.split("/");n=r[0];var o="";if(r.length>0&&(o=c(r).call(r,1).join("/")),-1===s(e).call(e,"?"))return{protocol:t,hostname:n,path:o,query:{}};var i=e.substr(s(e).call(e,"?")+1).split("&"),a={};return u(i).call(i,(function(e){if(s(e).call(e,"=")>0){var t=e.split("=");a[t[0]]=decodeURIComponent(t[1])}else a[e]=""})),{protocol:t,hostname:n,path:o,query:a}},object2url:function(e){var t,n=e.protocol,r=e.hostname,o=e.path,i=e.query;(n=n||"http://",r=r||"",o)&&(r=l(t="".concat(r,"/")).call(t,o));i=i||{};var a,s,c,u=[];for(var p in i){var m;if("imageView"!==p)u.push(l(m="".concat(p,"=")).call(m,encodeURIComponent(i[p])))}return u.length>0?l(a=l(s="".concat(n)).call(s,r,"?imageView&")).call(a,u.join("&")):l(c="".concat(n)).call(c,r)},genPrivateUrl:function(e,t,n){if(t&&t.length){var r,o=f(t);try{for(o.s();!(r=o.n()).done;){var i=r.value;if(s(e).call(e,i)>=0)return i!==n&&(e=e.replace(i,n)),e;if(s(e).call(e,i)>=0&&i===n)return e}}catch(e){o.e(e)}finally{o.f()}}var a,u,d=h.url2object(e),y=d.hostname,v=d.path,b=g.downloadUrl,T=g.downloadHostList,S=g.nosCdnEnable,x=g.serverNosConfig.cdnDomain,k=decodeURIComponent(g.serverNosConfig.objectPrefix),M=decodeURIComponent(v),w=s(M).call(M,k);if(x&&w>-1&&S)return l(a=l(u="".concat(d.protocol)).call(u,x,"/")).call(a,c(M).call(M,w));if(p(T).call(T,y)&&p(v).call(v,"/")){var _=s(v).call(v,"/"),C=v.substring(0,_),P=v.substring(_+1);return b.replace("{bucket}",C).replace("{object}",P)}var I=m(T).call(T,(function(e){return"string"==typeof y&&p(y).call(y,e)}))[0],A=I?y.replace(I,"").replace(/\W/g,""):null;return A?b.replace("{bucket}",A).replace("{object}",v):e}};e.exports=h},function(e,t,n){var r=n(11);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},function(e,t,n){var r=n(1).TypeError;e.exports=function(e){if(null==e)throw r("Can't call method on "+e);return e}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){var t=+e;return t!=t||0===t?0:(t>0?r:n)(t)}},function(e,t,n){var r=n(11),o=n(18),i=n(69),a=o("species");e.exports=function(e){return i>=51||!r((function(){var t=[];return(t.constructor={})[a]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},function(e,t,n){var r,o=n(34),i=n(132),a=n(133),s=n(103),c=n(168),u=n(127),l=n(102),p=l("IE_PROTO"),m=function(){},f=function(e){return"<script>"+e+"<\/script>"},d=function(e){e.write(f("")),e.close();var t=e.parentWindow.Object;return e=null,t},g=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}var e,t;g="undefined"!=typeof document?document.domain&&r?d(r):((t=u("iframe")).style.display="none",c.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(f("document.F=Object")),e.close(),e.F):d(r);for(var n=a.length;n--;)delete g.prototype[a[n]];return g()};s[p]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(m.prototype=o(e),n=new m,m.prototype=null,n[p]=e):n=g(),void 0===t?n:i.f(n,t)}},function(e,t,n){var r=n(130),o=n(40).f,i=n(46),a=n(26),s=n(240),c=n(18)("toStringTag");e.exports=function(e,t,n,u){if(e){var l=n?e:e.prototype;a(l,c)||o(l,c,{configurable:!0,value:t}),u&&!r&&i(l,"toString",s)}}},function(e,t){},function(e,t,n){var r=n(7);e.exports=r([].slice)},function(e,t,n){var r=n(12),o=n(28),i=n(82).genPrivateUrl,a=n(0),s=a.notundef,c=a.exist,u=n(144),l=n(204),p=l.typeMap;function m(e){e.resend?(a.verifyOptions(e,"idClient","msg::Message"),this.idClient=e.idClient):this.idClient=a.guid(),this.type=p[e.type],this.resend=e.resend?1:0,s(e.subType)&&(a.isInt(+e.subType)&&+e.subType>0?this.subType=+e.subType:a.onParamError("subType只能是大于0的整数","msg::Message")),s(e.custom)&&("object"===o(e.custom)?this.custom=r(e.custom):this.custom=""+e.custom),s(e.text)&&(this.body=""+e.text),s(e.body)&&(this.body=""+e.body),s(e.yidunEnable)&&(this.yidunEnable=e.yidunEnable?1:0),s(e.antiSpamUsingYidun)&&(this.antiSpamUsingYidun=e.antiSpamUsingYidun?1:0),s(e.antiSpamContent)&&("object"===o(e.antiSpamContent)?this.antiSpamContent=r(e.antiSpamContent):this.antiSpamContent=""+e.antiSpamContent),s(e.antiSpamBusinessId)&&("object"===o(e.antiSpamBusinessId)?this.antiSpamBusinessId=r(e.antiSpamBusinessId):this.antiSpamBusinessId=""+e.antiSpamBusinessId),s(e.yidunAntiCheating)&&(this.yidunAntiCheating=e.yidunAntiCheating+""),s(e.skipHistory)&&(this.skipHistory=e.skipHistory?1:0),s(e.highPriority)&&(this.highPriority=e.highPriority?1:0),s(e.clientAntiSpam)&&(this.clientAntiSpam=e.clientAntiSpam?1:0),s(e.env)&&(this.env=e.env),s(e.notifyTargetTags)&&(this.notifyTargetTags=e.notifyTargetTags),s(e.yidunAntiSpamExt)&&(this.yidunAntiSpamExt=e.yidunAntiSpamExt),(s(e.loc_x)||s(e.loc_y)||s(e.loc_z))&&(this.loc_x=e.loc_x,this.loc_y=e.loc_y,this.loc_z=e.loc_z)}m.validTypes=l.validTypes,a.merge(m.prototype,l.prototype),m.getType=l.getType,m.reverse=function(e){var t=a.filterObj(e,"chatroomId idClient from fromNick fromAvatar _fromAvatar_safe fromCustom userUpdateTime custom status notifyTargetTags");return s(t.fromAvatar)&&(t.fromAvatar=i(t.fromAvatar)),t=a.merge(t,{fromClientType:u.reverseType(e.fromClientType),time:+e.time,type:m.getType(e),text:c(e.body)?e.body:e.text||"",resend:1==+e.resend}),s(t.userUpdateTime)&&(t.userUpdateTime=+t.userUpdateTime),s(e.callbackExt)&&(t.callbackExt=e.callbackExt),s(e.subType)&&(t.subType=+e.subType),s(e.yidunAntiSpamRes)&&(t.yidunAntiSpamRes=e.yidunAntiSpamRes),s(e.toAccids)&&(t.toAccids=e.toAccids),t.status=t.status||"success",t},m.setExtra=function(e,t){l.setFlow(e,t)},e.exports=m},function(e,t,n){var r=n(167),o=n(133);e.exports=Object.keys||function(e){return r(e,o)}},function(e,t,n){"use strict";var r=n(39),o=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=r(t),this.reject=r(n)};e.exports.f=function(e){return new o(e)}},function(e,t,n){e.exports=n(410)},function(e,t,n){var r=n(1),o=n(7),i=n(11),a=n(68),s=r.Object,c=o("".split);e.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"==a(e)?c(e,""):s(e)}:s},function(e,t,n){var r=n(227),o=n(121);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},function(e,t,n){var r=n(1).String;e.exports=function(e){try{return r(e)}catch(e){return"Object"}}},function(e,t,n){var r=n(85),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},function(e,t,n){var r=n(7),o=n(11),i=n(21),a=n(47),s=n(36),c=n(131),u=function(){},l=[],p=s("Reflect","construct"),m=/^\s*(?:class|function)\b/,f=r(m.exec),d=!m.exec(u),g=function(e){if(!i(e))return!1;try{return p(u,l,e),!0}catch(e){return!1}},h=function(e){if(!i(e))return!1;switch(a(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!f(m,c(e))}catch(e){return!0}};h.sham=!0,e.exports=!p||o((function(){var e;return g(g.call)||!g(Object)||!g((function(){e=!0}))||e}))?h:g},function(e,t,n){"use strict";var r=n(236).charAt,o=n(42),i=n(101),a=n(164),s=i.set,c=i.getterFor("String Iterator");a(String,"String",(function(e){s(this,{type:"String Iterator",string:o(e),index:0})}),(function(){var e,t=c(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=r(n,o),t.index+=e.length,{value:e,done:!1})}))},function(e,t,n){var r,o,i,a=n(237),s=n(1),c=n(7),u=n(27),l=n(46),p=n(26),m=n(125),f=n(102),d=n(103),g=s.TypeError,h=s.WeakMap;if(a||m.state){var y=m.state||(m.state=new h),v=c(y.get),b=c(y.has),T=c(y.set);r=function(e,t){if(b(y,e))throw new g("Object already initialized");return t.facade=e,T(y,e,t),t},o=function(e){return v(y,e)||{}},i=function(e){return b(y,e)}}else{var S=f("state");d[S]=!0,r=function(e,t){if(p(e,S))throw new g("Object already initialized");return t.facade=e,l(e,S,t),t},o=function(e){return p(e,S)?e[S]:{}},i=function(e){return p(e,S)}}e.exports={set:r,get:o,has:i,enforce:function(e){return i(e)?o(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!u(t)||(n=o(t)).type!==e)throw g("Incompatible receiver, "+e+" required");return n}}}},function(e,t,n){var r=n(124),o=n(126),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},function(e,t){e.exports={}},function(e,t,n){var r=n(47),o=n(123),i=n(75),a=n(18)("iterator");e.exports=function(e){if(null!=e)return o(e,a)||o(e,"@@iterator")||i[r(e)]}},function(e,t,n){e.exports=n(299)},function(e,t,n){var r=n(68),o=n(1);e.exports="process"==r(o.process)},function(e,t,n){e.exports=n(321)},function(e,t,n){var r=n(1),o=n(70),i=n(31),a=n(34),s=n(97),c=n(170),u=n(38),l=n(16),p=n(171),m=n(104),f=n(169),d=r.TypeError,g=function(e,t){this.stopped=e,this.result=t},h=g.prototype;e.exports=function(e,t,n){var r,y,v,b,T,S,x,k=n&&n.that,M=!(!n||!n.AS_ENTRIES),w=!(!n||!n.IS_ITERATOR),_=!(!n||!n.INTERRUPTED),C=o(t,k),P=function(e){return r&&f(r,"normal",e),new g(!0,e)},I=function(e){return M?(a(e),_?C(e[0],e[1],P):C(e[0],e[1])):_?C(e,P):C(e)};if(w)r=e;else{if(!(y=m(e)))throw d(s(e)+" is not iterable");if(c(y)){for(v=0,b=u(e);b>v;v++)if((T=I(e[v]))&&l(h,T))return T;return new g(!1)}r=p(e,y)}for(S=r.next;!(x=i(S,r)).done;){try{T=I(x.value)}catch(e){f(r,"throw",e)}if("object"==typeof T&&T&&l(h,T))return T}return new g(!1)}},function(e,t){e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},function(e,t,n){var r=n(23),o=n(43),i=n(416),a=n(418),s=n(424),c={},u=o.f;function l(e){var t=e.upload="multipart/form-data"===(e.headers||o.o)["Content-Type"],n=!1;try{n=(location.protocol+"//"+location.host).toLowerCase()!==o.url2origin(e.url)}catch(e){}return e.cors=n,t||n||e.mode?function(e){var t=e.mode,n=i,r=o.getGlobal();return!r.FormData&&r.document&&(t="iframe"),"iframe"===t&&(n=e.upload?a:s),new n(e)}(e):new i(e)}function p(e,t,n){var r=c[e];if(r){"onload"===t&&r.result&&(n=function(e,t){t={data:t};var n=e.result.headers;return n&&(t.headers=e.req.header(n)),t}(r,n)),function(e){var t=c[e];t&&(t.req.destroy(),delete c[e])}(e);var o={type:t,result:n};u(o),o.stopped||r[t](o.result)}}function m(e,t){p(e,"onload",t)}function f(e,t){p(e,"onerror",t)}function d(e,t){var n=o.genUrlSep(e);return t=t||"",o.isObject(t)&&(t=o.object2query(t)),t&&(e+=n+t),e}function g(e,t){t=t||{};var n=o.uniqueID(),i={result:t.result,onload:t.onload||o.f,onerror:t.onerror||o.f};c[n]=i,t.onload=r(m).call(m,null,n),t.onerror=r(f).call(f,null,n),t.query&&(e=d(e,t.query));var a=t.method||"";return a&&!/get/i.test(a)||!t.data||(e=d(e,t.data),t.data=null),t.url=e,i.req=l(t),n}g.filter=function(e){o.isFunction(e)&&(u=e)},g.abort=function(e){var t=c[e];t&&t.req&&t.req.abort()},e.exports=g},function(e,t,n){var r,o=n(14),i=n(3),a=n(8),s=n(4),c=n(10),u=n(6),l=n(23),p=n(12),m=n(82).urlQuery2Object,f=n(0),d=n(33),g=n(218),h=n(467),y=n(468),v=n(469),b=n(470),T=n(471);function S(e){this.mixin(e)}S.prototype=o(function(){}.prototype,{protocol:{value:null,writable:!0,enumerable:!0,configurable:!0}}),S.prototype.setProtocol=function(e){this.protocol=e},S.prototype.mixin=function(e){var t,n,r=this;this.configMap=this.configMap||{},i(t=["idMap","cmdConfig","packetConfig"]).call(t,(function(t){r.configMap[t]=f.merge({},r.configMap[t],e.configMap&&e.configMap[t])})),i(n=["serializeMap","unserializeMap"]).call(n,(function(t){r[t]=f.merge({},r[t],e[t])}))},S.prototype.createCmd=(r=1,function(e,t){var n,o=this,s=this.configMap.cmdConfig[e],c="heartbeat"===e?0:r++;return c>32767&&(c=1,r=2),e={SID:s.sid,CID:s.cid,SER:c},s.params&&(e.Q=[],i(n=s.params).call(n,(function(n){var r=n.type,i=n.name,s=n.entity,c=t[i];if(!f.undef(c)){switch(r){case"PropertyArray":r="ArrayMable",c=a(c).call(c,(function(e){return{t:"Property",v:o.serialize(e,s)}}));break;case"Property":c=o.serialize(c,i);break;case"bool":c=c?"true":"false"}e.Q.push({t:r,v:c})}}))),e}),S.prototype.parseResponse=function(e){var t=this;return new s((function(n,r){var o=JSON.parse(e),a={raw:o,rawStr:e,error:d.genError(o.code)},p=t.configMap.packetConfig[o.sid+"_"+o.cid];if(!p)return a.notFound={sid:o.sid,cid:o.cid},void n(a);var m=o.r,g="notify"===p.service&&!p.cmd;if(a.isNotify=g,g){var h=o.r[1].headerPacket;if(p=t.configMap.packetConfig[h.sid+"_"+h.cid],m=o.r[1].body,!p)return a.notFound={sid:h.sid,cid:h.cid},void n(a)}if(a.service=p.service,a.cmd=p.cmd,a.error){var y,v,b=c(y="".concat(o.sid,"_")).call(y,o.cid);if(g)b=c(v="".concat(h.sid,"_")).call(v,h.cid);if(a.error.cmd=a.cmd,a.error.callFunc="protocol::parseResponse: ".concat(b),416===a.error.code){var T=m[0];T&&(a.frequencyControlDuration=1e3*T)}}var S,x=!1;a.error&&p.trivialErrorCodes&&(x=-1!==u(S=p.trivialErrorCodes).call(S,a.error.code));var k=[];if((!a.error||x)&&p.response){var M;a.content={};var w=function(e,t,n,r){if(e&&"msg"===r||"sysMsg"===r){var o=n.content[r];f.isObject(o)&&!o.idServer&&(o.idServer=""+t.r[0])}};i(M=p.response).call(M,(function(e,n){var r,s=m[n];if(!f.undef(s)){var c=e.type,u=e.name,p=e.entity||u;switch(c){case"Property":k.push(t.unserialize(s,p).then(l(r=function(e,t,n,r,o){n.content[r]=o,w(e,t,n,r)}).call(r,this,g,o,a,u)));break;case"PropertyArray":a.content[u]=[],i(s).call(s,(function(e,n){var r;k.push(t.unserialize(e,p).then(l(r=function(e,t,r){e.content[t][n]=r}).call(r,this,a,u)))}));break;case"KVArray":a.content[u]=s,w(g,o,a,u);break;case"long":case"Long":case"byte":case"Byte":case"Number":a.content[u]=+s;break;default:a.content[u]=s,w(g,o,a,u)}}}))}s.all(k).then((function(){n(a)}))}))},S.prototype.serialize=function(e,t){var n=this.serializeMap[t],r={};for(var o in n)e.hasOwnProperty(o)&&(r[n[o]]=e[o]);return r},S.prototype.matchNosSafeUrl=function(e){if(!f.isString(e)||!~u(e).call(e,"_im_url=1"))return!1;var t=m(e);return!(!t||!t._im_url||1!=t._im_url)},S.prototype.getOneNosOriginUrl=function(e,t,n){var r=this;return new s((function(o,i){r.protocol.getNosOriginUrlReqNum++,r.protocol.sendCmd("getNosOriginUrl",{nosFileUrlTag:{safeUrl:e}},(function(e,i,a){r.protocol.getNosOriginUrlReqNum--,e?r.protocol.logger.warn("error: get nos originUrl failed",e):(t["_"+n+"_safe"]=t[n],t[n]=a.nosFileUrlTag&&a.nosFileUrlTag.originUrl),o()}))}))},S.prototype.checkObjSafeUrl=function(e,t,n){var r=this;for(var o in e)if(e.hasOwnProperty(o)){var a=e[o];if(f.isString(a)){if(this.matchNosSafeUrl(a)){var s=this.getOneNosOriginUrl(a,e,o);t.push(s),n.push(s)}}else f.isObject(a)?this.checkObjSafeUrl(a,t,n):f.isArray(a)&&i(a).call(a,(function(e){f.isObject(e)&&r.checkObjSafeUrl(e,t,n)}))}};var x=["url","avatar","fromAvatar","chatroomAvatar"];S.prototype.unserialize=function(e,t){var n=this;return new s((function(r,o){var i=n.unserializeMap[t],a={},c=[];if(e)for(var m in i){var f,d=[];if(e.hasOwnProperty(m))if(a[i[m]]=e[m],!n.protocol.keepNosSafeUrl)if("attach"===i[m]&&e[m]&&u(e[m])&&~u(f=e[m]).call(f,"_im_url=1"))try{var g,h=JSON.parse(e[m]);h.isS3||n.checkObjSafeUrl(h,d,c),s.all(d).then(l(g=function(e,t){e.attach=p(t)}).call(g,n,a,h))}catch(e){n.logger.warn("unserialize",e&&e.message)}else~u(x).call(x,i[m])&&e[m]&&n.matchNosSafeUrl(e[m])&&c.push(n.getOneNosOriginUrl(e[m],a,i[m]))}s.all(c).then((function(e){r(a)}))}))},S.prototype.syncUnserialize=function(e,t){var n=this.unserializeMap[t],r={};if(e)for(var o in n)e.hasOwnProperty(o)&&(r[n[o]]=e[o]);return r};var k=new S({configMap:g,serializeMap:h,unserializeMap:y}),M=new S({configMap:v,serializeMap:b,unserializeMap:T});e.exports={IM:k,Chatroom:M}},function(e,t,n){"use strict";var r={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,i=o&&!r.call({1:2},1);t.f=i?function(e){var t=o(this,e);return!!t&&t.enumerable}:r},function(e,t,n){e.exports=n(163)},function(e,t,n){e.exports=n(173)},function(e,t,n){e.exports=n(291)},function(e,t){e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},,,function(e,t,n){e.exports=n(316)},function(e,t,n){var r=n(401),o=n(197),i=n(406);e.exports=function(e,t){if(null==e)return{};var n,a,s=i(e,t);if(r){var c=r(e);for(a=0;a<c.length;a++)n=c[a],o(t).call(t,n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(1),o=n(36),i=n(21),a=n(16),s=n(158),c=r.Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=o("Symbol");return i(t)&&a(t.prototype,c(e))}},function(e,t,n){var r=n(69),o=n(11);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},function(e,t,n){var r=n(39);e.exports=function(e,t){var n=e[t];return null==n?void 0:r(n)}},function(e,t,n){var r=n(58),o=n(125);(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.21.1",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})},function(e,t,n){var r=n(1),o=n(229),i=r["__core-js_shared__"]||o("__core-js_shared__",{});e.exports=i},function(e,t,n){var r=n(7),o=0,i=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},function(e,t,n){var r=n(1),o=n(27),i=r.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},function(e,t,n){var r=n(35),o=n(98),i=n(38),a=function(e){return function(t,n,a){var s,c=r(t),u=i(c),l=o(a,u);if(e&&n!=n){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((e||l in c)&&c[l]===n)return e||l||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},function(e,t,n){var r=n(234);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},function(e,t,n){var r={};r[n(18)("toStringTag")]="z",e.exports="[object z]"===String(r)},function(e,t,n){var r=n(7),o=n(21),i=n(125),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},function(e,t,n){var r=n(24),o=n(161),i=n(40),a=n(34),s=n(35),c=n(92);t.f=r&&!o?Object.defineProperties:function(e,t){a(e);for(var n,r=s(t),o=c(t),u=o.length,l=0;u>l;)i.f(e,n=o[l++],r[n]);return e}},function(e,t){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(e,t,n){var r=n(1),o=n(26),i=n(21),a=n(37),s=n(102),c=n(239),u=s("IE_PROTO"),l=r.Object,p=l.prototype;e.exports=c?l.getPrototypeOf:function(e){var t=a(e);if(o(t,u))return t[u];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof l?p:null}},function(e,t,n){var r=n(7),o=n(34),i=n(241);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return o(n),i(r),t?e(n,r):n.__proto__=r,n}}():void 0)},function(e,t,n){var r=n(167),o=n(133).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,o)}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,n){var r=n(18);t.f=r},function(e,t,n){e.exports=n(353)},function(e,t,n){var r=n(7),o=n(84),i=n(42),a=n(109),s=r("".replace),c="["+a+"]",u=RegExp("^"+c+c+"*"),l=RegExp(c+c+"*$"),p=function(e){return function(t){var n=i(o(t));return 1&e&&(n=s(n,u,"")),2&e&&(n=s(n,l,"")),n}};e.exports={start:p(1),end:p(2),trim:p(3)}},function(e,t,n){var r=n(14),o=n(25),i=n(6),a=n(3),s=n(43),c=s.f,u=n(417);function l(e){e.onload&&this.once("load",e.onload),e.onerror&&this.once("error",e.onerror),e.onbeforesend&&this.once("beforesend",e.onbeforesend),e.onaftersend&&this.once("aftersend",e.onaftersend);var t=(e=this.options=s.fetch({method:"GET",url:"",sync:!1,data:null,headers:{},cookie:!1,timeout:6e4,type:"text",form:null,input:null,putFileAtEnd:!1,proxyUrl:""},e)).headers;s.notexist(t["Content-Type"])&&(t["Content-Type"]="application/x-www-form-urlencoded"),this.send()}var p=l.prototype=r(u.prototype);p.send=function(){var e=this,t=e.options;o((function(){try{try{e.emit("beforesend",t)}catch(e){}e.doSend()}catch(t){e.onError("serverError","请求失败:"+t.message)}}),0)},p.doSend=c,p.afterSend=function(){var e=this;o((function(){e.emit("aftersend",e.options)}),0)},p.onLoad=function(e){var t,n=this.options,r=e.status,o=e.result;if("number"!=typeof r||0!==r)if(0===i(t=""+r).call(t,"2")){if("json"===n.type)try{o=JSON.parse(o)}catch(e){return void this.onError("parseError",o)}this.emit("load",o)}else this.onError("serverError","服务器返回异常状态",{status:r,result:o,date:e.date});else this.onError("netError","网络错误")},p.onError=function(e,t,n){var r=s.isObject(n)?n:{};r.code=e||"error",r.message=t||"发生错误",this.emit("error",r)},p.onTimeout=function(){this.onError("timeout","请求超时")},p.abort=function(){this.onError("abort","客户端中止")},p.header=function(e){var t=this;if(!s.isArray(e))return t.getResponseHeader(e||"");var n={};return a(e).call(e,(function(e){n[e]=t.header(e)})),n},p.getResponseHeader=c,p.destroy=c,e.exports=l},function(e,t){var n={link:{id:1,heartbeat:2,negotiateTransport:5,initTransport:6},sync:{id:5,sync:1,syncTeamMembers:2},misc:{id:6,getSimpleNosToken:1,getNosToken:2,notifyUploadLog:3,uploadSdkLogUrl:4,audioToText:5,processImage:6,getNosTokenTrans:7,notifyTransLog:8,fetchFile:9,fetchFileList:10,removeFile:11,getClientAntispam:17,fileQuickTransfer:18,getNosOriginUrl:22,getServerTime:23,getNosAccessToken:24,deleteNosAccessToken:25,getNosCdnHost:26,getGrayscaleConfig:27,getMixStorePolicy:28,getMixStoreToken:29,getBackSourceToken:30},avSignal:{id:15,signalingCreate:1,signalingDelay:2,signalingClose:3,signalingJoin:4,signalingLeave:5,signalingInvite:6,signalingCancel:7,signalingReject:8,signalingAccept:9,signalingControl:10,signalingNotify:11,signalingMutilClientSyncNotify:12,signalingUnreadMessageSyncNotify:13,signalingChannelsSyncNotify:14,signalingGetChannelInfo:15}},r={heartbeat:{sid:n.link.id,cid:n.link.heartbeat},negotiateTransport:{sid:n.link.id,cid:n.link.negotiateTransport,params:[{type:"int",name:"sdkVersion"},{type:"Property",name:"negotiateTransportTag"}]},initTransport:{sid:n.link.id,cid:n.link.initTransport,params:[{type:"Property",name:"initTransportTag"}]},getGrayscaleConfig:{sid:n.misc.id,cid:n.misc.getGrayscaleConfig,params:[{type:"Property",name:"providers"}]},getMixStorePolicy:{sid:n.misc.id,cid:n.misc.getMixStorePolicy,params:[{type:"LongArray",name:"providers"}]},getMixStoreToken:{sid:n.misc.id,cid:n.misc.getMixStoreToken,params:[{type:"Property",name:"mixTokenReq"}]},getBackSourceToken:{sid:n.misc.id,cid:n.misc.getBackSourceToken,params:[{type:"Property",name:"mixAuthTokenReq"}]},getSimpleNosToken:{sid:n.misc.id,cid:n.misc.getSimpleNosToken,params:[{type:"int",name:"num"}]},getNosToken:{sid:n.misc.id,cid:n.misc.getNosToken,params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken"}]},uploadSdkLogUrl:{sid:n.misc.id,cid:n.misc.uploadSdkLogUrl,params:[{type:"string",name:"url"}]},audioToText:{sid:n.misc.id,cid:n.misc.audioToText,params:[{type:"Property",name:"audioToText"}]},processImage:{sid:n.misc.id,cid:n.misc.processImage,params:[{type:"String",name:"url"},{type:"PropertyArray",name:"imageOps",entity:"imageOp"}]},getClientAntispam:{sid:n.misc.id,cid:n.misc.getClientAntispam,params:[{type:"Property",name:"clientAntispam"}]},fileQuickTransfer:{sid:n.misc.id,cid:n.misc.fileQuickTransfer,params:[{type:"Property",name:"fileQuickTransfer"}]},getNosOriginUrl:{sid:n.misc.id,cid:n.misc.getNosOriginUrl,params:[{type:"Property",name:"nosFileUrlTag"}]},getServerTime:{sid:n.misc.id,cid:n.misc.getServerTime,params:[]},getNosAccessToken:{sid:n.misc.id,cid:n.misc.getNosAccessToken,params:[{type:"Property",name:"nosAccessTokenTag"}]},deleteNosAccessToken:{sid:n.misc.id,cid:n.misc.deleteNosAccessToken,params:[{type:"Property",name:"nosAccessTokenTag"}]},getNosTokenTrans:{sid:n.misc.id,cid:n.misc.getNosTokenTrans,params:[{type:"Property",name:"transToken"}]},fetchFile:{sid:n.misc.id,cid:n.misc.fetchFile,params:[{type:"String",name:"docId"}]},fetchFileList:{sid:n.misc.id,cid:n.misc.fetchFileList,params:[{type:"Property",name:"fileListParam"}]},removeFile:{sid:n.misc.id,cid:n.misc.removeFile,params:[{type:"String",name:"docId"}]},getNosCdnHost:{sid:n.misc.id,cid:n.misc.getNosCdnHost,params:[]},signalingCreate:{sid:n.avSignal.id,cid:n.avSignal.signalingCreate,params:[{type:"Property",name:"avSignalTag"}]},signalingDelay:{sid:n.avSignal.id,cid:n.avSignal.signalingDelay,params:[{type:"Property",name:"avSignalTag"}]},signalingClose:{sid:n.avSignal.id,cid:n.avSignal.signalingClose,params:[{type:"Property",name:"avSignalTag"}]},signalingJoin:{sid:n.avSignal.id,cid:n.avSignal.signalingJoin,params:[{type:"Property",name:"avSignalTag"}]},signalingLeave:{sid:n.avSignal.id,cid:n.avSignal.signalingLeave,params:[{type:"Property",name:"avSignalTag"}]},signalingInvite:{sid:n.avSignal.id,cid:n.avSignal.signalingInvite,params:[{type:"Property",name:"avSignalTag"}]},signalingCancel:{sid:n.avSignal.id,cid:n.avSignal.signalingCancel,params:[{type:"Property",name:"avSignalTag"}]},signalingReject:{sid:n.avSignal.id,cid:n.avSignal.signalingReject,params:[{type:"Property",name:"avSignalTag"}]},signalingAccept:{sid:n.avSignal.id,cid:n.avSignal.signalingAccept,params:[{type:"Property",name:"avSignalTag"}]},signalingControl:{sid:n.avSignal.id,cid:n.avSignal.signalingControl,params:[{type:"Property",name:"avSignalTag"}]},signalingGetChannelInfo:{sid:n.avSignal.id,cid:n.avSignal.signalingGetChannelInfo,params:[{type:"Property",name:"avSignalTag"}]}};e.exports={idMap:n,cmdConfig:r,packetConfig:{"1_2":{service:"link",cmd:"heartbeat"},"1_5":{service:"link",cmd:"negotiateTransport",response:[{type:"Property",name:"negotiateTransportTag"}]},"1_6":{service:"link",cmd:"initTransport",response:[{type:"Property",name:"initTransportTag"}]},"6_1":{service:"misc",cmd:"getSimpleNosToken",response:[{type:"PropertyArray",name:"nosTokens",entity:"nosToken"}]},"6_2":{service:"misc",cmd:"getNosToken",response:[{type:"Property",name:"nosToken"}]},"6_3":{service:"misc",cmd:"notifyUploadLog"},"6_4":{service:"misc",cmd:"uploadSdkLogUrl"},"6_5":{service:"misc",cmd:"audioToText",response:[{type:"String",name:"text"}]},"6_6":{service:"misc",cmd:"processImage",response:[{type:"String",name:"url"}]},"6_7":{service:"misc",cmd:"getNosTokenTrans",response:[{type:"Property",name:"nosToken"},{type:"String",name:"docId"}]},"6_8":{service:"misc",cmd:"notifyTransLog",response:[{type:"Property",name:"transInfo"}]},"6_9":{service:"misc",cmd:"fetchFile",response:[{type:"Property",name:"info",entity:"transInfo"}]},"6_10":{service:"misc",cmd:"fetchFileList",response:[{type:"PropertyArray",name:"list",entity:"transInfo"},{type:"Number",name:"totalCount"}]},"6_11":{service:"misc",cmd:"removeFile",response:[{type:"String",name:"res"}]},"6_17":{service:"misc",cmd:"getClientAntispam",response:[{type:"Property",name:"clientAntispam"}]},"6_18":{service:"misc",cmd:"fileQuickTransfer",response:[{type:"Property",name:"fileQuickTransfer"}]},"6_22":{service:"misc",cmd:"getNosOriginUrl",response:[{type:"Property",name:"nosFileUrlTag"}]},"6_23":{service:"misc",cmd:"getServerTime",response:[{type:"Number",name:"time"}]},"6_24":{service:"misc",cmd:"getNosAccessToken",response:[{type:"Property",name:"nosAccessTokenTag"}]},"6_25":{service:"misc",cmd:"deleteNosAccessToken"},"6_26":{service:"misc",cmd:"getNosCdnHost",response:[{type:"Property",name:"nosConfigTag"}]},"6_27":{service:"misc",cmd:"getGrayscaleConfig",response:[{type:"Property",name:"appGrayConfigTag"}]},"6_28":{service:"misc",cmd:"getMixStorePolicy",response:[{type:"Property",name:"mixStorePolicyTag"}]},"6_29":{service:"misc",cmd:"getMixStoreToken",response:[{type:"Property",name:"mixStoreTokenTag"}]},"6_30":{service:"misc",cmd:"getBackSourceToken",response:[{type:"Property",name:"backSourceTokenTag"}]},"15_1":{service:"avSignal",cmd:"signalingCreate",response:[{type:"Property",name:"avSignalTag"}]},"15_2":{service:"avSignal",cmd:"signalingDelay",response:[{type:"Property",name:"avSignalTag"}]},"15_3":{service:"avSignal",cmd:"signalingClose",response:[{type:"Property",name:"avSignalTag"}]},"15_4":{service:"avSignal",cmd:"signalingJoin",response:[{type:"Property",name:"avSignalTag"}]},"15_5":{service:"avSignal",cmd:"signalingLeave",response:[]},"15_6":{service:"avSignal",cmd:"signalingInvite",response:[]},"15_7":{service:"avSignal",cmd:"signalingCancel",response:[]},"15_8":{service:"avSignal",cmd:"signalingReject",response:[]},"15_9":{service:"avSignal",cmd:"signalingAccept",response:[]},"15_10":{service:"avSignal",cmd:"signalingControl",response:[]},"15_11":{service:"avSignal",cmd:"signalingNotify",response:[{type:"Property",name:"avSignalTag"}]},"15_12":{service:"avSignal",cmd:"signalingMutilClientSyncNotify",response:[{type:"Property",name:"avSignalTag"}]},"15_13":{service:"avSignal",cmd:"signalingUnreadMessageSyncNotify",response:[{type:"PropertyArray",name:"avSignalTag"}]},"15_14":{service:"avSignal",cmd:"signalingChannelsSyncNotify",response:[{type:"PropertyArray",name:"avSignalTag"}]},"15_15":{service:"avSignal",cmd:"signalingGetChannelInfo",response:[{type:"Property",name:"avSignalTag"}]}}}},,function(e,t,n){var r=n(41).clientTypeMap;function o(){}o.reverse=function(e){var t=e;return t.type=r[t.type],t},o.reverseType=function(e){return r[e]||e},e.exports=o},,function(e,t,n){var r=n(14),o=n(23),i=n(8),a=n(17),s=n(5),c=n(65),u=n(0),l=u.undef,p=u.notundef,m=n(15),f=n(111),d=n(485),g=n(220);function h(e){u.verifyOptions(e,"appKey account chatroomId chatroomAddresses","protocol::ChatroomProtocol"),e.isAnonymous||u.verifyOptions(e,"token","protocol::ChatroomProtocol"),u.verifyParamType("chatroomAddresses",e.chatroomAddresses,"array","protocol::ChatroomProtocol"),u.verifyCallback(e,"onconnect onerror onwillreconnect ondisconnect onmsg onmsgs onrobots","protocol::ChatroomProtocol"),c.call(this,e)}var y=c.fn,v=h.fn=h.prototype=r(y);v.init=function(){var e;y.init.call(this),f.Chatroom.setProtocol(this),this.parser=f.Chatroom,o(e=this.sendCmd).call(e,this),this.syncResult={},this.timetags={},this.msgBuffer=[]},v.reset=function(){var e=this;y.reset.call(e);var t,n,r=e.options;(l(r.msgBufferInterval)&&(r.msgBufferInterval=300),u.verifyParamType("msgBufferInterval",r.msgBufferInterval,"number","protocol::ChatroomProtocol.reset"),l(r.msgBufferSize)&&(r.msgBufferSize=500),u.verifyParamType("msgBufferSize",r.msgBufferSize,"number","protocol::ChatroomProtocol.reset"),p(r.chatroomAddresses))&&(e.socketUrls=i(t=r.chatroomAddresses).call(t,(function(t){return m.formatSocketUrl({url:t,secure:e.options.secure})})),e.socketUrlsBackup=a(n=e.socketUrls).call(n,0))},v.processChatroom=function(e){switch(e.cmd){case"login":e.error||(e.obj={chatroom:s(d).call(d,e.content.chatroom),member:s(g).call(g,e.content.chatroomMember)},this.cdnInfo={},this.initCdnData(),clearTimeout(this.queryCdnTimer),this.onCdnMsgInfo(e.content.chatroomCdnInfo));break;case"kicked":this.onKicked(e);break;case"logout":break;case"sendMsg":this.onSendMsg(e);break;case"msg":this.onMsg(e),this.cdnInfo&&this.cdnInfo.enable&&this.correctCdnTime(+e.content.msg.time);break;case"getChatroomMembers":case"getChatroomMembersByTag":this.onChatroomMembers(e);break;case"getChatroomMemberCountByTag":this.onGetChatroomMemberCountByTag(e);break;case"getHistoryMsgs":this.onHistoryMsgs(e);break;case"markChatroomMember":this.onMarkChatroomMember(e);break;case"closeChatroom":break;case"getChatroom":this.onChatroom(e);break;case"updateChatroom":break;case"updateMyChatroomMemberInfo":delete e.obj.chatroomMember;break;case"getChatroomMembersInfo":this.onChatroomMembersInfo(e);break;case"kickChatroomMember":case"updateChatroomMemberTempMute":break;case"queueList":e.error||(e.obj=e.content);break;case"syncRobot":this.onSyncRobot(e);break;case"notifyCdnInfo":this.onCdnMsgInfo(e.content&&e.content.chatroomCdnInfo)}},v.onChatroom=function(e){e.error||(e.obj.chatroom=s(d).call(d,e.content.chatroom))},e.exports=h,n(588),n(589),n(590),n(599)},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){var n=t.split(".");for(;n.length;){var r=n.shift(),o=!1;if("?"==r[r.length-1]&&(r=r.slice(0,-1),o=!0),!(e=e[r])&&o)return e}return e}},function(e,t,n){var r=n(368),o=n(371),i=n(195),a=n(377);e.exports=function(e){return r(e)||o(e)||i(e)||a()},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(398),o=n(399),i=n(195),a=n(400);e.exports=function(e,t){return r(e)||o(e,t)||i(e,t)||a()},e.exports.default=e.exports,e.exports.__esModule=!0},,function(e,t,n){e.exports=n(457)},,,function(e,t,n){var r=n(14),o=n(48),i=n(12),a=n(44),s=n(146),c=n(15),u=n(485),l=n(600),p=n(0),m=p.verifyOptions,f=p.verifyParamType,d=n(111).Chatroom;function g(e){return this.subType="chatroom",this.nosScene=e.nosScene||"chatroom",this.nosSurvivalTime=e.nosSurvivalTime,e.Protocol=s,e.Message=l,e.constructor=g,e.isAnonymous&&(e.account=e.account||"nimanon_".concat(p.guid()),e.isAnonymous=1,p.verifyOptions(e,"chatroomNick","api::Chatroom"),e.chatroomAvatar=e.chatroomAvatar||" "),this.init(e)}g.Protocol=s,g.parser=d,g.use=a.use,g.getInstance=function(e){return e.isAnonymous&&(e.account=e.account||"nimanon_".concat(p.guid()),e.isAnonymous=1,p.verifyOptions(e,"chatroomNick","api::Chatroom.getInstance"),e.chatroomAvatar=e.chatroomAvatar||" "),a.getInstance.call(this,e)},g.genInstanceName=function(e){return p.verifyOptions(e,"chatroomId","api::Chatroom.genInstanceName"),"Chatroom-account-"+e.account+"-chatroomId-"+e.chatroomId};var h=g.fn=g.prototype=r(a.prototype);g.info=h.info=c.info,h.getChatroom=function(e){this.processCallback(e),this.sendCmd("getChatroom",e)},h.updateChatroom=function(e){m(e,"chatroom needNotify","api::updateChatroom"),f("needNotify",e.needNotify,"boolean"),this.processCustom(e),this.processCallback(e),e.chatroom=new u(e.chatroom),e.antispamTag={antiSpamBusinessId:e.antiSpamBusinessId},this.sendCmd("updateChatroom",e)},h.closeChatroom=function(e){this.processCustom(e),this.processCallback(e),this.sendCmd("closeChatroom",e)},h.updateTags=function(e){p.validate({tags:{type:"array",required:!1},notifyTargetTags:{type:"string",allowEmpty:!0,required:!1},needNotify:{type:"boolean",required:!1}},e,"chatroom::updateTags");var t=o({},e);this.processCallback(t),t.tags&&(t.tags=i(t.tags)),t.needNotify&&(t.needNotify=t.needNotify?1:0),this.sendCmd("updateChatroomTags",{chatRoomTagsUpdateTag:t},t.done)},e.exports=g,n(610),n(611),n(612)},function(e,t,n){var r=n(12),o=n(14),i=n(5),a=n(6),s=n(82).genPrivateUrl,c=n(91),u=n(0),l=n(15);function p(e){switch(u.notundef(e.type)?u.verifyFileType(e.type,"msg::FileMessage"):e.type="file",u.verifyOptions(e,"file","msg::FileMessage"),u.verifyOptions(e.file,"url ext size",!0,"file.","msg::FileMessage"),e.type){case"image":m.verifyFile(e.file,"msg::FileMessage");break;case"audio":f.verifyFile(e.file,"msg::FileMessage");break;case"video":d.verifyFile(e.file,"msg::FileMessage")}c.call(this,e),this.attach=r(e.file)}p.prototype=o(c.prototype),p.reverse=function(e){var t,n,r=i(c).call(c,e);(e.attach=e.attach?""+e.attach:"",r.file=e.attach?JSON.parse(e.attach):{},r.file.url=s(r.file.url),"audio"!==r.type||r.file.mp3Url)||(r.file.mp3Url=r.file.url+(~a(n=r.file.url).call(n,"?")?"&":"?")+"audioTrans&type=mp3");return l.httpsEnabled&&0!==a(t=r.file.url).call(t,"https://")&&(r.file.url=r.file.url.replace("http","https")),r},e.exports=p;var m=n(602),f=n(603),d=n(604)},function(e,t,n){var r=n(224);e.exports=r},function(e,t,n){var r=n(122);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(e,t,n){var r=n(24),o=n(11),i=n(127);e.exports=!r&&!o((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},function(e,t,n){var r=n(11),o=n(21),i=/#|\.prototype\./,a=function(e,t){var n=c[s(e)];return n==l||n!=u&&(o(t)?r(t):!!t)},s=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=a.data={},u=a.NATIVE="N",l=a.POLYFILL="P";e.exports=a},function(e,t,n){var r=n(24),o=n(11);e.exports=r&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},function(e,t,n){"use strict";var r=n(2),o=n(1),i=n(11),a=n(72),s=n(27),c=n(37),u=n(38),l=n(73),p=n(129),m=n(86),f=n(18),d=n(69),g=f("isConcatSpreadable"),h=o.TypeError,y=d>=51||!i((function(){var e=[];return e[g]=!1,e.concat()[0]!==e})),v=m("concat"),b=function(e){if(!s(e))return!1;var t=e[g];return void 0!==t?!!t:a(e)};r({target:"Array",proto:!0,forced:!y||!v},{concat:function(e){var t,n,r,o,i,a=c(this),s=p(a,0),m=0;for(t=-1,r=arguments.length;t<r;t++)if(b(i=-1===t?a:arguments[t])){if(m+(o=u(i))>9007199254740991)throw h("Maximum allowed index exceeded");for(n=0;n<o;n++,m++)n in i&&l(s,m,i[n])}else{if(m>=9007199254740991)throw h("Maximum allowed index exceeded");l(s,m++,i)}return s.length=m,s}})},function(e,t,n){var r=n(235);e.exports=r},function(e,t,n){"use strict";var r=n(2),o=n(31),i=n(58),a=n(165),s=n(21),c=n(238),u=n(134),l=n(135),p=n(88),m=n(46),f=n(74),d=n(18),g=n(75),h=n(166),y=a.PROPER,v=a.CONFIGURABLE,b=h.IteratorPrototype,T=h.BUGGY_SAFARI_ITERATORS,S=d("iterator"),x=function(){return this};e.exports=function(e,t,n,a,d,h,k){c(n,t,a);var M,w,_,C=function(e){if(e===d&&O)return O;if(!T&&e in A)return A[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},P=t+" Iterator",I=!1,A=e.prototype,E=A[S]||A["@@iterator"]||d&&A[d],O=!T&&E||C(d),j="Array"==t&&A.entries||E;if(j&&(M=u(j.call(new e)))!==Object.prototype&&M.next&&(i||u(M)===b||(l?l(M,b):s(M[S])||f(M,S,x)),p(M,P,!0,!0),i&&(g[P]=x)),y&&"values"==d&&E&&"values"!==E.name&&(!i&&v?m(A,"name","values"):(I=!0,O=function(){return o(E,this)})),d)if(w={values:C("values"),keys:h?O:C("keys"),entries:C("entries")},k)for(_ in w)(T||I||!(_ in A))&&f(A,_,w[_]);else r({target:t,proto:!0,forced:T||I},w);return i&&!k||A[S]===O||f(A,S,O,{name:d}),g[t]=O,w}},function(e,t,n){var r=n(24),o=n(26),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=o(i,"name"),c=s&&"something"===function(){}.name,u=s&&(!r||r&&a(i,"name").configurable);e.exports={EXISTS:s,PROPER:c,CONFIGURABLE:u}},function(e,t,n){"use strict";var r,o,i,a=n(11),s=n(21),c=n(87),u=n(134),l=n(74),p=n(18),m=n(58),f=p("iterator"),d=!1;[].keys&&("next"in(i=[].keys())?(o=u(u(i)))!==Object.prototype&&(r=o):d=!0),null==r||a((function(){var e={};return r[f].call(e)!==e}))?r={}:m&&(r=c(r)),s(r[f])||l(r,f,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:d}},function(e,t,n){var r=n(7),o=n(26),i=n(35),a=n(128).indexOf,s=n(103),c=r([].push);e.exports=function(e,t){var n,r=i(e),u=0,l=[];for(n in r)!o(s,n)&&o(r,n)&&c(l,n);for(;t.length>u;)o(r,n=t[u++])&&(~a(l,n)||c(l,n));return l}},function(e,t,n){var r=n(36);e.exports=r("document","documentElement")},function(e,t,n){var r=n(31),o=n(34),i=n(123);e.exports=function(e,t,n){var a,s;o(e);try{if(!(a=i(e,"return"))){if("throw"===t)throw n;return n}a=r(a,e)}catch(e){s=!0,a=e}if("throw"===t)throw n;if(s)throw a;return o(a),n}},function(e,t,n){var r=n(18),o=n(75),i=r("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[i]===e)}},function(e,t,n){var r=n(1),o=n(31),i=n(39),a=n(34),s=n(97),c=n(104),u=r.TypeError;e.exports=function(e,t){var n=arguments.length<2?c(e):t;if(i(n))return a(o(n,e));throw u(s(e)+" is not iterable")}},function(e,t,n){var r=n(18)("iterator"),o=!1;try{var i=0,a={next:function(){return{done:!!i++}},return:function(){o=!0}};a[r]=function(){return this},Array.from(a,(function(){throw 2}))}catch(e){}e.exports=function(e,t){if(!t&&!o)return!1;var n=!1;try{var i={};i[r]=function(){return{next:function(){return{done:n=!0}}}},e(i)}catch(e){}return n}},function(e,t,n){var r=n(245);n(59),e.exports=r},function(e,t,n){"use strict";var r=n(2),o=n(1),i=n(36),a=n(56),s=n(31),c=n(7),u=n(58),l=n(24),p=n(122),m=n(11),f=n(26),d=n(72),g=n(21),h=n(27),y=n(16),v=n(121),b=n(34),T=n(37),S=n(35),x=n(96),k=n(42),M=n(57),w=n(87),_=n(92),C=n(136),P=n(175),I=n(137),A=n(67),E=n(40),O=n(132),j=n(112),N=n(90),L=n(74),R=n(124),D=n(102),F=n(103),U=n(126),B=n(18),q=n(138),H=n(20),z=n(88),W=n(101),G=n(79).forEach,X=D("hidden"),K=B("toPrimitive"),$=W.set,J=W.getterFor("Symbol"),V=Object.prototype,Q=o.Symbol,Y=Q&&Q.prototype,Z=o.TypeError,ee=o.QObject,te=i("JSON","stringify"),ne=A.f,re=E.f,oe=P.f,ie=j.f,ae=c([].push),se=R("symbols"),ce=R("op-symbols"),ue=R("string-to-symbol-registry"),le=R("symbol-to-string-registry"),pe=R("wks"),me=!ee||!ee.prototype||!ee.prototype.findChild,fe=l&&m((function(){return 7!=w(re({},"a",{get:function(){return re(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=ne(V,t);r&&delete V[t],re(e,t,n),r&&e!==V&&re(V,t,r)}:re,de=function(e,t){var n=se[e]=w(Y);return $(n,{type:"Symbol",tag:e,description:t}),l||(n.description=t),n},ge=function(e,t,n){e===V&&ge(ce,t,n),b(e);var r=x(t);return b(n),f(se,r)?(n.enumerable?(f(e,X)&&e[X][r]&&(e[X][r]=!1),n=w(n,{enumerable:M(0,!1)})):(f(e,X)||re(e,X,M(1,{})),e[X][r]=!0),fe(e,r,n)):re(e,r,n)},he=function(e,t){b(e);var n=S(t),r=_(n).concat(Te(n));return G(r,(function(t){l&&!s(ye,n,t)||ge(e,t,n[t])})),e},ye=function(e){var t=x(e),n=s(ie,this,t);return!(this===V&&f(se,t)&&!f(ce,t))&&(!(n||!f(this,t)||!f(se,t)||f(this,X)&&this[X][t])||n)},ve=function(e,t){var n=S(e),r=x(t);if(n!==V||!f(se,r)||f(ce,r)){var o=ne(n,r);return!o||!f(se,r)||f(n,X)&&n[X][r]||(o.enumerable=!0),o}},be=function(e){var t=oe(S(e)),n=[];return G(t,(function(e){f(se,e)||f(F,e)||ae(n,e)})),n},Te=function(e){var t=e===V,n=oe(t?ce:S(e)),r=[];return G(n,(function(e){!f(se,e)||t&&!f(V,e)||ae(r,se[e])})),r};(p||(L(Y=(Q=function(){if(y(Y,this))throw Z("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?k(arguments[0]):void 0,t=U(e),n=function(e){this===V&&s(n,ce,e),f(this,X)&&f(this[X],t)&&(this[X][t]=!1),fe(this,t,M(1,e))};return l&&me&&fe(V,t,{configurable:!0,set:n}),de(t,e)}).prototype,"toString",(function(){return J(this).tag})),L(Q,"withoutSetter",(function(e){return de(U(e),e)})),j.f=ye,E.f=ge,O.f=he,A.f=ve,C.f=P.f=be,I.f=Te,q.f=function(e){return de(B(e),e)},l&&(re(Y,"description",{configurable:!0,get:function(){return J(this).description}}),u||L(V,"propertyIsEnumerable",ye,{unsafe:!0}))),r({global:!0,wrap:!0,forced:!p,sham:!p},{Symbol:Q}),G(_(pe),(function(e){H(e)})),r({target:"Symbol",stat:!0,forced:!p},{for:function(e){var t=k(e);if(f(ue,t))return ue[t];var n=Q(t);return ue[t]=n,le[n]=t,n},keyFor:function(e){if(!v(e))throw Z(e+" is not a symbol");if(f(le,e))return le[e]},useSetter:function(){me=!0},useSimple:function(){me=!1}}),r({target:"Object",stat:!0,forced:!p,sham:!l},{create:function(e,t){return void 0===t?w(e):he(w(e),t)},defineProperty:ge,defineProperties:he,getOwnPropertyDescriptor:ve}),r({target:"Object",stat:!0,forced:!p},{getOwnPropertyNames:be,getOwnPropertySymbols:Te}),r({target:"Object",stat:!0,forced:m((function(){I.f(1)}))},{getOwnPropertySymbols:function(e){return I.f(T(e))}}),te)&&r({target:"JSON",stat:!0,forced:!p||m((function(){var e=Q();return"[null]"!=te([e])||"{}"!=te({a:e})||"{}"!=te(Object(e))}))},{stringify:function(e,t,n){var r=N(arguments),o=t;if((h(t)||void 0!==e)&&!v(e))return d(t)||(t=function(e,t){if(g(o)&&(t=s(o,this,e,t)),!v(t))return t}),r[1]=t,a(te,null,r)}});if(!Y[K]){var Se=Y.valueOf;L(Y,K,(function(e){return s(Se,this)}))}z(Q,"Symbol"),F[X]=!0},function(e,t,n){var r=n(68),o=n(35),i=n(136).f,a=n(176),s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"Window"==r(e)?function(e){try{return i(e)}catch(e){return a(s)}}(e):i(o(e))}},function(e,t,n){var r=n(1),o=n(98),i=n(38),a=n(73),s=r.Array,c=Math.max;e.exports=function(e,t,n){for(var r=i(e),u=o(t,r),l=o(void 0===n?r:n,r),p=s(c(l-u,0)),m=0;u<l;u++,m++)a(p,m,e[u]);return p.length=m,p}},function(e,t,n){n(20)("iterator")},function(e,t,n){var r=n(267);e.exports=r},function(e,t,n){var r=n(36),o=n(7),i=n(136),a=n(137),s=n(34),c=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(s(e)),n=a.f;return n?c(t,n(e)):t}},function(e,t,n){var r=n(277);e.exports=r},function(e,t,n){var r=n(286);e.exports=r},function(e,t,n){var r=n(289);e.exports=r},function(e,t,n){var r=n(307);e.exports=r},function(e,t,n){var r=n(2),o=n(1),i=n(56),a=n(21),s=n(45),c=n(90),u=n(185),l=/MSIE .\./.test(s),p=o.Function,m=function(e){return function(t,n){var r=u(arguments.length,1)>2,o=a(t)?t:p(t),s=r?c(arguments,2):void 0;return e(r?function(){i(o,this,s)}:o,n)}};r({global:!0,bind:!0,forced:l},{setTimeout:m(o.setTimeout),setInterval:m(o.setInterval)})},function(e,t,n){var r=n(1).TypeError;e.exports=function(e,t){if(e<t)throw r("Not enough arguments");return e}},function(e,t,n){var r=n(1);e.exports=r.Promise},function(e,t,n){var r=n(34),o=n(345),i=n(18)("species");e.exports=function(e,t){var n,a=r(e).constructor;return void 0===a||null==(n=r(a)[i])?t:o(n)}},function(e,t,n){var r,o,i,a,s=n(1),c=n(56),u=n(70),l=n(21),p=n(26),m=n(11),f=n(168),d=n(90),g=n(127),h=n(185),y=n(189),v=n(106),b=s.setImmediate,T=s.clearImmediate,S=s.process,x=s.Dispatch,k=s.Function,M=s.MessageChannel,w=s.String,_=0,C={};try{r=s.location}catch(e){}var P=function(e){if(p(C,e)){var t=C[e];delete C[e],t()}},I=function(e){return function(){P(e)}},A=function(e){P(e.data)},E=function(e){s.postMessage(w(e),r.protocol+"//"+r.host)};b&&T||(b=function(e){h(arguments.length,1);var t=l(e)?e:k(e),n=d(arguments,1);return C[++_]=function(){c(t,void 0,n)},o(_),_},T=function(e){delete C[e]},v?o=function(e){S.nextTick(I(e))}:x&&x.now?o=function(e){x.now(I(e))}:M&&!y?(a=(i=new M).port2,i.port1.onmessage=A,o=u(a.postMessage,a)):s.addEventListener&&l(s.postMessage)&&!s.importScripts&&r&&"file:"!==r.protocol&&!m(E)?(o=E,s.addEventListener("message",A,!1)):o="onreadystatechange"in g("script")?function(e){f.appendChild(g("script")).onreadystatechange=function(){f.removeChild(this),P(e)}}:function(e){setTimeout(I(e),0)}),e.exports={set:b,clear:T}},function(e,t,n){var r=n(45);e.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},function(e,t,n){var r=n(34),o=n(27),i=n(93);e.exports=function(e,t){if(r(e),o(t)&&t.constructor===e)return t;var n=i.f(e);return(0,n.resolve)(t),n.promise}},function(e,t){e.exports=function e(t,n){"use strict";var r,o,i=/(^([+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?)?$|^0x[0-9a-f]+$|\d+)/gi,a=/(^[ ]*|[ ]*$)/g,s=/(^([\w ]+,?[\w ]+)?[\w ]+,?[\w ]+\d+:\d+(:\d+)?[\w ]?|^\d{1,4}[\/\-]\d{1,4}[\/\-]\d{1,4}|^\w+, \w+ \d+, \d{4})/,c=/^0x[0-9a-f]+$/i,u=/^0/,l=function(t){return e.insensitive&&(""+t).toLowerCase()||""+t},p=l(t).replace(a,"")||"",m=l(n).replace(a,"")||"",f=p.replace(i,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),d=m.replace(i,"\0$1\0").replace(/\0$/,"").replace(/^\0/,"").split("\0"),g=parseInt(p.match(c),16)||1!==f.length&&p.match(s)&&Date.parse(p),h=parseInt(m.match(c),16)||g&&m.match(s)&&Date.parse(m)||null;if(h){if(g<h)return-1;if(g>h)return 1}for(var y=0,v=Math.max(f.length,d.length);y<v;y++){if(r=!(f[y]||"").match(u)&&parseFloat(f[y])||f[y]||0,o=!(d[y]||"").match(u)&&parseFloat(d[y])||d[y]||0,isNaN(r)!==isNaN(o))return isNaN(r)?1:-1;if(typeof r!=typeof o&&(r+="",o+=""),r<o)return-1;if(r>o)return 1}return 0}},function(e,t,n){e.exports=n(369)},function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){e.exports=n(372)},function(e,t,n){var r=n(374),o=n(194),i=n(193);e.exports=function(e,t){var n;if(e){if("string"==typeof e)return i(e,t);var a=r(n=Object.prototype.toString.call(e)).call(n,8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?o(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(e,t):void 0}},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(6),o=n(77),i=n(3),a=n(12),s=n(17),c=n(10),u=n(4),l=n(53),p=n(0),m=n(15),f=n(41),d={};function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};p.merge(this,{options:e,debug:!1,api:"log",style:"color:blue;",log:p.emptyFunc,info:p.emptyFunc,warn:p.emptyFunc,error:p.emptyFunc}),this.prefix=e.prefix||"",this.localEnable=d.enable&&e.dbLog&&e.account,this.setDebug(e.debug),this.localEnable&&(this._local=new d(e.account,e.expire),this._local.logError=this.error)}d=n(395);var h=g.prototype,y=["Chrome","Safari","Firefox"];h.setDebug=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this;if(t.debug=e,e.style&&(t.style=e.style),p.exist(console))if(t.debug){var n=console;t.debug=function(){var e=t.formatArgsAndSave(arguments,"debug");-1!==r(y).call(y,l.name)&&p.isString(e[0])&&(e[0]="%c"+e[0],o(e).call(e,1,0,t.style)),t._log("debug",e)},t.log=function(){var e=t.formatArgsAndSave(arguments,"log");-1!==r(y).call(y,l.name)&&p.isString(e[0])&&(e[0]="%c"+e[0],o(e).call(e,1,0,t.style)),t._log("log",e)},t.info=function(){var e=t.formatArgsAndSave(arguments,"info");-1!==r(y).call(y,l.name)&&p.isString(e[0])&&(e[0]="%c"+e[0],o(e).call(e,1,0,t.style)),t._log("info",e)},t.warn=function(){var e=t.formatArgsAndSave(arguments,"warn");-1!==r(y).call(y,l.name)&&p.isString(e[0])&&(e[0]="%c"+e[0],o(e).call(e,1,0,t.style)),t._log("warn",e)},t.error=function(){var e=t.formatArgsAndSave(arguments,"error");-1!==r(y).call(y,l.name)&&p.isString(e[0])&&(e[0]="%c"+e[0],o(e).call(e,1,0,t.style)),t._log("error",e)},t.options.logFunc?t._log=function(){}:t._log=function(e,r){if(n[e])try{n[e].apply?t.chrome(e,r):t.ie(e,r)}catch(e){}},t.chrome=function(e,o){-1!==r(y).call(y,l.name)?n[e].apply(n,o):t.ie(e,o)},t.ie=function(e,t){i(t).call(t,(function(t){n[e](a(t,null,4))}))}}else if(m.isRN||this.localEnable||this.options.logFunc){var s=!m.isRN&&!this.localEnable;t.log=function(){t.formatArgsAndSave(arguments,"log",s)},t.info=function(){t.formatArgsAndSave(arguments,"info",s)},t.warn=function(){t.formatArgsAndSave(arguments,"warn",s)},t.error=function(){t.formatArgsAndSave(arguments,"error",s)}}},h.setLogDisabled=function(){this.localEnable=!1,this.log=function(){},this.info=function(){},this.warn=function(){},this.error=function(){}},h.formatArgsAndSave=function(e,t,n){var r;e=s([]).call(e,0);var u=new Date,l=v(u.getMonth()+1)+"-"+v(u.getDate())+" "+v(u.getHours())+":"+v(u.getMinutes())+":"+v(u.getSeconds())+":"+v(u.getMilliseconds(),3),m=c(r="[NIM LOG ".concat(l," ")).call(r,this.prefix.toUpperCase(),"]  "),f="";p.isString(e[0])?e[0]=m+e[0]:o(e).call(e,0,0,m),i(e).call(e,(function(t,n){p.isArray(t)||p.isObject(t)?(e[n]=p.simpleClone(t),f+=a(e[n])+" "):f+=t+" "}));var d=this.options.logFunc;return d&&p.isFunction(d[t])&&d[t].apply(d,e),!n&&this.writeLocalLog(f,t,+u),e},h.writeLocalLog=m.isRN?function(e,t){if(!(f.rnfs&&f.rnfs.writeFile&&f.rnfs.appendFile&&f.rnfs.DocumentDirectoryPath))return;if(!/error|warn|info/.test(t))return;var n,r=f.rnfs,o=r.size/2-256;function i(e){return f.rnfs.DocumentDirectoryPath+"/nimlog_"+e+".log"}e+="\r\n",r.nimPromise=r.nimPromise.then((function(){return n=i(r.nimIndex),r.exists(n)})).then((function(t){return t?r.appendFile(n,e):r.writeFile(n,e)})).then((function(){return r.stat(n)})).then((function(e){if(e.size>o)return r.nimIndex++,r.nimIndex>1&&(r.nimIndex=r.nimIndex%2),r.unlink(i(r.nimIndex)).catch((function(e){return u.resolve()}))})).catch((function(){}))}:m.isBrowser?function(e,t,n){this._local&&this._local.saveLog({log:e,level:t,time:n})}:function(){};var v=function(e,t){t=t||2;for(var n=""+e;n.length<t;)n="0"+n;return n};e.exports=g},function(e,t,n){e.exports=n(404)},function(e,t,n){var r=n(78),o=n(6),i=n(77),a=n(94),s=n(12),c=n(43),u=c.getGlobal(),l={},p=u.name||"_parent",m=[],f=[];l.addMsgListener=function(e){m.push(e)};var d,g,h,y,v=(d=/^([\w]+?:\/\/.*?(?=\/|$))/i,function(e){return e=e||"",d.test(e)?RegExp.$1:"*"}),b=function(){var e,t,n=r(e=unescape(u.name||"")).call(e);if(n&&0===o(n).call(n,"MSG|")){u.name="";var i=c.string2object(n.replace("MSG|",""),"|"),a=(i.origin||"").toLowerCase();a&&"*"!==a&&0!==o(t=location.href.toLowerCase()).call(t,a)||function(e){for(var t=0,n=m.length;t<n;t++)try{m[t].call(null,e)}catch(e){}}({data:JSON.parse(i.data||"null"),source:u.frames[i.self]||i.self,origin:v(i.ref||("undefined"==typeof document?"":document.referrer))})}},T=(h=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return!0;return!1},function(){if(f.length){g=[];for(var e,t=f.length-1;t>=0;t--)e=f[t],h(g,e.w)||(g.push(e.w),i(f).call(f,t,1),e.w.name=e.d);g=null}}),S=l.startTimer=(y=!1,function(){y||(y=!0,u.postMessage||(a(T,100),a(b,20)))});l.postMessage=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(c.fillUndef(t,{origin:"*",source:p}),u.postMessage){var n=t.data;u.FormData||(n=s(n)),e.postMessage(n,t.origin)}else{if(S(),c.isObject(t)){var r={};r.origin=t.origin||"",r.ref=location.href,r.self=t.source,r.data=s(t.data),t="MSG|"+c.object2string(r,"|",!0)}f.unshift({w:e,d:escape(t)})}},e.exports=l},function(e,t,n){var r=n(0),o={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},i={genResponseBody:function(e){return o[e=e||"file"]},parseResponse:function(e,t){r.notundef(e.size)&&(e.size=+e.size),r.notundef(e.w)&&(e.w=+e.w),r.notundef(e.h)&&(e.h=+e.h),r.notundef(e.dur)&&(e.dur=+e.dur);var n=e.orientation;if(r.notundef(n)&&(delete e.orientation,t&&("right, top"===n||"left, bottom"===n))){var o=e.w;e.w=e.h,e.h=o}return e}};e.exports=i},function(e,t,n){var r=n(6),o=n(0),i={fromDataURL:function(e){var t,n,i=o.getGlobal();n=r(t=e.split(",")[0]).call(t,"base64")>=0?i.atob(e.split(",")[1]):i.decodeURIComponent(e.split(",")[1]);for(var a=e.split(",")[0].split(":")[1].split(";")[0],s=new Uint8Array(n.length),c=0;c<n.length;c++)s[c]=n.charCodeAt(c);return new i.Blob([s],{type:a})}};e.exports=i},function(e,t,n){var r=n(9),o=n(8),i=n(5),a=n(0),s={stripmeta:0,blur:2,quality:3,crop:4,rotate:5,thumbnail:7,interlace:9},c={0:"stripmeta",1:"type",2:"blur",3:"quality",4:"crop",5:"rotate",6:"pixel",7:"thumbnail",8:"watermark",9:"interlace",10:"tmp"};function u(e){a.verifyOptions(e,"type","image::ImageOp"),a.verifyParamValid("type",e.type,u.validTypes,"image::ImageOp"),a.merge(this,e),this.type=s[e.type]}u.validTypes=r(s),u.reverse=function(e){var t=a.copy(e);return t.type=c[t.type],t},u.reverseImageOps=function(e){return o(e).call(e,(function(e){return i(u).call(u,e)}))},e.exports=u},function(e,t,n){var r=n(3),o=n(9),i={1:"ROOM_CLOSE",2:"ROOM_JOIN",3:"INVITE",4:"CANCEL_INVITE",5:"REJECT",6:"ACCEPT",7:"LEAVE",8:"CONTROL"},a={1:"accid",2:"uid",3:"createTime",4:"expireTime",5:"web_uid"},s={10404:"ROOM_NOT_EXISTS",10405:"ROOM_HAS_EXISTS",10406:"ROOM_MEMBER_NOT_EXISTS",10407:"ROOM_MEMBER_HAS_EXISTS",10408:"INVITE_NOT_EXISTS",10409:"INVITE_HAS_REJECT",10410:"INVITE_HAS_ACCEPT",10201:"PEER_NIM_OFFLINE",10202:"PEER_PUSH_OFFLINE",10419:"ROOM_MEMBER_EXCEED",10420:"ROOM_MEMBER_HAS_EXISTS_OTHER_CLIENT",10417:"UID_CONFLICT"};e.exports={parseAvSignalType:function(e){return i[e]||e},parseAvSignalMember:function(e){var t,n={};return r(t=o(e)).call(t,(function(t){n[a[t]]=e[t]})),n},parseAvSignalError:function(e){return e.message=s[e.code]||e.message||e,e}}},function(module,exports,__webpack_require__){(function(global,module){var __WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;/*! Socket.IO.js build:0.9.11, development. Copyright(c) 2011 LearnBoost <<EMAIL>> MIT Licensed */function getGlobal(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:void 0!==global?global:{}}var root=getGlobal(),io=module.exports;void 0===root.location&&(root.location=null),root.io?module&&(module.exports=io=root.io):root.io=io,function(){!function(e,t){var n=e;n.version="0.9.11",n.protocol=1,n.transports=[],n.j=[],n.sockets={},n.connect=function(e,r){var o,i,a=n.util.parseUri(e);t&&t.location&&(a.protocol=a.protocol||t.location.protocol.slice(0,-1),a.host=a.host||(t.document?t.document.domain:t.location.hostname),a.port=a.port||t.location.port),o=n.util.uniqueUri(a);var s={host:a.ipv6uri?"["+a.host+"]":a.host,secure:"https"===a.protocol,port:a.port||("https"===a.protocol?443:80),query:a.query||""};return n.util.merge(s,r),!s["force new connection"]&&n.sockets[o]||(i=new n.Socket(s)),!s["force new connection"]&&i&&(n.sockets[o]=i),(i=i||n.sockets[o]).of(a.path.length>1?a.path:"")}}(module.exports,root),function(e,t){var n=e.util={},r=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,o=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];n.parseUri=function(e){var t=e,n=e.indexOf("["),i=e.indexOf("]");-1!=n&&-1!=i&&(e=e.substring(0,n)+e.substring(n,i).replace(/:/g,";")+e.substring(i,e.length));for(var a=r.exec(e||""),s={},c=14;c--;)s[o[c]]=a[c]||"";return-1!=n&&-1!=i&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s},n.uniqueUri=function(e){var n=e.protocol,r=e.host,o=e.port;return"document"in t&&t.document?(r=r||document.domain,o=o||("https"==n&&"https:"!==document.location.protocol?443:document.location.port)):(r=r||"localhost",o||"https"!=n||(o=443)),(n||"http")+"://"+r+":"+(o||80)},n.query=function(e,t){var r=n.chunkQuery(e||""),o=[];for(var i in n.merge(r,n.chunkQuery(t||"")),r)r.hasOwnProperty(i)&&o.push(i+"="+r[i]);return o.length?"?"+o.join("&"):""},n.chunkQuery=function(e){for(var t,n={},r=e.split("&"),o=0,i=r.length;o<i;++o)(t=r[o].split("="))[0]&&(n[t[0]]=t[1]);return n};var i=!1;n.load=function(e){if("undefined"!=typeof document&&document&&"complete"===document.readyState||i)return e();n.on(t,"load",e,!1)},n.on=function(e,t,n,r){e.attachEvent?e.attachEvent("on"+t,n):e.addEventListener&&e.addEventListener(t,n,r)},n.request=function(e){if(e&&"undefined"!=typeof XDomainRequest&&!n.ua.hasCORS)return new XDomainRequest;if("undefined"!=typeof XMLHttpRequest&&(!e||n.ua.hasCORS))return new XMLHttpRequest;if(!e)try{return new(root[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(e){}return null},void 0!==root&&n.load((function(){i=!0})),n.defer=function(e){if(!n.ua.webkit||"undefined"!=typeof importScripts)return e();n.load((function(){setTimeout(e,100)}))},n.merge=function(e,t,r,o){var i,a=o||[],s=void 0===r?2:r;for(i in t)t.hasOwnProperty(i)&&n.indexOf(a,i)<0&&("object"==typeof e[i]&&s?n.merge(e[i],t[i],s-1,a):(e[i]=t[i],a.push(t[i])));return e},n.mixin=function(e,t){n.merge(e.prototype,t.prototype)},n.inherit=function(e,t){function n(){}n.prototype=t.prototype,e.prototype=new n},n.isArray=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},n.intersect=function(e,t){for(var r=[],o=e.length>t.length?e:t,i=e.length>t.length?t:e,a=0,s=i.length;a<s;a++)~n.indexOf(o,i[a])&&r.push(i[a]);return r},n.indexOf=function(e,t,n){var r=e.length;for(n=n<0?n+r<0?0:n+r:n||0;n<r&&e[n]!==t;n++);return r<=n?-1:n},n.toArray=function(e){for(var t=[],n=0,r=e.length;n<r;n++)t.push(e[n]);return t},n.ua={},n.ua.hasCORS="undefined"!=typeof XMLHttpRequest&&function(){try{var e=new XMLHttpRequest}catch(e){return!1}return null!=e.withCredentials}(),n.ua.webkit="undefined"!=typeof navigator&&/webkit/i.test(navigator.userAgent),n.ua.iDevice="undefined"!=typeof navigator&&/iPad|iPhone|iPod/i.test(navigator.userAgent)}(void 0!==io?io:module.exports,root),function(e,t){function n(){}e.EventEmitter=n,n.prototype.on=function(e,n){return this.$events||(this.$events={}),this.$events[e]?t.util.isArray(this.$events[e])?this.$events[e].push(n):this.$events[e]=[this.$events[e],n]:this.$events[e]=n,this},n.prototype.addListener=n.prototype.on,n.prototype.once=function(e,t){var n=this;function r(){n.removeListener(e,r),t.apply(this,arguments)}return r.listener=t,this.on(e,r),this},n.prototype.removeListener=function(e,n){if(this.$events&&this.$events[e]){var r=this.$events[e];if(t.util.isArray(r)){for(var o=-1,i=0,a=r.length;i<a;i++)if(r[i]===n||r[i].listener&&r[i].listener===n){o=i;break}if(o<0)return this;r.splice(o,1),r.length||delete this.$events[e]}else(r===n||r.listener&&r.listener===n)&&delete this.$events[e]}return this},n.prototype.removeAllListeners=function(e){return void 0===e?(this.$events={},this):(this.$events&&this.$events[e]&&(this.$events[e]=null),this)},n.prototype.listeners=function(e){return this.$events||(this.$events={}),this.$events[e]||(this.$events[e]=[]),t.util.isArray(this.$events[e])||(this.$events[e]=[this.$events[e]]),this.$events[e]},n.prototype.emit=function(e){if(!this.$events)return!1;var n=this.$events[e];if(!n)return!1;var r=Array.prototype.slice.call(arguments,1);if("function"==typeof n)n.apply(this,r);else{if(!t.util.isArray(n))return!1;for(var o=n.slice(),i=0,a=o.length;i<a;i++)o[i].apply(this,r)}return!0}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(exports,nativeJSON){"use strict";if(nativeJSON&&nativeJSON.parse)return exports.JSON={parse:nativeJSON.parse,stringify:nativeJSON.stringify};var JSON=exports.JSON={};function f(e){return e<10?"0"+e:e}function date(e,t){return isFinite(e.valueOf())?e.getUTCFullYear()+"-"+f(e.getUTCMonth()+1)+"-"+f(e.getUTCDate())+"T"+f(e.getUTCHours())+":"+f(e.getUTCMinutes())+":"+f(e.getUTCSeconds())+"Z":null}var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,(function(e){var t=meta[e];return"string"==typeof t?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))+'"':'"'+e+'"'}function str(e,t){var n,r,o,i,a,s=gap,c=t[e];switch(c instanceof Date&&(c=date(e)),"function"==typeof rep&&(c=rep.call(t,e,c)),typeof c){case"string":return quote(c);case"number":return isFinite(c)?String(c):"null";case"boolean":case"null":return String(c);case"object":if(!c)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(c)){for(i=c.length,n=0;n<i;n+=1)a[n]=str(n,c)||"null";return o=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+s+"]":"["+a.join(",")+"]",gap=s,o}if(rep&&"object"==typeof rep)for(i=rep.length,n=0;n<i;n+=1)"string"==typeof rep[n]&&(o=str(r=rep[n],c))&&a.push(quote(r)+(gap?": ":":")+o);else for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(o=str(r,c))&&a.push(quote(r)+(gap?": ":":")+o);return o=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+s+"}":"{"+a.join(",")+"}",gap=s,o}}JSON.stringify=function(e,t,n){var r;if(gap="",indent="","number"==typeof n)for(r=0;r<n;r+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=t,t&&"function"!=typeof t&&("object"!=typeof t||"number"!=typeof t.length))throw new Error("socket.io:: replacer cannot JSON.stringify");return str("",{"":e})},JSON.parse=function(text,reviver){var j;function walk(e,t){var n,r,o=e[t];if(o&&"object"==typeof o)for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(void 0!==(r=walk(o,n))?o[n]=r:delete o[n]);return reviver.call(e,t,o)}if(text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,(function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}))),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("socket.io:: reviver cannot JSON.parse")}}(void 0!==io?io:module.exports,"undefined"!=typeof JSON?JSON:void 0),function(e,t){var n=e.parser={},r=n.packets=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],o=n.reasons=["transport not supported","client not handshaken","unauthorized"],i=n.advice=["reconnect"],a=t.JSON,s=t.util.indexOf;n.encodePacket=function(e){var t=s(r,e.type),n=e.id||"",c=e.endpoint||"",u=e.ack,l=null;switch(e.type){case"error":var p=e.reason?s(o,e.reason):"",m=e.advice?s(i,e.advice):"";""===p&&""===m||(l=p+(""!==m?"+"+m:""));break;case"message":""!==e.data&&(l=e.data);break;case"event":var f={name:e.name};e.args&&e.args.length&&(f.args=e.args),l=a.stringify(f);break;case"json":l=a.stringify(e.data);break;case"connect":e.qs&&(l=e.qs);break;case"ack":l=e.ackId+(e.args&&e.args.length?"+"+a.stringify(e.args):"")}var d=[t,n+("data"==u?"+":""),c];return null!=l&&d.push(l),d.join(":")},n.encodePayload=function(e){var t="";if(1==e.length)return e[0];for(var n=0,r=e.length;n<r;n++){t+="�"+e[n].length+"�"+e[n]}return t};var c=/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/;n.decodePacket=function(e){if(!(s=e.match(c)))return{};var t=s[2]||"",n=(e=s[5]||"",{type:r[s[1]],endpoint:s[4]||""});switch(t&&(n.id=t,s[3]?n.ack="data":n.ack=!0),n.type){case"error":var s=e.split("+");n.reason=o[s[0]]||"",n.advice=i[s[1]]||"";break;case"message":n.data=e||"";break;case"event":try{var u=a.parse(e);n.name=u.name,n.args=u.args}catch(e){}n.args=n.args||[];break;case"json":try{n.data=a.parse(e)}catch(e){}break;case"connect":n.qs=e||"";break;case"ack":if((s=e.match(/^([0-9]+)(\+)?(.*)/))&&(n.ackId=s[1],n.args=[],s[3]))try{n.args=s[3]?a.parse(s[3]):[]}catch(e){}}return n},n.decodePayload=function(e){var t=function(e,t){for(var n=0,r=e;r<t.length;r++){if("�"==t.charAt(r))return n;n++}return n};if("�"==e.charAt(0)){for(var r=[],o=1,i="";o<e.length;o++)if("�"==e.charAt(o)){var a=e.substr(o+1).substr(0,i);if("�"!=e.charAt(o+1+Number(i))&&o+1+Number(i)!=e.length){var s=Number(i);l=t(o+s+1,e),a=e.substr(o+1).substr(0,s+l),o+=l}r.push(n.decodePacket(a)),o+=Number(i)+1,i=""}else i+=e.charAt(o);return r}return[n.decodePacket(e)]}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t){function n(e,t){this.socket=e,this.sessid=t}e.Transport=n,t.util.mixin(n,t.EventEmitter),n.prototype.heartbeats=function(){return!0},n.prototype.onData=function(e){if(this!==this.socket.transport)return this;if(this.clearCloseTimeout(),(this.socket.connected||this.socket.connecting||this.socket.reconnecting)&&this.setCloseTimeout(),""!==e){var n=t.parser.decodePayload(e);if(n&&n.length)for(var r=0,o=n.length;r<o;r++)this.onPacket(n[r])}return this},n.prototype.onPacket=function(e){return this.socket.setHeartbeatTimeout(),"heartbeat"==e.type?this.onHeartbeat():("connect"==e.type&&""==e.endpoint&&this.onConnect(),"error"==e.type&&"reconnect"==e.advice&&(this.isOpen=!1),this.socket.onPacket(e),this)},n.prototype.setCloseTimeout=function(){if(!this.closeTimeout){var e=this;this.closeTimeout=setTimeout((function(){e.onDisconnect()}),this.socket.closeTimeout)}},n.prototype.onDisconnect=function(){return this.isOpen&&this.close(),this.clearTimeouts(),this.socket?(this.socket.transport===this?this.socket.onDisconnect():this.socket.setBuffer(!1),this):this},n.prototype.onConnect=function(){return this.socket.onConnect(),this},n.prototype.clearCloseTimeout=function(){this.closeTimeout&&(clearTimeout(this.closeTimeout),this.closeTimeout=null)},n.prototype.clearTimeouts=function(){this.clearCloseTimeout(),this.reopenTimeout&&clearTimeout(this.reopenTimeout)},n.prototype.packet=function(e){this.send(t.parser.encodePacket(e))},n.prototype.onHeartbeat=function(e){this.packet({type:"heartbeat"})},n.prototype.onOpen=function(){this.isOpen=!0,this.clearCloseTimeout(),this.socket.onOpen()},n.prototype.onClose=function(){this.isOpen=!1,this.socket.transport===this?this.socket.onClose():this.socket.setBuffer(!1),this.onDisconnect(),this.onDisconnectDone instanceof Function&&this.onDisconnectDone(null),this.onConnectionOver instanceof Function&&this.onConnectionOver(null)},n.prototype.onDisconnectDone=function(){},n.prototype.onConnectionOver=function(){},n.prototype.prepareUrl=function(){var e=this.socket.options;return this.scheme()+"://"+e.host+":"+e.port+"/"+e.resource+"/"+t.protocol+"/"+this.name+"/"+this.sessid},n.prototype.ready=function(e,t){t.call(this)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t,n){function r(e){if(this.options={port:80,secure:!1,document:"document"in n&&document,resource:"socket.io",transports:e.transports||t.transports,"connect timeout":1e4,"try multiple transports":!0,reconnect:!0,"reconnection delay":500,"reconnection limit":1/0,"reopen delay":3e3,"max reconnection attempts":10,"sync disconnect on unload":!1,"auto connect":!0,"flash policy port":10843,manualFlush:!1},t.util.merge(this.options,e),this.connected=!1,this.open=!1,this.connecting=!1,this.reconnecting=!1,this.namespaces={},this.buffer=[],this.doBuffer=!1,this.options["sync disconnect on unload"]&&(!this.isXDomain()||t.util.ua.hasCORS)){var r=this;t.util.on(n,"beforeunload",(function(){r.disconnectSync()}),!1)}this.options["auto connect"]&&this.connect()}function o(){}e.Socket=r,t.util.mixin(r,t.EventEmitter),r.prototype.of=function(e){return this.namespaces[e]||(this.namespaces[e]=new t.SocketNamespace(this,e),""!==e&&this.namespaces[e].packet({type:"connect"})),this.namespaces[e]},r.prototype.publish=function(){var e;for(var t in this.emit.apply(this,arguments),this.namespaces)this.namespaces.hasOwnProperty(t)&&(e=this.of(t)).$emit.apply(e,arguments)},r.prototype.handshake=function(e){var n=this,r=this.options;function i(t){t instanceof Error?(n.connecting=!1,n.onError(t.message)):e.apply(null,t.split(":"))}var a=["http"+(r.secure?"s":"")+":/",r.host+":"+r.port,r.resource,t.protocol,t.util.query(this.options.query,"t="+ +new Date)].join("/");if(this.isXDomain()&&!t.util.ua.hasCORS&&"undefined"!=typeof document&&document){var s=document.getElementsByTagName("script")[0],c=document.createElement("script");c.src=a+"&jsonp="+t.j.length,c.onreadystatechange=function(){"loaded"==this.readyState&&c.parentNode&&(c.parentNode.removeChild(c),n.connecting=!1,!n.reconnecting&&n.onError("Server down or port not open"),n.publish("handshake_failed"))},s.parentNode.insertBefore(c,s),t.j.push((function(e){i(e),c.parentNode.removeChild(c)}))}else{var u=t.util.request();u.open("GET",a,!0),u.timeout=1e4,this.isXDomain()&&(u.withCredentials=!0),u.onreadystatechange=function(){4==u.readyState&&(u.onreadystatechange=o,200==u.status?i(u.responseText):403==u.status?(n.connecting=!1,n.onError(u.responseText),n.publish("handshake_failed")):(n.connecting=!1,!n.reconnecting&&n.onError(u.responseText),n.publish("handshake_failed")))},u.ontimeout=function(e){n.connecting=!1,!n.reconnecting&&n.onError(u.responseText),n.publish("handshake_failed")},u.send(null)}},r.prototype.connect=function(e){if(this.connecting)return this;var n=this;return n.connecting=!0,this.handshake((function(r,o,i,a){n.sessionid=r,n.closeTimeout=1e3*i,n.heartbeatTimeout=1e3*o,n.transports||(n.transports=n.origTransports=a?t.util.intersect(a.split(","),n.options.transports):n.options.transports),n.setHeartbeatTimeout(),n.once("connect",(function(){clearTimeout(n.connectTimeoutTimer),n.connectTimeoutTimer=null,e&&"function"==typeof e&&e()})),n.doConnect()})),this},r.prototype.doConnect=function(){var e=this;if(e.transport&&e.transport.clearTimeouts(),e.transport=e.getTransport(e.transports),!e.transport)return e.publish("connect_failed");e.transport.ready(e,(function(){e.connecting=!0,e.publish("connecting",e.transport.name),e.transport.open(),e.options["connect timeout"]&&(e.connectTimeoutTimer&&clearTimeout(e.connectTimeoutTimer),e.connectTimeoutTimer=setTimeout(e.tryNextTransport.bind(e),e.options["connect timeout"]))}))},r.prototype.getTransport=function(e){for(var n,r=e||this.transports,o=0;n=r[o];o++){if(t.Transport[n]&&t.Transport[n].check(this)&&(!this.isXDomain()||t.Transport[n].xdomainCheck(this)))return new t.Transport[n](this,this.sessionid)}return null},r.prototype.tryNextTransport=function(){if(!this.connected&&(this.connecting=!1,this.options["try multiple transports"])){for(var e=this.transports;e.length>0&&e.splice(0,1)[0]!=this.transport.name;);e.length?this.doConnect():this.publish("connect_failed")}},r.prototype.setHeartbeatTimeout=function(){if(clearTimeout(this.heartbeatTimeoutTimer),!this.transport||this.transport.heartbeats()){var e=this;this.heartbeatTimeoutTimer=setTimeout((function(){e.transport&&e.transport.onClose()}),this.heartbeatTimeout)}},r.prototype.packet=function(e){return this.connected&&!this.doBuffer?this.transport.packet(e):this.buffer.push(e),this},r.prototype.setBuffer=function(e){this.doBuffer=e,!e&&this.connected&&this.buffer.length&&(this.options.manualFlush||this.flushBuffer())},r.prototype.flushBuffer=function(){this.transport.payload(this.buffer),this.buffer=[]},r.prototype.disconnect=function(){return(this.connected||this.connecting)&&(this.open&&this.of("").packet({type:"disconnect"}),this.onDisconnect("booted")),this},r.prototype.disconnectSync=function(){var e=t.util.request(),n=["http"+(this.options.secure?"s":"")+":/",this.options.host+":"+this.options.port,this.options.resource,t.protocol,"",this.sessionid].join("/")+"/?disconnect=1";e.open("GET",n,!1),e.send(null),this.onDisconnect("booted")},r.prototype.isXDomain=function(){var e=n&&n.location||{},t=e.port||("https:"==e.protocol?443:80);return this.options.host!==e.hostname||this.options.port!=t},r.prototype.onConnect=function(){this.connected||(this.connected=!0,this.connecting=!1,this.doBuffer||this.setBuffer(!1),this.emit("connect"))},r.prototype.onOpen=function(){this.open=!0},r.prototype.onClose=function(){this.open=!1,clearTimeout(this.heartbeatTimeoutTimer)},r.prototype.onPacket=function(e){this.of(e.endpoint).onPacket(e)},r.prototype.onError=function(e){e&&e.advice&&"reconnect"===e.advice&&(this.connected||this.connecting)&&(this.disconnect(),this.options.reconnect&&this.reconnect()),this.publish("error",e&&e.reason?e.reason:e)},r.prototype.onDisconnect=function(e){var t=this.connected,n=this.connecting;this.connected=!1,this.connecting=!1,this.open=!1,(t||n)&&(this.transport.close(),this.transport.clearTimeouts(),t&&(this.publish("disconnect",e),"booted"!=e&&this.options.reconnect&&!this.reconnecting&&this.reconnect()),n&&(this.connectTimeoutTimer&&clearTimeout(this.connectTimeoutTimer),this.tryNextTransport()))},r.prototype.reconnect=function(){this.reconnecting=!0,this.reconnectionAttempts=0,this.reconnectionDelay=this.options["reconnection delay"];var e=this,t=this.options["max reconnection attempts"],n=this.options["try multiple transports"],r=this.options["reconnection limit"];function o(){if(e.connected){for(var t in e.namespaces)e.namespaces.hasOwnProperty(t)&&""!==t&&e.namespaces[t].packet({type:"connect"});e.publish("reconnect",e.transport.name,e.reconnectionAttempts)}clearTimeout(e.reconnectionTimer),e.removeListener("connect_failed",i),e.removeListener("connect",i),e.reconnecting=!1,delete e.reconnectionAttempts,delete e.reconnectionDelay,delete e.reconnectionTimer,delete e.redoTransports,e.options["try multiple transports"]=n}function i(){if(e.reconnecting)return e.connected?o():e.connecting&&e.reconnecting?e.reconnectionTimer=setTimeout(i,1e3):void(e.reconnectionAttempts++>=t?e.redoTransports?(e.publish("reconnect_failed"),o()):(e.on("connect_failed",i),e.options["try multiple transports"]=!0,e.transports=e.origTransports,e.transport=e.getTransport(),e.redoTransports=!0,e.connect()):(e.reconnectionDelay<r&&(e.reconnectionDelay*=2),e.connect(),e.publish("reconnecting",e.reconnectionDelay,e.reconnectionAttempts),e.reconnectionTimer=setTimeout(i,e.reconnectionDelay)))}this.options["try multiple transports"]=!1,this.reconnectionTimer=setTimeout(i,this.reconnectionDelay),this.on("connect",i)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t){function n(e,t){this.socket=e,this.name=t||"",this.flags={},this.json=new r(this,"json"),this.ackPackets=0,this.acks={}}function r(e,t){this.namespace=e,this.name=t}e.SocketNamespace=n,t.util.mixin(n,t.EventEmitter),n.prototype.$emit=t.EventEmitter.prototype.emit,n.prototype.of=function(){return this.socket.of.apply(this.socket,arguments)},n.prototype.packet=function(e){return e.endpoint=this.name,this.socket.packet(e),this.flags={},this},n.prototype.send=function(e,t){var n={type:this.flags.json?"json":"message",data:e};return"function"==typeof t&&(n.id=++this.ackPackets,n.ack=!0,this.acks[n.id]=t),this.packet(n)},n.prototype.emit=function(e){var t=Array.prototype.slice.call(arguments,1),n=t[t.length-1],r={type:"event",name:e};return"function"==typeof n&&(r.id=++this.ackPackets,r.ack="data",this.acks[r.id]=n,t=t.slice(0,t.length-1)),r.args=t,this.packet(r)},n.prototype.disconnect=function(){return""===this.name?this.socket.disconnect():(this.packet({type:"disconnect"}),this.$emit("disconnect")),this},n.prototype.onPacket=function(e){var n=this;function r(){n.packet({type:"ack",args:t.util.toArray(arguments),ackId:e.id})}switch(e.type){case"connect":this.$emit("connect");break;case"disconnect":""===this.name?this.socket.onDisconnect(e.reason||"booted"):this.$emit("disconnect",e.reason);break;case"message":case"json":var o=["message",e.data];"data"==e.ack?o.push(r):e.ack&&this.packet({type:"ack",ackId:e.id}),this.$emit.apply(this,o);break;case"event":o=[e.name].concat(e.args);"data"==e.ack&&o.push(r),this.$emit.apply(this,o);break;case"ack":this.acks[e.ackId]&&(this.acks[e.ackId].apply(this,e.args),delete this.acks[e.ackId]);break;case"error":console.error("SocketIO on packet error: ",e),e.advice?this.socket.onError(e):"unauthorized"===e.reason?this.$emit("connect_failed",e.reason):this.$emit("error",e.reason)}},r.prototype.send=function(){this.namespace.flags[this.name]=!0,this.namespace.send.apply(this.namespace,arguments)},r.prototype.emit=function(){this.namespace.flags[this.name]=!0,this.namespace.emit.apply(this.namespace,arguments)}}(void 0!==io?io:module.exports,void 0!==io?io:module.parent.exports),function(e,t,n){function r(e){t.Transport.apply(this,arguments)}e.websocket=r,t.util.inherit(r,t.Transport),r.prototype.name="websocket",r.prototype.open=function(){var e,r=t.util.query(this.socket.options.query),o=this;return e||(e=n.MozWebSocket||n.WebSocket),this.websocket=new e(this.prepareUrl()+r),this.websocket.onopen=function(){o.onOpen(),o.socket.setBuffer(!1)},this.websocket.onmessage=function(e){o.onData(e.data)},this.websocket.onclose=function(){o.socket.setBuffer(!0),o.onClose()},this.websocket.onerror=function(e){o.onError(e)},this},t.util.ua.iDevice?r.prototype.send=function(e){var t=this;return setTimeout((function(){t.websocket.send(e)}),0),this}:r.prototype.send=function(e){return this.websocket.send(e),this},r.prototype.payload=function(e){for(var t=0,n=e.length;t<n;t++)this.packet(e[t]);return this},r.prototype.close=function(){return this.websocket.close(),this},r.prototype.onError=function(e){this.socket.onError(e)},r.prototype.scheme=function(){return this.socket.options.secure?"wss":"ws"},r.check=function(){return"WebSocket"in n&&!("__addTask"in WebSocket)||"MozWebSocket"in n},r.xdomainCheck=function(){return!0},t.transports.push("websocket")}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t,n){function r(e){e&&(t.Transport.apply(this,arguments),this.sendBuffer=[])}function o(){}e.XHR=r,t.util.inherit(r,t.Transport),r.prototype.open=function(){return this.socket.setBuffer(!1),this.onOpen(),this.get(),this.setCloseTimeout(),this},r.prototype.payload=function(e){for(var n=[],r=0,o=e.length;r<o;r++)n.push(t.parser.encodePacket(e[r]));this.send(t.parser.encodePayload(n))},r.prototype.send=function(e){return this.post(e),this},r.prototype.post=function(e){var t=this;this.socket.setBuffer(!0),this.sendXHR=this.request("POST"),n.XDomainRequest&&this.sendXHR instanceof XDomainRequest?this.sendXHR.onload=this.sendXHR.onerror=function(){this.onload=o,t.socket.setBuffer(!1)}:this.sendXHR.onreadystatechange=function(){4==this.readyState&&(this.onreadystatechange=o,t.posting=!1,200==this.status?t.socket.setBuffer(!1):t.onClose())},this.sendXHR.send(e)},r.prototype.close=function(){return this.onClose(),this},r.prototype.request=function(e){var n=t.util.request(this.socket.isXDomain()),r=t.util.query(this.socket.options.query,"t="+ +new Date);if(n.open(e||"GET",this.prepareUrl()+r,!0),"POST"==e)try{n.setRequestHeader?n.setRequestHeader("Content-type","text/plain;charset=UTF-8"):n.contentType="text/plain"}catch(e){}return n},r.prototype.scheme=function(){return this.socket.options.secure?"https":"http"},r.check=function(e,r){try{var o=t.util.request(r),i=n.XDomainRequest&&o instanceof XDomainRequest,a=e&&e.options&&e.options.secure?"https:":"http:",s=n.location&&a!=n.location.protocol;if(o&&(!i||!s))return!0}catch(e){}return!1},r.xdomainCheck=function(e){return r.check(e,!0)}}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),function(e,t,n){function r(){t.Transport.XHR.apply(this,arguments)}function o(){}e["xhr-polling"]=r,t.util.inherit(r,t.Transport.XHR),t.util.merge(r,t.Transport.XHR),r.prototype.name="xhr-polling",r.prototype.heartbeats=function(){return!1},r.prototype.open=function(){return t.Transport.XHR.prototype.open.call(this),!1},r.prototype.get=function(){if(this.isOpen){var e=this;this.xhr=this.request(),n.XDomainRequest&&this.xhr instanceof XDomainRequest?(this.xhr.onload=function(){this.onload=o,this.onerror=o,e.retryCounter=1,e.onData(this.responseText),e.get()},this.xhr.onerror=function(){e.retryCounter++,!e.retryCounter||e.retryCounter>3?e.onClose():e.get()}):this.xhr.onreadystatechange=function(){4==this.readyState&&(this.onreadystatechange=o,200==this.status?(e.onData(this.responseText),e.get()):e.onClose())},this.xhr.send(null)}},r.prototype.onClose=function(){if(t.Transport.XHR.prototype.onClose.call(this),this.xhr){this.xhr.onreadystatechange=this.xhr.onload=this.xhr.onerror=o;try{this.xhr.abort()}catch(e){}this.xhr=null}},r.prototype.ready=function(e,n){var r=this;t.util.defer((function(){n.call(r)}))},t.transports.push("xhr-polling")}(void 0!==io?io.Transport:module.exports,void 0!==io?io:module.parent.exports,root),__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_RESULT__=function(){return io}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)}()}).call(this,__webpack_require__(49),__webpack_require__(462)(module))},function(e,t,n){var r=n(9),o=n(41);function i(){}i.typeMap={text:0,image:1,audio:2,video:3,geo:4,notification:5,file:6,tip:10,robot:11,g2:12,custom:100};var a=i.typeReverseMap={0:"text",1:"image",2:"audio",3:"video",4:"geo",5:"notification",6:"file",10:"tip",11:"robot",12:"g2",100:"custom"};i.validTypes=r(i.typeMap),i.setFlow=function(e,t){var n=t===e.from;n&&t===e.to&&(n=o.deviceId===e.fromDeviceId),e.flow=n?"out":"in","robot"===e.type&&e.content&&e.content.msgOut&&(e.flow="in")},i.getType=function(e){var t=e.type;return a[t]||t},e.exports=i},function(e,t,n){"use strict";e.exports=function(e,t,n,r){var o=self||window;try{try{var i;try{i=new o.Blob([e])}catch(t){(i=new(o.BlobBuilder||o.WebKitBlobBuilder||o.MozBlobBuilder||o.MSBlobBuilder)).append(e),i=i.getBlob()}var a=o.URL||o.webkitURL,s=a.createObjectURL(i),c=new o[t](s,n);return a.revokeObjectURL(s),c}catch(r){return new o[t]("data:application/javascript,".concat(encodeURIComponent(e)),n)}}catch(e){if(!r)throw Error("Inline worker is not supported");return new o[t](r,n)}}},function(e,t,n){n(222).polyfill(),n(15).isBrowser=!0},function(e,t,n){e.exports=n(279)},function(e,t,n){var r=n(335);n(59),e.exports=r},function(e,t,n){"use strict";var r=n(2),o=n(1),i=n(16),a=n(134),s=n(135),c=n(336),u=n(87),l=n(46),p=n(57),m=n(337),f=n(338),d=n(108),g=n(339),h=n(18),y=n(340),v=h("toStringTag"),b=o.Error,T=[].push,S=function(e,t){var n,r=arguments.length>2?arguments[2]:void 0,o=i(x,this);s?n=s(new b,o?a(this):x):(n=o?this:u(x),l(n,v,"Error")),void 0!==t&&l(n,"message",g(t)),y&&l(n,"stack",m(n.stack,1)),f(n,r);var c=[];return d(e,T,{that:c}),l(n,"errors",c),n};s?s(S,b):c(S,b,{name:!0});var x=S.prototype=u(b.prototype,{constructor:p(1,S),message:p(1,""),name:p(1,"AggregateError")});r({global:!0},{AggregateError:S})},function(e,t,n){"use strict";var r=n(2),o=n(31),i=n(39),a=n(93),s=n(116),c=n(108);r({target:"Promise",stat:!0},{allSettled:function(e){var t=this,n=a.f(t),r=n.resolve,u=n.reject,l=s((function(){var n=i(t.resolve),a=[],s=0,u=1;c(e,(function(e){var i=s++,c=!1;u++,o(n,t,e).then((function(e){c||(c=!0,a[i]={status:"fulfilled",value:e},--u||r(a))}),(function(e){c||(c=!0,a[i]={status:"rejected",reason:e},--u||r(a))}))})),--u||r(a)}));return l.error&&u(l.value),n.promise}})},function(e,t,n){"use strict";var r=n(2),o=n(39),i=n(36),a=n(31),s=n(93),c=n(116),u=n(108);r({target:"Promise",stat:!0},{any:function(e){var t=this,n=i("AggregateError"),r=s.f(t),l=r.resolve,p=r.reject,m=c((function(){var r=o(t.resolve),i=[],s=0,c=1,m=!1;u(e,(function(e){var o=s++,u=!1;c++,a(r,t,e).then((function(e){u||m||(m=!0,l(e))}),(function(e){u||m||(u=!0,i[o]=e,--c||p(new n(i,"No one promise resolved")))}))})),--c||p(new n(i,"No one promise resolved"))}));return m.error&&p(m.value),r.promise}})},function(e,t,n){(function(r){var o,i=n(28),a=n(17),s=n(30),c=n(4),u=n(3),l=n(50),p=n(10),m=n(9),f=n(19);!function(d,g){"use strict";var h,y=(d=void 0!==d?d:"undefined"!=typeof self?self:void 0!==r?r:{}).IDBKeyRange||d.webkitIDBKeyRange,v="readonly",b="readwrite",T=Object.prototype.hasOwnProperty,S=function(){if(!h&&!(h=d.indexedDB||d.webkitIndexedDB||d.mozIndexedDB||d.oIndexedDB||d.msIndexedDB||(null===d.indexedDB&&d.shimIndexedDB?d.shimIndexedDB:void 0)))throw"IndexedDB required";return h},x=function(e){return e},k=function(e){var t;return a(t=Object.prototype.toString.call(e)).call(t,8,-1).toLowerCase()},M=function(e){return"function"==typeof e},w=function(e){return"number"===k(e)},_=function(e){return"array"===k(e)},C=function(e){return void 0===e},P=function(e,t){var n=this,r=!1;this.name=t,this.getIndexedDB=function(){return e},this.add=function(t){if(r)throw"Database has been closed";for(var o=[],i=0,a=0;a<arguments.length-1;a++)if(s(arguments[a+1]))for(var p=0;p<arguments[a+1].length;p++)o[i]=arguments[a+1][p],i++;else o[i]=arguments[a+1],i++;var m=e.transaction(t,b),f=m.objectStore(t);return new c((function(e,t){u(o).call(o,(function(e){var t;if(e.item&&e.key){var n=e.key;e=e.item,t=f.add(e,n)}else t=f.add(e);t.onsuccess=function(t){var n=t.target,r=n.source.keyPath;null===r&&(r="__id__"),l(e,r,{value:n.result,enumerable:!0})}})),m.oncomplete=function(){e(o,n)},m.onerror=function(e){e.preventDefault(),t(e)},m.onabort=function(e){t(e)}}))},this.updateAndDelete=function(t,n,o){if(r)throw"Database has been closed";var i=e.transaction(t,b),a=i.objectStore(t),s=a.keyPath;return new c((function(e,t){u(n).call(n,(function(e){if(e.item&&e.key){var t=e.key;e=e.item,a.put(e,t)}else a.put(e)})),u(o).call(o,(function(e){a.delete(e[s])})),i.oncomplete=function(){e([n,o])},i.onerror=function(e){t(e)}}))},this.update=function(t){if(r)throw"Database has been closed";for(var o,i=[],a=1;a<arguments.length;a++)s(o=arguments[a])?i=p(i).call(i,o):i.push(o);var l=e.transaction(t,b),m=l.objectStore(t);m.keyPath;return new c((function(e,t){u(i).call(i,(function(e){var t;if(e.item&&e.key){var n=e.key;e=e.item,t=m.put(e,n)}else t=m.put(e);t.onsuccess=function(e){},t.onerror=function(e){}})),l.oncomplete=function(){e(i,n)},l.onerror=function(e){t(e)},l.onabort=function(e){t(e)}}))},this.remove=function(t,n,o,i,a,l,p){if(r)throw"Database has been closed";var m=e.transaction(t,b),f=m.objectStore(t);return new c((function(e,t){function r(e){return null==e}var c;(r(i)&&(i=-1/0),r(a)&&(a=1/0),null===n||s(n)||(n=[n]),r(o))?null!==n?u(n).call(n,(function(e){f.delete(e)})):f.delete(range=y.bound(i,a,l,p)):(c=null!==n?y.only(n[0]):y.bound(i,a,l,p),f.index(o).openCursor(c).onsuccess=function(e){var t=e.target.result;t&&(t.delete(),t.continue())});m.oncomplete=function(){e()},m.onerror=function(e){t(e)},m.onabort=function(e){t(e)}}))},this.clear=function(t){if(r)throw"Database has been closed";var n=e.transaction(t,b);n.objectStore(t).clear();return new c((function(e,t){n.oncomplete=function(){e()},n.onerror=function(e){t(e)}}))},this.close=function(){r||(e.close(),r=!0,delete E[t])},this.get=function(t,n){if(r)throw"Database has been closed";var o=e.transaction(t),i=o.objectStore(t).get(n);return new c((function(e,t){i.onsuccess=function(t){e(t.target.result)},o.onerror=function(e){t(e)}}))},this.query=function(t,n){if(r)throw"Database has been closed";return new I(t,e,n)},this.count=function(t,n){if(r)throw"Database has been closed";e.transaction(t).objectStore(t)};for(var o=0,i=e.objectStoreNames.length;o<i;o++)!function(e){for(var t in n[e]={},n)T.call(n,t)&&"close"!==t&&(n[e][t]=function(t){return function(){var r,o=p(r=[e]).call(r,a([]).call(arguments,0));return n[t].apply(n,o)}}(t))}(e.objectStoreNames[o])},I=function(e,t,n){var r,o=this,s=!1,l=!1,p=function(r,o,a,p,f,d,g){return new c((function(c,h){var T=s||l?b:v,S=t.transaction(e,T),x=S.objectStore(e),k=n?x.index(n):x,w=r?y[r].apply(null,o):null,_=[],C=[w],P=0;f=f||null,d=d||[],"count"!==a&&C.push(p||"next");var I=!!s&&m(s);k[a].apply(k,C).onsuccess=function(e){var t=e.target.result;if(i(t)===i(0))_=t;else if(t)if(null!==f&&f[0]>P)P=f[0],t.advance(f[0]);else if(null!==f&&P>=f[0]+f[1]);else{var n=!0,r="value"in t?t.value:t.key;u(d).call(d,(function(e){e&&e.length&&(2===e.length?n=n&&r[e[0]]===e[1]:M(e[0])&&(n=n&&e[0].apply(void 0,[r])))})),n&&(P++,_.push(g(r)),l?t.delete():s&&(r=function(e){for(var t=0;t<I.length;t++){var n=I[t],r=s[n];r instanceof Function&&(r=r(e)),e[n]=r}return e}(r),t.update(r))),t.continue()}},S.oncomplete=function(){c(_)},S.onerror=function(e){h(e)},S.onabort=function(e){h(e)}}))},d=function(e,t){var n="next",r="openCursor",o=[],i=null,c=x,u=!1,m=function(){return p(e,t,r,u?n+"unique":n,i,o,c)},f=function(){return n=null,r="count",{execute:m}},d=function e(){return 1==(i=_(arguments[0])?arguments[0]:a(Array.prototype).call(arguments,0,2)).length&&i.unshift(0),w(i[1])||(i=null),{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:b,modify:T,limit:e,map:S,remove:k}},g=function e(t){return(t=!!C(t)||!!t)&&(r="openKeyCursor"),{execute:m,keys:e,filter:h,asc:y,desc:v,distinct:b,modify:T,limit:d,map:S,remove:k}},h=function e(){return o.push(a(Array.prototype).call(arguments,0,2)),{execute:m,count:f,keys:g,filter:e,asc:y,desc:v,distinct:b,modify:T,limit:d,map:S,remove:k}},y=function e(t){return t=!!C(t)||!!t,n=t?"next":"prev",{execute:m,count:f,keys:g,filter:h,asc:e,desc:v,distinct:b,modify:T,limit:d,map:S,remove:k}},v=function e(t){return t=!!C(t)||!!t,n=t?"prev":"next",{execute:m,count:f,keys:g,filter:h,asc:y,desc:e,distinct:b,modify:T,limit:d,map:S,remove:k}},b=function e(t){return t=!!C(t)||!!t,u=t,{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:e,modify:T,limit:d,map:S,remove:k}},T=function e(t){return s=t,{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:b,modify:e,limit:d,map:S,remove:k}},S=function e(t){return M(t)&&(c=t),{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:b,modify:T,limit:d,map:e,remove:k}},k=function e(t){return t=!!C(t)||!!t,l=t,{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:b,modify:T,limit:d,map:S,remove:e}};return{execute:m,count:f,keys:g,filter:h,asc:y,desc:v,distinct:b,modify:T,limit:d,map:S,remove:k}};u(r="only bound upperBound lowerBound".split(" ")).call(r,(function(e){o[e]=function(){return new d(e,arguments)}})),this.filter=function(){var e=new d(null,null);return f(e).apply(e,arguments)},this.all=function(){var e;return f(e=this).call(e)}},A=function(e,t,n,r){var o=e.target.result,i=new P(o,t);return E[t]=o,c.resolve(i)},E={},O={version:"0.10.2",open:function(e){var t;return new c((function(n,r){if(E[e.server])A({target:{result:E[e.server]}},e.server,e.version,e.schema).then(n,r);else{try{t=S().open(e.server,e.version)}catch(e){r(e)}t.onsuccess=function(t){A(t,e.server,e.version,e.schema).then(n,r)},t.onupgradeneeded=function(t){!function(e,t,n){for(var r in"function"==typeof t&&(t=t()),t){var o,i=t[r];for(var a in o=!T.call(t,r)||n.objectStoreNames.contains(r)?e.currentTarget.transaction.objectStore(r):n.createObjectStore(r,i.key),i.indexes){var s=i.indexes[a];try{o.index(a)}catch(e){o.createIndex(a,s.key||a,m(s).length?s:{unique:!1})}}}}(t,e.schema,t.target.result)},t.onerror=function(e){r(e)}}}))},remove:function(e){return new c((function(t,n){if(!e)return t();var r,o;i(e)===P&&(e=e.name),"string"==typeof e&&(r=E[e]),r&&"function"==typeof r.close&&r.close();try{o=S().deleteDatabase(e)}catch(e){n(e)}o.onsuccess=function(n){delete E[e],t(e)},o.onerror=function(e){n(e)},o.onblocked=function(e){n(e)}}))}};void 0!==e.exports?e.exports=O:void 0===(o=function(){return O}.call(t,n,t,e))||(e.exports=o)}(void 0)}).call(this,n(49))},function(e,t,n){e.exports=n(438)},function(e,t,n){e.exports=n(448)},function(e,t){var n={set:function(e,t,r){n[e]=t,r&&(r.support=t)}};e.exports=n},,,function(e,t,n){var r=n(19),o=n(0),i=n(142),a=o.merge({},i.idMap,{auth:{id:2,login:3,kicked:5,logout:6,multiPortLogin:7,kick:8},user:{id:3,updatePushToken:1,appBackground:2,markInBlacklist:3,getBlacklist:4,markInMutelist:5,getMutelist:6,getRelations:8,getUsers:7,updateMyInfo:10,updateDonnop:15,syncMyInfo:109,syncUpdateMyInfo:110},notify:{id:4,markRead:3,syncOfflineMsgs:4,batchMarkRead:5,syncOfflineSysMsgs:6,syncOfflineNetcallMsgs:8,syncRoamingMsgs:9,syncMsgReceipts:12,syncRobots:15,syncBroadcastMsgs:16,syncSuperTeamRoamingMsgs:17,syncOfflineSuperTeamSysMsgs:18,syncDeleteSuperTeamMsgOfflineRoaming:19,syncDeleteMsgSelf:21,syncSessionsWithMoreRoaming:22,syncStickTopSessions:23,syncSessionHistoryMsgsDelete:24},sync:{id:5,sync:1,syncTeamMembers:2,syncSuperTeamMembers:3},msg:{id:7,sendMsg:1,msg:2,sysMsg:3,getHistoryMsgs:6,sendCustomSysMsg:7,searchHistoryMsgs:8,deleteSessions:9,getSessions:10,syncSendMsg:101,sendMsgReceipt:11,msgReceipt:12,deleteMsg:13,msgDeleted:14,markSessionAck:16,markSessionAckBatch:25,broadcastMsg:17,clearServerHistoryMsgs:18,getServerSessions:19,getServerSession:20,updateServerSession:21,deleteServerSessions:22,deleteMsgSelf:23,deleteMsgSelfBatch:24,msgFtsInServer:26,msgFtsInServerByTiming:27,onClearServerHistoryMsgs:118,syncUpdateServerSession:121,onDeleteMsgSelf:123,onDeleteMsgSelfBatch:124},msgExtend:{id:23,getThreadMsgs:1,getMsgsByIdServer:2,addQuickComment:3,deleteQuickComment:4,onQuickComment:5,onDeleteQuickComment:6,getQuickComments:7,addCollect:8,deleteCollects:9,updateCollect:10,getCollects:11,addStickTopSession:12,deleteStickTopSession:13,updateStickTopSession:14,addMsgPin:15,updateMsgPin:16,deleteMsgPin:17,onAddMsgPin:18,onUpdateMsgPin:19,onDeleteMsgPin:20,getMsgPins:21,syncAddQuickComment:103,syncDeleteQuickComment:104,syncAddStickTopSession:112,syncDeleteStickTopSession:113,syncUpdateStickTopSession:114,syncAddMsgPin:115,syncUpdateMsgPin:116,syncDeleteMsgPin:117},team:{id:8,createTeam:1,sendTeamMsg:2,teamMsg:3,teamMsgs:4,addTeamMembers:5,removeTeamMembers:6,updateTeam:7,leaveTeam:8,getTeam:9,getTeams:10,getTeamMembers:11,dismissTeam:12,applyTeam:13,passTeamApply:14,rejectTeamApply:15,addTeamManagers:16,removeTeamManagers:17,transferTeam:18,updateInfoInTeam:19,updateNickInTeam:20,acceptTeamInvite:21,rejectTeamInvite:22,getTeamHistoryMsgs:23,searchTeamHistoryMsgs:24,updateMuteStateInTeam:25,getMyTeamMembers:26,getMutedTeamMembers:27,sendTeamMsgReceipt:28,getTeamMsgReads:29,getTeamMsgReadAccounts:30,notifyTeamMsgReads:31,muteTeamAll:32,getTeamMemberInvitorAccid:33,getTeamsById:34,syncMyTeamMembers:126,syncTeams:109,syncTeamMembers:111,syncCreateTeam:101,syncSendTeamMsg:102,syncUpdateTeamMember:119},superTeam:{id:21,sendSuperTeamMsg:2,superTeamMsg:3,addSuperTeamMembers:5,removeSuperTeamMembers:6,leaveSuperTeam:7,updateSuperTeam:8,getSuperTeam:9,getSuperTeams:12,updateInfoInSuperTeam:10,getMySuperTeamMembers:11,getSuperTeamMembers:13,getSuperTeamHistoryMsgs:14,getSuperTeamMembersByJoinTime:15,sendSuperTeamCustomSysMsg:16,deleteSuperTeamMsg:17,superTeamMsgDelete:18,superTeamCustomSysMsg:19,applySuperTeam:20,passSuperTeamApply:21,rejectSuperTeamApply:22,acceptSuperTeamInvite:23,rejectSuperTeamInvite:24,markSuperTeamSessionAck:25,addSuperTeamManagers:26,removeSuperTeamManagers:27,updateSuperTeamMute:28,updateSuperTeamMembersMute:29,updateNickInSuperTeam:30,transferSuperTeam:31,markSuperTeamSessionsAck:32,getSuperTeamMembersByAccounts:33,getMutedSuperTeamMembers:34,syncMySuperTeamMembers:111,syncSuperTeams:109,syncSuperTeamMembers:113,syncCreateSuperTeam:101,syncSendSuperTeamMsg:102,syncUpdateSuperTeamMember:110,syncDeleteSuperTeamMsg:117},friend:{id:12,friendRequest:1,syncFriendRequest:101,deleteFriend:2,syncDeleteFriend:102,updateFriend:3,syncUpdateFriend:103,getFriends:4},chatroom:{id:13,getChatroomAddress:1},filter:{id:101,sendFilterMsg:1,filterMsg:2,filterSysMsg:3,sendFilterCustomSysMsg:7},eventService:{id:14,publishEvent:1,pushEvent:2,subscribeEvent:3,unSubscribeEventsByAccounts:4,unSubscribeEventsByType:5,querySubscribeEventsByAccounts:6,querySubscribeEventsByType:7,pushEvents:9},proxyService:{id:22,httpProxy:1,onProxyMsg:2},qchat:{id:24,getQChatAddress:1}}),s=o.merge({},i.cmdConfig,{login:{sid:a.auth.id,cid:a.auth.login,params:[{type:"Property",name:"login"}]},logout:{sid:a.auth.id,cid:a.auth.logout},kick:{sid:a.auth.id,cid:a.auth.kick,params:[{type:"StrArray",name:"deviceIds"}]},updatePushToken:{sid:a.user.id,cid:a.user.updatePushToken,params:[{type:"String",name:"tokenName"},{type:"String",name:"token"},{type:"int",name:"pushkit"}]},appBackground:{sid:a.user.id,cid:a.user.appBackground,params:[{type:"bool",name:"isBackground"},{type:"Int",name:"badge"}]},markInBlacklist:{sid:a.user.id,cid:a.user.markInBlacklist,params:[{type:"String",name:"account"},{type:"bool",name:"isAdd"}]},getBlacklist:{sid:a.user.id,cid:a.user.getBlacklist,params:[{type:"long",name:"time"}]},markInMutelist:{sid:a.user.id,cid:a.user.markInMutelist,params:[{type:"String",name:"account"},{type:"bool",name:"isAdd"}]},getMutelist:{sid:a.user.id,cid:a.user.getMutelist,params:[{type:"long",name:"time"}]},getRelations:{sid:a.user.id,cid:a.user.getRelations,params:[{type:"long",name:"timetag"}]},getUsers:{sid:a.user.id,cid:a.user.getUsers,params:[{type:"StrArray",name:"accounts"}]},updateMyInfo:{sid:a.user.id,cid:a.user.updateMyInfo,params:[{type:"Property",name:"user"},{type:"Property",name:"antispamTag"}]},updateDonnop:{sid:a.user.id,cid:a.user.updateDonnop,params:[{type:"Property",name:"donnop"}]},markRead:{sid:a.notify.id,cid:a.notify.markRead,params:[{type:"long",name:"id"},{type:"ph",name:"ph"}]},batchMarkRead:{sid:a.notify.id,cid:a.notify.batchMarkRead,params:[{type:"byte",name:"sid"},{type:"byte",name:"cid"},{type:"LongArray",name:"ids"}]},sync:{sid:a.sync.id,cid:a.sync.sync,params:[{type:"Property",name:"sync"}]},syncTeamMembers:{sid:a.sync.id,cid:a.sync.syncTeamMembers,params:[{type:"LongLongMap",name:"sync"}]},syncSuperTeamMembers:{sid:a.sync.id,cid:a.sync.syncSuperTeamMembers,params:[{type:"LongLongMap",name:"sync"}]},sendMsg:{sid:a.msg.id,cid:a.msg.sendMsg,params:[{type:"Property",name:"msg"}]},getHistoryMsgs:{sid:a.msg.id,cid:a.msg.getHistoryMsgs,params:[{type:"String",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},sendCustomSysMsg:{sid:a.msg.id,cid:a.msg.sendCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},searchHistoryMsgs:{sid:a.msg.id,cid:a.msg.searchHistoryMsgs,params:[{type:"String",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"String",name:"keyword"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},getSessions:{sid:a.msg.id,cid:a.msg.getSessions,params:[{type:"long",name:"time"}]},deleteSessions:{sid:a.msg.id,cid:a.msg.deleteSessions,params:[{type:"StrArray",name:"sessions"}]},sendMsgReceipt:{sid:a.msg.id,cid:a.msg.sendMsgReceipt,params:[{type:"Property",name:"msgReceipt"}]},deleteMsg:{sid:a.msg.id,cid:a.msg.deleteMsg,params:[{type:"Property",name:"sysMsg"}]},markSessionAck:{sid:a.msg.id,cid:a.msg.markSessionAck,params:[{type:"byte",name:"scene"},{type:"String",name:"to"},{type:"long",name:"timetag"}]},markSessionAckBatch:{sid:a.msg.id,cid:a.msg.markSessionAckBatch,params:[{type:"PropertyArray",name:"sessionAckTags",entity:"sessionAckTag"}]},clearServerHistoryMsgs:{sid:a.msg.id,cid:a.msg.clearServerHistoryMsgs,params:[{type:"Property",name:"clearMsgsParams"}]},clearServerHistoryMsgsWithSync:{sid:a.msg.id,cid:a.msg.clearServerHistoryMsgs,params:[{type:"Property",name:"clearMsgsParamsWithSync"}]},msgFtsInServer:{sid:a.msg.id,cid:a.msg.msgFtsInServer,params:[{type:"Property",name:"msgFullSearchRequestTag"}]},msgFtsInServerByTiming:{sid:a.msg.id,cid:a.msg.msgFtsInServerByTiming,params:[{type:"Property",name:"msgTimingFullSearchRequestTag"}]},onClearServerHistoryMsgs:{sid:a.msg.id,cid:a.msg.clearServerHistoryMsgs},getServerSessions:{sid:a.msg.id,cid:a.msg.getServerSessions,params:[{type:"Property",name:"sessionReqTag"}]},getServerSession:{sid:a.msg.id,cid:a.msg.getServerSession,params:[{type:"Property",name:"session"}]},updateServerSession:{sid:a.msg.id,cid:a.msg.updateServerSession,params:[{type:"Property",name:"session"}]},deleteServerSessions:{sid:a.msg.id,cid:a.msg.deleteServerSessions,params:[{type:"PropertyArray",name:"sessions",entity:"session"}]},deleteMsgSelf:{sid:a.msg.id,cid:a.msg.deleteMsgSelf,params:[{type:"Property",name:"deleteMsgSelfTag"}]},deleteMsgSelfBatch:{sid:a.msg.id,cid:a.msg.deleteMsgSelfBatch,params:[{type:"PropertyArray",name:"deleteMsgSelfTags",entity:"deleteMsgSelfTag"}]},onDeleteMsgSelf:{sid:a.msg.id,cid:a.msg.onDeleteMsgSelf},onDeleteMsgSelfBatch:{sid:a.msg.id,cid:a.msg.onDeleteMsgSelfBatch},sendSuperTeamMsg:{sid:a.superTeam.id,cid:a.superTeam.sendSuperTeamMsg,params:[{type:"Property",name:"msg"}]},addSuperTeamMembers:{sid:a.superTeam.id,cid:a.superTeam.addSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"}]},removeSuperTeamMembers:{sid:a.superTeam.id,cid:a.superTeam.removeSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},leaveSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.leaveSuperTeam,params:[{type:"long",name:"teamId"}]},updateSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.updateSuperTeam,params:[{type:"Property",name:"team"},{type:"Property",name:"antispamTag"}]},getSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeam,params:[{type:"long",name:"teamId"}]},getSuperTeams:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeams,params:[{type:"long",name:"timetag"}]},getSuperTeamMembers:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},updateInfoInSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.updateInfoInSuperTeam,params:[{type:"Property",name:"superTeamMember"}]},getSuperTeamHistoryMsgs:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},applySuperTeam:{sid:a.superTeam.id,cid:a.superTeam.applySuperTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"ps"}]},passSuperTeamApply:{sid:a.superTeam.id,cid:a.superTeam.passSuperTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectSuperTeamApply:{sid:a.superTeam.id,cid:a.superTeam.rejectSuperTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},acceptSuperTeamInvite:{sid:a.superTeam.id,cid:a.superTeam.acceptSuperTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectSuperTeamInvite:{sid:a.superTeam.id,cid:a.superTeam.rejectSuperTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},markSuperTeamSessionAck:{sid:a.superTeam.id,cid:a.superTeam.markSuperTeamSessionAck,params:[{type:"long",name:"to"},{type:"long",name:"timetag"}]},addSuperTeamManagers:{sid:a.superTeam.id,cid:a.superTeam.addSuperTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},removeSuperTeamManagers:{sid:a.superTeam.id,cid:a.superTeam.removeSuperTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},updateSuperTeamMute:{sid:a.superTeam.id,cid:a.superTeam.updateSuperTeamMute,params:[{type:"long",name:"teamId"},{type:"int",name:"mute"}]},updateSuperTeamMembersMute:{sid:a.superTeam.id,cid:a.superTeam.updateSuperTeamMembersMute,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"int",name:"mute"}]},updateNickInSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.updateNickInSuperTeam,params:[{type:"Property",name:"superTeamMember"}]},transferSuperTeam:{sid:a.superTeam.id,cid:a.superTeam.transferSuperTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"bool",name:"leave"}]},markSuperTeamSessionsAck:{sid:a.superTeam.id,cid:a.superTeam.markSuperTeamSessionsAck,params:[{type:"PropertyArray",name:"sessionAckTags",entity:"sessionAckTag"}]},getSuperTeamMembersByJoinTime:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeamMembersByJoinTime,params:[{type:"long",name:"teamId"},{type:"long",name:"joinTime"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},getSuperTeamMembersByAccounts:{sid:a.superTeam.id,cid:a.superTeam.getSuperTeamMembersByAccounts,params:[{type:"StrArray",name:"memberIds"}]},getMutedSuperTeamMembers:{sid:a.superTeam.id,cid:a.superTeam.getMutedSuperTeamMembers,params:[{type:"long",name:"teamId"},{type:"long",name:"joinTime"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},sendSuperTeamCustomSysMsg:{sid:a.superTeam.id,cid:a.superTeam.sendSuperTeamCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},deleteSuperTeamMsg:{sid:a.superTeam.id,cid:a.superTeam.deleteSuperTeamMsg,params:[{type:"Property",name:"sysMsg"}]},getMySuperTeamMembers:{sid:a.superTeam.id,cid:a.superTeam.getMySuperTeamMembers,params:[{type:"LongArray",name:"teamIds"}]},createTeam:{sid:a.team.id,cid:a.team.createTeam,params:[{type:"Property",name:"team"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"},{type:"Property",name:"antispamTag"}]},sendTeamMsg:{sid:a.team.id,cid:a.team.sendTeamMsg,params:[{type:"Property",name:"msg"}]},addTeamMembers:{sid:a.team.id,cid:a.team.addTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"},{type:"String",name:"ps"},{type:"String",name:"attach"}]},removeTeamMembers:{sid:a.team.id,cid:a.team.removeTeamMembers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},updateTeam:{sid:a.team.id,cid:a.team.updateTeam,params:[{type:"Property",name:"team"},{type:"Property",name:"antispamTag"}]},leaveTeam:{sid:a.team.id,cid:a.team.leaveTeam,params:[{type:"long",name:"teamId"}]},getTeam:{sid:a.team.id,cid:a.team.getTeam,params:[{type:"long",name:"teamId"}]},getTeams:{sid:a.team.id,cid:a.team.getTeams,params:[{type:"long",name:"timetag"}]},getTeamsById:{sid:a.team.id,cid:a.team.getTeamsById,params:[{type:"longArray",name:"teamIds"}]},getTeamMembers:{sid:a.team.id,cid:a.team.getTeamMembers,params:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},dismissTeam:{sid:a.team.id,cid:a.team.dismissTeam,params:[{type:"long",name:"teamId"}]},applyTeam:{sid:a.team.id,cid:a.team.applyTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"ps"}]},passTeamApply:{sid:a.team.id,cid:a.team.passTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectTeamApply:{sid:a.team.id,cid:a.team.rejectTeamApply,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},addTeamManagers:{sid:a.team.id,cid:a.team.addTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},removeTeamManagers:{sid:a.team.id,cid:a.team.removeTeamManagers,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},transferTeam:{sid:a.team.id,cid:a.team.transferTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"bool",name:"leave"}]},updateInfoInTeam:{sid:a.team.id,cid:a.team.updateInfoInTeam,params:[{type:"Property",name:"teamMember"}]},updateNickInTeam:{sid:a.team.id,cid:a.team.updateNickInTeam,params:[{type:"Property",name:"teamMember"}]},acceptTeamInvite:{sid:a.team.id,cid:a.team.acceptTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"}]},rejectTeamInvite:{sid:a.team.id,cid:a.team.rejectTeamInvite,params:[{type:"long",name:"teamId"},{type:"String",name:"from"},{type:"String",name:"ps"}]},getTeamHistoryMsgs:{sid:a.team.id,cid:a.team.getTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"long",name:"lastMsgId"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},searchTeamHistoryMsgs:{sid:a.team.id,cid:a.team.searchTeamHistoryMsgs,params:[{type:"long",name:"to"},{type:"long",name:"beginTime"},{type:"long",name:"endTime"},{type:"String",name:"keyword"},{type:"int",name:"limit"},{type:"bool",name:"reverse"}]},updateMuteStateInTeam:{sid:a.team.id,cid:a.team.updateMuteStateInTeam,params:[{type:"long",name:"teamId"},{type:"String",name:"account"},{type:"int",name:"mute"}]},getMyTeamMembers:{sid:a.team.id,cid:a.team.getMyTeamMembers,params:[{type:"LongArray",name:"teamIds"}]},getMutedTeamMembers:{sid:a.team.id,cid:a.team.getMutedTeamMembers,params:[{type:"long",name:"teamId"}]},sendTeamMsgReceipt:{sid:a.team.id,cid:a.team.sendTeamMsgReceipt,params:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},getTeamMsgReads:{sid:a.team.id,cid:a.team.getTeamMsgReads,params:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},getTeamMsgReadAccounts:{sid:a.team.id,cid:a.team.getTeamMsgReadAccounts,params:[{type:"Property",name:"teamMsgReceipt"}]},muteTeamAll:{sid:a.team.id,cid:a.team.muteTeamAll,params:[{type:"long",name:"teamId"},{type:"int",name:"mute"}]},getTeamMemberInvitorAccid:{sid:a.team.id,cid:a.team.getTeamMemberInvitorAccid,params:[{type:"long",name:"teamId"},{type:"StrArray",name:"accounts"}]},friendRequest:{sid:a.friend.id,cid:a.friend.friendRequest,params:[{type:"String",name:"account"},{type:"byte",name:"type"},{type:"String",name:"ps"}]},deleteFriend:{sid:a.friend.id,cid:a.friend.deleteFriend,params:[{type:"String",name:"account"},{type:"Property",name:"delFriendParams"}]},updateFriend:{sid:a.friend.id,cid:a.friend.updateFriend,params:[{type:"Property",name:"friend"}]},getFriends:{sid:a.friend.id,cid:a.friend.getFriends,params:[{type:"long",name:"timetag"}]},getChatroomAddress:{sid:a.chatroom.id,cid:a.chatroom.getChatroomAddress,params:[{type:"long",name:"chatroomId"},{type:"bool",name:"isWeixinApp"},{type:"number",name:"type"}]},sendFilterMsg:{sid:r(a).id,cid:r(a).sendFilterMsg,params:[{type:"Property",name:"msg"}]},sendFilterCustomSysMsg:{sid:r(a).id,cid:r(a).sendFilterCustomSysMsg,params:[{type:"Property",name:"sysMsg"}]},publishEvent:{sid:a.eventService.id,cid:a.eventService.publishEvent,params:[{type:"Property",name:"msgEvent"}]},pushEvent:{sid:a.eventService.id,cid:a.eventService.pushEvent},subscribeEvent:{sid:a.eventService.id,cid:a.eventService.subscribeEvent,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},unSubscribeEventsByAccounts:{sid:a.eventService.id,cid:a.eventService.unSubscribeEventsByAccounts,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},unSubscribeEventsByType:{sid:a.eventService.id,cid:a.eventService.unSubscribeEventsByType,params:[{type:"Property",name:"msgEventSubscribe"}]},querySubscribeEventsByAccounts:{sid:a.eventService.id,cid:a.eventService.querySubscribeEventsByAccounts,params:[{type:"Property",name:"msgEventSubscribe"},{type:"StrArray",name:"accounts"}]},querySubscribeEventsByType:{sid:a.eventService.id,cid:a.eventService.querySubscribeEventsByType,params:[{type:"Property",name:"msgEventSubscribe"}]},pushEvents:{sid:a.eventService.id,cid:a.eventService.pushEvents},getThreadMsgs:{sid:a.msgExtend.id,cid:a.msgExtend.getThreadMsgs,params:[{type:"Property",name:"msg"},{type:"Property",name:"threadMsgReq"}]},getMsgsByIdServer:{sid:a.msgExtend.id,cid:a.msgExtend.getMsgsByIdServer,params:[{type:"PropertyArray",name:"reqMsgs",entity:"msg"}]},addQuickComment:{sid:a.msgExtend.id,cid:a.msgExtend.addQuickComment,params:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},deleteQuickComment:{sid:a.msgExtend.id,cid:a.msgExtend.deleteQuickComment,params:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},getQuickComments:{sid:a.msgExtend.id,cid:a.msgExtend.getQuickComments,params:[{type:"PropertyArray",name:"commentReq",entity:"commentReq"}]},addCollect:{sid:a.msgExtend.id,cid:a.msgExtend.addCollect,params:[{type:"Property",name:"collect"}]},deleteCollects:{sid:a.msgExtend.id,cid:a.msgExtend.deleteCollects,params:[{type:"PropertyArray",name:"collectList",entity:"collect"}]},updateCollect:{sid:a.msgExtend.id,cid:a.msgExtend.updateCollect,params:[{type:"Property",name:"collect"}]},getCollects:{sid:a.msgExtend.id,cid:a.msgExtend.getCollects,params:[{type:"Property",name:"collectQuery"}]},addStickTopSession:{sid:a.msgExtend.id,cid:a.msgExtend.addStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},updateStickTopSession:{sid:a.msgExtend.id,cid:a.msgExtend.updateStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},deleteStickTopSession:{sid:a.msgExtend.id,cid:a.msgExtend.deleteStickTopSession,params:[{type:"Property",name:"stickTopSession"}]},addMsgPin:{sid:a.msgExtend.id,cid:a.msgExtend.addMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},updateMsgPin:{sid:a.msgExtend.id,cid:a.msgExtend.updateMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},deleteMsgPin:{sid:a.msgExtend.id,cid:a.msgExtend.deleteMsgPin,params:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"}]},getMsgPins:{sid:a.msgExtend.id,cid:a.msgExtend.getMsgPins,params:[{type:"Property",name:"msgPinReq"}]},httpProxy:{sid:a.proxyService.id,cid:a.proxyService.httpProxy,params:[{type:"Property",name:"proxyTag"}]},getQChatAddress:{sid:a.qchat.id,cid:a.qchat.getQChatAddress,params:[{type:"Property",name:"getQChatAddressTag"}]}}),c=o.merge({},i.packetConfig,{"2_3":{service:"auth",cmd:"login",response:[{type:"Property",name:"loginRes"},{type:"PropertyArray",name:"loginPorts",entity:"loginPort"},{type:"Property",name:"aosPushInfo"}]},"2_5":{service:"auth",cmd:"kicked",response:[{type:"Number",name:"from"},{type:"Number",name:"reason"},{type:"String",name:"custom"},{type:"Number",name:"customClientType"}]},"2_6":{service:"auth",cmd:"logout"},"2_7":{service:"auth",cmd:"multiPortLogin",response:[{type:"Number",name:"state"},{type:"PropertyArray",name:"loginPorts",entity:"loginPort"}]},"2_8":{service:"auth",cmd:"kick",response:[{type:"StrArray",name:"deviceIds"}]},"3_1":{service:"user",cmd:"updatePushToken"},"3_2":{service:"user",cmd:"appBackground"},"3_3":{service:"user",cmd:"markInBlacklist"},"3_103":{service:"user",cmd:"syncMarkInBlacklist",response:[{type:"String",name:"account"},{type:"Boolean",name:"isAdd"}]},"3_4":{service:"user",cmd:"getBlacklist",response:[{type:"StrArray",name:"blacklist"}]},"3_5":{service:"user",cmd:"markInMutelist"},"3_105":{service:"user",cmd:"syncMarkInMutelist",response:[{type:"String",name:"account"},{type:"Boolean",name:"isAdd"}]},"3_6":{service:"user",cmd:"getMutelist",response:[{type:"StrArray",name:"mutelist"}]},"3_8":{service:"user",cmd:"getRelations",response:[{type:"PropertyArray",name:"specialRelations",entity:"specialRelation"},{type:"Number",name:"timetag"}]},"3_7":{service:"user",cmd:"getUsers",response:[{type:"PropertyArray",name:"users",entity:"user"}]},"3_10":{service:"user",cmd:"updateMyInfo",response:[{type:"Number",name:"timetag"}]},"3_15":{service:"user",cmd:"updateDonnop",response:[{type:"Number",name:"timetag"}]},"3_115":{service:"user",cmd:"syncUpdateDonnop",response:[{type:"Property",name:"donnop"},{type:"Number",name:"timetag"}]},"3_109":{service:"user",cmd:"syncMyInfo",response:[{type:"Property",name:"user"},{type:"Number",name:"timetag"}]},"3_110":{service:"user",cmd:"syncUpdateMyInfo",response:[{type:"Property",name:"user"}]},"4_1":{service:"notify"},"4_2":{service:"notify"},"4_3":{service:"notify",cmd:"markRead"},"4_4":{service:"notify",cmd:"syncOfflineMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_5":{service:"notify",cmd:"batchMarkRead"},"4_6":{service:"notify",cmd:"syncOfflineSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"4_8":{service:"notify",cmd:"syncOfflineNetcallMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_9":{service:"notify",cmd:"syncRoamingMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_12":{service:"notify",cmd:"syncMsgReceipts",response:[{type:"PropertyArray",name:"msgReceipts",entity:"msgReceipt"},{type:"Number",name:"timetag"}]},"4_13":{service:"notify",cmd:"syncDonnop",response:[{type:"Property",name:"donnop"},{type:"Number",name:"timetag"}]},"4_14":{service:"notify",cmd:"syncSessionAck",response:[{type:"StrLongMap",name:"p2p"},{type:"LongLongMap",name:"team"},{type:"Number",name:"timetag"}]},"4_15":{service:"notify",cmd:"syncRobots",response:[{type:"PropertyArray",name:"robots",entity:"robot"}]},"4_16":{service:"notify",cmd:"syncBroadcastMsgs",response:[{type:"PropertyArray",name:"broadcastMsgs",entity:"broadcastMsg"}]},"4_17":{service:"notify",cmd:"syncSuperTeamRoamingMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_18":{service:"notify",cmd:"syncOfflineSuperTeamSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"4_19":{service:"notify",cmd:"syncDeleteSuperTeamMsgOfflineRoaming",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"},{type:"Number",name:"timetag"},{type:"Number",name:"type"}]},"4_20":{service:"notify",cmd:"syncSuperTeamSessionAck",response:[{type:"LongLongMap",name:"superTeam"},{type:"Number",name:"timetag"}]},"4_21":{service:"notify",cmd:"syncDeleteMsgSelf",response:[{type:"PropertyArray",name:"deletedMsgs",entity:"deleteMsgSelfTag"}]},"4_22":{service:"notify",cmd:"syncSessionsWithMoreRoaming",response:[{type:"PropertyArray",name:"sessions",entity:"msg"}]},"4_23":{service:"notify",cmd:"syncStickTopSessions",response:[{type:"Number",name:"timetag"},{type:"boolean",name:"modify"},{type:"PropertyArray",name:"sessions",entity:"stickTopSession"}]},"4_24":{service:"notify",cmd:"syncSessionHistoryMsgsDelete",response:[{type:"PropertyArray",name:"sessionHistoryMsgsDeleteTags",entity:"clearMsgsParamsWithSync"}]},"4_100":{service:"notify",cmd:"syncOfflineFilterMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"4_101":{service:"notify",cmd:"syncOfflineFilterSysMsgs",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"}]},"5_1":{service:"sync",cmd:"syncDone",response:[{type:"Number",name:"timetag"}]},"5_2":{service:"sync",cmd:"syncTeamMembersDone",response:[{type:"Number",name:"timetag"}]},"5_3":{service:"sync",cmd:"syncSuperTeamMembersDone",response:[{type:"Number",name:"timetag"}]},"7_1":{service:"msg",cmd:"sendMsg",response:[{type:"Property",name:"msg"}],trivialErrorCodes:[7101]},"7_2":{service:"msg",cmd:"msg",response:[{type:"Property",name:"msg"}]},"7_3":{service:"msg",cmd:"sysMsg",response:[{type:"Property",name:"sysMsg"}]},"7_6":{service:"msg",cmd:"getHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_7":{service:"msg",cmd:"sendCustomSysMsg",trivialErrorCodes:[7101]},"7_8":{service:"msg",cmd:"searchHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_9":{service:"msg",cmd:"deleteSessions"},"7_10":{service:"msg",cmd:"getSessions",response:[{type:"StrArray",name:"sessions"}]},"7_101":{service:"msg",cmd:"syncSendMsg",response:[{type:"Property",name:"msg"}]},"7_11":{service:"msg",cmd:"sendMsgReceipt",response:[{type:"Property",name:"msgReceipt"}]},"7_12":{service:"msg",cmd:"msgReceipt",response:[{type:"Property",name:"msgReceipt"}]},"7_13":{service:"msg",cmd:"onDeleteMsg"},"7_14":{service:"msg",cmd:"onMsgDeleted",response:[{type:"Property",name:"sysMsg"}]},"7_15":{service:"msg",cmd:"onDeleteMsgOfflineRoaming",response:[{type:"PropertyArray",name:"sysMsgs",entity:"sysMsg"},{type:"Number",name:"timetag"},{type:"Number",name:"type"}]},"7_16":{service:"msg",cmd:"onMarkSessionAck"},"7_17":{service:"msg",cmd:"broadcastMsg",response:[{type:"Property",name:"broadcastMsg"}]},"7_18":{service:"msg",cmd:"clearServerHistoryMsgs",response:[{type:"Long",name:"timetag"}]},"7_19":{service:"session",cmd:"getServerSessions",response:[{type:"Property",name:"sessionReqTag"},{type:"PropertyArray",name:"sessionList",entity:"session"}]},"7_20":{service:"session",cmd:"getServerSession",response:[{type:"Property",name:"session"}]},"7_21":{service:"session",cmd:"updateServerSession"},"7_22":{service:"session",cmd:"deleteServerSessions"},"7_23":{service:"msg",cmd:"deleteMsgSelf",response:[{type:"Long",name:"timetag"}]},"7_24":{service:"msg",cmd:"deleteMsgSelfBatch",response:[{type:"Long",name:"timetag"}]},"7_25":{service:"msg",cmd:"onMarkSessionAckBatch"},"7_26":{service:"msg",cmd:"msgFtsInServer",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_27":{service:"msg",cmd:"msgFtsInServerByTiming",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"7_118":{service:"msg",cmd:"onClearServerHistoryMsgs",response:[{type:"Property",name:"sessionHistoryMsgsDeleteTag",entity:"clearMsgsParamsWithSync"}]},"7_123":{service:"msg",cmd:"onDeleteMsgSelf",response:[{type:"Property",name:"deleteMsgSelfTag"}]},"7_124":{service:"msg",cmd:"onDeleteMsgSelfBatch",response:[{type:"PropertyArray",name:"deleteMsgSelfTags",entity:"deleteMsgSelfTag"}]},"7_116":{service:"msg",cmd:"syncMarkSessionAck",response:[{type:"Number",name:"scene"},{type:"String",name:"to"},{type:"Number",name:"timetag"}]},"7_121":{service:"msg",cmd:"syncUpdateServerSession",response:[{type:"Property",name:"session"}]},"23_1":{service:"msgExtend",cmd:"getThreadMsgs",response:[{type:"Property",name:"threadMsg",entity:"msg"},{type:"Property",name:"threadMsgsMeta"},{type:"PropertyArray",name:"msgs",entity:"msg"}]},"23_2":{service:"msgExtend",cmd:"getMsgsByIdServer",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"23_3":{service:"msgExtend",cmd:"addQuickComment",response:[{type:"Number",name:"timetag"}]},"23_4":{service:"msgExtend",cmd:"deleteQuickComment",response:[{type:"Number",name:"timetag"}]},"23_5":{service:"msgExtend",cmd:"onQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_6":{service:"msgExtend",cmd:"onDeleteQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_7":{service:"msgExtend",cmd:"getQuickComments",response:[{type:"PropertyArray",name:"commentRes",entity:"commentRes"}]},"23_8":{service:"msgExtend",cmd:"addCollect",response:[{type:"Property",name:"collect"}]},"23_9":{service:"msgExtend",cmd:"deleteCollects",response:[{type:"Number",name:"deleteNum"}]},"23_10":{service:"msgExtend",cmd:"updateCollect",response:[{type:"Property",name:"collect"}]},"23_11":{service:"msgExtend",cmd:"getCollects",response:[{type:"Number",name:"total"},{type:"PropertyArray",name:"collectList",entity:"collect"}]},"23_12":{service:"msgExtend",cmd:"addStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_13":{service:"msgExtend",cmd:"deleteStickTopSession",response:[{type:"Number",name:"timetag"}]},"23_14":{service:"msgExtend",cmd:"updateStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_15":{service:"msgExtend",cmd:"addMsgPin",response:[{type:"Number",name:"timetag"}]},"23_16":{service:"msgExtend",cmd:"updateMsgPin",response:[{type:"Number",name:"timetag"}]},"23_17":{service:"msgExtend",cmd:"deleteMsgPin",response:[{type:"Number",name:"timetag"}]},"23_18":{service:"msgExtend",cmd:"onAddMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_19":{service:"msgExtend",cmd:"onUpdateMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_20":{service:"msgExtend",cmd:"onDeleteMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_21":{service:"msgExtend",cmd:"getMsgPins",response:[{type:"Number",name:"timetag"},{type:"Boolean",name:"modify"},{type:"PropertyArray",name:"pins",entity:"msgPinRes"}]},"23_103":{service:"msgExtend",cmd:"syncAddQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_104":{service:"msgExtend",cmd:"syncDeleteQuickComment",response:[{type:"Property",name:"msg"},{type:"Property",name:"comment"}]},"23_112":{service:"msgExtend",cmd:"syncAddStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_113":{service:"msgExtend",cmd:"syncDeleteStickTopSession",response:[{type:"Number",name:"timetag"},{type:"Property",name:"stickTopSession"}]},"23_114":{service:"msgExtend",cmd:"syncUpdateStickTopSession",response:[{type:"Property",name:"stickTopSession"}]},"23_115":{service:"msgExtend",cmd:"syncAddMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_116":{service:"msgExtend",cmd:"syncUpdateMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"23_117":{service:"msgExtend",cmd:"syncDeleteMsgPin",response:[{type:"Property",name:"msg"},{type:"Property",name:"pinTag"},{type:"Number",name:"timetag"}]},"21_2":{service:"superTeam",cmd:"sendSuperTeamMsg",response:[{type:"Property",name:"msg"}]},"21_3":{service:"superTeam",cmd:"superTeamMsg",response:[{type:"Property",name:"msg"}]},"21_5":{service:"superTeam",cmd:"addSuperTeamMembers",response:[{type:"StrArray",name:"abortedAccidList"},{type:"long",name:"timetag"}]},"21_6":{service:"superTeam",cmd:"removeSuperTeamMembers"},"21_7":{service:"superTeam",cmd:"leaveSuperTeam"},"21_8":{service:"superTeam",cmd:"updateSuperTeam",response:[{type:"long",name:"teamId"},{type:"long",name:"timetag"}]},"21_9":{service:"superTeam",cmd:"getSuperTeam",response:[{type:"Property",name:"team"}]},"21_12":{service:"superTeam",cmd:"getSuperTeams",response:[{type:"PropertyArray",name:"teams",entity:"superTeam"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_10":{service:"superTeam",cmd:"updateInfoInSuperTeam"},"21_13":{service:"superTeam",cmd:"getSuperTeamMembers",response:[{type:"long",name:"timetag"}]},"21_11":{service:"superTeam",cmd:"getMySuperTeamMembers",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_14":{service:"superTeam",cmd:"getSuperTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"21_15":{service:"superTeam",cmd:"getSuperTeamMembersByJoinTime",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_16":{service:"superTeam",cmd:"sendSuperTeamCustomSysMsg",trivialErrorCodes:[7101]},"21_17":{service:"superTeam",cmd:"onDeleteSuperTeamMsg"},"21_18":{service:"superTeam",cmd:"onSuperTeamMsgDelete",response:[{type:"Property",name:"sysMsg"}]},"21_19":{service:"superTeam",cmd:"superTeamCustomSysMsg",response:[{type:"Property",name:"sysMsg"}]},"21_20":{service:"superTeam",cmd:"applySuperTeam",response:[{type:"Property",name:"team"}]},"21_21":{service:"superTeam",cmd:"passSuperTeamApply"},"21_22":{service:"superTeam",cmd:"rejectSuperTeamApply"},"21_23":{service:"superTeam",cmd:"acceptSuperTeamInvite",response:[{type:"Property",name:"team"}]},"21_24":{service:"superTeam",cmd:"rejectSuperTeamInvite"},"21_25":{service:"superTeam",cmd:"onMarkSuperTeamSessionAck"},"21_26":{service:"superTeam",cmd:"addSuperTeamManagers"},"21_27":{service:"superTeam",cmd:"removeSuperTeamManagers"},"21_28":{service:"superTeam",cmd:"updateSuperTeamMute"},"21_29":{service:"superTeam",cmd:"updateSuperTeamMembersMute",response:[{type:"long",name:"timetag"}]},"21_30":{service:"superTeam",cmd:"updateNickInSuperTeam"},"21_31":{service:"superTeam",cmd:"transferSuperTeam"},"21_32":{service:"superTeam",cmd:"onMarkSuperTeamSessionsAck"},"21_33":{service:"superTeam",cmd:"getSuperTeamMembersByAccounts",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_34":{service:"superTeam",cmd:"getMutedSuperTeamMembers",response:[{type:"PropertyArray",name:"members",entity:"superTeamMember"}]},"21_113":{service:"superTeam",cmd:"syncSuperTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"superTeamMember"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_111":{service:"superTeam",cmd:"syncMySuperTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"superTeamMember"},{type:"long",name:"timetag"}]},"21_109":{service:"superTeam",cmd:"syncSuperTeams",response:[{type:"PropertyArray",name:"teams",entity:"superTeam"},{type:"bool",name:"isAll"},{type:"long",name:"timetag"}]},"21_101":{service:"superTeam",cmd:"syncCreateSuperTeam",response:[{type:"Property",name:"team"}]},"21_102":{service:"superTeam",cmd:"syncSendSuperTeamMsg",response:[{type:"Property",name:"msg"}]},"21_110":{service:"superTeam",cmd:"syncUpdateSuperTeamMember",response:[{type:"Property",name:"teamMember",entity:"superTeamMember"}]},"21_117":{service:"superTeam",cmd:"syncDeleteSuperTeamMsg",response:[{type:"Property",name:"sysMsg"}]},"21_125":{service:"superTeam",cmd:"syncMarkSuperTeamSessionAck",response:[{type:"Long",name:"to"},{type:"Long",name:"timetag"}]},"8_1":{service:"team",cmd:"createTeam",response:[{type:"Property",name:"team"},{type:"StrArray",name:"abortedAccidList"}]},"8_2":{service:"team",cmd:"sendTeamMsg",response:[{type:"Property",name:"msg"}]},"8_3":{service:"team",cmd:"teamMsg",response:[{type:"Property",name:"msg"}]},"8_4":{service:"team",cmd:"teamMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_5":{service:"team",cmd:"addTeamMembers",response:[{type:"long",name:"time"},{type:"StrArray",name:"abortedAccidList"}]},"8_6":{service:"team",cmd:"removeTeamMembers"},"8_7":{service:"team",cmd:"updateTeam",response:[{type:"Number",name:"id"},{type:"Number",name:"time"}]},"8_8":{service:"team",cmd:"leaveTeam"},"8_9":{service:"team",cmd:"getTeam",response:[{type:"Property",name:"team"}]},"8_10":{service:"team",cmd:"getTeams",response:[{type:"PropertyArray",name:"teams",entity:"team"},{type:"Number",name:"timetag"}]},"8_11":{service:"team",cmd:"getTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_12":{service:"team",cmd:"dismissTeam"},"8_13":{service:"team",cmd:"applyTeam",response:[{type:"Property",name:"team"}]},"8_14":{service:"team",cmd:"passTeamApply"},"8_15":{service:"team",cmd:"rejectTeamApply"},"8_16":{service:"team",cmd:"addTeamManagers"},"8_17":{service:"team",cmd:"removeTeamManagers"},"8_18":{service:"team",cmd:"transferTeam"},"8_19":{service:"team",cmd:"updateInfoInTeam"},"8_20":{service:"team",cmd:"updateNickInTeam"},"8_21":{service:"team",cmd:"acceptTeamInvite",response:[{type:"Property",name:"team"}]},"8_22":{service:"team",cmd:"rejectTeamInvite"},"8_23":{service:"team",cmd:"getTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_24":{service:"team",cmd:"searchTeamHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"8_25":{service:"team",cmd:"updateMuteStateInTeam"},"8_26":{service:"team",cmd:"getMyTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"teamMember"}]},"8_27":{service:"team",cmd:"getMutedTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"teamMembers",entity:"teamMember"}]},"8_28":{service:"team",cmd:"sendTeamMsgReceipt",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_29":{service:"team",cmd:"getTeamMsgReads",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_30":{service:"team",cmd:"getTeamMsgReadAccounts",response:[{type:"Property",name:"teamMsgReceipt"},{type:"StrArray",name:"readAccounts"},{type:"StrArray",name:"unreadAccounts"}]},"8_31":{service:"team",cmd:"notifyTeamMsgReads",response:[{type:"PropertyArray",name:"teamMsgReceipts",entity:"teamMsgReceipt"}]},"8_32":{service:"team",cmd:"muteTeamAll",response:[]},"8_33":{service:"team",cmd:"getTeamMemberInvitorAccid",response:[{type:"object",name:"accountsMap"}]},"8_34":{service:"team",cmd:"getTeamsById",response:[{type:"PropertyArray",name:"teams",entity:"team"},{type:"StrArray",name:"tids"}],trivialErrorCodes:[816]},"8_126":{service:"team",cmd:"syncMyTeamMembers",response:[{type:"PropertyArray",name:"teamMembers",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_109":{service:"team",cmd:"syncTeams",response:[{type:"Number",name:"timetag"},{type:"PropertyArray",name:"teams",entity:"team"}]},"8_111":{service:"team",cmd:"syncTeamMembers",response:[{type:"Number",name:"teamId"},{type:"PropertyArray",name:"members",entity:"teamMember"},{type:"Number",name:"timetag"}]},"8_101":{service:"team",cmd:"syncCreateTeam",response:[{type:"Property",name:"team"}]},"8_102":{service:"team",cmd:"syncSendTeamMsg",response:[{type:"Property",name:"msg"}]},"8_119":{service:"team",cmd:"syncUpdateTeamMember",response:[{type:"Property",name:"teamMember"}]},"12_1":{service:"friend",cmd:"friendRequest"},"12_101":{service:"friend",cmd:"syncFriendRequest",response:[{type:"String",name:"account"},{type:"Number",name:"type"},{type:"String",name:"ps"}]},"12_2":{service:"friend",cmd:"deleteFriend"},"12_102":{service:"friend",cmd:"syncDeleteFriend",response:[{type:"String",name:"account"}]},"12_3":{service:"friend",cmd:"updateFriend"},"12_103":{service:"friend",cmd:"syncUpdateFriend",response:[{type:"Property",name:"friend"}]},"12_4":{service:"friend",cmd:"getFriends",response:[{type:"PropertyArray",name:"friends",entity:"friend"},{type:"Number",name:"timetag"}]},"12_5":{service:"friend",cmd:"syncFriends",response:[{type:"PropertyArray",name:"friends",entity:"friend"},{type:"Number",name:"timetag"}]},"12_6":{service:"friend",cmd:"syncFriendUsers",response:[{type:"PropertyArray",name:"users",entity:"user"},{type:"Number",name:"timetag"}]},"13_1":{service:"chatroom",cmd:"getChatroomAddress",response:[{type:"StrArray",name:"address"}]},"14_1":{service:"eventService",cmd:"publishEvent",response:[{type:"Property",name:"msgEvent"}]},"14_2":{service:"eventService",cmd:"pushEvent",response:[{type:"Property",name:"msgEvent"}]},"14_3":{service:"eventService",cmd:"subscribeEvent",response:[{type:"StrArray",name:"accounts"}]},"14_4":{service:"eventService",cmd:"unSubscribeEventsByAccounts",response:[{type:"StrArray",name:"accounts"}]},"14_5":{service:"eventService",cmd:"unSubscribeEventsByType"},"14_6":{service:"eventService",cmd:"querySubscribeEventsByAccounts",response:[{type:"PropertyArray",name:"msgEventSubscribes",entity:"msgEventSubscribe"}]},"14_7":{service:"eventService",cmd:"querySubscribeEventsByType",response:[{type:"PropertyArray",name:"msgEventSubscribes",entity:"msgEventSubscribe"}]},"14_9":{service:"eventService",cmd:"pushEvents",response:[{type:"PropertyArray",name:"msgEvents",entity:"msgEvent"}]},"22_1":{service:"proxyService",cmd:"httpProxy",response:[{type:"Property",name:"proxyTag"}]},"22_2":{service:"proxyService",cmd:"onProxyMsg",response:[{type:"Property",name:"proxyMsg",entity:"proxyMsgTag"}]},"24_1":{service:"qchat",cmd:"getQChatAddress",response:[{type:"StrArray",name:"address"}]},"101_1":{service:"filter",cmd:"sendFilterMsg",response:[{type:"Property",name:"msg"}]},"101_2":{service:"filter",cmd:"filterMsg",response:[{type:"Property",name:"msg"}]},"101_3":{service:"filter",cmd:"filterSysMsg",response:[{type:"Property",name:"sysMsg"}]},"101_7":{service:"filter",cmd:"sendFilterCustomSysMsg"}});e.exports={idMap:a,cmdConfig:s,packetConfig:c}},function(e,t,n){var r=n(53),o=n(478),i=n(203),a=n(191),s=n(148),c=n(0),u=n(215),l=n(200),p=n(55),m=n(483),f=n(484);e.exports=function(e){c.merge(e,{platform:r,xhr:o,io:i,naturalSort:a,deepAccess:s,util:c,support:u,blob:l,ajax:p,LoggerPlugin:m,usePlugin:f})}},function(e,t,n){var r=n(8),o=n(5),i=n(9),a=n(82).genPrivateUrl,s=n(0),c=s.notundef,u={"-2":"unset","-1":"restricted",0:"common",1:"owner",2:"manager",3:"guest",4:"anonymous"};function l(e){c(e.nick)&&(this.nick=""+e.nick),c(e.avatar)&&(this.avatar=""+e.avatar),c(e.custom)&&(this.custom=""+e.custom)}l.reverse=function(e){var t=s.copy(e);return c(t.chatroomId)&&(t.chatroomId=""+t.chatroomId),c(t.avatar)&&(t.avatar=a(t.avatar)),c(t.type)&&(t.type=u[t.type]),c(t.level)&&(t.level=+t.level),c(t.online)&&(t.online=1==+t.online),c(t.enterTime)&&(t.enterTime=+t.enterTime),c(t.guest)&&(t.guest=1==+t.guest),c(t.blacked)&&(t.blacked=1==+t.blacked),c(t.gaged)&&(t.gaged=1==+t.gaged),c(t.valid)&&(t.valid=1==+t.valid),c(t.updateTime)&&(t.updateTime=+t.updateTime),c(t.tempMuted)?t.tempMuted=1==+t.tempMuted:t.tempMuted=!1,c(t.tempMuteDuration)?t.tempMuteDuration=+t.tempMuteDuration:t.tempMuteDuration=0,t.online||delete t.enterTime,t.guest&&(t.type="guest",delete t.valid),"common"!==t.type&&delete t.level,delete t.guest,t},l.reverseMembers=function(e){return r(e).call(e,(function(e){return o(l).call(l,e)}))},l.validTypes=i(u),l.typeReverseMap=u,e.exports=l},function(e,t,n){var r,o,i,a,s,c,u,l,p,m,f,d,g,h,y,v,b,T,S;e.exports=(r=n(66),n(487),void(r.lib.Cipher||(o=r,i=o.lib,a=i.Base,s=i.WordArray,c=i.BufferedBlockAlgorithm,u=o.enc,u.Utf8,l=u.Base64,p=o.algo.EvpKDF,m=i.Cipher=c.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){c.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?S:b}return function(t){return{encrypt:function(n,r,o){return e(r).encrypt(t,n,r,o)},decrypt:function(n,r,o){return e(r).decrypt(t,n,r,o)}}}}()}),i.StreamCipher=m.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),f=o.mode={},d=i.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),g=f.CBC=function(){var e=d.extend();function t(e,t,n){var r,o=this._iv;o?(r=o,this._iv=void 0):r=this._prevBlock;for(var i=0;i<n;i++)e[t+i]^=r[i]}return e.Encryptor=e.extend({processBlock:function(e,n){var r=this._cipher,o=r.blockSize;t.call(this,e,n,o),r.encryptBlock(e,n),this._prevBlock=e.slice(n,n+o)}}),e.Decryptor=e.extend({processBlock:function(e,n){var r=this._cipher,o=r.blockSize,i=e.slice(n,n+o);r.decryptBlock(e,n),t.call(this,e,n,o),this._prevBlock=i}}),e}(),h=(o.pad={}).Pkcs7={pad:function(e,t){for(var n=4*t,r=n-e.sigBytes%n,o=r<<24|r<<16|r<<8|r,i=[],a=0;a<r;a+=4)i.push(o);var c=s.create(i,r);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},i.BlockCipher=m.extend({cfg:m.cfg.extend({mode:g,padding:h}),reset:function(){var e;m.reset.call(this);var t=this.cfg,n=t.iv,r=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=r.createEncryptor:(e=r.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,n&&n.words):(this._mode=e.call(r,this,n&&n.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),y=i.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),v=(o.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,n=e.salt;return(n?s.create([1398893684,1701076831]).concat(n).concat(t):t).toString(l)},parse:function(e){var t,n=l.parse(e),r=n.words;return 1398893684==r[0]&&1701076831==r[1]&&(t=s.create(r.slice(2,4)),r.splice(0,4),n.sigBytes-=16),y.create({ciphertext:n,salt:t})}},b=i.SerializableCipher=a.extend({cfg:a.extend({format:v}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var o=e.createEncryptor(n,r),i=o.finalize(t),a=o.cfg;return y.create({ciphertext:i,key:n,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),T=(o.kdf={}).OpenSSL={execute:function(e,t,n,r){r||(r=s.random(8));var o=p.create({keySize:t+n}).compute(e,r),i=s.create(o.words.slice(t),4*n);return o.sigBytes=4*t,y.create({key:o,iv:i,salt:r})}},S=i.PasswordBasedCipher=b.extend({cfg:b.cfg.extend({kdf:T}),encrypt:function(e,t,n,r){var o=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize);r.iv=o.iv;var i=b.encrypt.call(this,e,t,o.key,r);return i.mixIn(o),i},decrypt:function(e,t,n,r){r=this.cfg.extend(r),t=this._parse(t,r.format);var o=r.kdf.execute(n,e.keySize,e.ivSize,t.salt);return r.iv=o.iv,b.decrypt.call(this,e,t,o.key,r)}}))))},function(e,t,n){(function(t,n){
/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */var r;r=function(){"use strict";function e(e){return"function"==typeof e}var r=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},o=0,i=void 0,a=void 0,s=function(e,t){d[o]=e,d[o+1]=t,2===(o+=2)&&(a?a(g):T())},c="undefined"!=typeof window?window:void 0,u=c||{},l=u.MutationObserver||u.WebKitMutationObserver,p="undefined"==typeof self&&void 0!==t&&"[object process]"==={}.toString.call(t),m="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function f(){var e=setTimeout;return function(){return e(g,1)}}var d=new Array(1e3);function g(){for(var e=0;e<o;e+=2)(0,d[e])(d[e+1]),d[e]=void 0,d[e+1]=void 0;o=0}var h,y,v,b,T=void 0;function S(e,t){var n=this,r=new this.constructor(M);void 0===r[k]&&N(r);var o=n._state;if(o){var i=arguments[o-1];s((function(){return O(o,r,i,n._result)}))}else A(n,r,e,t);return r}function x(e){if(e&&"object"==typeof e&&e.constructor===this)return e;var t=new this(M);return _(t,e),t}p?T=function(){return t.nextTick(g)}:l?(y=0,v=new l(g),b=document.createTextNode(""),v.observe(b,{characterData:!0}),T=function(){b.data=y=++y%2}):m?((h=new MessageChannel).port1.onmessage=g,T=function(){return h.port2.postMessage(0)}):T=void 0===c?function(){try{var e=Function("return this")().require("vertx");return void 0!==(i=e.runOnLoop||e.runOnContext)?function(){i(g)}:f()}catch(e){return f()}}():f();var k=Math.random().toString(36).substring(2);function M(){}function w(t,n,r){n.constructor===t.constructor&&r===S&&n.constructor.resolve===x?function(e,t){1===t._state?P(e,t._result):2===t._state?I(e,t._result):A(t,void 0,(function(t){return _(e,t)}),(function(t){return I(e,t)}))}(t,n):void 0===r?P(t,n):e(r)?function(e,t,n){s((function(e){var r=!1,o=function(e,t,n,r){try{e.call(t,n,r)}catch(e){return e}}(n,t,(function(n){r||(r=!0,t!==n?_(e,n):P(e,n))}),(function(t){r||(r=!0,I(e,t))}),e._label);!r&&o&&(r=!0,I(e,o))}),e)}(t,n,r):P(t,n)}function _(e,t){if(e===t)I(e,new TypeError("You cannot resolve a promise with itself"));else if(o=typeof(r=t),null===r||"object"!==o&&"function"!==o)P(e,t);else{var n=void 0;try{n=t.then}catch(t){return void I(e,t)}w(e,t,n)}var r,o}function C(e){e._onerror&&e._onerror(e._result),E(e)}function P(e,t){void 0===e._state&&(e._result=t,e._state=1,0!==e._subscribers.length&&s(E,e))}function I(e,t){void 0===e._state&&(e._state=2,e._result=t,s(C,e))}function A(e,t,n,r){var o=e._subscribers,i=o.length;e._onerror=null,o[i]=t,o[i+1]=n,o[i+2]=r,0===i&&e._state&&s(E,e)}function E(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var r=void 0,o=void 0,i=e._result,a=0;a<t.length;a+=3)r=t[a],o=t[a+n],r?O(n,r,o,i):o(i);e._subscribers.length=0}}function O(t,n,r,o){var i=e(r),a=void 0,s=void 0,c=!0;if(i){try{a=r(o)}catch(e){c=!1,s=e}if(n===a)return void I(n,new TypeError("A promises callback cannot return that same promise."))}else a=o;void 0!==n._state||(i&&c?_(n,a):!1===c?I(n,s):1===t?P(n,a):2===t&&I(n,a))}var j=0;function N(e){e[k]=j++,e._state=void 0,e._result=void 0,e._subscribers=[]}var L=function(){function e(e,t){this._instanceConstructor=e,this.promise=new e(M),this.promise[k]||N(this.promise),r(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?P(this.promise,this._result):(this.length=this.length||0,this._enumerate(t),0===this._remaining&&P(this.promise,this._result))):I(this.promise,new Error("Array Methods must be provided an Array"))}return e.prototype._enumerate=function(e){for(var t=0;void 0===this._state&&t<e.length;t++)this._eachEntry(e[t],t)},e.prototype._eachEntry=function(e,t){var n=this._instanceConstructor,r=n.resolve;if(r===x){var o=void 0,i=void 0,a=!1;try{o=e.then}catch(e){a=!0,i=e}if(o===S&&void 0!==e._state)this._settledAt(e._state,t,e._result);else if("function"!=typeof o)this._remaining--,this._result[t]=e;else if(n===R){var s=new n(M);a?I(s,i):w(s,e,o),this._willSettleAt(s,t)}else this._willSettleAt(new n((function(t){return t(e)})),t)}else this._willSettleAt(r(e),t)},e.prototype._settledAt=function(e,t,n){var r=this.promise;void 0===r._state&&(this._remaining--,2===e?I(r,n):this._result[t]=n),0===this._remaining&&P(r,this._result)},e.prototype._willSettleAt=function(e,t){var n=this;A(e,void 0,(function(e){return n._settledAt(1,t,e)}),(function(e){return n._settledAt(2,t,e)}))},e}(),R=function(){function t(e){this[k]=j++,this._result=this._state=void 0,this._subscribers=[],M!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof t?function(e,t){try{t((function(t){_(e,t)}),(function(t){I(e,t)}))}catch(t){I(e,t)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return t.prototype.catch=function(e){return this.then(null,e)},t.prototype.finally=function(t){var n=this.constructor;return e(t)?this.then((function(e){return n.resolve(t()).then((function(){return e}))}),(function(e){return n.resolve(t()).then((function(){throw e}))})):this.then(t,t)},t}();return R.prototype.then=S,R.all=function(e){return new L(this,e).promise},R.race=function(e){var t=this;return r(e)?new t((function(n,r){for(var o=e.length,i=0;i<o;i++)t.resolve(e[i]).then(n,r)})):new t((function(e,t){return t(new TypeError("You must pass an array to race."))}))},R.resolve=x,R.reject=function(e){var t=new this(M);return I(t,e),t},R._setScheduler=function(e){a=e},R._setAsap=function(e){s=e},R._asap=s,R.polyfill=function(){var e=void 0;if(void 0!==n)e=n;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var r=null;try{r=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===r&&!t.cast)return}e.Promise=R},R.Promise=R,R},e.exports=r()}).call(this,n(223),n(49))},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var c,u=[],l=!1,p=-1;function m(){l&&c&&(l=!1,c.length?u=c.concat(u):p=-1,u.length&&f())}function f(){if(!l){var e=s(m);l=!0;for(var t=u.length;t;){for(c=u,u=[];++p<t;)c&&c[p].run();p=-1,t=u.length}c=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function g(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new d(e,t)),1!==u.length||l||s(f)},d.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=g,o.addListener=g,o.once=g,o.off=g,o.removeListener=g,o.removeAllListeners=g,o.emit=g,o.prependListener=g,o.prependOnceListener=g,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,n){var r=n(16),o=n(225),i=Array.prototype;e.exports=function(e){var t=e.indexOf;return e===i||r(i,e)&&t===i.indexOf?o:t}},function(e,t,n){n(226);var r=n(22);e.exports=r("Array").indexOf},function(e,t,n){"use strict";var r=n(2),o=n(7),i=n(128).indexOf,a=n(71),s=o([].indexOf),c=!!s&&1/s([1],1,-0)<0,u=a("indexOf");r({target:"Array",proto:!0,forced:c||!u},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return c?s(this,e,t)||0:i(this,e,t)}})},function(e,t,n){var r=n(1),o=n(31),i=n(27),a=n(121),s=n(123),c=n(228),u=n(18),l=r.TypeError,p=u("toPrimitive");e.exports=function(e,t){if(!i(e)||a(e))return e;var n,r=s(e,p);if(r){if(void 0===t&&(t="default"),n=o(r,e,t),!i(n)||a(n))return n;throw l("Can't convert object to primitive value")}return void 0===t&&(t="number"),c(e,t)}},function(e,t,n){var r=n(1),o=n(31),i=n(21),a=n(27),s=r.TypeError;e.exports=function(e,t){var n,r;if("string"===t&&i(n=e.toString)&&!a(r=o(n,e)))return r;if(i(n=e.valueOf)&&!a(r=o(n,e)))return r;if("string"!==t&&i(n=e.toString)&&!a(r=o(n,e)))return r;throw s("Can't convert object to primitive value")}},function(e,t,n){var r=n(1),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},function(e,t,n){var r=n(85),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},function(e,t,n){var r=n(232);e.exports=r},function(e,t,n){var r=n(16),o=n(233),i=Array.prototype;e.exports=function(e){var t=e.concat;return e===i||r(i,e)&&t===i.concat?o:t}},function(e,t,n){n(162);var r=n(22);e.exports=r("Array").concat},function(e,t,n){var r=n(1),o=n(72),i=n(99),a=n(27),s=n(18)("species"),c=r.Array;e.exports=function(e){var t;return o(e)&&(t=e.constructor,(i(t)&&(t===c||o(t.prototype))||a(t)&&null===(t=t[s]))&&(t=void 0)),void 0===t?c:t}},function(e,t,n){n(100),n(242);var r=n(13);e.exports=r.Array.from},function(e,t,n){var r=n(7),o=n(85),i=n(42),a=n(84),s=r("".charAt),c=r("".charCodeAt),u=r("".slice),l=function(e){return function(t,n){var r,l,p=i(a(t)),m=o(n),f=p.length;return m<0||m>=f?e?"":void 0:(r=c(p,m))<55296||r>56319||m+1===f||(l=c(p,m+1))<56320||l>57343?e?s(p,m):r:e?u(p,m,m+2):l-56320+(r-55296<<10)+65536}};e.exports={codeAt:l(!1),charAt:l(!0)}},function(e,t,n){var r=n(1),o=n(21),i=n(131),a=r.WeakMap;e.exports=o(a)&&/native code/.test(i(a))},function(e,t,n){"use strict";var r=n(166).IteratorPrototype,o=n(87),i=n(57),a=n(88),s=n(75),c=function(){return this};e.exports=function(e,t,n,u){var l=t+" Iterator";return e.prototype=o(r,{next:i(+!u,n)}),a(e,l,!1,!0),s[l]=c,e}},function(e,t,n){var r=n(11);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},function(e,t,n){"use strict";var r=n(130),o=n(47);e.exports=r?{}.toString:function(){return"[object "+o(this)+"]"}},function(e,t,n){var r=n(1),o=n(21),i=r.String,a=r.TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw a("Can't set "+i(e)+" as a prototype")}},function(e,t,n){var r=n(2),o=n(243);r({target:"Array",stat:!0,forced:!n(172)((function(e){Array.from(e)}))},{from:o})},function(e,t,n){"use strict";var r=n(1),o=n(70),i=n(31),a=n(37),s=n(244),c=n(170),u=n(99),l=n(38),p=n(73),m=n(171),f=n(104),d=r.Array;e.exports=function(e){var t=a(e),n=u(this),r=arguments.length,g=r>1?arguments[1]:void 0,h=void 0!==g;h&&(g=o(g,r>2?arguments[2]:void 0));var y,v,b,T,S,x,k=f(t),M=0;if(!k||this==d&&c(k))for(y=l(t),v=n?new this(y):d(y);y>M;M++)x=h?g(t[M],M):t[M],p(v,M,x);else for(S=(T=m(t,k)).next,v=n?new this:[];!(b=i(S,T)).done;M++)x=h?s(T,g,[b.value,M],!0):b.value,p(v,M,x);return v.length=M,v}},function(e,t,n){var r=n(34),o=n(169);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(t){o(e,"throw",t)}}},function(e,t,n){n(162),n(89),n(174),n(246),n(247),n(248),n(249),n(177),n(250),n(251),n(252),n(253),n(254),n(255),n(256),n(257),n(258),n(259),n(260),n(261);var r=n(13);e.exports=r.Symbol},function(e,t,n){n(20)("asyncIterator")},function(e,t){},function(e,t,n){n(20)("hasInstance")},function(e,t,n){n(20)("isConcatSpreadable")},function(e,t,n){n(20)("match")},function(e,t,n){n(20)("matchAll")},function(e,t,n){n(20)("replace")},function(e,t,n){n(20)("search")},function(e,t,n){n(20)("species")},function(e,t,n){n(20)("split")},function(e,t,n){n(20)("toPrimitive")},function(e,t,n){n(20)("toStringTag")},function(e,t,n){n(20)("unscopables")},function(e,t,n){var r=n(1);n(88)(r.JSON,"JSON",!0)},function(e,t){},function(e,t){},function(e,t){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(e,t,n){var r=n(264);e.exports=r},function(e,t,n){var r=n(265);e.exports=r},function(e,t,n){var r=n(266);n(59),e.exports=r},function(e,t,n){n(76),n(100);var r=n(104);e.exports=r},function(e,t,n){n(174);var r=n(13);e.exports=r.Object.getOwnPropertySymbols},function(e,t,n){var r=n(269);e.exports=r},function(e,t,n){n(270);var r=n(13).Object,o=e.exports=function(e,t){return r.getOwnPropertyDescriptor(e,t)};r.getOwnPropertyDescriptor.sham&&(o.sham=!0)},function(e,t,n){var r=n(2),o=n(11),i=n(35),a=n(67).f,s=n(24),c=o((function(){a(1)}));r({target:"Object",stat:!0,forced:!s||c,sham:!s},{getOwnPropertyDescriptor:function(e,t){return a(i(e),t)}})},function(e,t,n){var r=n(272);e.exports=r},function(e,t,n){n(273);var r=n(13);e.exports=r.Object.getOwnPropertyDescriptors},function(e,t,n){var r=n(2),o=n(24),i=n(179),a=n(35),s=n(67),c=n(73);r({target:"Object",stat:!0,sham:!o},{getOwnPropertyDescriptors:function(e){for(var t,n,r=a(e),o=s.f,u=i(r),l={},p=0;u.length>p;)void 0!==(n=o(r,t=u[p++]))&&c(l,t,n);return l}})},function(e,t,n){var r=n(275);e.exports=r},function(e,t,n){n(276);var r=n(13).Object,o=e.exports=function(e,t){return r.defineProperties(e,t)};r.defineProperties.sham&&(o.sham=!0)},function(e,t,n){var r=n(2),o=n(24),i=n(132).f;r({target:"Object",stat:!0,forced:Object.defineProperties!==i,sham:!o},{defineProperties:i})},function(e,t,n){n(278);var r=n(13).Object,o=e.exports=function(e,t,n){return r.defineProperty(e,t,n)};r.defineProperty.sham&&(o.sham=!0)},function(e,t,n){var r=n(2),o=n(24),i=n(40).f;r({target:"Object",stat:!0,forced:Object.defineProperty!==i,sham:!o},{defineProperty:i})},function(e,t,n){var r=n(280);e.exports=r},function(e,t,n){var r=n(180);e.exports=r},function(e,t,n){n(59);var r=n(47),o=n(26),i=n(16),a=n(282),s=Array.prototype,c={DOMTokenList:!0,NodeList:!0};e.exports=function(e){var t=e.forEach;return e===s||i(s,e)&&t===s.forEach||o(c,r(e))?a:t}},function(e,t,n){var r=n(283);e.exports=r},function(e,t,n){n(284);var r=n(22);e.exports=r("Array").forEach},function(e,t,n){"use strict";var r=n(2),o=n(285);r({target:"Array",proto:!0,forced:[].forEach!=o},{forEach:o})},function(e,t,n){"use strict";var r=n(79).forEach,o=n(71)("forEach");e.exports=o?[].forEach:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}},function(e,t,n){var r=n(16),o=n(287),i=Array.prototype;e.exports=function(e){var t=e.slice;return e===i||r(i,e)&&t===i.slice?o:t}},function(e,t,n){n(288);var r=n(22);e.exports=r("Array").slice},function(e,t,n){"use strict";var r=n(2),o=n(1),i=n(72),a=n(99),s=n(27),c=n(98),u=n(38),l=n(35),p=n(73),m=n(18),f=n(86),d=n(90),g=f("slice"),h=m("species"),y=o.Array,v=Math.max;r({target:"Array",proto:!0,forced:!g},{slice:function(e,t){var n,r,o,m=l(this),f=u(m),g=c(e,f),b=c(void 0===t?f:t,f);if(i(m)&&(n=m.constructor,(a(n)&&(n===y||i(n.prototype))||s(n)&&null===(n=n[h]))&&(n=void 0),n===y||void 0===n))return d(m,g,b);for(r=new(void 0===n?y:n)(v(b-g,0)),o=0;g<b;g++,o++)g in m&&p(r,o,m[g]);return r.length=o,r}})},function(e,t,n){n(290);var r=n(13);e.exports=r.Object.keys},function(e,t,n){var r=n(2),o=n(37),i=n(92);r({target:"Object",stat:!0,forced:n(11)((function(){i(1)}))},{keys:function(e){return i(o(e))}})},function(e,t,n){var r=n(292);e.exports=r},function(e,t,n){var r=n(16),o=n(293),i=Array.prototype;e.exports=function(e){var t=e.some;return e===i||r(i,e)&&t===i.some?o:t}},function(e,t,n){n(294);var r=n(22);e.exports=r("Array").some},function(e,t,n){"use strict";var r=n(2),o=n(79).some;r({target:"Array",proto:!0,forced:!n(71)("some")},{some:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var r=n(296);e.exports=r},function(e,t,n){var r=n(16),o=n(297),i=Array.prototype;e.exports=function(e){var t=e.splice;return e===i||r(i,e)&&t===i.splice?o:t}},function(e,t,n){n(298);var r=n(22);e.exports=r("Array").splice},function(e,t,n){"use strict";var r=n(2),o=n(1),i=n(98),a=n(85),s=n(38),c=n(37),u=n(129),l=n(73),p=n(86)("splice"),m=o.TypeError,f=Math.max,d=Math.min;r({target:"Array",proto:!0,forced:!p},{splice:function(e,t){var n,r,o,p,g,h,y=c(this),v=s(y),b=i(e,v),T=arguments.length;if(0===T?n=r=0:1===T?(n=0,r=v-b):(n=T-2,r=d(f(a(t),0),v-b)),v+n-r>9007199254740991)throw m("Maximum allowed length exceeded");for(o=u(y,r),p=0;p<r;p++)(g=b+p)in y&&l(o,p,y[g]);if(o.length=r,n<r){for(p=b;p<v-r;p++)h=p+n,(g=p+r)in y?y[h]=y[g]:delete y[h];for(p=v;p>v-r+n;p--)delete y[p-1]}else if(n>r)for(p=v-r;p>b;p--)h=p+n-1,(g=p+r-1)in y?y[h]=y[g]:delete y[h];for(p=0;p<n;p++)y[p+b]=arguments[p+2];return y.length=v-r+n,o}})},function(e,t,n){var r=n(300);e.exports=r},function(e,t,n){var r=n(16),o=n(301),i=Array.prototype;e.exports=function(e){var t=e.sort;return e===i||r(i,e)&&t===i.sort?o:t}},function(e,t,n){n(302);var r=n(22);e.exports=r("Array").sort},function(e,t,n){"use strict";var r=n(2),o=n(7),i=n(39),a=n(37),s=n(38),c=n(42),u=n(11),l=n(303),p=n(71),m=n(304),f=n(305),d=n(69),g=n(306),h=[],y=o(h.sort),v=o(h.push),b=u((function(){h.sort(void 0)})),T=u((function(){h.sort(null)})),S=p("sort"),x=!u((function(){if(d)return d<70;if(!(m&&m>3)){if(f)return!0;if(g)return g<603;var e,t,n,r,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)h.push({k:t+r,v:n})}for(h.sort((function(e,t){return t.v-e.v})),r=0;r<h.length;r++)t=h[r].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}}));r({target:"Array",proto:!0,forced:b||!T||!S||!x},{sort:function(e){void 0!==e&&i(e);var t=a(this);if(x)return void 0===e?y(t):y(t,e);var n,r,o=[],u=s(t);for(r=0;r<u;r++)r in t&&v(o,t[r]);for(l(o,function(e){return function(t,n){return void 0===n?-1:void 0===t?1:void 0!==e?+e(t,n)||0:c(t)>c(n)?1:-1}}(e)),n=o.length,r=0;r<n;)t[r]=o[r++];for(;r<u;)delete t[r++];return t}})},function(e,t,n){var r=n(176),o=Math.floor,i=function(e,t){var n=e.length,c=o(n/2);return n<8?a(e,t):s(e,i(r(e,0,c),t),i(r(e,c),t),t)},a=function(e,t){for(var n,r,o=e.length,i=1;i<o;){for(r=i,n=e[i];r&&t(e[r-1],n)>0;)e[r]=e[--r];r!==i++&&(e[r]=n)}return e},s=function(e,t,n,r){for(var o=t.length,i=n.length,a=0,s=0;a<o||s<i;)e[a+s]=a<o&&s<i?r(t[a],n[s])<=0?t[a++]:n[s++]:a<o?t[a++]:n[s++];return e};e.exports=i},function(e,t,n){var r=n(45).match(/firefox\/(\d+)/i);e.exports=!!r&&+r[1]},function(e,t,n){var r=n(45);e.exports=/MSIE|Trident/.test(r)},function(e,t,n){var r=n(45).match(/AppleWebKit\/(\d+)\./);e.exports=!!r&&+r[1]},function(e,t,n){n(308);var r=n(13);e.exports=r.Array.isArray},function(e,t,n){n(2)({target:"Array",stat:!0},{isArray:n(72)})},function(e,t,n){var r=n(310);e.exports=r},function(e,t,n){var r=n(16),o=n(311),i=Array.prototype;e.exports=function(e){var t=e.map;return e===i||r(i,e)&&t===i.map?o:t}},function(e,t,n){n(312);var r=n(22);e.exports=r("Array").map},function(e,t,n){"use strict";var r=n(2),o=n(79).map;r({target:"Array",proto:!0,forced:!n(86)("map")},{map:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var r=n(314);e.exports=r},function(e,t,n){n(315);var r=n(13),o=n(56);r.JSON||(r.JSON={stringify:JSON.stringify}),e.exports=function(e,t,n){return o(r.JSON.stringify,null,arguments)}},function(e,t,n){var r=n(2),o=n(1),i=n(36),a=n(56),s=n(7),c=n(11),u=o.Array,l=i("JSON","stringify"),p=s(/./.exec),m=s("".charAt),f=s("".charCodeAt),d=s("".replace),g=s(1..toString),h=/[\uD800-\uDFFF]/g,y=/^[\uD800-\uDBFF]$/,v=/^[\uDC00-\uDFFF]$/,b=function(e,t,n){var r=m(n,t-1),o=m(n,t+1);return p(y,e)&&!p(v,o)||p(v,e)&&!p(y,r)?"\\u"+g(f(e,0),16):e},T=c((function(){return'"\\udf06\\ud834"'!==l("\udf06\ud834")||'"\\udead"'!==l("\udead")}));l&&r({target:"JSON",stat:!0,forced:T},{stringify:function(e,t,n){for(var r=0,o=arguments.length,i=u(o);r<o;r++)i[r]=arguments[r];var s=a(l,null,i);return"string"==typeof s?d(s,h,b):s}})},function(e,t,n){var r=n(317);e.exports=r},function(e,t,n){var r=n(16),o=n(318),i=Array.prototype;e.exports=function(e){var t=e.reduce;return e===i||r(i,e)&&t===i.reduce?o:t}},function(e,t,n){n(319);var r=n(22);e.exports=r("Array").reduce},function(e,t,n){"use strict";var r=n(2),o=n(320).left,i=n(71),a=n(69),s=n(106);r({target:"Array",proto:!0,forced:!i("reduce")||!s&&a>79&&a<83},{reduce:function(e){var t=arguments.length;return o(this,e,t,t>1?arguments[1]:void 0)}})},function(e,t,n){var r=n(1),o=n(39),i=n(37),a=n(95),s=n(38),c=r.TypeError,u=function(e){return function(t,n,r,u){o(n);var l=i(t),p=a(l),m=s(l),f=e?m-1:0,d=e?-1:1;if(r<2)for(;;){if(f in p){u=p[f],f+=d;break}if(f+=d,e?f<0:m<=f)throw c("Reduce of empty array with no initial value")}for(;e?f>=0:m>f;f+=d)f in p&&(u=n(u,p[f],f,l));return u}};e.exports={left:u(!1),right:u(!0)}},function(e,t,n){var r=n(322);e.exports=r},function(e,t,n){var r=n(16),o=n(323),i=Array.prototype;e.exports=function(e){var t=e.lastIndexOf;return e===i||r(i,e)&&t===i.lastIndexOf?o:t}},function(e,t,n){n(324);var r=n(22);e.exports=r("Array").lastIndexOf},function(e,t,n){var r=n(2),o=n(325);r({target:"Array",proto:!0,forced:o!==[].lastIndexOf},{lastIndexOf:o})},function(e,t,n){"use strict";var r=n(56),o=n(35),i=n(85),a=n(38),s=n(71),c=Math.min,u=[].lastIndexOf,l=!!u&&1/[1].lastIndexOf(1,-0)<0,p=s("lastIndexOf"),m=l||!p;e.exports=m?function(e){if(l)return r(u,this,arguments)||0;var t=o(this),n=a(t),s=n-1;for(arguments.length>1&&(s=c(s,i(arguments[1]))),s<0&&(s=n+s);s>=0;s--)if(s in t&&t[s]===e)return s||0;return-1}:u},function(e,t,n){n(184);var r=n(13);e.exports=r.setTimeout},function(e,t,n){var r=n(328);e.exports=r},function(e,t,n){var r=n(16),o=n(329),i=Array.prototype;e.exports=function(e){var t=e.filter;return e===i||r(i,e)&&t===i.filter?o:t}},function(e,t,n){n(330);var r=n(22);e.exports=r("Array").filter},function(e,t,n){"use strict";var r=n(2),o=n(79).filter;r({target:"Array",proto:!0,forced:!n(86)("filter")},{filter:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){e.exports=n(332)},function(e,t,n){n(59);var r=n(47),o=n(26),i=n(16),a=n(333),s=Array.prototype,c={DOMTokenList:!0,NodeList:!0};e.exports=function(e){var t=e.values;return e===s||i(s,e)&&t===s.values||o(c,r(e))?a:t}},function(e,t,n){var r=n(334);e.exports=r},function(e,t,n){n(76),n(89);var r=n(22);e.exports=r("Array").values},function(e,t,n){n(209),n(76),n(89),n(341),n(210),n(211),n(352),n(100);var r=n(13);e.exports=r.Promise},function(e,t,n){var r=n(26),o=n(179),i=n(67),a=n(40);e.exports=function(e,t,n){for(var s=o(t),c=a.f,u=i.f,l=0;l<s.length;l++){var p=s[l];r(e,p)||n&&r(n,p)||c(e,p,u(t,p))}}},function(e,t,n){var r=n(7)("".replace),o=String(Error("zxcasd").stack),i=/\n\s*at [^:]*:[^\n]*/,a=i.test(o);e.exports=function(e,t){if(a&&"string"==typeof e)for(;t--;)e=r(e,i,"");return e}},function(e,t,n){var r=n(27),o=n(46);e.exports=function(e,t){r(t)&&"cause"in t&&o(e,"cause",t.cause)}},function(e,t,n){var r=n(42);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:r(e)}},function(e,t,n){var r=n(11),o=n(57);e.exports=!r((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",o(1,7)),7!==e.stack)}))},function(e,t,n){"use strict";var r,o,i,a,s=n(2),c=n(58),u=n(1),l=n(36),p=n(31),m=n(186),f=n(74),d=n(342),g=n(135),h=n(88),y=n(343),v=n(39),b=n(21),T=n(27),S=n(344),x=n(131),k=n(108),M=n(172),w=n(187),_=n(188).set,C=n(346),P=n(190),I=n(349),A=n(93),E=n(116),O=n(350),j=n(101),N=n(160),L=n(18),R=n(351),D=n(106),F=n(69),U=L("species"),B="Promise",q=j.getterFor(B),H=j.set,z=j.getterFor(B),W=m&&m.prototype,G=m,X=W,K=u.TypeError,$=u.document,J=u.process,V=A.f,Q=V,Y=!!($&&$.createEvent&&u.dispatchEvent),Z=b(u.PromiseRejectionEvent),ee=!1,te=N(B,(function(){var e=x(G),t=e!==String(G);if(!t&&66===F)return!0;if(c&&!X.finally)return!0;if(F>=51&&/native code/.test(e))return!1;var n=new G((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};return(n.constructor={})[U]=r,!(ee=n.then((function(){}))instanceof r)||!t&&R&&!Z})),ne=te||!M((function(e){G.all(e).catch((function(){}))})),re=function(e){var t;return!(!T(e)||!b(t=e.then))&&t},oe=function(e,t){var n,r,o,i=t.value,a=1==t.state,s=a?e.ok:e.fail,c=e.resolve,u=e.reject,l=e.domain;try{s?(a||(2===t.rejection&&ue(t),t.rejection=1),!0===s?n=i:(l&&l.enter(),n=s(i),l&&(l.exit(),o=!0)),n===e.promise?u(K("Promise-chain cycle")):(r=re(n))?p(r,n,c,u):c(n)):u(i)}catch(e){l&&!o&&l.exit(),u(e)}},ie=function(e,t){e.notified||(e.notified=!0,C((function(){for(var n,r=e.reactions;n=r.get();)oe(n,e);e.notified=!1,t&&!e.rejection&&se(e)})))},ae=function(e,t,n){var r,o;Y?((r=$.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),u.dispatchEvent(r)):r={promise:t,reason:n},!Z&&(o=u["on"+e])?o(r):"unhandledrejection"===e&&I("Unhandled promise rejection",n)},se=function(e){p(_,u,(function(){var t,n=e.facade,r=e.value;if(ce(e)&&(t=E((function(){D?J.emit("unhandledRejection",r,n):ae("unhandledrejection",n,r)})),e.rejection=D||ce(e)?2:1,t.error))throw t.value}))},ce=function(e){return 1!==e.rejection&&!e.parent},ue=function(e){p(_,u,(function(){var t=e.facade;D?J.emit("rejectionHandled",t):ae("rejectionhandled",t,e.value)}))},le=function(e,t,n){return function(r){e(t,r,n)}},pe=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,ie(e,!0))},me=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw K("Promise can't be resolved itself");var r=re(t);r?C((function(){var n={done:!1};try{p(r,t,le(me,n,e),le(pe,n,e))}catch(t){pe(n,t,e)}})):(e.value=t,e.state=1,ie(e,!1))}catch(t){pe({done:!1},t,e)}}};if(te&&(X=(G=function(e){S(this,X),v(e),p(r,this);var t=q(this);try{e(le(me,t),le(pe,t))}catch(e){pe(t,e)}}).prototype,(r=function(e){H(this,{type:B,done:!1,notified:!1,parent:!1,reactions:new O,rejection:!1,state:0,value:void 0})}).prototype=d(X,{then:function(e,t){var n=z(this),r=V(w(this,G));return n.parent=!0,r.ok=!b(e)||e,r.fail=b(t)&&t,r.domain=D?J.domain:void 0,0==n.state?n.reactions.add(r):C((function(){oe(r,n)})),r.promise},catch:function(e){return this.then(void 0,e)}}),o=function(){var e=new r,t=q(e);this.promise=e,this.resolve=le(me,t),this.reject=le(pe,t)},A.f=V=function(e){return e===G||e===i?new o(e):Q(e)},!c&&b(m)&&W!==Object.prototype)){a=W.then,ee||(f(W,"then",(function(e,t){var n=this;return new G((function(e,t){p(a,n,e,t)})).then(e,t)}),{unsafe:!0}),f(W,"catch",X.catch,{unsafe:!0}));try{delete W.constructor}catch(e){}g&&g(W,X)}s({global:!0,wrap:!0,forced:te},{Promise:G}),h(G,B,!1,!0),y(B),i=l(B),s({target:B,stat:!0,forced:te},{reject:function(e){var t=V(this);return p(t.reject,void 0,e),t.promise}}),s({target:B,stat:!0,forced:c||te},{resolve:function(e){return P(c&&this===i?G:this,e)}}),s({target:B,stat:!0,forced:ne},{all:function(e){var t=this,n=V(t),r=n.resolve,o=n.reject,i=E((function(){var n=v(t.resolve),i=[],a=0,s=1;k(e,(function(e){var c=a++,u=!1;s++,p(n,t,e).then((function(e){u||(u=!0,i[c]=e,--s||r(i))}),o)})),--s||r(i)}));return i.error&&o(i.value),n.promise},race:function(e){var t=this,n=V(t),r=n.reject,o=E((function(){var o=v(t.resolve);k(e,(function(e){p(o,t,e).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}})},function(e,t,n){var r=n(74);e.exports=function(e,t,n){for(var o in t)n&&n.unsafe&&e[o]?e[o]=t[o]:r(e,o,t[o],n);return e}},function(e,t,n){"use strict";var r=n(36),o=n(40),i=n(18),a=n(24),s=i("species");e.exports=function(e){var t=r(e),n=o.f;a&&t&&!t[s]&&n(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){var r=n(1),o=n(16),i=r.TypeError;e.exports=function(e,t){if(o(t,e))return e;throw i("Incorrect invocation")}},function(e,t,n){var r=n(1),o=n(99),i=n(97),a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not a constructor")}},function(e,t,n){var r,o,i,a,s,c,u,l,p=n(1),m=n(70),f=n(67).f,d=n(188).set,g=n(189),h=n(347),y=n(348),v=n(106),b=p.MutationObserver||p.WebKitMutationObserver,T=p.document,S=p.process,x=p.Promise,k=f(p,"queueMicrotask"),M=k&&k.value;M||(r=function(){var e,t;for(v&&(e=S.domain)&&e.exit();o;){t=o.fn,o=o.next;try{t()}catch(e){throw o?a():i=void 0,e}}i=void 0,e&&e.enter()},g||v||y||!b||!T?!h&&x&&x.resolve?((u=x.resolve(void 0)).constructor=x,l=m(u.then,u),a=function(){l(r)}):v?a=function(){S.nextTick(r)}:(d=m(d,p),a=function(){d(r)}):(s=!0,c=T.createTextNode(""),new b(r).observe(c,{characterData:!0}),a=function(){c.data=s=!s})),e.exports=M||function(e){var t={fn:e,next:void 0};i&&(i.next=t),o||(o=t,a()),i=t}},function(e,t,n){var r=n(45),o=n(1);e.exports=/ipad|iphone|ipod/i.test(r)&&void 0!==o.Pebble},function(e,t,n){var r=n(45);e.exports=/web0s(?!.*chrome)/i.test(r)},function(e,t,n){var r=n(1);e.exports=function(e,t){var n=r.console;n&&n.error&&(1==arguments.length?n.error(e):n.error(e,t))}},function(e,t){var n=function(){this.head=null,this.tail=null};n.prototype={add:function(e){var t={item:e,next:null};this.head?this.tail.next=t:this.head=t,this.tail=t},get:function(){var e=this.head;if(e)return this.head=e.next,this.tail===e&&(this.tail=null),e.item}},e.exports=n},function(e,t){e.exports="object"==typeof window},function(e,t,n){"use strict";var r=n(2),o=n(58),i=n(186),a=n(11),s=n(36),c=n(21),u=n(187),l=n(190),p=n(74);if(r({target:"Promise",proto:!0,real:!0,forced:!!i&&a((function(){i.prototype.finally.call({then:function(){}},(function(){}))}))},{finally:function(e){var t=u(this,s("Promise")),n=c(e);return this.then(n?function(n){return l(t,e()).then((function(){return n}))}:e,n?function(n){return l(t,e()).then((function(){throw n}))}:e)}}),!o&&c(i)){var m=s("Promise").prototype.finally;i.prototype.finally!==m&&p(i.prototype,"finally",m,{unsafe:!0})}},function(e,t,n){var r=n(354);n(355),n(356),n(357),n(358),n(359),n(360),n(361),e.exports=r},function(e,t,n){var r=n(173);e.exports=r},function(e,t,n){n(20)("asyncDispose")},function(e,t,n){n(20)("dispose")},function(e,t,n){n(20)("matcher")},function(e,t,n){n(20)("metadata")},function(e,t,n){n(20)("observable")},function(e,t,n){n(20)("patternMatch")},function(e,t,n){n(20)("replaceAll")},function(e,t,n){e.exports=n(363)},function(e,t,n){var r=n(364);e.exports=r},function(e,t,n){var r=n(365);e.exports=r},function(e,t,n){var r=n(366);n(59),e.exports=r},function(e,t,n){n(76),n(89),n(100),n(177);var r=n(138);e.exports=r.f("iterator")},function(e,t,n){var r=n(15);"undefined"!=typeof window&&(window.console||r.isWeixinApp||(window.console={log:function(){},info:function(){},warn:function(){},error:function(){}}))},function(e,t,n){var r=n(192),o=n(193);e.exports=function(e){if(r(e))return o(e)},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(370);e.exports=r},function(e,t,n){var r=n(183);e.exports=r},function(e,t,n){var r=n(139),o=n(80),i=n(194);e.exports=function(e){if(void 0!==r&&null!=o(e)||null!=e["@@iterator"])return i(e)},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(373);e.exports=r},function(e,t,n){var r=n(163);e.exports=r},function(e,t,n){e.exports=n(375)},function(e,t,n){var r=n(376);e.exports=r},function(e,t,n){var r=n(181);e.exports=r},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(379);e.exports=r},function(e,t,n){n(380);var r=n(13).Object;e.exports=function(e,t){return r.create(e,t)}},function(e,t,n){n(2)({target:"Object",stat:!0,sham:!n(24)},{create:n(87)})},function(e,t,n){e.exports=n(382)},function(e,t,n){var r=n(383);e.exports=r},function(e,t,n){n(384);var r=n(13).Object;e.exports=function(e){return r.getOwnPropertyNames(e)}},function(e,t,n){var r=n(2),o=n(11),i=n(175).f;r({target:"Object",stat:!0,forced:o((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:i})},function(e,t,n){"use strict";var r=Object.prototype.hasOwnProperty,o="~";function i(){}function a(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function s(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(o=!1)),s.prototype.eventNames=function(){var e,t,n=[];if(0===this._eventsCount)return n;for(t in e=this._events)r.call(e,t)&&n.push(o?t.slice(1):t);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},s.prototype.listeners=function(e,t){var n=o?o+e:e,r=this._events[n];if(t)return!!r;if(!r)return[];if(r.fn)return[r.fn];for(var i=0,a=r.length,s=new Array(a);i<a;i++)s[i]=r[i].fn;return s},s.prototype.emit=function(e,t,n,r,i,a){var s=o?o+e:e;if(!this._events[s])return!1;var c,u,l=this._events[s],p=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),p){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,r),!0;case 5:return l.fn.call(l.context,t,n,r,i),!0;case 6:return l.fn.call(l.context,t,n,r,i,a),!0}for(u=1,c=new Array(p-1);u<p;u++)c[u-1]=arguments[u];l.fn.apply(l.context,c)}else{var m,f=l.length;for(u=0;u<f;u++)switch(l[u].once&&this.removeListener(e,l[u].fn,void 0,!0),p){case 1:l[u].fn.call(l[u].context);break;case 2:l[u].fn.call(l[u].context,t);break;case 3:l[u].fn.call(l[u].context,t,n);break;case 4:l[u].fn.call(l[u].context,t,n,r);break;default:if(!c)for(m=1,c=new Array(p-1);m<p;m++)c[m-1]=arguments[m];l[u].fn.apply(l[u].context,c)}}return!0},s.prototype.on=function(e,t,n){var r=new a(t,n||this),i=o?o+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],r]:this._events[i].push(r):(this._events[i]=r,this._eventsCount++),this},s.prototype.once=function(e,t,n){var r=new a(t,n||this,!0),i=o?o+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],r]:this._events[i].push(r):(this._events[i]=r,this._eventsCount++),this},s.prototype.removeListener=function(e,t,n,r){var a=o?o+e:e;if(!this._events[a])return this;if(!t)return 0==--this._eventsCount?this._events=new i:delete this._events[a],this;var s=this._events[a];if(s.fn)s.fn!==t||r&&!s.once||n&&s.context!==n||(0==--this._eventsCount?this._events=new i:delete this._events[a]);else{for(var c=0,u=[],l=s.length;c<l;c++)(s[c].fn!==t||r&&!s[c].once||n&&s[c].context!==n)&&u.push(s[c]);u.length?this._events[a]=1===u.length?u[0]:u:0==--this._eventsCount?this._events=new i:delete this._events[a]}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=o?o+e:e,this._events[t]&&(0==--this._eventsCount?this._events=new i:delete this._events[t])):(this._events=new i,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prototype.setMaxListeners=function(){return this},s.prefixed=o,s.EventEmitter=s,e.exports=s},function(e,t,n){e.exports=n(387)},function(e,t,n){var r=n(388);e.exports=r},function(e,t,n){n(389);var r=n(13);e.exports=r.parseFloat},function(e,t,n){var r=n(2),o=n(390);r({global:!0,forced:parseFloat!=o},{parseFloat:o})},function(e,t,n){var r=n(1),o=n(11),i=n(7),a=n(42),s=n(140).trim,c=n(109),u=i("".charAt),l=r.parseFloat,p=r.Symbol,m=p&&p.iterator,f=1/l(c+"-0")!=-1/0||m&&!o((function(){l(Object(m))}));e.exports=f?function(e){var t=s(a(e)),n=l(t);return 0===n&&"-"==u(t,0)?-0:n}:l},function(e,t,n){var r=n(392);e.exports=r},function(e,t,n){n(393);var r=n(13);e.exports=r.parseInt},function(e,t,n){var r=n(2),o=n(394);r({global:!0,forced:parseInt!=o},{parseInt:o})},function(e,t,n){var r=n(1),o=n(11),i=n(7),a=n(42),s=n(140).trim,c=n(109),u=r.parseInt,l=r.Symbol,p=l&&l.iterator,m=/^[+-]?0x/i,f=i(m.exec),d=8!==u(c+"08")||22!==u(c+"0x16")||p&&!o((function(){u(Object(p))}));e.exports=d?function(e,t){var n=s(a(e));return u(n,t>>>0||(f(m,n)?16:10))}:u},function(e,t,n){var r=n(17),o=n(4),i=n(396),a=n(397),s=n(0).getGlobal(),c=n(53).name,u="function"==typeof i,l=s.indexedDB&&!/^(IE)$/.test(c);function p(e,t){this.logWorker=null,this.db=null,this.logQueue=[],this.callbackList=[],this.preTime=null,this.lastTime=+new Date,this.initLogLocal({name:e,expire:t})}var m=p.prototype;p.enable=!0,m.saveLog=function(e){var t,n=this.logQueue.length,o=this.logQueue[n-1];e.time!==this.preTime?((this.logQueue.length>50||e.time-this.lastTime>6e4&&this.logQueue.length>0)&&(this.doSaveLog(r(t=this.logQueue).call(t,0)),this.lastTime=o.time,this.logQueue=[]),this.logQueue.push(e),this.preTime=e.time):o.log+="\r\n"+e.log},m.doSaveLog=function(){},m.initLogLocal=function(){},m.fetchLog=function(){},m.deleteLogs=function(e){},m.logError=function(){},l?u?(m.doSaveLog=function(e){this.logWorker.postMessage(e)},m.initLogLocal=function(e){var t=this;this.logWorker=new i({}),this.logWorker.onmessage=function(e){var n=e.data||{};switch(n.type){case"fetchDone":t.fetchLogDone(n.code,n.msg);break;case"error":t.logError(n.msg)}},this.logWorker.postMessage({type:"init",msg:e})},m.fetchLog=function(){var e=this;return new o((function(t,n){0===e.callbackList.length&&e.logWorker.postMessage({type:"fetch"}),e.callbackList.push(t),e.callbackList.push(n)}))},m.fetchLogDone=function(e,t){for(var n=200===e?0:1,r=0;r<this.callbackList.length/2;r++)this.callbackList[2*r+n](t);this.callbackList=[]},m.deleteLogs=function(e){return this.logWorker.postMessage({type:"delete",msg:e}),o.resolve()}):(m.doSaveLog=function(e){var t=this;this.db.putLog(e).catch((function(e){t.logError({msg:"putLog error",error:e})}))},m.initLogLocal=function(e){var t=this;this.db=new a(e),this.db.init().catch((function(e){t.logError({msg:"dbLog init error",error:e})}))},m.fetchLog=function(){return this.db.getAllLogs()},m.deleteLogs=function(e){return this.db.deleteLogs(e)}):p.enable=!1,e.exports=p},function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return i}));var r=n(205),o=n.n(r);function i(){return o()('!function(t){var n={};function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,e),o.l=!0,o.exports}e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var o in t)e.d(r,o,function(n){return t[n]}.bind(null,o));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=98)}([function(t,n,e){(function(n){var e=function(t){return t&&t.Math==Math&&t};t.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||function(){return this}()||Function("return this")()}).call(this,e(74))},function(t,n,e){var r=e(15),o=e(7),i=e(72),c=e(14).f;t.exports=function(t){var n=r.Symbol||(r.Symbol={});o(n,t)||c(n,t,{value:i.f(t)})}},function(t,n,e){var r=e(0),o=e(52),i=e(7),c=e(54),u=e(50),a=e(77),f=o("wks"),s=r.Symbol,p=s&&s.for,l=a?s:s&&s.withoutSetter||c;t.exports=function(t){if(!i(f,t)||!u&&"string"!=typeof f[t]){var n="Symbol."+t;u&&i(s,t)?f[t]=s[t]:f[t]=a&&p?p(n):l(n)}return f[t]}},function(t,n){t.exports=function(t){return"function"==typeof t}},function(t,n){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,n,e){var r=e(31),o=Function.prototype,i=o.bind,c=o.call,u=r&&i.bind(c,c);t.exports=r?function(t){return t&&u(t)}:function(t){return t&&function(){return c.apply(t,arguments)}}},function(t,n,e){"use strict";var r=e(0),o=e(47),i=e(5),c=e(3),u=e(32).f,a=e(79),f=e(15),s=e(27),p=e(17),l=e(7),v=function(t){var n=function(e,r,i){if(this instanceof n){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,r)}return new t(e,r,i)}return o(t,this,arguments)};return n.prototype=t.prototype,n};t.exports=function(t,n){var e,o,d,h,y,m,g,x,b=t.target,w=t.global,j=t.stat,S=t.proto,O=w?r:j?r[b]:(r[b]||{}).prototype,P=w?f:f[b]||p(f,b,{})[b],A=P.prototype;for(d in n)e=!a(w?d:b+(j?".":"#")+d,t.forced)&&O&&l(O,d),y=P[d],e&&(m=t.noTargetGet?(x=u(O,d))&&x.value:O[d]),h=e&&m?m:n[d],e&&typeof y==typeof h||(g=t.bind&&e?s(h,r):t.wrap&&e?v(h):S&&c(h)?i(h):h,(t.sham||h&&h.sham||y&&y.sham)&&p(g,"sham",!0),p(P,d,g),S&&(l(f,o=b+"Prototype")||p(f,o,{}),p(f[o],d,h),t.real&&A&&!A[d]&&p(A,d,h)))}},function(t,n,e){var r=e(5),o=e(21),i=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,n){return i(o(t),n)}},function(t,n,e){var r=e(3);t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},function(t,n,e){var r=e(4);t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(t,n,e){var r=e(31),o=Function.prototype.call;t.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},function(t,n,e){var r=e(0),o=e(8),i=r.String,c=r.TypeError;t.exports=function(t){if(o(t))return t;throw c(i(t)+" is not an object")}},function(t,n,e){var r=e(15),o=e(0),i=e(3),c=function(t){return i(t)?t:void 0};t.exports=function(t,n){return arguments.length<2?c(r[t])||c(o[t]):r[t]&&r[t][n]||o[t]&&o[t][n]}},function(t,n,e){var r=e(5);t.exports=r({}.isPrototypeOf)},function(t,n,e){var r=e(0),o=e(9),i=e(78),c=e(80),u=e(11),a=e(33),f=r.TypeError,s=Object.defineProperty,p=Object.getOwnPropertyDescriptor;n.f=o?c?function(t,n,e){if(u(t),n=a(n),u(e),"function"==typeof t&&"prototype"===n&&"value"in e&&"writable"in e&&!e.writable){var r=p(t,n);r&&r.writable&&(t[n]=e.value,e={configurable:"configurable"in e?e.configurable:r.configurable,enumerable:"enumerable"in e?e.enumerable:r.enumerable,writable:!1})}return s(t,n,e)}:s:function(t,n,e){if(u(t),n=a(n),u(e),i)try{return s(t,n,e)}catch(t){}if("get"in e||"set"in e)throw f("Accessors not supported");return"value"in e&&(t[n]=e.value),t}},function(t,n){t.exports={}},function(t,n,e){var r=e(76),o=e(48);t.exports=function(t){return r(o(t))}},function(t,n,e){var r=e(9),o=e(14),i=e(18);t.exports=r?function(t,n,e){return o.f(t,n,i(1,e))}:function(t,n,e){return t[n]=e,t}},function(t,n){t.exports=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}}},function(t,n,e){var r=e(0),o=e(3),i=e(36),c=r.TypeError;t.exports=function(t){if(o(t))return t;throw c(i(t)+" is not a function")}},function(t,n){t.exports=!0},function(t,n,e){var r=e(0),o=e(48),i=r.Object;t.exports=function(t){return i(o(t))}},function(t,n,e){var r=e(111);t.exports=function(t){return r(t.length)}},function(t,n){t.exports={}},function(t,n,e){var r=e(0),o=e(63),i=e(3),c=e(26),u=e(2)("toStringTag"),a=r.Object,f="Arguments"==c(function(){return arguments}());t.exports=o?c:function(t){var n,e,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(e=function(t,n){try{return t[n]}catch(t){}}(n=a(t),u))?e:f?c(n):"Object"==(r=c(n))&&i(n.callee)?"Arguments":r}},function(t,n,e){var r=e(17);t.exports=function(t,n,e,o){o&&o.enumerable?t[n]=e:r(t,n,e)}},function(t,n,e){var r=e(5),o=r({}.toString),i=r("".slice);t.exports=function(t){return i(o(t),8,-1)}},function(t,n,e){var r=e(5),o=e(19),i=e(31),c=r(r.bind);t.exports=function(t,n){return o(t),void 0===n?t:i?c(t,n):function(){return t.apply(n,arguments)}}},function(t,n,e){var r=e(63),o=e(14).f,i=e(17),c=e(7),u=e(123),a=e(2)("toStringTag");t.exports=function(t,n,e,f){if(t){var s=e?t:t.prototype;c(s,a)||o(s,a,{configurable:!0,value:n}),f&&!r&&i(s,"toString",u)}}},function(t,n,e){var r=e(26);t.exports=Array.isArray||function(t){return"Array"==r(t)}},function(t,n,e){var r=e(15);t.exports=function(t){return r[t+"Prototype"]}},function(t,n,e){var r=e(4);t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},function(t,n,e){var r=e(9),o=e(10),i=e(75),c=e(18),u=e(16),a=e(33),f=e(7),s=e(78),p=Object.getOwnPropertyDescriptor;n.f=r?p:function(t,n){if(t=u(t),n=a(n),s)try{return p(t,n)}catch(t){}if(f(t,n))return c(!o(i.f,t,n),t[n])}},function(t,n,e){var r=e(103),o=e(49);t.exports=function(t){var n=r(t,"string");return o(n)?n:n+""}},function(t,n,e){var r,o,i=e(0),c=e(35),u=i.process,a=i.Deno,f=u&&u.versions||a&&a.version,s=f&&f.v8;s&&(o=(r=s.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&c&&(!(r=c.match(/Edge\\/(\\d+)/))||r[1]>=74)&&(r=c.match(/Chrome\\/(\\d+)/))&&(o=+r[1]),t.exports=o},function(t,n,e){var r=e(12);t.exports=r("navigator","userAgent")||""},function(t,n,e){var r=e(0).String;t.exports=function(t){try{return r(t)}catch(t){return"Object"}}},function(t,n,e){var r=e(52),o=e(54),i=r("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},function(t,n){t.exports={}},function(t,n,e){var r,o=e(11),i=e(83),c=e(61),u=e(38),a=e(84),f=e(55),s=e(37),p=s("IE_PROTO"),l=function(){},v=function(t){return"<script>"+t+"<\\/script>"},d=function(t){t.write(v("")),t.close();var n=t.parentWindow.Object;return t=null,n},h=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}var t,n;h="undefined"!=typeof document?document.domain&&r?d(r):((n=f("iframe")).style.display="none",a.appendChild(n),n.src=String("javascript:"),(t=n.contentWindow.document).open(),t.write(v("document.F=Object")),t.close(),t.F):d(r);for(var e=c.length;e--;)delete h.prototype[c[e]];return h()};u[p]=!0,t.exports=Object.create||function(t,n){var e;return null!==t?(l.prototype=o(t),e=new l,l.prototype=null,e[p]=t):e=h(),void 0===n?e:i.f(e,n)}},function(t,n,e){var r=e(0),o=e(27),i=e(10),c=e(11),u=e(36),a=e(114),f=e(22),s=e(13),p=e(115),l=e(85),v=e(116),d=r.TypeError,h=function(t,n){this.stopped=t,this.result=n},y=h.prototype;t.exports=function(t,n,e){var r,m,g,x,b,w,j,S=e&&e.that,O=!(!e||!e.AS_ENTRIES),P=!(!e||!e.IS_ITERATOR),A=!(!e||!e.INTERRUPTED),E=o(n,S),k=function(t){return r&&v(r,"normal",t),new h(!0,t)},T=function(t){return O?(c(t),A?E(t[0],t[1],k):E(t[0],t[1])):A?E(t,k):E(t)};if(P)r=t;else{if(!(m=l(t)))throw d(u(t)+" is not iterable");if(a(m)){for(g=0,x=f(t);x>g;g++)if((b=T(t[g]))&&s(y,b))return b;return new h(!1)}r=p(t,m)}for(w=r.next;!(j=i(w,r)).done;){try{b=T(j.value)}catch(t){v(r,"throw",t)}if("object"==typeof b&&b&&s(y,b))return b}return new h(!1)}},function(t,n,e){var r=e(0),o=e(24),i=r.String;t.exports=function(t){if("Symbol"===o(t))throw TypeError("Cannot convert a Symbol value to a string");return i(t)}},function(t,n,e){var r,o,i,c=e(120),u=e(0),a=e(5),f=e(8),s=e(17),p=e(7),l=e(53),v=e(37),d=e(38),h=u.TypeError,y=u.WeakMap;if(c||l.state){var m=l.state||(l.state=new y),g=a(m.get),x=a(m.has),b=a(m.set);r=function(t,n){if(x(m,t))throw new h("Object already initialized");return n.facade=t,b(m,t,n),n},o=function(t){return g(m,t)||{}},i=function(t){return x(m,t)}}else{var w=v("state");d[w]=!0,r=function(t,n){if(p(t,w))throw new h("Object already initialized");return n.facade=t,s(t,w,n),n},o=function(t){return p(t,w)?t[w]:{}},i=function(t){return p(t,w)}}t.exports={set:r,get:o,has:i,enforce:function(t){return i(t)?o(t):r(t,{})},getterFor:function(t){return function(n){var e;if(!f(n)||(e=o(n)).type!==t)throw h("Incompatible receiver, "+t+" required");return e}}}},function(t,n,e){"use strict";var r=e(19),o=function(t){var n,e;this.promise=new t((function(t,r){if(void 0!==n||void 0!==e)throw TypeError("Bad Promise constructor");n=t,e=r})),this.resolve=r(n),this.reject=r(e)};t.exports.f=function(t){return new o(t)}},function(t,n,e){e(64);var r=e(141),o=e(0),i=e(24),c=e(17),u=e(23),a=e(2)("toStringTag");for(var f in r){var s=o[f],p=s&&s.prototype;p&&i(p)!==a&&c(p,a,f),u[f]=u.Array}},function(t,n,e){var r=e(4),o=e(2),i=e(34),c=o("species");t.exports=function(t){return i>=51||!r((function(){var n=[];return(n.constructor={})[c]=function(){return{foo:1}},1!==n[t](Boolean).foo}))}},function(t,n,e){var r=e(27),o=e(5),i=e(76),c=e(21),u=e(22),a=e(95),f=o([].push),s=function(t){var n=1==t,e=2==t,o=3==t,s=4==t,p=6==t,l=7==t,v=5==t||p;return function(d,h,y,m){for(var g,x,b=c(d),w=i(b),j=r(h,y),S=u(w),O=0,P=m||a,A=n?P(d,S):e||l?P(d,0):void 0;S>O;O++)if((v||O in w)&&(x=j(g=w[O],O,b),t))if(n)A[O]=x;else if(x)switch(t){case 3:return!0;case 5:return g;case 6:return O;case 2:f(A,g)}else switch(t){case 4:return!1;case 7:f(A,g)}return p?-1:o||s?s:A}};t.exports={forEach:s(0),map:s(1),filter:s(2),some:s(3),every:s(4),find:s(5),findIndex:s(6),filterReject:s(7)}},function(t,n,e){var r=e(31),o=Function.prototype,i=o.apply,c=o.call;t.exports="object"==typeof Reflect&&Reflect.apply||(r?c.bind(i):function(){return c.apply(i,arguments)})},function(t,n,e){var r=e(0).TypeError;t.exports=function(t){if(null==t)throw r("Can\'t call method on "+t);return t}},function(t,n,e){var r=e(0),o=e(12),i=e(3),c=e(13),u=e(77),a=r.Object;t.exports=u?function(t){return"symbol"==typeof t}:function(t){var n=o("Symbol");return i(n)&&c(n.prototype,a(t))}},function(t,n,e){var r=e(34),o=e(4);t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},function(t,n,e){var r=e(19);t.exports=function(t,n){var e=t[n];return null==e?void 0:r(e)}},function(t,n,e){var r=e(20),o=e(53);(t.exports=function(t,n){return o[t]||(o[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.21.1",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.21.1/LICENSE",source:"https://github.com/zloirock/core-js"})},function(t,n,e){var r=e(0),o=e(105),i=r["__core-js_shared__"]||o("__core-js_shared__",{});t.exports=i},function(t,n,e){var r=e(5),o=0,i=Math.random(),c=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+c(++o+i,36)}},function(t,n,e){var r=e(0),o=e(8),i=r.document,c=o(i)&&o(i.createElement);t.exports=function(t){return c?i.createElement(t):{}}},function(t,n,e){var r=e(0),o=e(7),i=e(3),c=e(21),u=e(37),a=e(106),f=u("IE_PROTO"),s=r.Object,p=s.prototype;t.exports=a?s.getPrototypeOf:function(t){var n=c(t);if(o(n,f))return n[f];var e=n.constructor;return i(e)&&n instanceof e?e.prototype:n instanceof s?p:null}},function(t,n,e){var r=e(5),o=e(11),i=e(107);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,n=!1,e={};try{(t=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(e,[]),n=e instanceof Array}catch(t){}return function(e,r){return o(e),i(r),n?t(e,r):e.__proto__=r,e}}():void 0)},function(t,n,e){var r=e(81),o=e(61).concat("length","prototype");n.f=Object.getOwnPropertyNames||function(t){return r(t,o)}},function(t,n,e){var r=e(60),o=Math.max,i=Math.min;t.exports=function(t,n){var e=r(t);return e<0?o(e+n,0):i(e,n)}},function(t,n){var e=Math.ceil,r=Math.floor;t.exports=function(t){var n=+t;return n!=n||0===n?0:(n>0?r:e)(n)}},function(t,n){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(t,n,e){var r=e(81),o=e(61);t.exports=Object.keys||function(t){return r(t,o)}},function(t,n,e){var r={};r[e(2)("toStringTag")]="z",t.exports="[object z]"===String(r)},function(t,n,e){"use strict";var r=e(16),o=e(119),i=e(23),c=e(42),u=e(14).f,a=e(86),f=e(20),s=e(9),p=c.set,l=c.getterFor("Array Iterator");t.exports=a(Array,"Array",(function(t,n){p(this,{type:"Array Iterator",target:r(t),index:0,kind:n})}),(function(){var t=l(this),n=t.target,e=t.kind,r=t.index++;return!n||r>=n.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==e?{value:r,done:!1}:"values"==e?{value:n[r],done:!1}:{value:[r,n[r]],done:!1}}),"values");var v=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!f&&s&&"values"!==v.name)try{u(v,"name",{value:"values"})}catch(t){}},function(t,n,e){var r=e(5),o=e(3),i=e(53),c=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return c(t)}),t.exports=i.inspectSource},function(t,n){},function(t,n,e){var r=e(5),o=e(4),i=e(3),c=e(24),u=e(12),a=e(65),f=function(){},s=[],p=u("Reflect","construct"),l=/^\\s*(?:class|function)\\b/,v=r(l.exec),d=!l.exec(f),h=function(t){if(!i(t))return!1;try{return p(f,s,t),!0}catch(t){return!1}},y=function(t){if(!i(t))return!1;switch(c(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return d||!!v(l,a(t))}catch(t){return!0}};y.sham=!0,t.exports=!p||o((function(){var t;return h(h.call)||!h(Object)||!h((function(){t=!0}))||t}))?y:h},function(t,n,e){var r=e(5);t.exports=r([].slice)},function(t,n,e){var r=e(26),o=e(0);t.exports="process"==r(o.process)},function(t,n){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},function(t,n,e){"use strict";var r=e(33),o=e(14),i=e(18);t.exports=function(t,n,e){var c=r(n);c in t?o.f(t,c,i(0,e)):t[c]=e}},function(t,n,e){var r=e(2);n.f=r},function(t,n,e){t.exports=e(100)},function(t,n){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,n,e){"use strict";var r={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,i=o&&!r.call({1:2},1);n.f=i?function(t){var n=o(this,t);return!!n&&n.enumerable}:r},function(t,n,e){var r=e(0),o=e(5),i=e(4),c=e(26),u=r.Object,a=o("".split);t.exports=i((function(){return!u("z").propertyIsEnumerable(0)}))?function(t){return"String"==c(t)?a(t,""):u(t)}:u},function(t,n,e){var r=e(50);t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(t,n,e){var r=e(9),o=e(4),i=e(55);t.exports=!r&&!o((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},function(t,n,e){var r=e(4),o=e(3),i=/#|\\.prototype\\./,c=function(t,n){var e=a[u(t)];return e==s||e!=f&&(o(n)?r(n):!!n)},u=c.normalize=function(t){return String(t).replace(i,".").toLowerCase()},a=c.data={},f=c.NATIVE="N",s=c.POLYFILL="P";t.exports=c},function(t,n,e){var r=e(9),o=e(4);t.exports=r&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},function(t,n,e){var r=e(5),o=e(7),i=e(16),c=e(110).indexOf,u=e(38),a=r([].push);t.exports=function(t,n){var e,r=i(t),f=0,s=[];for(e in r)!o(u,e)&&o(r,e)&&a(s,e);for(;n.length>f;)o(r,e=n[f++])&&(~c(s,e)||a(s,e));return s}},function(t,n){n.f=Object.getOwnPropertySymbols},function(t,n,e){var r=e(9),o=e(80),i=e(14),c=e(11),u=e(16),a=e(62);n.f=r&&!o?Object.defineProperties:function(t,n){c(t);for(var e,r=u(n),o=a(n),f=o.length,s=0;f>s;)i.f(t,e=o[s++],r[e]);return t}},function(t,n,e){var r=e(12);t.exports=r("document","documentElement")},function(t,n,e){var r=e(24),o=e(51),i=e(23),c=e(2)("iterator");t.exports=function(t){if(null!=t)return o(t,c)||o(t,"@@iterator")||i[r(t)]}},function(t,n,e){"use strict";var r=e(6),o=e(10),i=e(20),c=e(121),u=e(3),a=e(122),f=e(56),s=e(57),p=e(28),l=e(17),v=e(25),d=e(2),h=e(23),y=e(87),m=c.PROPER,g=c.CONFIGURABLE,x=y.IteratorPrototype,b=y.BUGGY_SAFARI_ITERATORS,w=d("iterator"),j=function(){return this};t.exports=function(t,n,e,c,d,y,S){a(e,n,c);var O,P,A,E=function(t){if(t===d&&_)return _;if(!b&&t in L)return L[t];switch(t){case"keys":case"values":case"entries":return function(){return new e(this,t)}}return function(){return new e(this)}},k=n+" Iterator",T=!1,L=t.prototype,I=L[w]||L["@@iterator"]||d&&L[d],_=!b&&I||E(d),M="Array"==n&&L.entries||I;if(M&&(O=f(M.call(new t)))!==Object.prototype&&O.next&&(i||f(O)===x||(s?s(O,x):u(O[w])||v(O,w,j)),p(O,k,!0,!0),i&&(h[k]=j)),m&&"values"==d&&I&&"values"!==I.name&&(!i&&g?l(L,"name","values"):(T=!0,_=function(){return o(I,this)})),d)if(P={values:E("values"),keys:y?_:E("keys"),entries:E("entries")},S)for(A in P)(b||T||!(A in L))&&v(L,A,P[A]);else r({target:n,proto:!0,forced:b||T},P);return i&&!S||L[w]===_||v(L,w,_,{name:d}),h[n]=_,P}},function(t,n,e){"use strict";var r,o,i,c=e(4),u=e(3),a=e(39),f=e(56),s=e(25),p=e(2),l=e(20),v=p("iterator"),d=!1;[].keys&&("next"in(i=[].keys())?(o=f(f(i)))!==Object.prototype&&(r=o):d=!0),null==r||c((function(){var t={};return r[v].call(t)!==t}))?r={}:l&&(r=a(r)),u(r[v])||s(r,v,(function(){return this})),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:d}},function(t,n,e){var r=e(0);t.exports=r.Promise},function(t,n,e){var r=e(11),o=e(129),i=e(2)("species");t.exports=function(t,n){var e,c=r(t).constructor;return void 0===c||null==(e=r(c)[i])?n:o(e)}},function(t,n,e){var r,o,i,c,u=e(0),a=e(47),f=e(27),s=e(3),p=e(7),l=e(4),v=e(84),d=e(68),h=e(55),y=e(130),m=e(91),g=e(69),x=u.setImmediate,b=u.clearImmediate,w=u.process,j=u.Dispatch,S=u.Function,O=u.MessageChannel,P=u.String,A=0,E={};try{r=u.location}catch(t){}var k=function(t){if(p(E,t)){var n=E[t];delete E[t],n()}},T=function(t){return function(){k(t)}},L=function(t){k(t.data)},I=function(t){u.postMessage(P(t),r.protocol+"//"+r.host)};x&&b||(x=function(t){y(arguments.length,1);var n=s(t)?t:S(t),e=d(arguments,1);return E[++A]=function(){a(n,void 0,e)},o(A),A},b=function(t){delete E[t]},g?o=function(t){w.nextTick(T(t))}:j&&j.now?o=function(t){j.now(T(t))}:O&&!m?(c=(i=new O).port2,i.port1.onmessage=L,o=f(c.postMessage,c)):u.addEventListener&&s(u.postMessage)&&!u.importScripts&&r&&"file:"!==r.protocol&&!l(I)?(o=I,u.addEventListener("message",L,!1)):o="onreadystatechange"in h("script")?function(t){v.appendChild(h("script")).onreadystatechange=function(){v.removeChild(this),k(t)}}:function(t){setTimeout(T(t),0)}),t.exports={set:x,clear:b}},function(t,n,e){var r=e(35);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(r)},function(t,n,e){var r=e(11),o=e(8),i=e(43);t.exports=function(t,n){if(r(t),o(n)&&n.constructor===t)return n;var e=i.f(t);return(0,e.resolve)(n),e.promise}},function(t,n,e){"use strict";var r=e(140).charAt,o=e(41),i=e(42),c=e(86),u=i.set,a=i.getterFor("String Iterator");c(String,"String",(function(t){u(this,{type:"String Iterator",string:o(t),index:0})}),(function(){var t,n=a(this),e=n.string,o=n.index;return o>=e.length?{value:void 0,done:!0}:(t=r(e,o),n.index+=t.length,{value:t,done:!1})}))},function(t,n,e){t.exports=e(142)},function(t,n,e){var r=e(151);t.exports=function(t,n){return new(r(t))(0===n?0:n)}},function(t,n,e){"use strict";var r=e(6),o=e(0),i=e(4),c=e(29),u=e(8),a=e(21),f=e(22),s=e(71),p=e(95),l=e(45),v=e(2),d=e(34),h=v("isConcatSpreadable"),y=o.TypeError,m=d>=51||!i((function(){var t=[];return t[h]=!1,t.concat()[0]!==t})),g=l("concat"),x=function(t){if(!u(t))return!1;var n=t[h];return void 0!==n?!!n:c(t)};r({target:"Array",proto:!0,forced:!m||!g},{concat:function(t){var n,e,r,o,i,c=a(this),u=p(c,0),l=0;for(n=-1,r=arguments.length;n<r;n++)if(x(i=-1===n?c:arguments[n])){if(l+(o=f(i))>9007199254740991)throw y("Maximum allowed index exceeded");for(e=0;e<o;e++,l++)e in i&&s(u,l,i[e])}else{if(l>=9007199254740991)throw y("Maximum allowed index exceeded");s(u,l++,i)}return u.length=l,u}})},function(t,n,e){e(1)("iterator")},function(t,n,e){var r=e(99),o=null;function i(t){return t&&"string"!=typeof t?t.message?t.message:t.target&&t.target.error?t.target.error.message:"unknown error":t}self.onmessage=function(t){switch(t.data.type){case"init":e=t.data.msg,(o=new r(e)).init().catch((function(t){postMessage({type:"error",msg:{msg:"dbLog init error",error:i(t)}})}));break;case"fetch":o.getAllLogs().then((function(t){postMessage({type:"fetchDone",code:200,msg:t})})).catch((function(t){postMessage({type:"fetchDone",code:500,msg:i(t)})}));break;case"delete":o.deleteLogs(n).catch((function(t){postMessage({type:"deleteLogs",code:500,msg:i(t)})}));break;default:!function(t){if(!o)return;o.putLog(t).then((function(t){postMessage(200)})).catch((function(t){postMessage({type:"error",msg:{msg:"putLog error",error:i(t)}})}))}(t.data)}var n,e}},function(t,n,e){var r=e(73),o=e(94),i=e(146),c=e(152),u={log:{key:{keyPath:"time"},indexes:{level:{unique:!1},time:{unique:!0}}}};function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.server=null,this.expire=t.expire||72,this.name=t.name}var f=a.prototype;f.init=function(){var t=this;if(!this.server)return c.open({server:"nim-log-"+this.name,version:1,schema:u}).then((function(n){t.server=n}))},f.putLog=function(t){var n=this;if(!this.server)return r.reject("no db server");var e=o(t).call(t,-1)[0].time-36e5*this.expire;return this.deleteLogs(e).then((function(){return n.server.add("log",t)}))},f.getAllLogs=function(){return this.server?this.server.query("log","time").all().execute().then((function(t){var n={},e=o(t).call(t,-1)[0];return n.logs=i(t).call(t,(function(t){return t.log})).join("\\r\\n"),n.time=e&&e.time,n})):r.reject("no db server")},f.deleteLogs=function(t){return this.server?this.server.remove("log",null,"time",0,t):r.reject("no db server")},t.exports=a},function(t,n,e){var r=e(101);e(44),t.exports=r},function(t,n,e){e(102),e(64),e(66),e(124),e(137),e(138),e(139),e(93);var r=e(15);t.exports=r.Promise},function(t,n,e){"use strict";var r=e(6),o=e(0),i=e(13),c=e(56),u=e(57),a=e(108),f=e(39),s=e(17),p=e(18),l=e(112),v=e(113),d=e(40),h=e(117),y=e(2),m=e(118),g=y("toStringTag"),x=o.Error,b=[].push,w=function(t,n){var e,r=arguments.length>2?arguments[2]:void 0,o=i(j,this);u?e=u(new x,o?c(this):j):(e=o?this:f(j),s(e,g,"Error")),void 0!==n&&s(e,"message",h(n)),m&&s(e,"stack",l(e.stack,1)),v(e,r);var a=[];return d(t,b,{that:a}),s(e,"errors",a),e};u?u(w,x):a(w,x,{name:!0});var j=w.prototype=f(x.prototype,{constructor:p(1,w),message:p(1,""),name:p(1,"AggregateError")});r({global:!0},{AggregateError:w})},function(t,n,e){var r=e(0),o=e(10),i=e(8),c=e(49),u=e(51),a=e(104),f=e(2),s=r.TypeError,p=f("toPrimitive");t.exports=function(t,n){if(!i(t)||c(t))return t;var e,r=u(t,p);if(r){if(void 0===n&&(n="default"),e=o(r,t,n),!i(e)||c(e))return e;throw s("Can\'t convert object to primitive value")}return void 0===n&&(n="number"),a(t,n)}},function(t,n,e){var r=e(0),o=e(10),i=e(3),c=e(8),u=r.TypeError;t.exports=function(t,n){var e,r;if("string"===n&&i(e=t.toString)&&!c(r=o(e,t)))return r;if(i(e=t.valueOf)&&!c(r=o(e,t)))return r;if("string"!==n&&i(e=t.toString)&&!c(r=o(e,t)))return r;throw u("Can\'t convert object to primitive value")}},function(t,n,e){var r=e(0),o=Object.defineProperty;t.exports=function(t,n){try{o(r,t,{value:n,configurable:!0,writable:!0})}catch(e){r[t]=n}return n}},function(t,n,e){var r=e(4);t.exports=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},function(t,n,e){var r=e(0),o=e(3),i=r.String,c=r.TypeError;t.exports=function(t){if("object"==typeof t||o(t))return t;throw c("Can\'t set "+i(t)+" as a prototype")}},function(t,n,e){var r=e(7),o=e(109),i=e(32),c=e(14);t.exports=function(t,n,e){for(var u=o(n),a=c.f,f=i.f,s=0;s<u.length;s++){var p=u[s];r(t,p)||e&&r(e,p)||a(t,p,f(n,p))}}},function(t,n,e){var r=e(12),o=e(5),i=e(58),c=e(82),u=e(11),a=o([].concat);t.exports=r("Reflect","ownKeys")||function(t){var n=i.f(u(t)),e=c.f;return e?a(n,e(t)):n}},function(t,n,e){var r=e(16),o=e(59),i=e(22),c=function(t){return function(n,e,c){var u,a=r(n),f=i(a),s=o(c,f);if(t&&e!=e){for(;f>s;)if((u=a[s++])!=u)return!0}else for(;f>s;s++)if((t||s in a)&&a[s]===e)return t||s||0;return!t&&-1}};t.exports={includes:c(!0),indexOf:c(!1)}},function(t,n,e){var r=e(60),o=Math.min;t.exports=function(t){return t>0?o(r(t),9007199254740991):0}},function(t,n,e){var r=e(5)("".replace),o=String(Error("zxcasd").stack),i=/\\n\\s*at [^:]*:[^\\n]*/,c=i.test(o);t.exports=function(t,n){if(c&&"string"==typeof t)for(;n--;)t=r(t,i,"");return t}},function(t,n,e){var r=e(8),o=e(17);t.exports=function(t,n){r(n)&&"cause"in n&&o(t,"cause",n.cause)}},function(t,n,e){var r=e(2),o=e(23),i=r("iterator"),c=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||c[i]===t)}},function(t,n,e){var r=e(0),o=e(10),i=e(19),c=e(11),u=e(36),a=e(85),f=r.TypeError;t.exports=function(t,n){var e=arguments.length<2?a(t):n;if(i(e))return c(o(e,t));throw f(u(t)+" is not iterable")}},function(t,n,e){var r=e(10),o=e(11),i=e(51);t.exports=function(t,n,e){var c,u;o(t);try{if(!(c=i(t,"return"))){if("throw"===n)throw e;return e}c=r(c,t)}catch(t){u=!0,c=t}if("throw"===n)throw e;if(u)throw c;return o(c),e}},function(t,n,e){var r=e(41);t.exports=function(t,n){return void 0===t?arguments.length<2?"":n:r(t)}},function(t,n,e){var r=e(4),o=e(18);t.exports=!r((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",o(1,7)),7!==t.stack)}))},function(t,n){t.exports=function(){}},function(t,n,e){var r=e(0),o=e(3),i=e(65),c=r.WeakMap;t.exports=o(c)&&/native code/.test(i(c))},function(t,n,e){var r=e(9),o=e(7),i=Function.prototype,c=r&&Object.getOwnPropertyDescriptor,u=o(i,"name"),a=u&&"something"===function(){}.name,f=u&&(!r||r&&c(i,"name").configurable);t.exports={EXISTS:u,PROPER:a,CONFIGURABLE:f}},function(t,n,e){"use strict";var r=e(87).IteratorPrototype,o=e(39),i=e(18),c=e(28),u=e(23),a=function(){return this};t.exports=function(t,n,e,f){var s=n+" Iterator";return t.prototype=o(r,{next:i(+!f,e)}),c(t,s,!1,!0),u[s]=a,t}},function(t,n,e){"use strict";var r=e(63),o=e(24);t.exports=r?{}.toString:function(){return"[object "+o(this)+"]"}},function(t,n,e){"use strict";var r,o,i,c,u=e(6),a=e(20),f=e(0),s=e(12),p=e(10),l=e(88),v=e(25),d=e(125),h=e(57),y=e(28),m=e(126),g=e(19),x=e(3),b=e(8),w=e(127),j=e(65),S=e(40),O=e(128),P=e(89),A=e(90).set,E=e(131),k=e(92),T=e(134),L=e(43),I=e(70),_=e(135),M=e(42),D=e(79),R=e(2),C=e(136),F=e(69),N=e(34),B=R("species"),G="Promise",z=M.getterFor(G),q=M.set,V=M.getterFor(G),U=l&&l.prototype,W=l,H=U,K=f.TypeError,J=f.document,Y=f.process,X=L.f,Q=X,Z=!!(J&&J.createEvent&&f.dispatchEvent),$=x(f.PromiseRejectionEvent),tt=!1,nt=D(G,(function(){var t=j(W),n=t!==String(W);if(!n&&66===N)return!0;if(a&&!H.finally)return!0;if(N>=51&&/native code/.test(t))return!1;var e=new W((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))};return(e.constructor={})[B]=r,!(tt=e.then((function(){}))instanceof r)||!n&&C&&!$})),et=nt||!O((function(t){W.all(t).catch((function(){}))})),rt=function(t){var n;return!(!b(t)||!x(n=t.then))&&n},ot=function(t,n){var e,r,o,i=n.value,c=1==n.state,u=c?t.ok:t.fail,a=t.resolve,f=t.reject,s=t.domain;try{u?(c||(2===n.rejection&&ft(n),n.rejection=1),!0===u?e=i:(s&&s.enter(),e=u(i),s&&(s.exit(),o=!0)),e===t.promise?f(K("Promise-chain cycle")):(r=rt(e))?p(r,e,a,f):a(e)):f(i)}catch(t){s&&!o&&s.exit(),f(t)}},it=function(t,n){t.notified||(t.notified=!0,E((function(){for(var e,r=t.reactions;e=r.get();)ot(e,t);t.notified=!1,n&&!t.rejection&&ut(t)})))},ct=function(t,n,e){var r,o;Z?((r=J.createEvent("Event")).promise=n,r.reason=e,r.initEvent(t,!1,!0),f.dispatchEvent(r)):r={promise:n,reason:e},!$&&(o=f["on"+t])?o(r):"unhandledrejection"===t&&T("Unhandled promise rejection",e)},ut=function(t){p(A,f,(function(){var n,e=t.facade,r=t.value;if(at(t)&&(n=I((function(){F?Y.emit("unhandledRejection",r,e):ct("unhandledrejection",e,r)})),t.rejection=F||at(t)?2:1,n.error))throw n.value}))},at=function(t){return 1!==t.rejection&&!t.parent},ft=function(t){p(A,f,(function(){var n=t.facade;F?Y.emit("rejectionHandled",n):ct("rejectionhandled",n,t.value)}))},st=function(t,n,e){return function(r){t(n,r,e)}},pt=function(t,n,e){t.done||(t.done=!0,e&&(t=e),t.value=n,t.state=2,it(t,!0))},lt=function(t,n,e){if(!t.done){t.done=!0,e&&(t=e);try{if(t.facade===n)throw K("Promise can\'t be resolved itself");var r=rt(n);r?E((function(){var e={done:!1};try{p(r,n,st(lt,e,t),st(pt,e,t))}catch(n){pt(e,n,t)}})):(t.value=n,t.state=1,it(t,!1))}catch(n){pt({done:!1},n,t)}}};if(nt&&(H=(W=function(t){w(this,H),g(t),p(r,this);var n=z(this);try{t(st(lt,n),st(pt,n))}catch(t){pt(n,t)}}).prototype,(r=function(t){q(this,{type:G,done:!1,notified:!1,parent:!1,reactions:new _,rejection:!1,state:0,value:void 0})}).prototype=d(H,{then:function(t,n){var e=V(this),r=X(P(this,W));return e.parent=!0,r.ok=!x(t)||t,r.fail=x(n)&&n,r.domain=F?Y.domain:void 0,0==e.state?e.reactions.add(r):E((function(){ot(r,e)})),r.promise},catch:function(t){return this.then(void 0,t)}}),o=function(){var t=new r,n=z(t);this.promise=t,this.resolve=st(lt,n),this.reject=st(pt,n)},L.f=X=function(t){return t===W||t===i?new o(t):Q(t)},!a&&x(l)&&U!==Object.prototype)){c=U.then,tt||(v(U,"then",(function(t,n){var e=this;return new W((function(t,n){p(c,e,t,n)})).then(t,n)}),{unsafe:!0}),v(U,"catch",H.catch,{unsafe:!0}));try{delete U.constructor}catch(t){}h&&h(U,H)}u({global:!0,wrap:!0,forced:nt},{Promise:W}),y(W,G,!1,!0),m(G),i=s(G),u({target:G,stat:!0,forced:nt},{reject:function(t){var n=X(this);return p(n.reject,void 0,t),n.promise}}),u({target:G,stat:!0,forced:a||nt},{resolve:function(t){return k(a&&this===i?W:this,t)}}),u({target:G,stat:!0,forced:et},{all:function(t){var n=this,e=X(n),r=e.resolve,o=e.reject,i=I((function(){var e=g(n.resolve),i=[],c=0,u=1;S(t,(function(t){var a=c++,f=!1;u++,p(e,n,t).then((function(t){f||(f=!0,i[a]=t,--u||r(i))}),o)})),--u||r(i)}));return i.error&&o(i.value),e.promise},race:function(t){var n=this,e=X(n),r=e.reject,o=I((function(){var o=g(n.resolve);S(t,(function(t){p(o,n,t).then(e.resolve,r)}))}));return o.error&&r(o.value),e.promise}})},function(t,n,e){var r=e(25);t.exports=function(t,n,e){for(var o in n)e&&e.unsafe&&t[o]?t[o]=n[o]:r(t,o,n[o],e);return t}},function(t,n,e){"use strict";var r=e(12),o=e(14),i=e(2),c=e(9),u=i("species");t.exports=function(t){var n=r(t),e=o.f;c&&n&&!n[u]&&e(n,u,{configurable:!0,get:function(){return this}})}},function(t,n,e){var r=e(0),o=e(13),i=r.TypeError;t.exports=function(t,n){if(o(n,t))return t;throw i("Incorrect invocation")}},function(t,n,e){var r=e(2)("iterator"),o=!1;try{var i=0,c={next:function(){return{done:!!i++}},return:function(){o=!0}};c[r]=function(){return this},Array.from(c,(function(){throw 2}))}catch(t){}t.exports=function(t,n){if(!n&&!o)return!1;var e=!1;try{var i={};i[r]=function(){return{next:function(){return{done:e=!0}}}},t(i)}catch(t){}return e}},function(t,n,e){var r=e(0),o=e(67),i=e(36),c=r.TypeError;t.exports=function(t){if(o(t))return t;throw c(i(t)+" is not a constructor")}},function(t,n,e){var r=e(0).TypeError;t.exports=function(t,n){if(t<n)throw r("Not enough arguments");return t}},function(t,n,e){var r,o,i,c,u,a,f,s,p=e(0),l=e(27),v=e(32).f,d=e(90).set,h=e(91),y=e(132),m=e(133),g=e(69),x=p.MutationObserver||p.WebKitMutationObserver,b=p.document,w=p.process,j=p.Promise,S=v(p,"queueMicrotask"),O=S&&S.value;O||(r=function(){var t,n;for(g&&(t=w.domain)&&t.exit();o;){n=o.fn,o=o.next;try{n()}catch(t){throw o?c():i=void 0,t}}i=void 0,t&&t.enter()},h||g||m||!x||!b?!y&&j&&j.resolve?((f=j.resolve(void 0)).constructor=j,s=l(f.then,f),c=function(){s(r)}):g?c=function(){w.nextTick(r)}:(d=l(d,p),c=function(){d(r)}):(u=!0,a=b.createTextNode(""),new x(r).observe(a,{characterData:!0}),c=function(){a.data=u=!u})),t.exports=O||function(t){var n={fn:t,next:void 0};i&&(i.next=n),o||(o=n,c()),i=n}},function(t,n,e){var r=e(35),o=e(0);t.exports=/ipad|iphone|ipod/i.test(r)&&void 0!==o.Pebble},function(t,n,e){var r=e(35);t.exports=/web0s(?!.*chrome)/i.test(r)},function(t,n,e){var r=e(0);t.exports=function(t,n){var e=r.console;e&&e.error&&(1==arguments.length?e.error(t):e.error(t,n))}},function(t,n){var e=function(){this.head=null,this.tail=null};e.prototype={add:function(t){var n={item:t,next:null};this.head?this.tail.next=n:this.head=n,this.tail=n},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}},t.exports=e},function(t,n){t.exports="object"==typeof window},function(t,n,e){"use strict";var r=e(6),o=e(10),i=e(19),c=e(43),u=e(70),a=e(40);r({target:"Promise",stat:!0},{allSettled:function(t){var n=this,e=c.f(n),r=e.resolve,f=e.reject,s=u((function(){var e=i(n.resolve),c=[],u=0,f=1;a(t,(function(t){var i=u++,a=!1;f++,o(e,n,t).then((function(t){a||(a=!0,c[i]={status:"fulfilled",value:t},--f||r(c))}),(function(t){a||(a=!0,c[i]={status:"rejected",reason:t},--f||r(c))}))})),--f||r(c)}));return s.error&&f(s.value),e.promise}})},function(t,n,e){"use strict";var r=e(6),o=e(19),i=e(12),c=e(10),u=e(43),a=e(70),f=e(40);r({target:"Promise",stat:!0},{any:function(t){var n=this,e=i("AggregateError"),r=u.f(n),s=r.resolve,p=r.reject,l=a((function(){var r=o(n.resolve),i=[],u=0,a=1,l=!1;f(t,(function(t){var o=u++,f=!1;a++,c(r,n,t).then((function(t){f||l||(l=!0,s(t))}),(function(t){f||l||(f=!0,i[o]=t,--a||p(new e(i,"No one promise resolved")))}))})),--a||p(new e(i,"No one promise resolved"))}));return l.error&&p(l.value),r.promise}})},function(t,n,e){"use strict";var r=e(6),o=e(20),i=e(88),c=e(4),u=e(12),a=e(3),f=e(89),s=e(92),p=e(25);if(r({target:"Promise",proto:!0,real:!0,forced:!!i&&c((function(){i.prototype.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var n=f(this,u("Promise")),e=a(t);return this.then(e?function(e){return s(n,t()).then((function(){return e}))}:t,e?function(e){return s(n,t()).then((function(){throw e}))}:t)}}),!o&&a(i)){var l=u("Promise").prototype.finally;i.prototype.finally!==l&&p(i.prototype,"finally",l,{unsafe:!0})}},function(t,n,e){var r=e(5),o=e(60),i=e(41),c=e(48),u=r("".charAt),a=r("".charCodeAt),f=r("".slice),s=function(t){return function(n,e){var r,s,p=i(c(n)),l=o(e),v=p.length;return l<0||l>=v?t?"":void 0:(r=a(p,l))<55296||r>56319||l+1===v||(s=a(p,l+1))<56320||s>57343?t?u(p,l):r:t?f(p,l,l+2):s-56320+(r-55296<<10)+65536}};t.exports={codeAt:s(!1),charAt:s(!0)}},function(t,n){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(t,n,e){var r=e(143);t.exports=r},function(t,n,e){var r=e(13),o=e(144),i=Array.prototype;t.exports=function(t){var n=t.slice;return t===i||r(i,t)&&n===i.slice?o:n}},function(t,n,e){e(145);var r=e(30);t.exports=r("Array").slice},function(t,n,e){"use strict";var r=e(6),o=e(0),i=e(29),c=e(67),u=e(8),a=e(59),f=e(22),s=e(16),p=e(71),l=e(2),v=e(45),d=e(68),h=v("slice"),y=l("species"),m=o.Array,g=Math.max;r({target:"Array",proto:!0,forced:!h},{slice:function(t,n){var e,r,o,l=s(this),v=f(l),h=a(t,v),x=a(void 0===n?v:n,v);if(i(l)&&(e=l.constructor,(c(e)&&(e===m||i(e.prototype))||u(e)&&null===(e=e[y]))&&(e=void 0),e===m||void 0===e))return d(l,h,x);for(r=new(void 0===e?m:e)(g(x-h,0)),o=0;h<x;h++,o++)h in l&&p(r,o,l[h]);return r.length=o,r}})},function(t,n,e){t.exports=e(147)},function(t,n,e){var r=e(148);t.exports=r},function(t,n,e){var r=e(13),o=e(149),i=Array.prototype;t.exports=function(t){var n=t.map;return t===i||r(i,t)&&n===i.map?o:n}},function(t,n,e){e(150);var r=e(30);t.exports=r("Array").map},function(t,n,e){"use strict";var r=e(6),o=e(46).map;r({target:"Array",proto:!0,forced:!e(45)("map")},{map:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,n,e){var r=e(0),o=e(29),i=e(67),c=e(8),u=e(2)("species"),a=r.Array;t.exports=function(t){var n;return o(t)&&(n=t.constructor,(i(n)&&(n===a||o(n.prototype))||c(n)&&null===(n=n[u]))&&(n=void 0)),void 0===n?a:n}},function(t,n,e){(function(r){var o,i=e(153),c=e(94),u=e(190),a=e(73),f=e(194),s=e(201),p=e(205),l=e(209),v=e(213);!function(d,h){"use strict";var y,m=(d=void 0!==d?d:"undefined"!=typeof self?self:void 0!==r?r:{}).IDBKeyRange||d.webkitIDBKeyRange,g="readonly",x="readwrite",b=Object.prototype.hasOwnProperty,w=function(){if(!y&&!(y=d.indexedDB||d.webkitIndexedDB||d.mozIndexedDB||d.oIndexedDB||d.msIndexedDB||(null===d.indexedDB&&d.shimIndexedDB?d.shimIndexedDB:void 0)))throw"IndexedDB required";return y},j=function(t){return t},S=function(t){var n;return c(n=Object.prototype.toString.call(t)).call(n,8,-1).toLowerCase()},O=function(t){return"function"==typeof t},P=function(t){return"number"===S(t)},A=function(t){return"array"===S(t)},E=function(t){return void 0===t},k=function(t,n){var e=this,r=!1;this.name=n,this.getIndexedDB=function(){return t},this.add=function(n){if(r)throw"Database has been closed";for(var o=[],i=0,c=0;c<arguments.length-1;c++)if(u(arguments[c+1]))for(var p=0;p<arguments[c+1].length;p++)o[i]=arguments[c+1][p],i++;else o[i]=arguments[c+1],i++;var l=t.transaction(n,x),v=l.objectStore(n);return new a((function(t,n){f(o).call(o,(function(t){var n;if(t.item&&t.key){var e=t.key;t=t.item,n=v.add(t,e)}else n=v.add(t);n.onsuccess=function(n){var e=n.target,r=e.source.keyPath;null===r&&(r="__id__"),s(t,r,{value:e.result,enumerable:!0})}})),l.oncomplete=function(){t(o,e)},l.onerror=function(t){t.preventDefault(),n(t)},l.onabort=function(t){n(t)}}))},this.updateAndDelete=function(n,e,o){if(r)throw"Database has been closed";var i=t.transaction(n,x),c=i.objectStore(n),u=c.keyPath;return new a((function(t,n){f(e).call(e,(function(t){if(t.item&&t.key){var n=t.key;t=t.item,c.put(t,n)}else c.put(t)})),f(o).call(o,(function(t){c.delete(t[u])})),i.oncomplete=function(){t([e,o])},i.onerror=function(t){n(t)}}))},this.update=function(n){if(r)throw"Database has been closed";for(var o,i=[],c=1;c<arguments.length;c++)u(o=arguments[c])?i=p(i).call(i,o):i.push(o);var s=t.transaction(n,x),l=s.objectStore(n);l.keyPath;return new a((function(t,n){f(i).call(i,(function(t){var n;if(t.item&&t.key){var e=t.key;t=t.item,n=l.put(t,e)}else n=l.put(t);n.onsuccess=function(t){},n.onerror=function(t){}})),s.oncomplete=function(){t(i,e)},s.onerror=function(t){n(t)},s.onabort=function(t){n(t)}}))},this.remove=function(n,e,o,i,c,s,p){if(r)throw"Database has been closed";var l=t.transaction(n,x),v=l.objectStore(n);return new a((function(t,n){function r(t){return null==t}var a;(r(i)&&(i=-1/0),r(c)&&(c=1/0),null===e||u(e)||(e=[e]),r(o))?null!==e?f(e).call(e,(function(t){v.delete(t)})):v.delete(range=m.bound(i,c,s,p)):(a=null!==e?m.only(e[0]):m.bound(i,c,s,p),v.index(o).openCursor(a).onsuccess=function(t){var n=t.target.result;n&&(n.delete(),n.continue())});l.oncomplete=function(){t()},l.onerror=function(t){n(t)},l.onabort=function(t){n(t)}}))},this.clear=function(n){if(r)throw"Database has been closed";var e=t.transaction(n,x);e.objectStore(n).clear();return new a((function(t,n){e.oncomplete=function(){t()},e.onerror=function(t){n(t)}}))},this.close=function(){r||(t.close(),r=!0,delete I[n])},this.get=function(n,e){if(r)throw"Database has been closed";var o=t.transaction(n),i=o.objectStore(n).get(e);return new a((function(t,n){i.onsuccess=function(n){t(n.target.result)},o.onerror=function(t){n(t)}}))},this.query=function(n,e){if(r)throw"Database has been closed";return new T(n,t,e)},this.count=function(n,e){if(r)throw"Database has been closed";t.transaction(n).objectStore(n)};for(var o=0,i=t.objectStoreNames.length;o<i;o++)!function(t){for(var n in e[t]={},e)b.call(e,n)&&"close"!==n&&(e[t][n]=function(n){return function(){var r,o=p(r=[t]).call(r,c([]).call(arguments,0));return e[n].apply(e,o)}}(n))}(t.objectStoreNames[o])},T=function(t,n,e){var r,o=this,u=!1,s=!1,p=function(r,o,c,p,v,d,h){return new a((function(a,y){var b=u||s?x:g,w=n.transaction(t,b),j=w.objectStore(t),S=e?j.index(e):j,P=r?m[r].apply(null,o):null,A=[],E=[P],k=0;v=v||null,d=d||[],"count"!==c&&E.push(p||"next");var T=!!u&&l(u);S[c].apply(S,E).onsuccess=function(t){var n=t.target.result;if(i(n)===i(0))A=n;else if(n)if(null!==v&&v[0]>k)k=v[0],n.advance(v[0]);else if(null!==v&&k>=v[0]+v[1]);else{var e=!0,r="value"in n?n.value:n.key;f(d).call(d,(function(t){t&&t.length&&(2===t.length?e=e&&r[t[0]]===t[1]:O(t[0])&&(e=e&&t[0].apply(void 0,[r])))})),e&&(k++,A.push(h(r)),s?n.delete():u&&(r=function(t){for(var n=0;n<T.length;n++){var e=T[n],r=u[e];r instanceof Function&&(r=r(t)),t[e]=r}return t}(r),n.update(r))),n.continue()}},w.oncomplete=function(){a(A)},w.onerror=function(t){y(t)},w.onabort=function(t){y(t)}}))},d=function(t,n){var e="next",r="openCursor",o=[],i=null,a=j,f=!1,l=function(){return p(t,n,r,f?e+"unique":e,i,o,a)},v=function(){return e=null,r="count",{execute:l}},d=function t(){return 1==(i=A(arguments[0])?arguments[0]:c(Array.prototype).call(arguments,0,2)).length&&i.unshift(0),P(i[1])||(i=null),{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:x,modify:b,limit:t,map:w,remove:S}},h=function t(n){return(n=!!E(n)||!!n)&&(r="openKeyCursor"),{execute:l,keys:t,filter:y,asc:m,desc:g,distinct:x,modify:b,limit:d,map:w,remove:S}},y=function t(){return o.push(c(Array.prototype).call(arguments,0,2)),{execute:l,count:v,keys:h,filter:t,asc:m,desc:g,distinct:x,modify:b,limit:d,map:w,remove:S}},m=function t(n){return n=!!E(n)||!!n,e=n?"next":"prev",{execute:l,count:v,keys:h,filter:y,asc:t,desc:g,distinct:x,modify:b,limit:d,map:w,remove:S}},g=function t(n){return n=!!E(n)||!!n,e=n?"prev":"next",{execute:l,count:v,keys:h,filter:y,asc:m,desc:t,distinct:x,modify:b,limit:d,map:w,remove:S}},x=function t(n){return n=!!E(n)||!!n,f=n,{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:t,modify:b,limit:d,map:w,remove:S}},b=function t(n){return u=n,{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:x,modify:t,limit:d,map:w,remove:S}},w=function t(n){return O(n)&&(a=n),{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:x,modify:b,limit:d,map:t,remove:S}},S=function t(n){return n=!!E(n)||!!n,s=n,{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:x,modify:b,limit:d,map:w,remove:t}};return{execute:l,count:v,keys:h,filter:y,asc:m,desc:g,distinct:x,modify:b,limit:d,map:w,remove:S}};f(r="only bound upperBound lowerBound".split(" ")).call(r,(function(t){o[t]=function(){return new d(t,arguments)}})),this.filter=function(){var t=new d(null,null);return v(t).apply(t,arguments)},this.all=function(){var t;return v(t=this).call(t)}},L=function(t,n,e,r){var o=t.target.result,i=new k(o,n);return I[n]=o,a.resolve(i)},I={},_={version:"0.10.2",open:function(t){var n;return new a((function(e,r){if(I[t.server])L({target:{result:I[t.server]}},t.server,t.version,t.schema).then(e,r);else{try{n=w().open(t.server,t.version)}catch(t){r(t)}n.onsuccess=function(n){L(n,t.server,t.version,t.schema).then(e,r)},n.onupgradeneeded=function(n){!function(t,n,e){for(var r in"function"==typeof n&&(n=n()),n){var o,i=n[r];for(var c in o=!b.call(n,r)||e.objectStoreNames.contains(r)?t.currentTarget.transaction.objectStore(r):e.createObjectStore(r,i.key),i.indexes){var u=i.indexes[c];try{o.index(c)}catch(t){o.createIndex(c,u.key||c,l(u).length?u:{unique:!1})}}}}(n,t.schema,n.target.result)},n.onerror=function(t){r(t)}}}))},remove:function(t){return new a((function(n,e){if(!t)return n();var r,o;i(t)===k&&(t=t.name),"string"==typeof t&&(r=I[t]),r&&"function"==typeof r.close&&r.close();try{o=w().deleteDatabase(t)}catch(t){e(t)}o.onsuccess=function(e){delete I[t],n(t)},o.onerror=function(t){e(t)},o.onblocked=function(t){e(t)}}))}};void 0!==t.exports?t.exports=_:void 0===(o=function(){return _}.call(n,e,n,t))||(t.exports=o)}(void 0)}).call(this,e(74))},function(t,n,e){var r=e(154),o=e(185);function i(n){return"function"==typeof r&&"symbol"==typeof o?(t.exports=i=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=i=function(t){return t&&"function"==typeof r&&t.constructor===r&&t!==r.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),i(n)}t.exports=i,t.exports.default=t.exports,t.exports.__esModule=!0},function(t,n,e){t.exports=e(155)},function(t,n,e){var r=e(156);e(178),e(179),e(180),e(181),e(182),e(183),e(184),t.exports=r},function(t,n,e){var r=e(157);t.exports=r},function(t,n,e){var r=e(158);e(44),t.exports=r},function(t,n,e){e(96),e(66),e(159),e(162),e(163),e(164),e(165),e(97),e(166),e(167),e(168),e(169),e(170),e(171),e(172),e(173),e(174),e(175),e(176),e(177);var r=e(15);t.exports=r.Symbol},function(t,n,e){"use strict";var r=e(6),o=e(0),i=e(12),c=e(47),u=e(10),a=e(5),f=e(20),s=e(9),p=e(50),l=e(4),v=e(7),d=e(29),h=e(3),y=e(8),m=e(13),g=e(49),x=e(11),b=e(21),w=e(16),j=e(33),S=e(41),O=e(18),P=e(39),A=e(62),E=e(58),k=e(160),T=e(82),L=e(32),I=e(14),_=e(83),M=e(75),D=e(68),R=e(25),C=e(52),F=e(37),N=e(38),B=e(54),G=e(2),z=e(72),q=e(1),V=e(28),U=e(42),W=e(46).forEach,H=F("hidden"),K=G("toPrimitive"),J=U.set,Y=U.getterFor("Symbol"),X=Object.prototype,Q=o.Symbol,Z=Q&&Q.prototype,$=o.TypeError,tt=o.QObject,nt=i("JSON","stringify"),et=L.f,rt=I.f,ot=k.f,it=M.f,ct=a([].push),ut=C("symbols"),at=C("op-symbols"),ft=C("string-to-symbol-registry"),st=C("symbol-to-string-registry"),pt=C("wks"),lt=!tt||!tt.prototype||!tt.prototype.findChild,vt=s&&l((function(){return 7!=P(rt({},"a",{get:function(){return rt(this,"a",{value:7}).a}})).a}))?function(t,n,e){var r=et(X,n);r&&delete X[n],rt(t,n,e),r&&t!==X&&rt(X,n,r)}:rt,dt=function(t,n){var e=ut[t]=P(Z);return J(e,{type:"Symbol",tag:t,description:n}),s||(e.description=n),e},ht=function(t,n,e){t===X&&ht(at,n,e),x(t);var r=j(n);return x(e),v(ut,r)?(e.enumerable?(v(t,H)&&t[H][r]&&(t[H][r]=!1),e=P(e,{enumerable:O(0,!1)})):(v(t,H)||rt(t,H,O(1,{})),t[H][r]=!0),vt(t,r,e)):rt(t,r,e)},yt=function(t,n){x(t);var e=w(n),r=A(e).concat(bt(e));return W(r,(function(n){s&&!u(mt,e,n)||ht(t,n,e[n])})),t},mt=function(t){var n=j(t),e=u(it,this,n);return!(this===X&&v(ut,n)&&!v(at,n))&&(!(e||!v(this,n)||!v(ut,n)||v(this,H)&&this[H][n])||e)},gt=function(t,n){var e=w(t),r=j(n);if(e!==X||!v(ut,r)||v(at,r)){var o=et(e,r);return!o||!v(ut,r)||v(e,H)&&e[H][r]||(o.enumerable=!0),o}},xt=function(t){var n=ot(w(t)),e=[];return W(n,(function(t){v(ut,t)||v(N,t)||ct(e,t)})),e},bt=function(t){var n=t===X,e=ot(n?at:w(t)),r=[];return W(e,(function(t){!v(ut,t)||n&&!v(X,t)||ct(r,ut[t])})),r};(p||(R(Z=(Q=function(){if(m(Z,this))throw $("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?S(arguments[0]):void 0,n=B(t),e=function(t){this===X&&u(e,at,t),v(this,H)&&v(this[H],n)&&(this[H][n]=!1),vt(this,n,O(1,t))};return s&&lt&&vt(X,n,{configurable:!0,set:e}),dt(n,t)}).prototype,"toString",(function(){return Y(this).tag})),R(Q,"withoutSetter",(function(t){return dt(B(t),t)})),M.f=mt,I.f=ht,_.f=yt,L.f=gt,E.f=k.f=xt,T.f=bt,z.f=function(t){return dt(G(t),t)},s&&(rt(Z,"description",{configurable:!0,get:function(){return Y(this).description}}),f||R(X,"propertyIsEnumerable",mt,{unsafe:!0}))),r({global:!0,wrap:!0,forced:!p,sham:!p},{Symbol:Q}),W(A(pt),(function(t){q(t)})),r({target:"Symbol",stat:!0,forced:!p},{for:function(t){var n=S(t);if(v(ft,n))return ft[n];var e=Q(n);return ft[n]=e,st[e]=n,e},keyFor:function(t){if(!g(t))throw $(t+" is not a symbol");if(v(st,t))return st[t]},useSetter:function(){lt=!0},useSimple:function(){lt=!1}}),r({target:"Object",stat:!0,forced:!p,sham:!s},{create:function(t,n){return void 0===n?P(t):yt(P(t),n)},defineProperty:ht,defineProperties:yt,getOwnPropertyDescriptor:gt}),r({target:"Object",stat:!0,forced:!p},{getOwnPropertyNames:xt,getOwnPropertySymbols:bt}),r({target:"Object",stat:!0,forced:l((function(){T.f(1)}))},{getOwnPropertySymbols:function(t){return T.f(b(t))}}),nt)&&r({target:"JSON",stat:!0,forced:!p||l((function(){var t=Q();return"[null]"!=nt([t])||"{}"!=nt({a:t})||"{}"!=nt(Object(t))}))},{stringify:function(t,n,e){var r=D(arguments),o=n;if((y(n)||void 0!==t)&&!g(t))return d(n)||(n=function(t,n){if(h(o)&&(n=u(o,this,t,n)),!g(n))return n}),r[1]=n,c(nt,null,r)}});if(!Z[K]){var wt=Z.valueOf;R(Z,K,(function(t){return u(wt,this)}))}V(Q,"Symbol"),N[H]=!0},function(t,n,e){var r=e(26),o=e(16),i=e(58).f,c=e(161),u="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return u&&"Window"==r(t)?function(t){try{return i(t)}catch(t){return c(u)}}(t):i(o(t))}},function(t,n,e){var r=e(0),o=e(59),i=e(22),c=e(71),u=r.Array,a=Math.max;t.exports=function(t,n,e){for(var r=i(t),f=o(n,r),s=o(void 0===e?r:e,r),p=u(a(s-f,0)),l=0;f<s;f++,l++)c(p,l,t[f]);return p.length=l,p}},function(t,n,e){e(1)("asyncIterator")},function(t,n){},function(t,n,e){e(1)("hasInstance")},function(t,n,e){e(1)("isConcatSpreadable")},function(t,n,e){e(1)("match")},function(t,n,e){e(1)("matchAll")},function(t,n,e){e(1)("replace")},function(t,n,e){e(1)("search")},function(t,n,e){e(1)("species")},function(t,n,e){e(1)("split")},function(t,n,e){e(1)("toPrimitive")},function(t,n,e){e(1)("toStringTag")},function(t,n,e){e(1)("unscopables")},function(t,n,e){var r=e(0);e(28)(r.JSON,"JSON",!0)},function(t,n){},function(t,n){},function(t,n,e){e(1)("asyncDispose")},function(t,n,e){e(1)("dispose")},function(t,n,e){e(1)("matcher")},function(t,n,e){e(1)("metadata")},function(t,n,e){e(1)("observable")},function(t,n,e){e(1)("patternMatch")},function(t,n,e){e(1)("replaceAll")},function(t,n,e){t.exports=e(186)},function(t,n,e){var r=e(187);t.exports=r},function(t,n,e){var r=e(188);t.exports=r},function(t,n,e){var r=e(189);e(44),t.exports=r},function(t,n,e){e(64),e(66),e(93),e(97);var r=e(72);t.exports=r.f("iterator")},function(t,n,e){t.exports=e(191)},function(t,n,e){var r=e(192);t.exports=r},function(t,n,e){e(193);var r=e(15);t.exports=r.Array.isArray},function(t,n,e){e(6)({target:"Array",stat:!0},{isArray:e(29)})},function(t,n,e){t.exports=e(195)},function(t,n,e){e(44);var r=e(24),o=e(7),i=e(13),c=e(196),u=Array.prototype,a={DOMTokenList:!0,NodeList:!0};t.exports=function(t){var n=t.forEach;return t===u||i(u,t)&&n===u.forEach||o(a,r(t))?c:n}},function(t,n,e){var r=e(197);t.exports=r},function(t,n,e){e(198);var r=e(30);t.exports=r("Array").forEach},function(t,n,e){"use strict";var r=e(6),o=e(199);r({target:"Array",proto:!0,forced:[].forEach!=o},{forEach:o})},function(t,n,e){"use strict";var r=e(46).forEach,o=e(200)("forEach");t.exports=o?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},function(t,n,e){"use strict";var r=e(4);t.exports=function(t,n){var e=[][t];return!!e&&r((function(){e.call(null,n||function(){return 1},1)}))}},function(t,n,e){t.exports=e(202)},function(t,n,e){var r=e(203);t.exports=r},function(t,n,e){e(204);var r=e(15).Object,o=t.exports=function(t,n,e){return r.defineProperty(t,n,e)};r.defineProperty.sham&&(o.sham=!0)},function(t,n,e){var r=e(6),o=e(9),i=e(14).f;r({target:"Object",stat:!0,forced:Object.defineProperty!==i,sham:!o},{defineProperty:i})},function(t,n,e){t.exports=e(206)},function(t,n,e){var r=e(207);t.exports=r},function(t,n,e){var r=e(13),o=e(208),i=Array.prototype;t.exports=function(t){var n=t.concat;return t===i||r(i,t)&&n===i.concat?o:n}},function(t,n,e){e(96);var r=e(30);t.exports=r("Array").concat},function(t,n,e){t.exports=e(210)},function(t,n,e){var r=e(211);t.exports=r},function(t,n,e){e(212);var r=e(15);t.exports=r.Object.keys},function(t,n,e){var r=e(6),o=e(21),i=e(62);r({target:"Object",stat:!0,forced:e(4)((function(){i(1)}))},{keys:function(t){return i(o(t))}})},function(t,n,e){t.exports=e(214)},function(t,n,e){var r=e(215);t.exports=r},function(t,n,e){var r=e(13),o=e(216),i=Array.prototype;t.exports=function(t){var n=t.filter;return t===i||r(i,t)&&n===i.filter?o:n}},function(t,n,e){e(217);var r=e(30);t.exports=r("Array").filter},function(t,n,e){"use strict";var r=e(6),o=e(46).filter;r({target:"Array",proto:!0,forced:!e(45)("filter")},{filter:function(t){return o(this,t,arguments.length>1?arguments[1]:void 0)}})}]);',"Worker",void 0,void 0)}},function(e,t,n){var r=n(4),o=n(17),i=n(8),a=n(212),s={log:{key:{keyPath:"time"},indexes:{level:{unique:!1},time:{unique:!0}}}};function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.server=null,this.expire=e.expire||72,this.name=e.name}var u=c.prototype;u.init=function(){var e=this;if(!this.server)return a.open({server:"nim-log-"+this.name,version:1,schema:s}).then((function(t){e.server=t}))},u.putLog=function(e){var t=this;if(!this.server)return r.reject("no db server");var n=o(e).call(e,-1)[0].time-36e5*this.expire;return this.deleteLogs(n).then((function(){return t.server.add("log",e)}))},u.getAllLogs=function(){return this.server?this.server.query("log","time").all().execute().then((function(e){var t={},n=o(e).call(e,-1)[0];return t.logs=i(e).call(e,(function(e){return e.log})).join("\r\n"),t.time=n&&n.time,t})):r.reject("no db server")},u.deleteLogs=function(e){return this.server?this.server.remove("log",null,"time",0,e):r.reject("no db server")},e.exports=c},function(e,t,n){var r=n(192);e.exports=function(e){if(r(e))return e},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){var r=n(139),o=n(80);e.exports=function(e,t){var n=null==e?null:void 0!==r&&o(e)||e["@@iterator"];if(null!=n){var i,a,s=[],c=!0,u=!1;try{for(n=n.call(e);!(c=(i=n.next()).done)&&(s.push(i.value),!t||s.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{c||null==n.return||n.return()}finally{if(u)throw a}}return s}},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){e.exports=n(402)},function(e,t,n){var r=n(403);e.exports=r},function(e,t,n){var r=n(178);e.exports=r},function(e,t,n){var r=n(405);e.exports=r},function(e,t,n){var r=n(157);e.exports=r},function(e,t,n){var r=n(407),o=n(197);e.exports=function(e,t){if(null==e)return{};var n,i,a={},s=r(e);for(i=0;i<s.length;i++)n=s[i],o(t).call(t,n)>=0||(a[n]=e[n]);return a},e.exports.default=e.exports,e.exports.__esModule=!0},function(e,t,n){e.exports=n(408)},function(e,t,n){var r=n(409);e.exports=r},function(e,t,n){var r=n(182);e.exports=r},function(e,t,n){n(184);var r=n(13);e.exports=r.setInterval},function(e,t,n){var r=n(412);e.exports=r},function(e,t,n){var r=n(16),o=n(413),i=Function.prototype;e.exports=function(e){var t=e.bind;return e===i||r(i,e)&&t===i.bind?o:t}},function(e,t,n){n(414);var r=n(22);e.exports=r("Function").bind},function(e,t,n){var r=n(2),o=n(415);r({target:"Function",proto:!0,forced:Function.bind!==o},{bind:o})},function(e,t,n){"use strict";var r=n(1),o=n(7),i=n(39),a=n(27),s=n(26),c=n(90),u=n(83),l=r.Function,p=o([].concat),m=o([].join),f={},d=function(e,t,n){if(!s(f,t)){for(var r=[],o=0;o<t;o++)r[o]="a["+o+"]";f[t]=l("C,a","return new C("+m(r,",")+")")}return f[t](e,n)};e.exports=u?l.bind:function(e){var t=i(this),n=t.prototype,r=c(arguments,1),o=function(){var n=p(r,c(arguments));return this instanceof o?d(t,n.length,n):t.apply(e,n)};return a(n)&&(o.prototype=n),o}},function(e,t,n){var r=n(14),o=n(23),i=n(3),a=n(25),s=n(9),c=n(43),u=n(141);function l(e){e.onuploading&&this.on("uploading",e.onuploading),u.call(this,e)}var p=u.prototype,m=l.prototype=r(p);m.doSend=function(){var e,t,n,r=this.options,u=r.headers,l=this.xhr=new XMLHttpRequest;if("multipart/form-data"===u["Content-Type"]){var p,m;delete u["Content-Type"],l.upload.onprogress=o(p=this.onProgress).call(p,this),l.upload.onload=o(m=this.onProgress).call(m,this);var f,d=r.data;if(r.data=new window.FormData,d)i(f=c.getKeys(d,r.putFileAtEnd)).call(f,(function(e){var t=d[e];t.tagName&&"INPUT"===t.tagName.toUpperCase()?"file"===t.type&&i([]).call(t.files,(function(e){r.data.append(c.dataset(t,"name")||t.name||e.name||"file-"+c.uniqueID(),e)})):r.data.append(e,t)}))}else if(u["x-nos-token"]){var g,h;l.upload.onprogress=o(g=this.onProgress).call(g,this),l.upload.onload=o(h=this.onProgress).call(h,this)}(l.onreadystatechange=o(e=this.onStateChange).call(e,this),0!==r.timeout)&&(this.timer=a(o(n=this.onTimeout).call(n,this),r.timeout));l.open(r.method,r.url,!r.sync),i(t=s(u)).call(t,(function(e){l.setRequestHeader(e,u[e])})),r.cookie&&"withCredentials"in l&&(l.withCredentials=!0),l.send(r.data),this.afterSend()},m.onProgress=function(e){e.lengthComputable&&e.loaded<=e.total&&this.emit("uploading",e)},m.onStateChange=function(){var e,t=this.xhr;4===t.readyState&&(e={status:t.status,result:t.responseText||""},this.onLoad(e))},m.getResponseHeader=function(e){var t=this.xhr;return t?t.getResponseHeader(e):""},m.destroy=function(){clearTimeout(this.timer);try{this.xhr.onreadystatechange=c.f,this.xhr.abort()}catch(e){}p.destroy.call(this)},e.exports=l},function(e,t,n){var r;
/*!
 * EventEmitter v5.2.6 - git.io/ee
 * Unlicense - http://unlicense.org/
 * Oliver Caldwell - https://oli.me.uk/
 * @preserve
 */!function(t){"use strict";function o(){}var i=o.prototype,a=t.EventEmitter;function s(e,t){for(var n=e.length;n--;)if(e[n].listener===t)return n;return-1}function c(e){return function(){return this[e].apply(this,arguments)}}i.getListeners=function(e){var t,n,r=this._getEvents();if(e instanceof RegExp)for(n in t={},r)r.hasOwnProperty(n)&&e.test(n)&&(t[n]=r[n]);else t=r[e]||(r[e]=[]);return t},i.flattenListeners=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n.push(e[t].listener);return n},i.getListenersAsObject=function(e){var t,n=this.getListeners(e);return n instanceof Array&&((t={})[e]=n),t||n},i.addListener=function(e,t){if(!function e(t){return"function"==typeof t||t instanceof RegExp||!(!t||"object"!=typeof t)&&e(t.listener)}(t))throw new TypeError("listener must be a function");var n,r=this.getListenersAsObject(e),o="object"==typeof t;for(n in r)r.hasOwnProperty(n)&&-1===s(r[n],t)&&r[n].push(o?t:{listener:t,once:!1});return this},i.on=c("addListener"),i.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},i.once=c("addOnceListener"),i.defineEvent=function(e){return this.getListeners(e),this},i.defineEvents=function(e){for(var t=0;t<e.length;t+=1)this.defineEvent(e[t]);return this},i.removeListener=function(e,t){var n,r,o=this.getListenersAsObject(e);for(r in o)o.hasOwnProperty(r)&&-1!==(n=s(o[r],t))&&o[r].splice(n,1);return this},i.off=c("removeListener"),i.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},i.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},i.manipulateListeners=function(e,t,n){var r,o,i=e?this.removeListener:this.addListener,a=e?this.removeListeners:this.addListeners;if("object"!=typeof t||t instanceof RegExp)for(r=n.length;r--;)i.call(this,t,n[r]);else for(r in t)t.hasOwnProperty(r)&&(o=t[r])&&("function"==typeof o?i.call(this,r,o):a.call(this,r,o));return this},i.removeEvent=function(e){var t,n=typeof e,r=this._getEvents();if("string"===n)delete r[e];else if(e instanceof RegExp)for(t in r)r.hasOwnProperty(t)&&e.test(t)&&delete r[t];else delete this._events;return this},i.removeAllListeners=c("removeEvent"),i.emitEvent=function(e,t){var n,r,o,i,a=this.getListenersAsObject(e);for(i in a)if(a.hasOwnProperty(i))for(n=a[i].slice(0),o=0;o<n.length;o++)!0===(r=n[o]).once&&this.removeListener(e,r.listener),r.listener.apply(this,t||[])===this._getOnceReturnValue()&&this.removeListener(e,r.listener);return this},i.trigger=c("emitEvent"),i.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return this.emitEvent(e,t)},i.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},i._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},i._getEvents=function(){return this._events||(this._events={})},o.noConflict=function(){return t.EventEmitter=a,o},void 0===(r=function(){return o}.call(t,n,t,e))||(e.exports=r)}("undefined"!=typeof window?window:this||{})},function(e,t,n){var r=n(14),o=n(6),i=n(3),a=n(23),s=n(78),c=n(43),u=n(141),l=n(198),p="NEJ-UPLOAD-RESULT:",m={};function f(e){this.init(),u.call(this,e)}var d=u.prototype,g=f.prototype=r(d);g.init=function(){var e=!1;function t(e){var t=e.data;if(0===o(t).call(t,p)){var n=(t=JSON.parse(t.replace(p,""))).key,r=m[n];r&&(delete m[n],t.result=decodeURIComponent(t.result||""),r.onLoad(t.result))}}return function(){!function(){if(!e){e=!0;var n=c.getGlobal();n.postMessage?c.on(n,"message",t):(l.addMsgListener(t),l.startTimer())}}()}}(),g.doSend=function(){var e=this,t=e.options,n=e.key="zoro-ajax-upload-iframe-"+c.uniqueID();m[n]=e;var r=e.form=c.html2node('<form style="display:none;"></form>');"undefined"==typeof document||document.body.appendChild(r),r.target=n,r.method="POST",r.enctype="multipart/form-data",r.encoding="multipart/form-data";var o=t.url,s=c.genUrlSep(o);r.action=o+s+"_proxy_=form";var u,l=t.data,p=[],f=[];l&&i(u=c.getKeys(l,t.putFileAtEnd)).call(u,(function(e){var t=l[e];if(t.tagName&&"INPUT"===t.tagName.toUpperCase()){if("file"===t.type){var n=t,o=n.cloneNode(!0);n.parentNode.insertBefore(o,n);var i=c.dataset(n,"name");i&&(n.name=i),r.appendChild(n),c.isFunction(n.setAttribute)&&(n.setAttribute("form",""),n.removeAttribute("form")),p.push(t),f.push(o)}}else{var a=c.html2node('<input type="hidden"/>');a.name=e,a.value=t,r.appendChild(a)}}));function d(){i(p).call(p,(function(e,t){var n=f[t];n.parentNode&&(e.name=n.name,c.isFunction(e.setAttribute)&&e.setAttribute("form",n.getAttribute("form")),n.parentNode.replaceChild(e,n))}))}var g=e.iframe=c.createIframe({name:n,onload:function(){var t;e.aborted?d():(c.on(g,"load",a(t=e.checkResult).call(t,e)),r.submit(),d(),e.afterSend())}})},g.checkResult=function(){var e,t;try{var n,r;if(e=this.iframe.contentWindow.document.body,t=s(n=e.innerText||e.textContent||"").call(n),o(t).call(t,p)>=0||o(r=e.innerHTML).call(r,p)>=0)return}catch(e){return}this.onLoad(t)},g.onLoad=function(e){d.onLoad.call(this,{status:200,result:e}),c.remove(this.form),c.remove(this.iframe),d.destroy.call(this)},g.destroy=function(){c.remove(this.iframe),c.remove(this.form)},g.abort=function(){this.aborted=!0,delete m[this.key],d.abort.call(this)},e.exports=f},function(e,t,n){var r=n(420);e.exports=r},function(e,t,n){var r=n(16),o=n(421),i=String.prototype;e.exports=function(e){var t=e.trim;return"string"==typeof e||e===i||r(i,e)&&t===i.trim?o:t}},function(e,t,n){n(422);var r=n(22);e.exports=r("String").trim},function(e,t,n){"use strict";var r=n(2),o=n(140).trim;r({target:"String",proto:!0,forced:n(423)("trim")},{trim:function(){return o(this)}})},function(e,t,n){var r=n(165).PROPER,o=n(11),i=n(109);e.exports=function(e){return o((function(){return!!i[e]()||"​᠎"!=="​᠎"[e]()||r&&i[e].name!==e}))}},function(e,t,n){var r=n(14),o=n(6),i=n(23),a=n(3),s=n(43),c=n(198),u=n(141),l={};function p(e){this.init(),u.call(this,e)}var m=u.prototype,f=p.prototype=r(m);f.init=function(){var e="NEJ-AJAX-DATA:",t=!1;function n(t){var n=t.data;if(0===o(n).call(n,e)){var r=(n=JSON.parse(n.replace(e,""))).key,i=l[r];i&&(delete l[r],n.result=decodeURIComponent(n.result||""),i.onLoad(n))}}return function(){!function(){if(!t){t=!0;var e=s.getGlobal();e.postMessage?s.on(e,"message",n):c.addMsgListener(n)}}()}}(),f.doSend=function(){var e=this.options,t=s.url2origin(e.url),n=e.proxyUrl||t+"/res/nej_proxy_frame.html",r=l[n];if(s.isArray(r)){var o;r.push(i(o=this.doSend).call(o,this,e))}else{var u;if(!r)return l[n]=[i(u=this.doSend).call(u,this,e)],void s.createIframe({src:n,onload:function(e){var t=l[n];l[n]=s.target(e).contentWindow,a(t).call(t,(function(e){try{e()}catch(e){}}))}});if(!this.aborted){var p=this.key=s.uniqueID();l[p]=this;var m=s.fetch({method:"GET",url:"",data:null,headers:{},timeout:0},e);m.key=p,c.postMessage(r,{data:m}),this.afterSend()}}},f.abort=function(){this.aborted=!0,delete l[this.key],m.abort.call(this)},e.exports=p},function(e,t,n){var r,o,i=n(12),a=n(43),s=n(110),c=(r=/json/i,o=/post/i,function(e,t){var n=(t=t||{}).data=t.data||{},c=t.headers=t.headers||{},u=a.checkWithDefault(c,"Accept","application/json"),l=a.checkWithDefault(c,"Content-Type","application/json");return r.test(u)&&(t.type="json"),o.test(t.method)&&r.test(l)&&(t.data=i(n)),s(e,t)});e.exports=c},function(e,t,n){var r=n(110);e.exports=function(e,t){return t.method="POST",t.headers=t.headers||{},t.headers["Content-Type"]="multipart/form-data",t.timeout=0,t.type=t.type||"json",r(e,t)}},function(e,t,n){var r=n(17),o=n(51),i=n(6),a=n(4),s=n(15),c=s.chunkSize,u=n(110),l={mp4:"video/mp4",avi:"video/x-msvideo",wmv:"video/x-ms-wmv",mpeg:"video/mpeg",mov:"video/quicktime",aac:"audio/x-aac",wma:"audio/x-ms-wma",wav:"audio/x-wav",mp3:"audio/mp3"};e.exports=function e(t,n,p,m){var f,d={file:t.data[n],fileSize:t.data[n].size,fileUploadedSize:0,percentage:0},g=t.url;function h(e){var n=d.fileUploadedSize+e.loaded,r=Math.floor(1e4*n/d.fileSize)/100;if(o(r)>=100&&(r=100,h=function(){}),d.percentage!==r){d.percentage=r;var i={docId:t.docId,total:d.fileSize,loaded:n,percentage:r,percentageText:r+"%"};t.fileInput&&(i.fileInput=t.fileInput),t.blob&&(i.blob=t.blob),t.uploadprogress(i)}}function y(e){try{e=JSON.parse(e)}catch(e){return void p.onError(e)}if(e.errMsg||e.errCode)p.onError(e);else if(e.offset<d.fileSize)delete S.onaftersend,d.fileUploadedSize=e.offset,p.sn=function(e,t,n,o){var i,a=e.offset,s=e.offset+c;return t.data=r(i=o.file).call(i,a,s),t.query.offset=e.offset,t.query.complete=s>=o.fileSize,t.query.context=e.context,t.onuploading=h,t.onload=y,t.onerror=v,u(g,t)}(e,S,0,d);else{var n=s.genFileUrl(t.nosToken);"image"===t.type?u(n+"?imageInfo",{onload:function(n){try{n=JSON.parse(n),t.uploaddone(null,{docId:e.docId,w:n.Width,h:n.Height,orientation:n.Orientation||"",type:n.Type,size:n.Size||d.fileSize})}catch(e){o(e)}},onerror:function(r){if("undefined"!=typeof Image){var i=new Image;i.src=n,i.onload=function(){t.uploaddone(null,{docId:e.docId,w:i.width,h:i.height,size:d.fileSize})},i.onerror=function(e){o(e)}}else o(r)}}):"video"===t.type||"audio"===t.type?u(n+"?vinfo",{onload:function(n){try{(n=JSON.parse(n)).GetVideoInfo&&n.GetVideoInfo.VideoInfo&&(n=n.GetVideoInfo.VideoInfo),t.uploaddone(null,{docId:e.docId,w:n.Width,h:n.Height,dur:n.Duration,orientation:n.Rotate,audioCodec:n.AudioCodec,videoCodec:n.VideoCodec,container:n.Container,size:n.Size||d.fileSize})}catch(e){o(e)}},onerror:o}):t.uploaddone(null,{docId:e.docId,size:d.fileSize})}function o(e){p.onError(e)}}function v(r){var o=r&&r.code;function i(){try{if(r.result)var e=JSON.parse(r.result);else e=r;p.onError(e)}catch(e){p.onError(e)}}0===d.fileUploadedSize&&t.nosLbsUrls&&t.nosLbsUrls.length>0&&"abort"!==o?t.edgeList?m<t.edgeList.length-1?e(t,n,p,m+1):i():function(e,t,n,r){return new a((function(o,i){function a(){u(n[e],{query:{version:"1.0",bucketname:t},method:"GET",onerror:s,onload:function(e){try{(e=JSON.parse(e))&&e.upload&&e.upload.length?o(e.upload):s()}catch(e){s()}}})}function s(){r.onLbsUrlFail(n[e]),e<n.length-1?(e++,a()):o([])}a()}))}(0,t.nosToken.bucket,t.nosLbsUrls,p).then((function(r){r.length>0?(t.edgeList=r,t.updateNosEdgeList&&t.updateNosEdgeList(r),e(t,n,p,m+1)):i()})):i()}"number"!=typeof m&&(m=-1),t.edgeList&&t.edgeList.length&&(m=m>0?m:0,g=t.edgeList[m]),g+="/"+t.nosToken.bucket+"/"+t.nosToken.objectName;var b=t.data.file&&t.data.file.type;if(!b||i(b).call(b,"/")<0){var T=(t.fileInputName||"").split(".").pop();"image"===t.type?b="image/"+("jpg"===T?"jpeg":T):"audio"!==t.type&&"video"!==t.type||(b=l[T])}var S={query:{offset:0,complete:c>=d.fileSize,version:"1.0"},headers:{"Content-Type":b||"application/octet-stream","x-nos-token":t.nosToken.token},method:"POST",timeout:0,onaftersend:function(){t.beginupload(p)},onuploading:h,onload:y,onerror:v};return S.data=r(f=d.file).call(f,0,c),u(g,S)}},function(e,t,n){var r=n(44).fn;r.isConnected=function(){return!!this.protocol&&this.protocol.isConnected()},r.connect=function(){this.protocol.connect(!0)},r.disconnect=function(e){e=e||{},this.protocol.disconnect(e.done)}},function(e,t,n){var r=n(9),o=n(60),i=n(19),a=n(61),s=n(62),c=n(63),u=n(50),l=n(28),p=n(12),m=n(23),f=n(8),d=n(17),g=n(107),h=n(78),y=n(6),v=n(25),b=n(5),T=n(30),S=n(3),x=n(10),k=n(64),M=n(120),w=["loc_x","loc_y","loc_z"];function _(e,t){var n=r(e);if(o){var s=o(e);t&&(s=i(s).call(s,(function(t){return a(e,t).enumerable}))),n.push.apply(n,s)}return n}var C=n(44).fn,P=n(0),I=n(434),A=n(33),E=n(15),O=n(199),j=n(435),N=n(54),L=n(200);C.sendText=function(e){var t=e,n=t.loc_x,r=t.loc_y,o=t.loc_z,i=M(t,w);return void 0===n||void 0===r||void 0===o?e=function(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?S(n=_(Object(o),!0)).call(n,(function(t){k(e,t,o[t])})):s?c(e,s(o)):S(r=_(Object(o))).call(r,(function(t){u(e,t,a(o,t))}))}return e}({},i):("number"!=typeof e.loc_x&&(e.loc_x=0),"number"!=typeof e.loc_y&&(e.loc_y=0),"number"!=typeof e.loc_z&&(e.loc_z=0)),this.processCallback(e),e.msg=new this.message.TextMessage(e),this.sendMsg(e)},C.previewFile=function(e){var t,n=this,r=n.protocol.appGrayConfig;if(r&&r.grayConfig&&r.grayConfig.mixStoreEnable&&(e.mixEnable=!0),P.verifyOptions(e,"done","msg::previewFile"),e.type||(e.type="file"),P.verifyParamPresentJustOne(e,"dataURL blob fileInput filePath wxFilePath fileObject","msg::previewFile"),P.exist(e.maxSize)&&P.verifyParamType("maxSize",e.maxSize,"number","api::previewFile"),P.exist(e.commonUpload)&&P.verifyParamType("commonUpload",e.commonUpload,"boolean","api::previewFile"),e.nosSurvivalTime?(P.verifyParamType("nosSurvivalTime",e.nosSurvivalTime,"number","api::Base.getInstance"),P.verifyParamMin("nosSurvivalTime",e.nosSurvivalTime,86400,"api::Base.getInstance")):e.nosSurvivalTime=this.nosSurvivalTime,e.filePath=e.filePath||e.wxFilePath,delete e.wxFilePath,e.dataURL)e.blob=L.fromDataURL(e.dataURL);else if(e.blob);else if(e.fileInput){if(e.fileInput=P.verifyFileInput(e.fileInput,"msg::previewFile"),e.fileInput.files){if(!e.fileInput.files.length)return void e.done(A.newNoFileError("请选择"+e.type+"文件",{callFunc:"msg::previewFile",fileInput:e.fileInput}),e);e.fileSize=e.fileInput.files[0].size}e.fileInputName=P.getFileName(e.fileInput)}this.processCallback(e);var o=p(O.genResponseBody(e.type)||{}).replace(/"/gi,'\\"'),i=null,a=e.transcode?"getNosTokenTrans":"getNosToken";if(e.transcode){P.verifyOptions(e,"fileInput","msg::previewFile");var s=P.getFileInfo(e.fileInput);i={transToken:{name:s.name,type:s.transcodeType,transType:"png"===e.transcode?11:10,size:s.size,body:o}}}else i=o;this[a]({responseBody:i,nosToken:{nosScene:e.nosScene||this.nosScene,nosSurvivalTime:e.nosSurvivalTime},callback:m(t=function(t,r){if(t)e.done(t);else if(e.transcode?(e.nosToken=r.nosToken,e.docId=r.docId):e.nosToken=r,e.mixEnable){var o,a;if(!this.protocol.mixStorePolicy)try{this.protocol.mixStorePolicy=JSON.parse(localStorage.getItem("NIM-AllMixStorePolicy"))[this.options.appKey]}catch(e){n.logger.error("error:","get last mixStorePolicy failed",e)}if(!this.protocol.mixStorePolicy)return void n.logger.error("error:","get mixStorePolicy failed");e.providers=f(o=this.protocol.mixStorePolicy.providers||[]).call(o,(function(e){return Number(e)})),e.curProvider=e.providers[0]?Number(e.providers[0]):1,e.nosPolicy=this.protocol.mixStorePolicy.nosPolicy,e.s3Policy=this.protocol.mixStorePolicy.s3Policy,e.logger=n.logger,n.getMixStoreToken({curProvider:e.curProvider,responseBody:i,callback:m(a=function(t,r){t?e.done(t):(e.mixStoreToken=r,e.getMixStoreToken=function(t,r){n.getMixStoreToken({curProvider:t,responseBody:i,callback:function(t,n){t?e.done(t):r(n)}})},1===e.curProvider&&(e.nosToken=r),n._doPreviewFile(e))}).call(a,n)})}else this._doPreviewFile(e)}).call(t,this)})},C._doPreviewFile=function(e){var t=this;N.startUniErrCache("nos",{user_id:this.account,action:"upload"});var n=e.uploaddone,r=E.genUploadUrl(e.nosToken.bucket);e.mixEnable&&1===e.curProvider&&(r=E.genUploadUrl(e.mixStoreToken.bucket));var o,i=E.chunkUploadUrl;t.logger.info("_doPreviewFile: start upload"),e.commonUpload||!i||E.isWeixinApp||E.isNodejs||E.isRN?(e.commonUpload=!0,o=r):(this.logger.info("use chunkUrl: ",i,E.nosLbsUrls),o=i,e.nosLbsUrls=E.nosLbsUrls,t.edgeList?e.edgeList=t.edgeList:e.updateNosEdgeList=function(e){t.edgeList=e});var a=this.assembleUploadParams(e.nosToken);e.mixEnable&&1===e.curProvider&&(a=this.assembleUploadParams(e.mixStoreToken));function s(r,o,i){if(t.logger.info("_doPreviewFile: upload done",r&&r.message),e.uploaddone=n,r)return N.updateUniErrCache("nos",{operation_type:"transfer",error:r&&r.message}),N.concludeUniErrCache("nos",1),void e.done(r,e.callback.options);if(N.concludeUniErrCache("nos",0),o=O.parseResponse(o,t.options.exifOrientation),i||o.isS3||(o.url=E.genDownloadUrl(e.nosToken,a.Object,E.serverNosConfig.cdnDomain),e.nosToken.shortUrl&&(o._url_safe=e.nosToken.shortUrl)),P.exist(e.fileInputName))o.name=e.fileInputName;else if(e.blob){var s=e.blob.name;if(o.name=s||"blob-"+o.md5,!s){var c=e.blob.type;o.ext=d(c).call(c,g(c).call(c,"/")+1)}}else e.filePath?o.name=e.filePath:e.fileObject&&(o.name=e.fileObject.fileName);if(!o.ext){var u,l,p=g(u=o.name).call(u,".");if(-1===p)o.ext="unknown";else o.ext=d(l=o.name).call(l,p+1)}o.size=o.size||0,o.isS3&&o._url_safe?t.getNosOriginUrl({safeShortUrl:o._url_safe,done:function(n,r){n?t.logger.error("error:","getNosOriginUrl failed",n):(o.url=r,e.done(null,P.copy(o)))}}):e.done(null,P.copy(o))}if(E.isWeixinApp)P.verifyOptions(e,"filePath","msg::_doPreviewFile"),t.fileQuickTransfer(e,s,(function(){var n=wx.uploadFile({url:o,filePath:e.filePath,name:"file",formData:a,fail:function(e){s({code:"FAILED",msg:e}),t.logger.error("error:","api::msg:upload file failed",e)},success:function(e){if(200===e.statusCode)try{s(null,JSON.parse(e.data))}catch(n){t.logger.error("error:","parse wx upload file res error",n),s({code:"PARSE_WX_UPLOAD_FILE_RES_ERROR",str:e.data,msg:e.errMsg})}else s({code:e.statusCode,msg:e.errMsg})}});"function"==typeof e.uploadprogress&&n&&(t.logger.info("_doPreviewFile::mini uploadprogress"),n.onProgressUpdate((function(t){e.uploadprogress({total:t.totalBytesExpectedToSend,loaded:t.totalBytesSent,percentage:t.progress,percentageText:t.progress+"%"})})))}));else if(E.isNodejs){var c={url:o,name:"file",formData:a,success:function(e){if(200===e.statusCode)try{s(null,JSON.parse(e.data))}catch(n){t.protocol.logger.error("error:","parse nodejs upload file res error",n),s({code:"PARSE_NODEJS_UPLOAD_FILE_RES_ERROR",str:e.data,msg:e.errMsg})}else s({code:e.statusCode,msg:e.errMsg})},fail:function(e){s({code:"FAILED",msg:e}),t.protocol.logger.error("error:","api::msg:upload file failed",e)}};if(e.filePath)c.filePath=e.filePath;else{if("object"!==l(e.fileObject))throw new A("Nodejs上传fileObject参数类型应如 {fileName:..,fileData:..} ");c.fileData=e.fileObject.fileData}t.fileQuickTransfer(e,s,(function(){j.uploadFile(c)}))}else if(E.isRN){var u={url:o,name:"file",formData:a,filePath:e.filePath,success:function(e){if(e.ok&&200===e.status)try{var n=f(e.headers)&&f(e.headers).etag;e.md5=n?"string"==typeof n?n:n[0]:"UNKNOWN",s(null,e)}catch(n){t.protocol.logger.error("error:","parse React Native upload file res error",n),s({code:"PARSE_React_Native_UPLOAD_FILE_RES_ERROR",res:e})}else s({code:e.status,msg:e.statusText})},fail:function(e){s({code:"FAILED",msg:e}),t.protocol.logger.error("error:","api::msg:upload file failed",e)}};t.fileQuickTransfer(e,s,(function(){j.uploadFile(u)}))}else e.uploaddone=s,e.url=o,e.params=a,e.fileName="file",t.fileQuickTransfer(e,s,(function(){return new j(e)}))},C.fileQuickTransfer=function(e,t,n){var r,o=this;e=e||{},t instanceof Function||(t=function(){}),n instanceof Function||(n=function(){});var i=e.fastPass;if(i)try{i=JSON.parse(i),e.fastPass=i}catch(e){o.protocol.logger.error("快传参数解析失败")}var a=e.fileInputName||e.name||e.blob&&e.blob.name||"",s=e.fileSize||e.size||e.blob&&e.blob.size||0,c=i?h(r=(i.md5||e.digest||"")+"").call(r):"",u=e.type||e.blob&&e.blob.type;if(c&&s>=E.threshold){var l=!0,p={name:a,md5:c,ext:d(a).call(a,g(a).call(a,".")+1),type:u};switch(u){case"image":i&&i.w&&i.h?(p.w=i.w,p.h=i.h):(l=!1,o.protocol.logger.error("快传 image 文件缺少参数 w 或 h"));break;case"video":i&&i.w&&i.h&&i.dur?(p.w=i.w,p.h=i.h,p.dur=i.dur):(l=!1,o.protocol.logger.error("快传 video 文件缺少参数 w 或 h 或 dur"));break;case"audio":i&&i.dur?p.dur=i.dur:(l=!1,o.protocol.logger.error("快传 audio 文件缺少参数 dur"))}if(!l)return void n();var m={fileQuickTransfer:{md5:c}};return s&&(m.fileQuickTransfer.size=s),this.protocol.sendCmd("fileQuickTransfer",m,(function(e,r,i){!e&&i&&i.fileQuickTransfer&&i.fileQuickTransfer.url||(o.protocol.logger.error("misc::fileQuickTransfer: not found",e,r),n()),i&&i.fileQuickTransfer&&i.fileQuickTransfer.threshold&&(E.threshold=i.fileQuickTransfer.threshold||0),i&&i.fileQuickTransfer&&i.fileQuickTransfer.url&&(p.size=s||i.fileQuickTransfer.size,p.url=i.fileQuickTransfer.url,i.fileQuickTransfer._url_safe&&(p._url_safe=i.fileQuickTransfer._url_safe),t(e,p,!0))}))}n()},C.sendFile=function(e){if(e.type||(e.type="file"),e=P.merge(e,this.options.providers),P.verifyParamPresentJustOne(e,"dataURL blob fileInput file filePath wxFilePath fileObject","msg::sendFile"),P.exist(e.maxSize)&&P.verifyParamType("maxSize",e.maxSize,"number","api::previewFile"),P.exist(e.commonUpload)&&P.verifyParamType("commonUpload",e.commonUpload,"boolean","api::previewFile"),this.processCallback(e),e.filePath=e.filePath||e.wxFilePath,delete e.wxFilePath,e.dataURL)this._previewAndSendFile(e);else if(e.blob)this._previewAndSendFile(e);else if(e.fileInput){if(e.fileInput=P.verifyFileInput(e.fileInput,"msg::sendFile"),e.fileInput.files&&!e.fileInput.files.length)return void e.done(A.newNoFileError("请选择"+e.type+"文件",{callFunc:"msg::sendFile",fileInput:e.fileInput}),e.callback.options);this._previewAndSendFile(e)}else if(e.filePath||e.fileObject)this._previewAndSendFile(e);else if(e.file){var t,n=e.file._url_safe;return n&&(t=e.file.url,e.file.url=n,delete e.file._url_safe),e.msg=new this.message.FileMessage(e),this.sendMsg(e,t)}},C._previewAndSendFile=function(e){var t=this;P.verifyCallback(e,"uploaddone beforesend","msg::_previewAndSendFile"),N.startUniErrCache("nos",{user_id:this.account,action:"upload"});var n=e.done;e.done=function(r,o){if(e.done=n,r)e.uploaddone(r,e.callback.options),e.done(r,e.callback.options);else{if(/chatroom/.test(e.scene))return;var i;e.uploaddone(null,P.copy(o));var a=o._url_safe;a&&!o.isS3&&(i=o.url,o.url=a,delete o._url_safe),e.file=o,e.msg=new t.message.FileMessage(e),e.beforesend(t.sendMsg(e,i))}},t.previewFile(e)},C.assembleUploadParams=function(e){return e?{Object:decodeURIComponent(e.objectName),"x-nos-token":e.token,"x-nos-entity-type":"json"}:null},C.deleteFile=function(e){P.verifyParamPresentJustOne(e,"docId","msg::deleteFile"),this.removeFile({docId:e.docId,callback:function(t,n){t?e.error&&e.error(t,n):e.success&&e.success(n)}})},C.getFile=function(e){P.verifyParamPresentJustOne(e,"docId","msg::getFile"),this.fetchFile({docId:e.docId,callback:function(t,n){t?e.error&&e.error(t,n):e.success&&e.success(n.info)}})},C.getFileList=function(e){var t=e.fromDocId,n=void 0===t?"":t,r=e.limit,o=void 0===r?10:r,i={limit:o};n&&(i.fromDocId=n),this.fetchFileList({fileListParam:i,callback:function(t,n){t?(o>30&&(t.message=t.message+"::文档条数超过限制:30"),e.error&&e.error(t,n)):e.success&&e.success(n)}})},C.sendGeo=function(e){return this.processCallback(e),e.msg=new this.message.GeoMessage(e),this.sendMsg(e)},C.sendTipMsg=function(e){return this.processCallback(e),e.msg=new this.message.TipMessage(e),this.sendMsg(e)},C.sendCustomMsg=function(e){return this.processCallback(e),e.msg=new this.message.CustomMessage(e),this.sendMsg(e)},C.sendRobotMsg=function(e){this.logger.warn("this api will be abandon");return this.processCallback(e),e.msg=new this.message.RobotMessage(e),this.sendMsg(e)},C.sendMsg=function(e,t){var n,r=this.protocol,o=e.msg,i={},a=!!e.isLocal;if(this.logger.warn("sendMsg::start: "+o.idClient),a&&(e.time&&(o.time=e.time),e.idClient&&(o.idClient=e.idClient),e.localFrom&&(n=e.localFrom+"")),e.resend&&("out"!==e.flow||"fail"!==e.status))return P.onError("只能重发发送失败的消息");e.callback.options.idClient=o.idClient,this.beforeSendMsg(e,i);var s=e.rtnMsg=this.formatReturnMsg(o,n);return t&&!this.options.keepNosSafeUrl&&s.file&&(s.file._url_safe=s.file.url,s.file.url=t,"audio"===s.type&&(s.file.mp3Url=t+(~y(t).call(t,"?")?"&":"?")+"audioTrans&type=mp3")),s.hasOwnProperty("chatroomId")&&!s.chatroomId?P.onError("聊天室未连接"):(a&&(s.status="success",s.isLocal=!0),r.storeSendMsg&&(i.promise=r.storeSendMsg(s)),e.cbaop=function(e){if(e)return 7101===e.code&&(s.isInBlackList=!0),"server"!==e.from?(s.status="fail",r.updateSendMsgError&&r.updateSendMsgError(s),s):void 0},a||(t&&!this.options.keepNosSafeUrl&&e.callback&&(e.callback.originUrl=t),e.toAccids&&e.toAccids.length&&(o.toAccids=p(e.toAccids)),i.msg=o,this.sendCmd(e.cmd,i,e.callback)),this.afterSendMsg(e),a&&v((function(){s=P.simpleClone(s),e.done(null,s)}),0),P.copy(s))},C.beforeSendMsg=function(){},C.afterSendMsg=function(){},C.formatReturnMsg=function(e,t){var n;return e=P.copy(e),this.protocol.completeMsg(e),e.status="sending",t&&(e.from=t),e=b(n=this.message).call(n,e)},C.resendMsg=function(e){return P.verifyOptions(e,"msg","msg::resendMsg"),this.trimMsgFlag(e),e.resend=!0,this._sendMsgByType(e)},C.forwardMsg=function(e){return P.verifyOptions(e,"msg","msg::forwardMsg"),this.trimMsgFlag(e),this.beforeForwardMsg(e),e.forward=!0,e.msg.idClient=P.guid(),this._sendMsgByType(e)},C.trimMsgFlag=function(e){e&&e.msg&&(e.msg=P.copy(e.msg),delete e.msg.resend,delete e.msg.forward)},C.beforeForwardMsg=function(){},C._sendMsgByType=function(e){switch(P.verifyOptions(e,"msg","msg::_sendMsgByType"),P.verifyParamValid("msg.type",e.msg.type,this.message.validTypes,"msg::_sendMsgByType"),P.merge(e,e.msg),e.type){case"text":return this.sendText(e);case"image":case"audio":case"video":case"file":return this.sendFile(e);case"geo":return this.sendGeo(e);case"custom":return this.sendCustomMsg(e);case"tip":return this.sendTipMsg(e);default:throw new A("不能发送类型为 "+e.type+" 的消息")}},C.parseRobotTemplate=function(e){if(this.logger.warn("this api will be abandon"),/<template[^>/]+\/>/.test(e))return{raw:e,json:[{type:"text",name:"",text:""}]};if(!/<template[^>/]+>/.test(e))return{raw:e,json:[{type:"text",name:"",text:e}]};var t=new I({escapeMode:!1});e=e.replace(/<template [^>]+>/,"<template>");var n=t.xml2js(e);n=n.template.LinearLayout,T(n)||(n=[n]);var r=[];return n=S(n).call(n,(function(e){e.image&&(r=x(r).call(r,i(e))),e.text&&(r=x(r).call(r,o(e))),e.link&&(r=x(r).call(r,function(e){if(e.link){var t=e.link;T(t)||(t=[t]),t=f(t).call(t,(function(e){return e.image&&(e.image=i(e)),e.text&&(e.text=o(e)),"url"===e._type?(e.type="url",e.style=e._style||"",e.target=e._target,delete e._target,delete e._style):"block"===e._type&&(e.type="block",e.style=e._style||"",e.params=e._params||"",e.target=e._target,delete e._params,delete e._target,delete e._style),delete e._type,e})),e.link=t}return e.link}(e)))})),{raw:e,json:r};function o(e){var t;return T(e.text)||(e.text=[e.text]),e.text=f(t=e.text).call(t,(function(e){return{type:"text",name:e._name,text:e.__text}})),e.text}function i(e){var t;return T(e.image)||(e.image=[e.image]),e.image=f(t=e.image).call(t,(function(e){return{type:"image",name:e._name,url:e._url}})),e.image}}},function(e,t,n){var r=n(431);e.exports=r},function(e,t,n){var r=n(16),o=n(432),i=Array.prototype;e.exports=function(e){var t=e.reverse;return e===i||r(i,e)&&t===i.reverse?o:t}},function(e,t,n){n(433);var r=n(22);e.exports=r("Array").reverse},function(e,t,n){"use strict";var r=n(2),o=n(7),i=n(72),a=o([].reverse),s=[1,2];r({target:"Array",proto:!0,forced:String(s)===String(s.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),a(this)}})},function(e,t,n){var r,o,i;!function(n,a){"use strict";o=[],void 0===(i="function"==typeof(r=function(e){return function(t){(t=t||{}).arrayAccessForm=t.arrayAccessForm||"none",t.emptyNodeForm=t.emptyNodeForm||"text",t.jsAttributeFilter=t.jsAttributeFilter,t.jsAttributeConverter=t.jsAttributeConverter,t.attributeConverters=t.attributeConverters||[],t.datetimeAccessFormPaths=t.datetimeAccessFormPaths||[],t.arrayAccessFormPaths=t.arrayAccessFormPaths||[],t.xmldomOptions=t.xmldomOptions||{},void 0===t.enableToStringFunc&&(t.enableToStringFunc=!0),void 0===t.skipEmptyTextNodesForObj&&(t.skipEmptyTextNodesForObj=!0),void 0===t.stripWhitespaces&&(t.stripWhitespaces=!0),void 0===t.useDoubleQuotes&&(t.useDoubleQuotes=!0),void 0===t.ignoreRoot&&(t.ignoreRoot=!1),void 0===t.escapeMode&&(t.escapeMode=!0),void 0===t.attributePrefix&&(t.attributePrefix="_"),void 0===t.selfClosingElements&&(t.selfClosingElements=!0),void 0===t.keepCData&&(t.keepCData=!1),void 0===t.jsDateUTC&&(t.jsDateUTC=!1),function(){function e(e){var t=String(e);return 1===t.length&&(t="0"+t),t}"function"!=typeof String.prototype.trim&&(String.prototype.trim=function(){return this.replace(/^\s+|^\n+|(\s|\n)+$/g,"")}),"function"!=typeof Date.prototype.toISOString&&(Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+e(this.getUTCMonth()+1)+"-"+e(this.getUTCDate())+"T"+e(this.getUTCHours())+":"+e(this.getUTCMinutes())+":"+e(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"})}();var n=1,r=3,o=4,i=8,a=9;function s(e){var t=e.localName;return null==t&&(t=e.baseName),null!=t&&""!==t||(t=e.nodeName),t}function c(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"):e}function u(e,n,r){switch(t.arrayAccessForm){case"property":e[n]instanceof Array?e[n+"_asArray"]=e[n]:e[n+"_asArray"]=[e[n]]}if(!(e[n]instanceof Array)&&t.arrayAccessFormPaths.length>0){for(var o=!1,i=0;i<t.arrayAccessFormPaths.length;i++){var a=t.arrayAccessFormPaths[i];if("string"==typeof a){if(a===r){o=!0;break}}else if(a instanceof RegExp){if(a.test(r)){o=!0;break}}else if("function"==typeof a&&a(n,r)){o=!0;break}}o&&(e[n]=[e[n]])}}function l(e){var t=e.split(/[-T:+Z]/g),n=new Date(t[0],t[1]-1,t[2]),r=t[5].split(".");if(n.setHours(t[3],t[4],r[0]),r.length>1&&n.setMilliseconds(r[1]),t[6]&&t[7]){var o=60*t[6]+Number(t[7]);o=0+("-"===(/\d\d-\d\d:\d\d$/.test(e)?"-":"+")?-1*o:o),n.setMinutes(n.getMinutes()-o-n.getTimezoneOffset())}else-1!==e.indexOf("Z",e.length-1)&&(n=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds())));return n}function p(e,n){for(var r={__cnt:0},o=e.childNodes,a=0;a<o.length;a++){var c=o.item(a),p=s(c);c.nodeType!==i&&(r.__cnt++,null==r[p]?(r[p]=m(c,n+"."+p),u(r,p,n+"."+p)):(r[p]instanceof Array||(r[p]=[r[p]],u(r,p,n+"."+p)),r[p][r[p].length]=m(c,n+"."+p)))}for(var f=0;f<e.attributes.length;f++){var d=e.attributes.item(f);r.__cnt++;for(var g=d.value,h=0;h<t.attributeConverters.length;h++){var y=t.attributeConverters[h];y.test.call(null,d.name,d.value)&&(g=y.convert.call(null,d.name,d.value))}r[t.attributePrefix+d.name]=g}var v=e.prefix;return v&&(r.__cnt++,r.__prefix=v),r["#text"]&&(r.__text=r["#text"],r.__text instanceof Array&&(r.__text=r.__text.join("\n")),t.escapeMode&&(r.__text=r.__text.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&amp;/g,"&")),t.stripWhitespaces&&(r.__text=r.__text.trim()),delete r["#text"],"property"===t.arrayAccessForm&&delete r["#text_asArray"],r.__text=function(e,n,r){if(t.datetimeAccessFormPaths.length>0)for(var o=r.split(".#")[0],i=0;i<t.datetimeAccessFormPaths.length;i++){var a=t.datetimeAccessFormPaths[i];if("string"==typeof a){if(a===o)return l(e)}else if(a instanceof RegExp){if(a.test(o))return l(e)}else if("function"==typeof a&&a(o))return l(e)}return e}(r.__text,0,n+".#text")),r.hasOwnProperty("#cdata-section")&&(r.__cdata=r["#cdata-section"],delete r["#cdata-section"],"property"===t.arrayAccessForm&&delete r["#cdata-section_asArray"]),1===r.__cnt&&r.__text?r=r.__text:0===r.__cnt&&"text"===t.emptyNodeForm?r="":r.__cnt>1&&void 0!==r.__text&&t.skipEmptyTextNodesForObj&&(t.stripWhitespaces&&""===r.__text||""===r.__text.trim())&&delete r.__text,delete r.__cnt,t.keepCData||r.hasOwnProperty("__text")||!r.hasOwnProperty("__cdata")||1!==Object.keys(r).length?(t.enableToStringFunc&&(r.__text||r.__cdata)&&(r.toString=function(){return(this.__text?this.__text:"")+(this.__cdata?this.__cdata:"")}),r):r.__cdata?r.__cdata:""}function m(e,i){return e.nodeType===a?function(e){for(var r={},o=e.childNodes,i=0;i<o.length;i++){var a=o.item(i);if(a.nodeType===n){var c=s(a);t.ignoreRoot?r=m(a,c):r[c]=m(a,c)}}return r}(e):e.nodeType===n?p(e,i):e.nodeType===r||e.nodeType===o?e.nodeValue:null}function f(e,n,r,o){var i="<"+(e&&e.__prefix?e.__prefix+":":"")+n;if(r)for(var a=0;a<r.length;a++){var s=r[a],u=e[s];t.escapeMode&&(u=c(u)),i+=" "+s.substr(t.attributePrefix.length)+"=",t.useDoubleQuotes?i+='"'+u+'"':i+="'"+u+"'"}return i+=o?" />":">"}function d(e,t){return"</"+(e&&e.__prefix?e.__prefix+":":"")+t+">"}function g(e,n){return"property"===t.arrayAccessForm&&(r=n.toString(),o="_asArray",-1!==r.indexOf(o,r.length-o.length))||0===n.toString().indexOf(t.attributePrefix)||0===n.toString().indexOf("__")||e[n]instanceof Function;var r,o}function h(e){var t=0;if(e instanceof Object)for(var n in e)g(e,n)||t++;return t}function y(e){var n=[];if(e instanceof Object)for(var r in e)-1===r.toString().indexOf("__")&&0===r.toString().indexOf(t.attributePrefix)&&n.push(r);return n}function v(e){var n="";return e instanceof Object?n+=function(e){var n="";return e.__cdata&&(n+="<![CDATA["+e.__cdata+"]]>"),(e.__text||"number"==typeof e.__text||"boolean"==typeof e.__text)&&(t.escapeMode?n+=c(e.__text):n+=e.__text),n}(e):null!==e&&(t.escapeMode?n+=c(e):n+=e),n}function b(e,n,r){var o="";if(t.jsAttributeFilter&&t.jsAttributeFilter.call(null,n,e))return o;if(t.jsAttributeConverter&&(e=t.jsAttributeConverter.call(null,n,e)),null!=e&&""!==e||!t.selfClosingElements)if("object"==typeof e)if("[object Array]"===Object.prototype.toString.call(e))o+=function(e,t,n){var r="";if(0===e.length)r+=f(e,t,n,!0);else for(var o=0;o<e.length;o++)r+=b(e[o],t,y(e[o]));return r}(e,n,r);else if(e instanceof Date)o+=f(e,n,r,!1),o+=t.jsDateUTC?e.toUTCString():e.toISOString(),o+=d(e,n);else{h(e)>0||"number"==typeof e.__text||"boolean"==typeof e.__text||e.__text||e.__cdata?(o+=f(e,n,r,!1),o+=T(e),o+=d(e,n)):t.selfClosingElements?o+=f(e,n,r,!0):(o+=f(e,n,r,!1),o+=d(e,n))}else o+=f(e,n,r,!1),o+=v(e),o+=d(e,n);else o+=f(e,n,r,!0);return o}function T(e){var t="";if(h(e)>0)for(var n in e)if(!g(e,n)){var r=e[n];t+=b(r,n,y(r))}return t+=v(e)}function S(n){if(void 0===n)return null;if("string"!=typeof n)return null;var r=null,o=null;if(e)o=(r=new e(t.xmldomOptions)).parseFromString(n,"text/xml");else if(window&&window.DOMParser){r=new window.DOMParser;var i=null;if(!(window.ActiveXObject||"ActiveXObject"in window))try{i=r.parseFromString("INVALID","text/xml").childNodes[0].namespaceURI}catch(e){i=null}try{o=r.parseFromString(n,"text/xml"),null!==i&&o.getElementsByTagNameNS(i,"parsererror").length>0&&(o=null)}catch(e){o=null}}else 0===n.indexOf("<?")&&(n=n.substr(n.indexOf("?>")+2)),(o=new ActiveXObject("Microsoft.XMLDOM")).async="false",o.loadXML(n);return o}this.asArray=function(e){return null==e?[]:e instanceof Array?e:[e]},this.toXmlDateTime=function(e){return e instanceof Date?e.toISOString():"number"==typeof e?new Date(e).toISOString():null},this.asDateTime=function(e){return"string"==typeof e?l(e):e},this.xml2dom=function(e){return S(e)},this.dom2js=function(e){return m(e,null)},this.js2dom=function(e){return S(this.js2xml(e))},this.xml2js=function(e){var t=S(e);return null!=t?this.dom2js(t):null},this.js2xml=function(e){return T(e)},this.getVersion=function(){return"3.1.1"}}})?r.apply(t,o):r)||(e.exports=i)}()},function(e,t,n){var r=n(17),o=n(6),i=n(25),a=n(15),s=n(0),c=n(33),u=n(55).upload,l=n(54),p=n(55).chunkUpload,m=n(55).abort,f=(n(196),s.supportFormData);function d(e){var t=this;t.options=s.copy(e),s.verifyOptions(e,"url fileName"),s.verifyParamPresentJustOne(e,"blob fileInput"),s.verifyCallback(e,"beginupload uploadprogress uploaddone"),e.fileInput&&(e.fileInput=s.verifyFileInput(e.fileInput)),e.type&&s.verifyFileType(e.type),e.timeout?s.verifyParamType("timeout",e.timeout,"number"):e.timeout=6e5,s.verifyFileUploadCallback(e),e.data={},e.params&&s.merge(e.data,e.params);var n,i,m=e.fileName,d=e.fileInput;function g(){if(d){if(!(i=e.type?s.filterFiles(d.files,e.type):r([]).call(d.files,0))||!i.length)return void e.uploaddone(c.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保文件选择节点的文件不为空, 并且请确保选择了"+e.type+"类型的文件"));e.data[m]=i[0],n=d.files[0].size}else if(e.blob){var u;if(e.data[m]=e.blob,"file"!==e.type&&e.blob.type&&-1===o(u=e.blob.type).call(u,e.type))return void e.uploaddone(c.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保选择了"+e.type+"类型的文件"));n=e.blob.size}if(e.maxSize&&n>e.maxSize)e.uploaddone(c.newFileTooLargeError("上传文件大小超过".concat(e.maxSize,"限制")));else{if(!e.commonUpload)return n>a.chunkMaxSize?void e.uploaddone(c.newFileTooLargeError("直传文件大小超过".concat(a.chunkMaxSize,"限制"))):void(t.sn=p(e,m,t,-1));if(n>a.commonMaxSize)e.uploaddone(c.newFileTooLargeError("普通上传文件大小超过".concat(a.commonMaxSize,"限制")));else if(e.s3Policy&&e.s3Policy.uploadConfig&&e.s3Policy.uploadConfig.retryPolicy){var l=e.s3Policy.uploadConfig.retryPolicy,f={accessKeyId:e.mixStoreToken.accessKeyId,secretAccessKey:e.mixStoreToken.secretAccessKey,sessionToken:e.mixStoreToken.sessionToken,region:e.mixStoreToken.region,maxRetries:l.retry},g=new(0,e.s3);g.config.update(f);var y=decodeURIComponent(e.mixStoreToken.bucket),v=decodeURIComponent(e.mixStoreToken.objectName),b=e.data[m],T=g.upload({Bucket:y,Key:v,Body:b,Metadata:{token:e.mixStoreToken.token},ContentType:b.type||"application/octet-stream"});T.on("httpUploadProgress",(function(t){var n=Math.floor(1e4*t.loaded/t.total)/100,r={docId:e.docId,total:t.total,loaded:t.loaded,percentage:n,percentageText:n+"%"};e.fileInput&&(r.fileInput=e.fileInput),e.blob&&(r.blob=e.blob),e.uploadprogress(r)})),T.send((function(n,r){if(e.docId&&(r.docId=e.docId),n){var i;e.logger.error("error:","api::s3:upload file failed",n);var a=e.providers[(o(i=e.providers).call(i,e.curProvider)+1)%e.providers.length];l.retryNext&&a?(t.addCircuitTimer(e,a),1===a&&e.getMixStoreToken(1,(function(t){e.mixStoreToken=t,h()}))):t.onError(r)}else{var s=t.options.s3Policy.cdnSchema;s=(s=s.replace("{cdnDomain}",t.options.s3Policy.dlcdn)).replace("{objectName}",r.Key),e.uploaddone(null,{size:b.size,name:b.name,url:s,ext:b.name.split(".")[1]||"unknown",_url_safe:e.mixStoreToken.shortUrl,isS3:!0})}}))}}}function h(){if(f){if(d){if(!(i=e.type?s.filterFiles(d.files,e.type):r([]).call(d.files,0))||!i.length)return void e.uploaddone(c.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保文件选择节点的文件不为空, 并且请确保选择了"+e.type+"类型的文件"));e.data[m]=i[0],n=d.files[0].size}else if(e.blob){var h;if(e.data[m]=e.blob,"file"!==e.type&&e.blob.type&&-1===o(h=e.blob.type).call(h,e.type))return void e.uploaddone(c.newWrongFileTypeError("未读取到"+e.type+"类型的文件, 请确保选择了"+e.type+"类型的文件"));n=e.blob.size}if(e.maxSize&&n>e.maxSize)return void e.uploaddone(c.newFileTooLargeError("上传文件大小超过".concat(e.maxSize,"限制")));if(!e.commonUpload)return n>a.chunkMaxSize?void e.uploaddone(c.newFileTooLargeError("直传文件大小超过".concat(a.chunkMaxSize,"限制"))):(t.onLbsUrlFail=function(t){l.startUniErrCache("nos",{user_id:e.account,action:"upload"}),l.updateUniErrCache("nos",{operation_type:"transfer",error:"lbs failed",target:t})},void(t.sn=p(e,m,t,-1)));if(n>a.commonMaxSize)return void e.uploaddone(c.newFileTooLargeError("普通上传文件大小超过".concat(a.commonMaxSize,"限制")))}else s.dataset(d,"name",m),e.data.input=d;var y={data:e.data,onaftersend:function(){e.beginupload(t)},onuploading:function(t){var n=Math.floor(1e4*t.loaded/t.total)/100,r={docId:e.docId,total:t.total,loaded:t.loaded,percentage:n,percentageText:n+"%"};e.fileInput&&(r.fileInput=e.fileInput),e.blob&&(r.blob=e.blob),e.uploadprogress(r)},onload:function(n){var r;if(e.docId&&(n.docId=e.docId),n.Error)if(e.mixEnable&&1===e.curProvider){var i=e.nosPolicy.uploadConfig.retryPolicy,a=e.providers[(o(r=e.providers).call(r,e.curProvider)+1)%e.providers.length];i.retryNext&&a?(t.addCircuitTimer(e,a),2===a&&e.getMixStoreToken(2,(function(t){e.mixStoreToken=t,g()}))):t.onError(n)}else t.onError(n);else e.uploaddone(null,n)},onerror:function(n){var r;if(e.mixEnable&&1===e.curProvider){var i,a=e.nosPolicy.uploadConfig.retryPolicy,s=e.providers[(o(i=e.providers).call(i,e.curProvider)+1)%e.providers.length];if(a.retryNext&&s)t.addCircuitTimer(e,s),2===s&&e.getMixStoreToken(2,(function(t){e.mixStoreToken=t,g()}));else try{r=n.result?JSON.parse(n.result):n,t.onError(r)}catch(r){e.uploaddone(new c(n.message,n.code),t.options)}}else try{r=n.result?JSON.parse(n.result):n,t.onError(r)}catch(r){e.uploaddone(new c(n.message,n.code),t.options)}}};f||(y.mode="iframe"),y.putFileAtEnd=!0,t.sn=u(e.url,y)}e.mixEnable&&2===e.curProvider?g():h()}d.prototype.addCircuitTimer=function(e,t){var n=this;if(e.curProvider=t,e.s3Policy&&e.nosPolicy){var r=e[1===e.curProvider?"nosPolicy":"s3Policy"].uploadConfig.retryPolicy.circuit;if(!r||0===r)return;n.circuitTimer&&clearTimeout(n.circuitTimer),n.circuitTimer=i((function(){e.curProvider=e.providers[0],n.circuitTimer=void 0}),1e3*r)}},d.prototype.onError=function(e){var t,n,r,o=this.options;n=(t=(e=e||{}).Error||e||{}).Code||t.code||"unknown",r=t.Message||t.message||"未知错误",o.uploaddone(new c(n+"("+r+")",n))},d.prototype.abort=function(){m(this.sn)},e.exports=d},function(e,t,n){var r,o=n(107),i=n(6),a=n(12),s=n(23),c=n(8),u=n(17),l=n(9),p=n(0),m=n(44).fn,f=n(201),d=n(199),g=n(33);m.transDoc=function(e){var t;p.verifyOptions(e,"fileInput done","nos::transDoc");try{var n=e.fileInput.files[0],r=["ppt","pdf","pptx","doc","docx"],c=e.fileInputName=n.name,u={ppt:1,pptx:2,pdf:3,doc:6,docx:7},l=c.substring(o(c).call(c,".")+1);if(i(r).call(r,l)<0)return void e.done(g.newNoFileError("请上传正确格式的文件【ppt, pptx, pdf, doc, docx】",{callFunc:"nos: transDoc",fileInput:e.fileInput}),e)}catch(t){return void e.done(g.newNoFileError("请上传正确的文件节点",{callFunc:"msg::previewFile",fileInput:e.fileInput}),e)}var m=a(d.genResponseBody("file")||{}).replace(/"/gi,'\\"'),f={transToken:{name:c,type:u[l],transType:"png"===e.transcode?11:10,size:n.size,body:m}};this.getNosTokenTrans({responseBody:f,nosToken:{nosScene:e.nosScene||this.nosScene,nosSurvivalTime:e.nosSurvivalTime},callback:s(t=function(t,n){t?e.done(t):(e.nosToken=n.nosToken,e.docId=n.docId,this._doPreviewFile(e))}).call(t,this)})},m.getSimpleNosToken=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.num=1,p.verifyOptions(e),this.cbAndSendCmd("getSimpleNosToken",e)},m.getNosToken=function(e){var t=e.callback,n=e.nosToken,r=e.responseBody,o={tag:n.nosScene};n.nosSurvivalTime&&n.nosSurvivalTime!==1/0&&(o.expireSec=n.nosSurvivalTime),this.sendCmd("getNosToken",{responseBody:r,nosToken:o},t)},m.getNosTokenTrans=function(e){this.sendCmd("getNosTokenTrans",e.responseBody,e.callback)},m.getMixStoreToken=function(e){this.sendCmd("getMixStoreToken",{mixTokenReq:{provider:e.curProvider,tokenCount:e.tokenCount||1,tag:"im",returnBody:e.responseBody}},e.callback)},m.packFileDownloadName=function(e){p.verifyOptions(e,"url name",!0,"","nos::packFileDownloadName");var t=e.url;return t+p.genUrlSep(t)+"download="+encodeURIComponent(e.name)},m.audioToMp3=function(e){p.verifyOptions(e,"url","nos::audioToMp3");var t=e.url;return t+p.genUrlSep(t)+"audioTrans&type=mp3"},m.removeFile=function(e){this.sendCmd("removeFile",e,e.callback)},m.fetchFile=function(e){this.sendCmd("fetchFile",e,e.callback)},m.fetchFileList=function(e){this.sendCmd("fetchFileList",e,e.callback)},m.stripImageMeta=function(e){return this.beforeProcessImage(e,"stripmeta")},m.qualityImage=function(e){return this.beforeProcessImage(e,"quality")},m.interlaceImage=function(e){return this.beforeProcessImage(e,"interlace")},m.rotateImage=function(e){return this.beforeProcessImage(e,"rotate")},m.blurImage=function(e){return this.beforeProcessImage(e,"blur")},m.cropImage=function(e){return this.beforeProcessImage(e,"crop")},m.thumbnailImage=function(e){return this.beforeProcessImage(e,"thumbnail")},m.beforeProcessImage=function(e,t){var n=p.copy(e);return n.type=t,e.ops=[n],this.processImage(e)},m.processImage=function(e){var t,n=this;p.verifyOptions(e,"url ops",!0,"","nos::processImage"),p.verifyParamType("ops",e.ops,"array","nos::processImage");var r=c(t=e.ops).call(t,(function(e){var t,r;return p.verifyOptions(e,"type",!0,"","nos::processImage"),p.verifyParamValid("type",e.type,f.validTypes,"nos::processImage"),n["gen"+u(t=e.type).call(t,0,1).toUpperCase()+u(r=e.type).call(r,1)+"Op"](e)}));n.processCallback(e),n.sendCmd("processImage",{url:e.url,imageOps:r},e.callback)},m.genStripmetaOp=function(e){return new f({type:e.type,stripmeta:e.strip?1:0})},m.genQualityOp=function(e){p.verifyOptions(e,"quality",!0,"","nos::genQualityOp"),p.verifyParamType("quality",e.quality,"number","nos::genQualityOp"),p.verifyParamMin("quality",e.quality,0,"nos::genQualityOp"),p.verifyParamMax("quality",e.quality,100,"nos::genQualityOp");var t=Math.round(e.quality);return new f({type:e.type,qualityQuality:t})},m.genInterlaceOp=function(e){return new f({type:e.type})},m.genRotateOp=function(e){for(p.verifyOptions(e,"angle",!0,"","nos::genRotateOp"),p.verifyParamType("angle",e.angle,"number","nos::genRotateOp");e.angle<0;)e.angle=e.angle+360;e.angle=e.angle%360;var t=Math.round(e.angle);return new f({type:e.type,rotateAngle:t})},m.genBlurOp=function(e){p.verifyOptions(e,"radius sigma","nos::genBlurOp"),p.verifyParamType("radius",e.radius,"number","nos::genBlurOp"),p.verifyParamMin("radius",e.radius,1,"nos::genBlurOp"),p.verifyParamMax("radius",e.radius,50,"nos::genBlurOp"),p.verifyParamType("sigma",e.sigma,"number","nos::genBlurOp"),p.verifyParamMin("sigma",e.sigma,0,"nos::genBlurOp");var t=Math.round(e.radius),n=Math.round(e.sigma);return new f({type:e.type,blurRadius:t,blurSigma:n})},m.genCropOp=function(e){p.verifyOptions(e,"x y width height","nos::genCropOp"),p.verifyParamType("x",e.x,"number","nos::genCropOp"),p.verifyParamMin("x",e.x,0,"nos::genCropOp"),p.verifyParamType("y",e.y,"number","nos::genCropOp"),p.verifyParamMin("y",e.y,0,"nos::genCropOp"),p.verifyParamType("width",e.width,"number","nos::genCropOp"),p.verifyParamMin("width",e.width,0,"nos::genCropOp"),p.verifyParamType("height",e.height,"number","nos::genCropOp"),p.verifyParamMin("height",e.height,0,"nos::genCropOp");var t=Math.round(e.x),n=Math.round(e.y),r=Math.round(e.width),o=Math.round(e.height);return new f({type:e.type,cropX:t,cropY:n,cropWidth:r,cropHeight:o})},m.genThumbnailOp=(r={cover:"z",contain:"x",crop:"y"},function(e){p.verifyOptions(e,"mode","nos::genThumbnailOp"),p.verifyParamValid("mode",e.mode,l(r),"nos::genThumbnailOp"),"contain"===e.mode?p.verifyParamAtLeastPresentOne(e,"width height","nos::genThumbnailOp"):p.verifyOptions(e,"width height","nos::genThumbnailOp"),p.undef(e.width)&&(e.width=0),p.undef(e.height)&&(e.height=0),p.verifyParamType("width",e.width,"number","nos::genThumbnailOp"),p.verifyParamMin("width",e.width,0,"nos::genThumbnailOp"),p.verifyParamType("height",e.height,"number","nos::genThumbnailOp"),p.verifyParamMin("height",e.height,0,"nos::genThumbnailOp");var t=Math.round(e.width),n=Math.round(e.height),o=new f({type:e.type,thumbnailMode:r[e.mode],thumbnailWidth:t,thumbnailHeight:n});if("crop"===e.mode&&p.notundef(e.axis)){p.undef(e.axis.x)&&(e.axis.x=5),p.undef(e.axis.y)&&(e.axis.y=5),p.verifyParamMin("axis.x",e.axis.x,0,"nos::genThumbnailOp"),p.verifyParamMax("axis.x",e.axis.x,10,"nos::genThumbnailOp"),p.verifyParamMin("axis.y",e.axis.y,0,"nos::genThumbnailOp"),p.verifyParamMax("axis.y",e.axis.y,10,"nos::genThumbnailOp");var i=Math.round(e.axis.x),a=Math.round(e.axis.y);o.thumbnailAxisX=i,o.thumbnailAxisY=a}return p.notundef(e.enlarge)&&(p.verifyParamType("enlarge",e.enlarge,"boolean","nos::genThumbnailOp"),e.enlarge&&(o.thumbnailEnlarge=1)),o.thumbnailToStatic=this.options.thumbnailToStatic?1:0,o}),m.getNosOriginUrl=function(e){var t;p.verifyOptions(e,"safeShortUrl",!0,"","nos::getNosOriginUrl"),p.verifyParamType("safeShortUrl",e.safeShortUrl,"string","nos::getNosOriginUrl"),/^http(s)?:/.test(e.safeShortUrl)&&~i(t=e.safeShortUrl).call(t,"im_url=1")?(this.processCallback(e),this.sendCmd("getNosOriginUrl",{nosFileUrlTag:{safeUrl:e.safeShortUrl}},e.callback)):e.done(new g("参数 “safeShortUrl” 内容非文件安全短链",{callFunc:"nos: getNosOriginUrl"}),e)}},function(e,t,n){var r,o=n(10),i=n(6),a=n(9),s=n(28),c=n(82),u=c.genUrlSep,l=c.url2object,p=c.object2url,m=n(0),f=n(44).fn;f.viewImageSync=function(e){var t=this.options;m.verifyOptions(e,"url","nos::viewImageSync");var n=e.url,r=l(n),a=r.protocol,c=r.hostname,u=r.path,f=r.query;if("boolean"==typeof e.strip&&(f.stripmeta=e.strip?1:0),"number"==typeof e.quality&&(m.verifyParamMin("quality",e.quality,0,"nos::viewImageSync"),m.verifyParamMax("quality",e.quality,100,"nos::viewImageSync"),f.quality=Math.round(e.quality)),"boolean"==typeof e.interlace&&(f.interlace=e.interlace?1:0),"number"==typeof e.rotate&&(f.rotate=Math.round(e.rotate)),"object"===s(e.thumbnail)){var d=e.thumbnail.mode||"crop",g=e.thumbnail.width,h=e.thumbnail.height;if(g>=0&&h>=0&&g<4096&&h<4096&&(g>0||h>0)){var y,v;switch(d){case"crop":d="y";break;case"contain":d="x";break;case"cover":d="z";break;default:d="x"}f.thumbnail=o(y=o(v="".concat(g)).call(v,d)).call(y,h)}}if(t.downloadUrl){var b=l(e.url),T=t.downloadUrl,S=b.path,x=i(S).call(S,"/");if(-1!==x){var k=S.substring(0,x),M=S.substring(x+1);T=T.replace("{bucket}",k).replace("{object}",M)}var w=l(T);return p({protocol:w.protocol,hostname:w.hostname,path:w.path,query:m.merge(w.query,f)})}return p({protocol:a,hostname:c,path:u,query:f})},f.viewImageStripMeta=function(e){m.verifyOptions(e,"url strip","nos::viewImageStripMeta"),m.verifyParamType("strip",e.strip,"boolean","nos::viewImageStripMeta");var t="stripmeta="+(e.strip?1:0),n=u(e.url);return e.url+n+t},f.viewImageQuality=function(e){m.verifyOptions(e,"url quality","nos::viewImageQuality"),m.verifyParamType("quality",e.quality,"number","nos::viewImageQuality"),m.verifyParamMin("quality",e.quality,0,"nos::viewImageQuality"),m.verifyParamMax("quality",e.quality,100,"nos::viewImageQuality");var t="quality="+Math.round(e.quality),n=u(e.url);return e.url+n+t},f.viewImageInterlace=function(e){m.verifyOptions(e,"url","nos::viewImageInterlace");var t=u(e.url);return e.url+t+"interlace=1"},f.viewImageRotate=function(e){for(m.verifyOptions(e,"url angle","nos::viewImageRotate"),m.verifyParamType("angle",e.angle,"number","nos::viewImageRotate");e.angle<0;)e.angle=e.angle+360;e.angle=e.angle%360;var t="rotate="+Math.round(e.angle),n=u(e.url);return e.url+n+t},f.viewImageBlur=function(e){m.verifyOptions(e,"url radius sigma","nos::viewImageBlur"),m.verifyParamType("radius",e.radius,"number","nos::viewImageBlur"),m.verifyParamMin("radius",e.radius,1,"nos::viewImageBlur"),m.verifyParamMax("radius",e.radius,50,"nos::viewImageBlur"),m.verifyParamType("sigma",e.sigma,"number","nos::viewImageBlur"),m.verifyParamMin("sigma",e.sigma,0,"nos::viewImageBlur");var t="blur="+Math.round(e.radius)+"x"+Math.round(e.sigma),n=u(e.url);return e.url+n+t},f.viewImageCrop=function(e){m.verifyOptions(e,"url x y width height","nos::viewImageCrop"),m.verifyParamType("x",e.x,"number","nos::viewImageCrop"),m.verifyParamMin("x",e.x,0,"nos::viewImageCrop"),m.verifyParamType("y",e.y,"number","nos::viewImageCrop"),m.verifyParamMin("y",e.y,0,"nos::viewImageCrop"),m.verifyParamType("width",e.width,"number","nos::viewImageCrop"),m.verifyParamMin("width",e.width,0,"nos::viewImageCrop"),m.verifyParamType("height",e.height,"number","nos::viewImageCrop"),m.verifyParamMin("height",e.height,0,"nos::viewImageCrop");var t="crop="+Math.round(e.x)+"_"+Math.round(e.y)+"_"+Math.round(e.width)+"_"+Math.round(e.height),n=u(e.url);return e.url+n+t},f.viewImageThumbnail=(r={cover:"z",contain:"x",crop:"y"},function(e){m.verifyOptions(e,"url mode","nos::viewImageThumbnail"),m.verifyParamValid("mode",e.mode,a(r),"nos::viewImageThumbnail"),"contain"===e.mode?m.verifyParamAtLeastPresentOne(e,"width height","nos::viewImageThumbnail"):m.verifyOptions(e,"width height","nos::viewImageThumbnail"),m.undef(e.width)&&(e.width=0),m.undef(e.height)&&(e.height=0),m.verifyParamType("width",e.width,"number","nos::viewImageThumbnail"),m.verifyParamMin("width",e.width,0,"nos::viewImageThumbnail"),m.verifyParamType("height",e.height,"number","nos::viewImageThumbnail"),m.verifyParamMin("height",e.height,0,"nos::viewImageThumbnail");var t=Math.round(e.width),n=Math.round(e.height),o="thumbnail="+t+r[e.mode]+n;"crop"===e.mode&&m.notundef(e.axis)&&(m.undef(e.axis.x)&&(e.axis.x=5),m.undef(e.axis.y)&&(e.axis.y=5),m.verifyParamMin("axis.x",e.axis.x,0,"nos::viewImageThumbnail"),m.verifyParamMax("axis.x",e.axis.x,10,"nos::viewImageThumbnail"),m.verifyParamMin("axis.y",e.axis.y,0,"nos::viewImageThumbnail"),m.verifyParamMax("axis.y",e.axis.y,10,"nos::viewImageThumbnail"),o=o+"&axis="+Math.round(e.axis.x)+"_"+Math.round(e.axis.y)),m.notundef(e.enlarge)&&(m.verifyParamType("enlarge",e.enlarge,"boolean","nos::viewImageThumbnail"),e.enlarge&&(o+="&enlarge=1"));var i=u(e.url);return e.url+i+o})},function(e,t,n){var r=n(439);e.exports=r},function(e,t,n){var r=n(16),o=n(440),i=n(442),a=Array.prototype,s=String.prototype;e.exports=function(e){var t=e.includes;return e===a||r(a,e)&&t===a.includes?o:"string"==typeof e||e===s||r(s,e)&&t===s.includes?i:t}},function(e,t,n){n(441);var r=n(22);e.exports=r("Array").includes},function(e,t,n){"use strict";var r=n(2),o=n(128).includes,i=n(147);r({target:"Array",proto:!0},{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i("includes")},function(e,t,n){n(443);var r=n(22);e.exports=r("String").includes},function(e,t,n){"use strict";var r=n(2),o=n(7),i=n(444),a=n(84),s=n(42),c=n(446),u=o("".indexOf);r({target:"String",proto:!0,forced:!c("includes")},{includes:function(e){return!!~u(s(a(this)),s(i(e)),arguments.length>1?arguments[1]:void 0)}})},function(e,t,n){var r=n(1),o=n(445),i=r.TypeError;e.exports=function(e){if(o(e))throw i("The method doesn't accept regular expressions");return e}},function(e,t,n){var r=n(27),o=n(68),i=n(18)("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[i])?!!t:"RegExp"==o(e))}},function(e,t,n){var r=n(18)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[r]=!1,"/./"[e](t)}catch(e){}}return!1}},function(e,t,n){var r=n(6),o=n(214),i=n(28),a=n(0),s=n(44).fn;function c(e,t,n,o){var i=!1,a="";if(1===n?r(e).call(e,t)>=0&&(i=!0,a=t):2===n&&(a=new RegExp(t,"g")).test(e)&&(i=!0),i&&""!==a)switch(o){case 1:return e.replace(a,"**");case 2:return{code:2};case 3:return{code:3}}return e}function u(e,t){for(var n=t.match,r=t.operate,a=e,s=0;s<o(t).length;s++){var u=o(t)[s],l=u.match||n,p=u.operate||r;try{if(a=c(a,u.key,l,p),"object"===i(a))return a}catch(e){this.logger.warn("misc::filterContent: js cannot parse this regexp ",e)}}return a}s.uploadSdkLogUrl=function(e){return a.verifyOptions(e,"url","misc::uploadSdkLogUrl"),this.cbAndSendCmd("uploadSdkLogUrl",e)},s.getClientAntispamLexicon=function(e){var t=this,n=(e=e||{}).done;n instanceof Function||(n=function(){}),e={clientAntispam:{version:0}};var r=this;return this.protocol.sendCmd("getClientAntispam",e,(function(e,o,i){e?(r.protocol.logger.error("misc::getClientAntispamLexicon:",e),n.call(t,e,{})):(r.antispamLexicon=i.clientAntispam||{},n.call(t,null,i))}))},s.filterClientAntispam=function(e){var t=e.content,n=e.antispamLexicon;if(!t)return{code:404,errmsg:"待反垃圾文本content不存在"};n=n||this.antispamLexicon||{};var r=this.antispamLexicon&&this.antispamLexicon.thesaurus;if(!r)return{code:404,errmsg:"没有反垃圾词库或者词库格式不合法"};try{r=JSON.parse(r).thesaurus}catch(e){return this.protocol.logger.error("misc::filterClientAntispam: parse thesaurus error"),{code:500,errmsg:"反垃圾词库格式不合法"}}for(var o=t,a=0;a<r.length;a++)if(o=u.call(this,o,r[a]),"object"===i(o)){if(2===o.code)return{code:200,type:2,errmsg:"建议拒绝发送",content:t,result:""};if(3===o.code)return{code:200,type:3,errmsg:"建议服务器处理反垃圾，发消息带上字段clientAntiSpam",content:t,result:t}}return o===t?{code:200,type:0,errmsg:"",content:t,result:o}:{code:200,type:1,errmsg:"已对特殊字符做了过滤",content:t,result:o}},s.getServerTime=function(e){this.processCallback(e),this.sendCmd("getServerTime",{},e.callback)},s.getNosAccessToken=function(e){a.verifyOptions(e,"url","misc::getNosAccessToken"),this.processCallback(e);var t={url:e.url};e.userAgent&&(t.userAgent=e.userAgentv),e.ext&&(t.ext=e.ext),this.sendCmd("getNosAccessToken",{nosAccessTokenTag:t},(function(t,n,o){var i=o&&o.nosAccessTokenTag&&o.nosAccessTokenTag.token,a=e.url,s=i?{token:i,resUrl:r(a).call(a,"?")?a+"&token="+i:a+"?token="+i}:{};e.done(t,s)}))},s.deleteNosAccessToken=function(e){a.verifyOptions(e,"token","misc::deleteNosAccessToken"),this.processCallback(e),this.sendCmd("deleteNosAccessToken",{nosAccessTokenTag:{token:e.token}},e.callback)}},function(e,t,n){n(59);var r=n(47),o=n(26),i=n(16),a=n(449),s=Array.prototype,c={DOMTokenList:!0,NodeList:!0};e.exports=function(e){var t=e.keys;return e===s||i(s,e)&&t===s.keys||o(c,r(e))?a:t}},function(e,t,n){var r=n(450);e.exports=r},function(e,t,n){n(76),n(89);var r=n(22);e.exports=r("Array").keys},function(e,t,n){var r=n(10),o=n(44).fn,i=n(0),a=n(55),s=n(15),c=n(53);(c=c||{}).name=c.name||"",c.version=c.version||"",o.reportLogs=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this,o=n.options,u=s.ntServerAddress;if(u){var l=s.info;t=i.merge(t,{appkey:o.appKey,uid:o.account,os:"web",session:n.protocol.sdkSession||"",ver:l.sdkVersion,type:n.subType,platform:r(e="".concat(c.name.toLowerCase())).call(e,c.version.replace(/(\.\d+)+$/,""))});var p=u+i.genUrlSep(u),m=[];for(var f in t){var d;m.push(r(d="".concat(f,"=")).call(d,t[f]))}p+=m.join("&"),a(p,{proxyUrl:i.url2origin(p)+"/lbs/res/cors/nej_proxy_frame.html",timeout:s.xhrTimeout,onload:function(){},onerror:function(e){n.logger.error("report::ajax report error",e)}})}}},function(e,t,n){var r=n(28),o=n(4),i=n(8),a=n(12),s=n(44).fn,c=n(0),u=n(142),l=n(202);s.signalingCreate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type,n=e.channelName,r=e.ext;return c.verifyOptions(e,"type","api::signalling"),this.sendCmdUsePromise("signalingCreate",{avSignalTag:{type:t,channelName:n,ext:r}}).then((function(e){var t=e.avSignalTag;return o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingDelay=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return c.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingDelay",{avSignalTag:e}).then((function(e){var t=e.avSignalTag;return o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingClose=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return c.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingClose",{avSignalTag:c.merge(e,{isSave:!0===t?1:0})}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingJoin=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return c.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingJoin",{avSignalTag:c.merge(e,{isSave:!0===t?1:0})}).then((function(e){var t,n=e.avSignalTag,r=n.members;"string"==typeof n.members&&(r=i(t=JSON.parse(n.members)).call(t,(function(e){return l.parseAvSignalMember(e)})));return n.members=r,o.resolve(n)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingLeave=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.offlineEnabled;return c.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingLeave",{avSignalTag:c.merge(e,{isSave:!0===t?1:0})}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingGetChannelInfo=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.channelName;return c.verifyOptions(e,"channelName","api::signalling"),this.sendCmdUsePromise("signalingGetChannelInfo",{avSignalTag:{channelName:t}}).then((function(e){var t,n=e.avSignalTag,r=n.members;"string"==typeof n.members&&(r=i(t=JSON.parse(n.members)).call(t,(function(e){return l.parseAvSignalMember(e)})));return n.members=r,o.resolve(n)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingInvite=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.account,n=e.offlineEnabled,i=e.pushInfo,s=void 0===i?{}:i;c.verifyOptions(e,"channelId requestId account","api::signalling"),"object"===r(s.pushPayload)&&(s.pushPayload=a(s.pushPayload));var u=c.merge(e,s,{to:t,isSave:!0===n?1:0,needPush:!0===s.needPush?1:0,needBadge:!1===s.needBadge?0:1});return this.sendCmdUsePromise("signalingInvite",{avSignalTag:u}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,t.needBadge=1===t.needBadge,t.needPush=1===t.needPush,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingCancel=function(e){var t=e.account,n=e.offlineEnabled;return c.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingCancel",{avSignalTag:c.merge(e,{to:t,isSave:!0===n?1:0})}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingCreateAndJoin=function(e){var t=this,n=e.channelName,r=e.uid,i=void 0===r?0:r,a=e.offlineEnabled,s=void 0===a||a,u=e.attachExt,l=void 0===u?"":u;return this.signalingCreate(e).catch((function(e){return 10405===e.code?(t.logger.warn("api::avSignal:signalingCall room already exists:",e),t.signalingGetChannelInfo({channelName:n})):o.reject(e)})).then((function(e){var n={channelId:e.channelId,offlineEnabled:s,attachExt:l};return i&&c.merge(n,{uid:i}),t.signalingJoin(n)}))},s.signalingCall=function(e){var t=this,n=e.account,r=e.offlineEnabled,o=e.requestId;c.verifyOptions(e,"type requestId account","api::signalling");var i="";return this.signalingCreateAndJoin(e).then((function(a){i=a.channelId||i,t.logger.info("api::avSignal:signalingCall join:",i);var s={channelId:i,account:n,requestId:o,offlineEnabled:r,attachExt:e.attachExt||"",pushInfo:e.pushInfo||{}};return t.signalingInvite(s)}))},s.signalingReject=function(e){var t=e.account,n=e.offlineEnabled;return c.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingReject",{avSignalTag:c.merge(e,{to:t,isSave:!0===n?1:0})}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingAccept=function(e){var t=this,n=e.account,r=e.offlineEnabled;return c.verifyOptions(e,"channelId requestId account","api::signalling"),this.sendCmdUsePromise("signalingAccept",{avSignalTag:c.merge(e,{to:n,isSave:!0===r?1:0})}).then((function(e){var t=e.avSignalTag;return t.offlineEnabled=1===t.isSave,delete t.isSave,o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))})).then((function(n){if(e.autoJoin){var r={channelId:e.channelId,offlineEnabled:e.offlineEnabled,attachExt:e.joinAttachExt,uid:e.uid};return t.signalingJoin(r)}return n}))},s.signalingControl=function(e){var t=e.account;return c.verifyOptions(e,"channelId","api::signalling"),this.sendCmdUsePromise("signalingControl",{avSignalTag:c.merge(e,t?{to:t}:{})}).then((function(e){var t=e.avSignalTag;return o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingSync=function(){return this.sendCmdUsePromise("sync",{sync:{avSignal:0}}).then((function(e){var t=e.avSignalTag;return o.resolve(t)})).catch((function(e){return o.reject(l.parseAvSignalError(e))}))},s.signalingMarkMsgRead=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};c.verifyOptions(e,"msgid","api::signalling");var t,n=u.idMap.avSignal;return t="string"==typeof e.msgid?[e.msgid]:e.msgid,this.sendCmd("batchMarkRead",{sid:n.id,cid:n.signalingNotify,ids:t})}},function(e,t,n){var r=n(454);e.exports=r},function(e,t,n){n(455);var r=n(13);e.exports=r.Object.assign},function(e,t,n){var r=n(2),o=n(456);r({target:"Object",stat:!0,forced:Object.assign!==o},{assign:o})},function(e,t,n){"use strict";var r=n(24),o=n(7),i=n(31),a=n(11),s=n(92),c=n(137),u=n(112),l=n(37),p=n(95),m=Object.assign,f=Object.defineProperty,d=o([].concat);e.exports=!m||a((function(){if(r&&1!==m({b:1},m(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol();return e[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(e){t[e]=e})),7!=m({},e)[n]||"abcdefghijklmnopqrst"!=s(m({},t)).join("")}))?function(e,t){for(var n=l(e),o=arguments.length,a=1,m=c.f,f=u.f;o>a;)for(var g,h=p(arguments[a++]),y=m?d(s(h),m(h)):s(h),v=y.length,b=0;v>b;)g=y[b++],r&&!i(f,h,g)||(n[g]=h[g]);return n}:m},function(e,t,n){var r=n(458);e.exports=r},function(e,t,n){n(459);var r=n(13);e.exports=r.Date.now},function(e,t,n){var r=n(2),o=n(1),i=n(7),a=o.Date,s=i(a.prototype.getTime);r({target:"Date",stat:!0},{now:function(){return s(new a)}})},function(e,t,n){var r=n(10),o=n(12),i=n(23),a=n(25),s=n(9),c=n(4),u=n(65).fn,l=n(33),p=n(461),m=n(203),f=n(15),d=n(54),g=n(0);function h(e){this.logger.log("offlineListener enter"),this.forceDisconnect(),this.options.ondisconnect&&this.options.ondisconnect({callFunc:"link::offlineListener",message:"offlineListener disconnect"}),this.onDisconnect("link::offlineListener")}function y(){var e=this;this.logger.log("onlineListener start"),this&&this.isConnected()&&!this.connecting?(this.stopHeartbeat(),this.sendCmd("heartbeat",null,(function(t){if(t){e.logger.info("onlineListener heartbeat detect error",t);try{e.forceDisconnect(),e.onDisconnect("link::onHeartbeat")}catch(t){e.logger.info("onlineListener heartbeat websocket.onclose",t)}}else e.logger.log("onlineListener heartbeat detect success")}))):this.logger.log("onlineListener disconnected or connecting",this&&this.isConnected(),this.connecting)}u.initConnect=function(){this.socket=null,this.retryCount=0,this.connecting=!1,this.shouldReconnect=!0,this.hasNotifyDisconnected=!1,this.doLogout=!1,d.initUniErrReport({appKey:this.options.appKey})},u.resetConnect=function(){var e=this.options;g.notundef(e.needReconnect)?(g.verifyParamType("needReconnect",e.needReconnect,"boolean","link::resetConnect"),this.needReconnect=e.needReconnect):this.needReconnect=!0,this.logger.info("link::resetConnect: needReconnect ".concat(this.needReconnect)),g.notundef(e.reconnectionAttempts)&&g.verifyParamType("reconnectionAttempts",e.reconnectionAttempts,"number","link::resetConnect"),g.notundef(e.noCacheLinkUrl)&&g.verifyParamType("noCacheLinkUrl",e.noCacheLinkUrl,"boolean","link::resetConnect"),this.reconnectionAttempts="number"==typeof e.reconnectionAttempts?e.reconnectionAttempts:1/0,this.backoff=new p({min:f.reconnectionDelay,max:f.reconnectionDelayMax,jitter:f.reconnectionJitter})},u.connect=function(){if(clearTimeout(this.connectTimer),this.isConnected())this.logger.warn("link::connect: already connected");else if(this.connecting)this.logger.warn("link::connect: already connecting");else if(d.restore(),this.autoconnect||d.startUniErrCache("login",{user_id:this.options.account,action:"manual_login"}),this.connecting=!0,this.hasNotifyDisconnected=!1,this.shouldReconnect=!0,this.socket&&this.forceDisconnect(),this.logger.info("link::connect: connect to new socket, autoconnect is "+this.autoconnect),"string"==typeof this.options.socketUrl)this.connectToUrl(this.options.socketUrl);else{var e=this.getNextSocketUrl();e&&!this.options.noCacheLinkUrl?this.connectToUrl(e):this.refreshSocketUrl()}},u.getNextSocketUrl=function(){return this.socketUrls.shift()},u.isConnected=function(){return!!this.socket&&!!this.socket.socket&&this.socket.socket.connected},u.connectToUrl=function(e){var t,n,a,s,c,u,l=this;if(e=e||"",l.url=e,l.logger.info("link::connectToUrl: ".concat(e)),"undefined"==typeof window){var p=g.getGlobal(),d=e.split(":");p&&!p.location&&d.length>1&&(p.location={protocol:d.shift(),port:d.pop(),hostname:d.join("")}),this.options.transports=["websocket"]}var h=this.options.transports||["websocket","xhr-polling"];l.socket=m.connect(e,{transports:h,reconnect:!1,"force new connection":!0,"connect timeout":f.connectTimeout}),l.logger.info(r(t="link::connectToUrl: socket url: ".concat(e,", transports: ")).call(t,o(h))),l.handshakeUrl=e,l.socket.on("connect",i(n=l.onConnect).call(n,l)),l.socket.on("handshake_failed",i(a=l.onHandshakeFailed).call(a,l)),l.socket.on("connect_failed",i(s=l.onConnectFailed).call(s,l)),l.socket.on("error",i(c=l.onError).call(c,l)),l.socket.on("message",i(u=l.onMessage).call(u,l)),l.socket.on("disconnect",(function(t){l.logger.warn("link::connectToUrl: socket url: ".concat(e,", disconnected")),l.hasLogin=!1,l.doLogout?l.logout():l.onDisconnect("link::socketDisconnect")}))},u.disconnect=function(e){var t=this;function n(n){t.logger.info("link::disconnect: socket finally closed, ",n),clearTimeout(t.disconnectCallbackTimer),t.msgAckBufferManager&&t.msgAckBufferManager.clear(),e(n)}e instanceof Function||(e=function(){}),clearTimeout(t.connectTimer),t.disconnectCallbackTimer=a((function(){e.call(t,"mark disconnected due to timeout")}),1e4),t.mixStorePolicy=null,t.appGrayConfig=null,clearTimeout(t.mixStorePolicyClock),this.onlineListener&&"undefined"!=typeof window&&g.isFunction(window.removeEventListener)&&(window.removeEventListener("online",this.onlineListener),window.removeEventListener("offline",this.offlineListener)),t.socket&&t.socket.socket&&t.socket.socket.transport?t.socket.socket.transport.onDisconnectDone=function(e){n(e)}:n(null),t.isConnected()?(t.logger.warn("link::disconnect: start disconnecting"),t.logout()):t.connecting?(t.logger.warn("link::disconnect: abort connecting"),t.disconnectSocket()):(t.logger.warn("link::disconnect: start otherwise"),t.connecting=!1,t.shouldReconnect=!1,t.socket=null,t.autoconnect=!1,t.options.ondisconnect({callFunc:"link::disconnect",message:"manually disconnect status"}))},u.onConnect=function(){this.backoff&&this.backoff.reset(),this.retryCount=0,this.connecting=!1,this.shouldReconnect=!0,this.hasNotifyDisconnected=!1,this.logger.info("link::onConnect: socket onconnected, start login"),d.updateUniSuccCache("login",{operation_type:"TCP",target:this.url}),this.login(),this.api.reportLogs({event:"ws_connected"})},u.onHandshakeFailed=function(){this.logger.warn("link::onHandshakeFailed: shandshake failed"),this.api.reportLogs({event:"ws_handshake_failed"}),d.updateUniErrCache("login",{operation_type:"TCP",error:"ws_handshake_failed",target:this.handshakeUrl}),this.onDisconnect("link::onHandshakeFailed")},u.onConnectFailed=function(){this.api.reportLogs({event:"ws_connect_failed"}),this.onDisconnect("link::onConnectFailed")},u.onError=function(){var e=arguments[0];if(e){if(d.updateUniErrCache("login",{operation_type:"TCP",error:"connect_timeout"}),this.api.reportLogs({event:"connect_timeout"}),void 0!==e.x5ImgDecodeStatus)return;if("[object Object]"===Object.prototype.toString.call(e)&&s(e).length<=0)return;this.onMiscError("连接错误",new l(e,"LINK_ERROR",{callFunc:"link::onError"}))}this.connecting=!1},u.onDisconnect=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=this;this.logger.warn("socket::onDisconnect: "+e),t.connecting=!1,t.markAllCallbackInvalid(l.newNetworkError({callFunc:e})),t.msgAckBufferManager&&t.msgAckBufferManager.clear(),t.stopHeartbeat(),t.reconnect()},u.willReconnect=function(){return this.shouldReconnect&&this.needReconnect&&this.retryCount<this.reconnectionAttempts},u.reconnect=function(){this.willReconnect()?(d.startUniErrCache("login",{user_id:this.options.account,action:"auto_login"}),this.socket&&(this.logger.info("link::reconnect: try to force disconnect"),this.forceDisconnect()),this.doReconnect()):this.notifyDisconnect()},u.doReconnect=function(){var e,t=this;this.logger.warn("doReconnect"),t.socket=null,t.retryCount++,t.hasLogin=!1;var n=t.backoff.duration();t.logger.info(r(e="link::reconnect: will retry after ".concat(n,"ms, retryCount ")).call(e,t.retryCount));var o=t.options.onwillreconnect({retryCount:t.retryCount,duration:n});clearTimeout(t.connectTimer),t.connectTimer=a((function(){1==+t.options.authType&&o instanceof c?(t.logger.info("link::reconnect: wait onwillreconnect promise"),o.finally((function(){return t.connect()}))):t.connect()}),n)},u.notifyConnectError=function(e){var t=l.newConnectError({message:e,callFunc:"link::notifyConnectError"});this.logger.error("link::notifyConnectError:",t),this.options.onerror(t)},u.notifyDisconnect=function(e){this.hasNotifyDisconnected?this.logger.warn("notifyDisconnect:: already notified"):(this.hasNotifyDisconnected=!0,this.disconnectSocket(),this.forceDisconnect(),(e=e||new l).retryCount=this.retryCount,e.willReconnect=this.willReconnect(),this.backoff&&this.backoff.reset(),this.retryCount=0,this.autoconnect=!1,this.logger.info("link::notifyDisconnect: ondisconnected",e),this.options.ondisconnect(e),d.concludeUniErrCache("login",1),this.onWbNotifyHangup instanceof Function&&this.onWbNotifyHangup({content:{account:this.account,channelId:null,timetag:+Date()}}))},u.disconnectSocket=function(){if(this.isConnected()||this.connecting){this.connecting=!1,this.shouldReconnect=!1;try{this.socket.disconnect()}catch(e){this.socket&&"function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),this.logger.info("link::disconnectSocket: disconnect failed, error ",e)}}},u.initOnlineListener=function(e){this.needReconnect&&this.options&&this.options.quickReconnect?"undefined"!=typeof window&&g.isFunction(window.addEventListener)?this.onlineListener||(this.onlineListener=i(y).call(y,this),this.offlineListener=i(h).call(h,this),this.logger.info("initOnlineListener success"),window.addEventListener("online",this.onlineListener),window.addEventListener("offline",this.offlineListener)):this.logger.warn("initOnlineListener no window.addEventListener"):this.logger.warn("initOnlineListener no quickReconnect")},u.forceDisconnect=function(){var e=this.socket||{};this.socket=null;try{"function"==typeof e.removeAllListeners&&e.removeAllListeners(),"function"==typeof e.disconnect&&e.disconnect()}catch(e){this.logger.error("forceDisconnect:: oldSocket error",e||e.message)}this.hasLogin=!1}},function(e,t){function n(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}e.exports=n,n.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(e){this.ms=e},n.prototype.setMax=function(e){this.max=e},n.prototype.setJitter=function(e){this.jitter=e}},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){var r=n(9),o=n(60),i=n(19),a=n(61),s=n(3),c=n(62),u=n(63),l=n(50),p=n(64),m=n(4),f=n(23),d=n(25);function g(e,t){var n=r(e);if(o){var s=o(e);t&&(s=i(s).call(s,(function(t){return a(e,t).enumerable}))),n.push.apply(n,s)}return n}var h,y=n(65).fn,v=n(33),b=n(53),T=n(144),S=n(41),x=n(15),k=n(54),M=n(0),w=M.notundef;y.login=function(){this.doLogin()},y.doLogin=function(){var e=this,t=this;m.resolve().then((function(){return t.assembleLogin()})).then((function(n){var r,o=e.socket&&e.socket.socket&&e.socket.socket.sessionid;if(o){if(e.socketIds||(e.socketIds={}),e.socketIds[o])return void e.logger.warn("onConnect::repeat login",o);e.socketIds[o]=!0}else e.logger.warn("onConnect:: no socketId ",e.socket&&e.socket.socket);e.logger.warn("link::doLogin: "+o+" appLogin is "+n.login.appLogin),t.sendCmd("login",function(e){for(var t=1;t<arguments.length;t++){var n,r,o=null!=arguments[t]?arguments[t]:{};t%2?s(n=g(Object(o),!0)).call(n,(function(t){p(e,t,o[t])})):c?u(e,c(o)):s(r=g(Object(o))).call(r,(function(t){l(e,t,a(o,t))}))}return e}({},n),f(r=t.onLogin).call(r,t))}))},y.genSessionKey=(h={},function(){var e=this.name;return h[e]=h[e]||M.guid()}),y.assembleIMLogin=function(){var e=this.options,t=e.account;this.sdkSession=this.genSessionKey();var n={appLogin:this.autoconnect?0:1,appKey:e.appKey,account:t,token:e.token,sdkVersion:x.info.sdkVersion,sdkHumanVersion:x.info.sdkHumanVersion,protocolVersion:x.info.protocolVersion,os:"[object Object]"===b.os.toString()?b.os.family:b.os.toString(),browser:b.name+" "+b.version,clientType:x.CLIENTTYPE||16,session:this.sdkSession,deviceId:S.deviceId,isReactNative:x.isRN?1:0,customTag:e.customTag||"",sdkType:0};return n.userAgent="Native/".concat(x.info.sdkHumanVersion),x.isBrowser?n.sdkType=0:x.isRN?n.sdkType=2:x.isWeixinApp&&(n.sdkType=6),e.customClientType&&(n.customClientType=+e.customClientType),e.authType&&(n.authType=+e.authType),e.loginExt&&(n.loginExt=e.loginExt),n},y.onLogin=function(e,t){var n=this,r=0;n.loginResult=t,e?t&&!t.chatroom&&(k.updateUniErrCache("login",{operation_type:"protocol",error:e}),k.concludeUniErrCache("login",1),n.onAuthError(e,"link::onLogin")):(this.heartbeatFail=0,this.hasLogin=!0,this.autoconnect=!0,n.startHeartbeat(),n.afterLogin(t),n.initOnlineListener(),r=5e3,t.chatroom||(k.updateUniSuccCache("login",{operation_type:"protocol",target:"2-2"}),k.concludeUniErrCache("login",0))),!0===n.options.logReport&&d((function(){var e={appKey:n.options.appKey,sdk_ver:x.info.version,deviceId:S.deviceId};t.chatroom||k.reportErrEvent(e)}),r)},y.afterLogin=M.emptyFunc,y.notifyLogin=function(){var e=this.loginResult;this.logger.info("link::notifyLogin: on connect",e),this.options.onconnect(e)},y.logout=function(){k.pause();var e="done disconnect";if(this.doLogout)return this.doLogout=!1,e="done logout",void this.onAuthError(new v(e,"logout"),"link::logout");if(this.isConnected()){var t=new v(e,"logout");this.onAuthError(t,"link::logout")}},y.onKicked=function(e){var t=e.content,n=t.from,r=t.reason,o=t.custom,i=t.customClientType,a={reason:this.kickedReasons[r]||"unknown",message:this.kickedMessages[r]||"未知原因"};if(w(n)&&(a.from=T.reverseType(n)),w(o)&&(a.custom=o),+i>0&&(a.customClientType=i),this.logger.warn("link::onKicked:",a),"silentlyKick"!==a.reason){var s=new v("被踢了","kicked");M.merge(s,a),this.onAuthError(s,"link::onKicked")}else this.logger.warn("link::onKicked: silentlyKick"),this.forceDisconnect(),this.onDisconnect("link::onKicked")},y.onAuthError=function(e,t){var n=e&&e.code;if(this.logger.error("onAuthError ",t,n),/^(Error_Internet_Disconnected|Error_Timeout|Error_Connection_Socket_State_not_Match)$/.test(n))return this.forceDisconnect(),void this.onDisconnect("link::onAuthError::"+n);(e=e||v.newConnectionError({callFunc:t})).callFunc=e.callFunc||t||null,this.shouldReconnect=!1,this.markAllCallbackInvalid(e),this.notifyDisconnect(e)}},function(e,t,n){var r=n(25),o=n(23),i=n(65).fn,a=n(15);i.processLink=function(e){switch(e.cmd){case"heartbeat":this.startHeartbeat()}},i.startHeartbeat=function(){var e=this;e.stopHeartbeat(),e.heartbeatTimer=r((function(){var t;e.sendCmd("heartbeat",null,o(t=e.onHeartbeat).call(t,e))}),a.heartbeatInterval)},i.stopHeartbeat=function(){this.heartbeatTimer&&(clearTimeout(this.heartbeatTimer),this.heartbeatTimer=null)},i.onHeartbeat=function(e,t){if(e){if(this.syncing)return this.logger.warn("onHeartbeat::ignore error in connecting"),void this.startHeartbeat();var n;if(e.callFunc="link::onHeartbeat",this.onCustomError("heartbeat error",e),this.heartbeatFail++,this.logger.warn("onHeartbeat::error ",e.code,e),"Error_Timeout"===e.code)if(this.getOnlineStatus())if(this.heartbeatFail>1)this.forceDisconnect(),this.onDisconnect("link::onHeartbeat");else this.sendCmd("heartbeat",null,o(n=this.onHeartbeat).call(n,this));else this.startHeartbeat()}else this.heartbeatFail=0},i.getOnlineStatus="undefined"!=typeof navigator&&"boolean"==typeof navigator.onLine?function(){return navigator.onLine}:function(){return!0},i.heartbeat=function(){}},function(e,t,n){var r=n(4),o=n(6),i=n(25),a=n(51),s=n(65).fn,c=n(201),u=n(15),l=(n(0),n(41));s.processMisc=function(e){switch(e.cmd){case"getSimpleNosToken":e.error||(e.obj=e.content.nosTokens[0]);break;case"getNosToken":e.error||(e.obj=e.content.nosToken);break;case"getGrayscaleConfig":e.error||(e.obj=e.content.appGrayConfigTag);break;case"getMixStorePolicy":e.error||(e.obj=e.content.mixStorePolicyTag);break;case"getMixStoreToken":e.error||(e.obj=e.content.mixStoreTokenTag);break;case"getBackSourceToken":e.error||(e.obj=e.content.backSourceTokenTag);break;case"uploadSdkLogUrl":e.error?this.logger.error("uploadSdkLogUrl::error",e.error):this.logger.info("uploadSdkLogUrl::success",e.obj&&e.obj.url);break;case"notifyUploadLog":e.error||(u.isRN?this.uploadLocalLogRN():u.isBrowser&&this.uploadLocalLogWeb(),this.emitAPI({type:"notifyUploadLog"}));break;case"audioToText":e.error||(e.obj.text=e.content.text);break;case"processImage":e.obj.imageOps=c.reverseImageOps(e.obj.imageOps),e.error||(e.obj={url:e.content.url});break;case"getNosTokenTrans":e.error||(e.obj={nosToken:e.content.nosToken,docId:e.content.docId});break;case"getNosOriginUrl":e.error||(e.obj=e.content.nosFileUrlTag.originUrl);break;case"notifyTransLog":e.error||this.emitAPI({type:"notifyTransLog",obj:e.content.transInfo});break;case"fetchFile":case"fetchFileList":case"removeFile":e.error||(e.obj=e.content);break;case"getServerTime":e.obj=e.content&&e.content.time;break;case"getNosCdnHost":this.getNosCdnHost(e)}},s.uploadLocalLogRN=function(e){if(u.isRN&&l.rnfs){var t=l.rnfs,n=this,i=t.nimIndex,a=(t.nimIndex+1)%2;t.nimPromise=t.nimPromise.then((function(){return r.all([t.exists(s(i)),t.exists(s(a))])})).then((function(e){return e&&(e[0]||e[1])?e[0]&&e[1]?t.copyFile(s(a),s(2)).then((function(){return t.readFile(s(i))})).then((function(e){return t.appendFile(s(2),e)})):e[0]?t.copyFile(s(i),s(2)):void(e[1]&&t.copyFile(s(a),s(2))):r.reject()})).then((function(e){return new r((function(e,i){n.api.previewFile({filePath:s(2),done:function(i,a){if(r.all([t.unlink(s(2)),t.unlink(s(1)),t.unlink(s(0))]).finally((function(){e()})),i)n.logger.error("nim::uploadLocalLogRN:previewFile:error",i);else{var c=a.url;o(c).call(c,"?")>0?c+="&":c+="?",c+="download="+(new Date).getTime()+"_rn.log",n.api.uploadSdkLogUrl({url:c})}}})}))})).catch((function(e){t.unlink(s(2)).catch((function(e){})),n.logger.error("nim::protocol::uploadLocalLogRN",e)}))}function s(e){return t.DocumentDirectoryPath+"/nimlog_"+e+".log"}},s.uploadLocalLogWeb=function(){var e=this;!1!==this.options.dbLog&&this.logger._local?this.logger._local.fetchLog().then((function(t){e.api.previewFile({blob:new Blob([t.logs],{type:"text/plain"}),done:function(n,r){if(n)e.logger.error("uploadLocalLogWeb::previewFile:error",n);else{e.logger.log("uploadLocalLogWeb::previewFile:success",r);var i=r.url;i+=(o(i).call(i,"?")>0?"&":"?")+"download="+(new Date).getTime()+"_web.log",e.api.uploadSdkLogUrl({url:i}),e.logger._local.deleteLogs(t.time).then((function(){e.logger.log("uploadLocalLogWeb::deleteLogs success")})).catch((function(t){return e.logger.error("uploadLocalLogWeb::deleteLogs:error ,",t)}))}}})})).catch((function(t){e.logger.error("uploadLocalLogWeb::fetchLog:error",t)})):this.logger.warn("uploadLocalLogWeb::no dbLog")},s.getNosCdnHost=function(e){var t=this,n=e.error;if(n&&(n.callFunc="events::getNosCdnHost",t.onCustomError("getNosCdnHost","EVENT_GET_NOS_CDN_HOST_ERROR",n)),e.content&&e.content.nosConfigTag){var r=e.content.nosConfigTag,o="",s="";0!==r.expire&&r.cdnDomain?-1===r.expire?(o=r.cdnDomain,s=r.objectNamePrefix):(o=r.cdnDomain,s=r.objectNamePrefix,t.nosCdnHostTimer&&clearTimeout(t.nosCdnHostTimer),t.nosCdnHostTimer=i((function(){t.sendCmd("getNosCdnHost",{})}),800*a(r.expire))):(o="",s=""),u.serverNosConfig.cdnDomain=o,u.serverNosConfig.objectPrefix=s,u.hasLocalStorage&&(localStorage.setItem("nim_cdn_domain",o),localStorage.setItem("nim_object_prefix",s))}}},function(e,t,n){var r=n(8),o=n(30),i=n(65).fn,a=n(0),s=n(202);i.processAvSignal=function(e){switch(e.cmd){case"signalingCreate":case"signalingDelay":case"signalingClose":case"signalingJoin":case"signalingLeave":case"signalingInvite":case"signalingCancel":case"signalingReject":case"signalingAccept":case"signalingControl":case"signalingSyncMsgRead":case"signalingGetChannelInfo":break;case"signalingNotify":this.onSignalingNotify(e);break;case"signalingMutilClientSyncNotify":this.onSignalingMutilClientSyncNotify(e);break;case"signalingUnreadMessageSyncNotify":this.onSignalingUnreadMessageSyncNotify(e);break;case"signalingChannelsSyncNotify":this.onSignalingMembersSyncNotify(e);break;default:this.logger.warn("avSignal::unhandled cmd:",e.cmd)}};var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(e.needPush&&(e.needPush="1"===e.needPush),e.needBadge&&(e.needBadge="1"===e.needBadge),e.channelInValid&&(e.channelInValid="1"===e.channelInValid),e.attach){var t=JSON.parse(e.attach);e.eventType=s.parseAvSignalType(t.type)}if(e.members){var n=JSON.parse(e.members);e.members=r(n).call(n,(function(e){return s.parseAvSignalMember(e)}))}return e};i.onSignalingNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{e.raw&&e.raw.r&&e.raw.r.length&&e.content&&e.content.avSignalTag&&(e.content.avSignalTag.msgid=e.raw.r[0]);var n=e.content.avSignalTag;n=o(n)?r(n).call(n,(function(e){return c(e)})):c(n),this.emitAPI({type:"signalingNotify",obj:n}),a.isFunction(this.options.onSignalingNotify)&&this.options.onSignalingNotify(n)}},i.onSignalingMutilClientSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingMutilClientSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{var n=e.content.avSignalTag;n=o(n)?r(n).call(n,(function(e){return c(e)})):c(n),this.emitAPI({type:"signalingMutilClientSyncNotify",obj:n}),a.isFunction(this.options.onSignalingMutilClientSyncNotify)&&this.options.onSignalingMutilClientSyncNotify(n)}},i.onSignalingUnreadMessageSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingUnreadMessageSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{var n=e.content.avSignalTag;o(n)&&(n=r(n).call(n,(function(e){return c(e)}))),this.emitAPI({type:"signalingUnreadMessageSyncNotify",obj:n}),a.isFunction(this.options.onSignalingUnreadMessageSyncNotify)&&this.options.onSignalingUnreadMessageSyncNotify(n)}},i.onSignalingMembersSyncNotify=function(e){if(e.error){var t=e.error;this.logger.error("protocal::avSignal:onSignalingMembersSyncNotify error",t),this.emitAPI({type:"error",error:t}),this.options.onerror(t)}else{var n=e.content.avSignalTag;o(n)||(n=[n]),n=r(n).call(n,(function(e){return c(e)})),this.emitAPI({type:"signalingChannelsSyncNotify",obj:n}),a.isFunction(this.options.onSignalingMembersSyncNotify)&&this.options.onSignalingMembersSyncNotify(n)}}},function(e,t){e.exports={negotiateTransportTag:{version:-1,serializeList:1,serialize:101},initTransportTag:{},nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},audioToText:{url:2},imageOp:{type:0,stripmeta:1,typeType:2,blurRadius:3,blurSigma:4,qualityQuality:5,cropX:6,cropY:7,cropWidth:8,cropHeight:9,rotateAngle:10,pixelPixel:11,thumbnailMode:12,thumbnailWidth:13,thumbnailHeight:14,thumbnailAxisX:15,thumbnailAxisY:16,thumbnailCenterX:17,thumbnailCenterY:18,thumbnailEnlarge:19,thumbnailToStatic:20,watermarkType:21,watermarkGravity:22,watermarkDissolve:23,watermarkDx:24,watermarkDy:25,watermarkImage:26,watermarkText:27,watermarkFont:28,watermarkFontSize:29,watermarkFontColor:30,interlace:31},robot:{account:4,nick:5,avatar:6,intro:7,config:8,valid:9,createTime:10,updateTime:11,custid:12,botid:13,bindTime:14},clientAntispam:{version:1,md5:2,nosurl:3,thesaurus:4},fileQuickTransfer:{md5:1,url:2,size:3,threshold:4},transToken:{name:1,type:2,transType:3,size:4,extra:5,body:6},transInfo:{docId:1,name:2,prefix:3,size:4,type:5,state:6,transType:7,transSize:8,pageCount:9,picInfo:10,extra:11,flag:12},nosFileUrlTag:{safeUrl:0,originUrl:1},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3},fileListParam:{fromDocId:1,limit:2},avSignalTag:{type:1,channelName:2,channelId:3,channelCreateTime:4,channelExpireTime:5,creator:6,ext:7,channelInValid:8,from:10,to:11,requestId:12,needPush:13,pushTitle:14,pushContent:15,pushPayload:16,needBadge:17,members:18,attach:19,attachExt:20,isSave:21,msgid:22,uid:23,time:24},login:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appKey:18,account:19,browser:24,session:26,deviceInfo:32,sdkType:41,userAgent:42,isReactNative:112,token:1e3,customTag:38,customClientType:39,sdkHumanVersion:40,authType:115,loginExt:116},loginRes:{lastLoginDeviceId:17,customTag:38,connectionId:102,ip:103,port:104,country:106,hasXMPush:111},loginPort:{type:3,os:4,mac:5,deviceId:13,account:19,deviceInfo:32,connectionId:102,ip:103,time:109,customTag:38,customClientType:39},aosPushInfo:{pushType:110,hasTokenPreviously:111},sync:{myInfo:1,offlineMsgs:2,teams:3,netcallMsgs:6,roamingMsgs:7,relations:9,friends:11,sessions:12,friendUsers:13,msgReceipts:14,myTeamMembers:15,donnop:16,deleteMsg:17,sessionAck:18,robots:19,broadcastMsgs:20,avSignal:21,superTeams:22,myInfoInSuperTeams:23,superTeamRoamingMsgs:24,deleteSuperTeamMsg:25,superTeamSessionAck:26,deleteMsgSelf:27,stickTopSessions:28,sessionHistoryMsgsDelete:29,filterMsgs:100},donnop:{open:1},sessionReqTag:{minTimestamp:1,maxTimestamp:2,needLastMsg:3,limit:4,hasMore:5},session:{id:1,updateTime:2,ext:3,lastMsg:4},superTeam:{teamId:1,name:3,type:4,owner:5,level:6,selfCustom:7,valid:8,memberNum:9,memberUpdateTime:10,createTime:11,updateTime:12,validToCurrentUser:13,intro:14,announcement:15,joinMode:16,bits:17,custom:18,serverCustom:19,avatar:20,beInviteMode:21,inviteMode:22,updateTeamMode:23,updateCustomMode:24,mute:100,muteType:101},superTeamMember:{teamId:1,account:3,type:4,nickInTeam:5,bits:7,active:8,valid:9,createTime:10,updateTime:11,custom:12,mute:13,invitoraccid:14,joinTime:15},team:{teamId:1,name:3,type:4,owner:5,level:6,selfCustom:7,valid:8,memberNum:9,memberUpdateTime:10,createTime:11,updateTime:12,validToCurrentUser:13,intro:14,announcement:15,joinMode:16,bits:17,custom:18,serverCustom:19,avatar:20,beInviteMode:21,inviteMode:22,updateTeamMode:23,updateCustomMode:24,mute:100,muteType:101},teamMember:{teamId:1,account:3,type:4,nickInTeam:5,bits:7,active:8,valid:9,joinTime:10,updateTime:11,custom:12,mute:13,invitorAccid:14},msg:{scene:0,to:1,from:2,fromClientType:4,fromDeviceId:5,fromNick:6,time:7,type:8,body:9,attach:10,idClient:11,idServer:12,resend:13,userUpdateTime:14,custom:15,pushPayload:16,pushContent:17,apnsAccounts:18,apnsContent:19,apnsForcePush:20,yidunEnable:21,antiSpamContent:22,antiSpamBusinessId:23,clientAntiSpam:24,antiSpamUsingYidun:25,needMsgReceipt:26,needUpdateSession:28,replyMsgFromAccount:29,replyMsgToAccount:30,replyMsgTime:31,replyMsgIdServer:32,replyMsgIdClient:33,threadMsgFromAccount:34,threadMsgToAccount:35,threadMsgTime:36,threadMsgIdServer:37,threadMsgIdClient:38,delete:39,callbackExt:40,subType:41,yidunAntiCheating:42,env:43,yidunAntiSpamExt:44,yidunAntiSpamRes:45,isHistoryable:100,isRoamingable:101,isSyncable:102,isMuted:104,cc:105,isInBlackList:106,isPushable:107,isOfflinable:108,isUnreadable:109,needPushNick:110,isReplyMsg:111,tempTeamMemberCount:112},threadMsgReq:{beginTime:1,endTime:2,lastMsgId:3,limit:4,reverse:5},threadMsgsMeta:{total:1,lastMsgTime:2},comment:{from:1,body:2,time:3,custom:4,needPush:5,needBadge:6,pushTitle:7,apnsText:8,pushPayload:9},commentReq:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,timestamp:100},commentRes:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,detail:7,modify:8,timestamp:100},collect:{id:1,type:2,data:3,custom:4,uniqueId:5,createTime:6,updateTime:7},collectQuery:{beginTime:1,endTime:2,lastMsgId:3,limit:4,reverse:5,type:6},stickTopSession:{id:1,topCustom:2,createTime:3,updateTime:4},pinTag:{pinFrom:1,pinCustom:2,createTime:3,updateTime:4},msgPinReq:{sessionId:1,timetag:2},msgPinRes:{scene:1,from:2,to:3,time:4,idServer:5,idClient:6,pinFrom:7,pinCustom:8},msgReceipt:{to:1,from:2,time:7,idClient:11},teamMsgReceipt:{teamId:0,idServer:1,read:100,unread:101,idClient:102,account:103},deleteMsgSelfTag:{scene:1,from:2,to:3,idServer:4,idClient:5,time:6,deletedTime:7,custom:8},sysMsg:{time:0,type:1,to:2,from:3,ps:4,attach:5,idServer:6,sendToOnlineUsersOnly:7,apnsText:8,pushPayload:9,deletedIdClient:10,deletedIdServer:11,yidunEnable:12,antiSpamContent:13,deletedMsgTime:14,deletedMsgFromNick:15,opeAccount:16,env:21,callbackExt:22,cc:105,isPushable:107,isUnreadable:109,needPushNick:110},broadcastMsg:{broadcastId:1,fromAccid:2,fromUid:3,timestamp:4,body:5},friend:{account:4,flag:5,beflag:6,source:7,alias:8,bits:9,custom:10,createTime:11,updateTime:12,serverex:13},user:{account:1,nick:3,avatar:4,sign:5,gender:6,email:7,birth:8,tel:9,custom:10,createTime:12,updateTime:13},antispamTag:{antiSpamBusinessId:1},specialRelation:{account:0,isMuted:1,isBlacked:2,createTime:3,updateTime:4},msgType:{text:0,picture:1,audio:2,video:3,location:4,notification:5,file:6,netcall_audio:7,netcall_vedio:8,datatunnel_new:9,tips:10,robot:11,custom:100},msgEvent:{type:1,value:2,idClient:3,custom:4,validTime:5,broadcastType:6,sync:7,validTimeType:8,durable:9,time:10,idServer:11,clientType:12,serverConfig:13,serverCustom:14,appid:101,account:103,enableMultiClient:104,consid:106},msgEventSubscribe:{type:1,subscribeTime:2,sync:3,to:102,from:104,time:105},clearMsgsParams:{account:1,delRoam:2},clearMsgsParamsWithSync:{type:0,otherAccid:1,isDeleteRoam:2,toTid:3,isSyncSelf:4,fromAccid:5,time:6,ext:7},msgFullSearchRequestTag:{keyword:1,fromTime:2,toTime:3,sessionLimit:4,msgLimit:5,order:6,p2pList:7,teamList:8,senderList:9,msgTypeList:10,msgSubTypeList:11},msgTimingFullSearchRequestTag:{keyword:1,fromTime:2,toTime:3,msgLimit:5,order:6,p2pList:7,teamList:8,senderList:9,msgTypeList:10,msgSubTypeList:11},delFriendParams:{delAlias:1},proxyTag:{zone:1,path:2,method:3,header:4,body:5},proxyMsgTag:{from:1,body:2,time:3},sessionAckTag:{scene:1,to:2,timetag:3},mixTokenReq:{provider:0,tokenCount:1,fileExpireSec:2,tag:3,returnBody:4},mixAuthTokenReq:{type:1,urls:2},getQChatAddressTag:{ipType:1}}},function(e,t){e.exports={negotiateTransportTag:{"-1":"version",1:"serializeList",101:"serialize"},initTransportTag:{},nosToken:{1:"objectName",2:"token",3:"bucket",4:"expireTime",7:"expireSec",8:"tag",9:"shortUrl"},audioToText:{2:"url"},imageOp:{0:"type",1:"stripmeta",2:"typeType",3:"blurRadius",4:"blurSigma",5:"qualityQuality",6:"cropX",7:"cropY",8:"cropWidth",9:"cropHeight",10:"rotateAngle",11:"pixelPixel",12:"thumbnailMode",13:"thumbnailWidth",14:"thumbnailHeight",15:"thumbnailAxisX",16:"thumbnailAxisY",17:"thumbnailCenterX",18:"thumbnailCenterY",19:"thumbnailEnlarge",20:"thumbnailToStatic",21:"watermarkType",22:"watermarkGravity",23:"watermarkDissolve",24:"watermarkDx",25:"watermarkDy",26:"watermarkImage",27:"watermarkText",28:"watermarkFont",29:"watermarkFontSize",30:"watermarkFontColor",31:"interlace"},robot:{4:"account",5:"nick",6:"avatar",7:"intro",8:"config",9:"valid",10:"createTime",11:"updateTime",12:"custid",13:"botid",14:"bindTime",_6_safe:"_avatar_safe"},clientAntispam:{1:"version",2:"md5",3:"nosurl",4:"thesaurus"},fileQuickTransfer:{1:"md5",2:"url",3:"size",4:"threshold",_2_safe:"_url_safe"},transToken:{1:"name",2:"type",3:"transType",4:"size",5:"extra",6:"body"},transInfo:{1:"docId",2:"name",3:"prefix",4:"size",5:"type",6:"state",7:"transType",8:"transSize",9:"pageCount",10:"picInfo",11:"extra",12:"flag"},nosFileUrlTag:{0:"safeUrl",1:"originUrl"},nosAccessTokenTag:{0:"token",1:"url",2:"userAgent",3:"ext"},nosConfigTag:{1:"bucket",2:"cdnDomain",3:"expire",4:"objectNamePrefix"},appGrayConfigTag:{0:"grayConfig",1:"ttl"},mixStorePolicyTag:{0:"providers",1:"ttl",2:"mixEnable",3:"nosPolicy",4:"s3Policy"},mixStoreTokenTag:{0:"provider",1:"accessKeyId",2:"secretAccessKey",3:"sessionToken",4:"token",5:"expireAt",6:"bucket",7:"objectName",8:"fileExpireSec",9:"tag",10:"shortUrl",11:"region"},backSourceTokenTag:{1:"type",2:"tokens",3:"token",4:"ttl"},fileListParam:{1:"fromDocId",2:"limit"},avSignalTag:{1:"type",2:"channelName",3:"channelId",4:"channelCreateTime",5:"channelExpireTime",6:"creator",7:"ext",8:"channelInValid",10:"from",11:"to",12:"requestId",13:"needPush",14:"pushTitle",15:"pushContent",16:"pushPayload",17:"needBadge",18:"members",19:"attach",20:"attachExt",21:"isSave",22:"msgid",23:"uid",24:"time"},login:{3:"clientType",4:"os",6:"sdkVersion",8:"appLogin",9:"protocolVersion",10:"pushTokenName",11:"pushToken",13:"deviceId",18:"appKey",19:"account",24:"browser",26:"session",32:"deviceInfo",38:"customTag",39:"customClientType",40:"sdkHumanVersion",112:"isReactNative",115:"authType",116:"loginExt",1e3:"token"},loginRes:{17:"lastLoginDeviceId",38:"customTag",102:"connectionId",103:"ip",104:"port",106:"country",111:"hasXMPush"},loginPort:{3:"type",4:"os",5:"mac",13:"deviceId",19:"account",32:"deviceInfo",38:"customTag",39:"customClientType",102:"connectionId",103:"ip",109:"time"},aosPushInfo:{110:"pushType",111:"hasTokenPreviously"},sync:{1:"myInfo",2:"offlineMsgs",3:"teams",6:"netcallMsgs",7:"roamingMsgs",9:"relations",11:"friends",12:"sessions",13:"friendUsers",14:"msgReceipts",15:"myTeamMembers",16:"donnop",17:"deleteMsg",18:"sessionAck",19:"robots",20:"broadcastMsgs",21:"avSignal",22:"superTeams",23:"myInfoInSuperTeams",24:"superTeamRoamingMsgs",25:"deleteSuperTeamMsg",26:"superTeamSessionAck",27:"deleteMsgSelf",28:"stickTopSessions",29:"sessionHistoryMsgsDelete",100:"filterMsgs"},donnop:{1:"open"},sessionReqTag:{1:"minTimestamp",2:"maxTimestamp",3:"needLastMsg",4:"limit",5:"hasMore"},session:{1:"id",2:"updateTime",3:"ext",4:"lastMsg",5:"lastMsgType"},superTeam:{1:"teamId",3:"name",4:"type",5:"owner",6:"level",7:"selfCustom",8:"valid",9:"memberNum",10:"memberUpdateTime",11:"createTime",12:"updateTime",13:"validToCurrentUser",14:"intro",15:"announcement",16:"joinMode",17:"bits",18:"custom",19:"serverCustom",20:"avatar",21:"beInviteMode",22:"inviteMode",23:"updateTeamMode",24:"updateCustomMode",100:"mute",101:"muteType",_20_safe:"_avatar_safe"},superTeamMember:{1:"teamId",3:"account",4:"type",5:"nickInTeam",7:"bits",8:"active",9:"valid",10:"createTime",11:"updateTime",12:"custom",13:"mute",14:"invitoraccid",15:"joinTime"},team:{1:"teamId",3:"name",4:"type",5:"owner",6:"level",7:"selfCustom",8:"valid",9:"memberNum",10:"memberUpdateTime",11:"createTime",12:"updateTime",13:"validToCurrentUser",14:"intro",15:"announcement",16:"joinMode",17:"bits",18:"custom",19:"serverCustom",20:"avatar",21:"beInviteMode",22:"inviteMode",23:"updateTeamMode",24:"updateCustomMode",100:"mute",101:"muteType",_20_safe:"_avatar_safe"},teamMember:{1:"teamId",3:"account",4:"type",5:"nickInTeam",7:"bits",8:"active",9:"valid",10:"joinTime",11:"updateTime",12:"custom",13:"mute",14:"invitorAccid"},msg:{0:"scene",1:"to",2:"from",4:"fromClientType",5:"fromDeviceId",6:"fromNick",7:"time",8:"type",9:"body",10:"attach",11:"idClient",12:"idServer",13:"resend",14:"userUpdateTime",15:"custom",16:"pushPayload",17:"pushContent",18:"apnsAccounts",19:"apnsContent",20:"apnsForcePush",21:"yidunEnable",22:"antiSpamContent",23:"antiSpamBusinessId",24:"clientAntiSpam",25:"antiSpamUsingYidun",26:"needMsgReceipt",28:"needUpdateSession",29:"replyMsgFromAccount",30:"replyMsgToAccount",31:"replyMsgTime",32:"replyMsgIdServer",33:"replyMsgIdClient",34:"threadMsgFromAccount",35:"threadMsgToAccount",36:"threadMsgTime",37:"threadMsgIdServer",38:"threadMsgIdClient",39:"delete",40:"callbackExt",41:"subType",42:"yidunAntiCheating",43:"env",44:"yidunAntiSpamExt",45:"yidunAntiSpamRes",100:"isHistoryable",101:"isRoamingable",102:"isSyncable",104:"isMuted",105:"cc",106:"isInBlackList",107:"isPushable",108:"isOfflinable",109:"isUnreadable",110:"needPushNick",111:"isReplyMsg",112:"tempTeamMemberCount"},threadMsgReq:{1:"beginTime",2:"endTime",3:"lastMsgId",4:"limit",5:"reverse"},threadMsgsMeta:{1:"total",2:"lastMsgTime"},comment:{1:"from",2:"body",3:"time",4:"custom",5:"needPush",6:"needBadge",7:"pushTitle",8:"apnsText",9:"pushPayload"},commentReq:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",100:"timestamp"},commentRes:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",7:"detail",8:"modify",100:"timestamp"},collect:{1:"id",2:"type",3:"data",4:"custom",5:"uniqueId",6:"createTime",7:"updateTime"},collectQuery:{1:"beginTime",2:"endTime",3:"lastMsgId",4:"limit",5:"reverse",6:"type"},stickTopSession:{1:"id",2:"topCustom",3:"createTime",4:"updateTime"},pinTag:{1:"pinFrom",2:"pinCustom",3:"createTime",4:"updateTime"},msgPinReq:{1:"sessionId",2:"timetag"},msgPinRes:{1:"scene",2:"from",3:"to",4:"time",5:"idServer",6:"idClient",7:"pinFrom",8:"pinCustom"},msgReceipt:{1:"to",2:"from",7:"time",11:"idClient"},teamMsgReceipt:{0:"teamId",1:"idServer",100:"read",101:"unread",102:"idClient",103:"account"},deleteMsgSelfTag:{1:"scene",2:"from",3:"to",4:"idServer",5:"idClient",6:"time",7:"deletedTime",8:"custom"},sysMsg:{0:"time",1:"type",2:"to",3:"from",4:"ps",5:"attach",6:"idServer",7:"sendToOnlineUsersOnly",8:"apnsText",9:"pushPayload",10:"deletedIdClient",11:"deletedIdServer",12:"yidunEnable",13:"antiSpamContent",14:"deletedMsgTime",15:"deletedMsgFromNick",16:"opeAccount",21:"env",22:"callbackExt",105:"cc",107:"isPushable",109:"isUnreadable",110:"needPushNick"},broadcastMsg:{1:"broadcastId",2:"fromAccid",3:"fromUid",4:"timestamp",5:"body"},friend:{4:"account",5:"flag",6:"beflag",7:"source",8:"alias",9:"bits",10:"custom",11:"createTime",12:"updateTime",13:"serverex"},user:{1:"account",3:"nick",4:"avatar",5:"sign",6:"gender",7:"email",8:"birth",9:"tel",10:"custom",12:"createTime",13:"updateTime",_4_safe:"_avatar_safe"},antispamTag:{antiSpamBusinessId:1},specialRelation:{0:"account",1:"isMuted",2:"isBlacked",3:"createTime",4:"updateTime"},msgType:{0:"text",1:"picture",2:"audio",3:"video",4:"location",5:"notification",6:"file",7:"netcall_audio",8:"netcall_vedio",9:"datatunnel_new",10:"tips",11:"robot",100:"custom"},msgEvent:{1:"type",2:"value",3:"idClient",4:"custom",5:"validTime",6:"broadcastType",7:"sync",8:"validTimeType",9:"durable",10:"time",11:"idServer",12:"clientType",13:"serverConfig",14:"serverCustom",101:"appid",103:"account",104:"enableMultiClient",106:"consid"},msgEventSubscribe:{1:"type",2:"subscribeTime",3:"sync",102:"to",104:"from",105:"time"},clearMsgsParams:{1:"account",2:"delRoam"},clearMsgsParamsWithSync:{0:"type",1:"otherAccid",2:"delRoam",3:"toTid",4:"isSyncSelf",5:"fromAccid",6:"time",7:"ext"},msgFullSearchRequestTag:{1:"keyword",2:"fromTime",3:"toTime",4:"sessionLimit",5:"msgLimit",6:"order",7:"p2pList",8:"teamList",9:"senderList",10:"msgTypeList",11:"msgSubTypeList"},delFriendParams:{1:"delAlias"},proxyTag:{1:"zone",2:"path",3:"method",4:"header",5:"body"},proxyMsgTag:{1:"from",2:"body",3:"time"}}},function(e,t,n){var r=n(0),o=n(142),i=r.merge({},o.idMap,{chatroom:{id:13,login:2,kicked:3,logout:4,sendMsg:6,msg:7,getChatroomMembers:8,getHistoryMsgs:9,markChatroomMember:11,closeChatroom:12,getChatroom:13,updateChatroom:14,updateMyChatroomMemberInfo:15,getChatroomMembersInfo:16,kickChatroomMember:17,updateChatroomMemberTempMute:19,queueOffer:20,queuePoll:21,queueList:22,peak:23,queueDrop:24,queueInit:25,queueChange:26,updateTagMembersTempMute:30,getChatroomMembersByTag:31,getChatroomMemberCountByTag:32,updateCoordinate:33,updateChatroomTags:34,notifyCdnInfo:99},user:{id:3,syncRobot:16}}),a=r.merge({},o.cmdConfig,{login:{sid:i.chatroom.id,cid:i.chatroom.login,params:[{type:"byte",name:"type"},{type:"Property",name:"login"},{type:"Property",name:"imLogin"}]},logout:{sid:i.chatroom.id,cid:i.chatroom.logout},sendMsg:{sid:i.chatroom.id,cid:i.chatroom.sendMsg,params:[{type:"Property",name:"msg"}]},getChatroomMembers:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMembers,params:[{type:"byte",name:"type"},{type:"long",name:"time"},{type:"int",name:"limit"}]},getHistoryMsgs:{sid:i.chatroom.id,cid:i.chatroom.getHistoryMsgs,params:[{type:"long",name:"timetag"},{type:"int",name:"limit"},{type:"bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}]},markChatroomMember:{sid:i.chatroom.id,cid:i.chatroom.markChatroomMember,params:[{type:"string",name:"account"},{type:"int",name:"type"},{type:"bool",name:"isAdd"},{type:"int",name:"level"},{type:"string",name:"custom"}]},closeChatroom:{sid:i.chatroom.id,cid:i.chatroom.closeChatroom,params:[{type:"string",name:"custom"}]},getChatroom:{sid:i.chatroom.id,cid:i.chatroom.getChatroom},updateChatroom:{sid:i.chatroom.id,cid:i.chatroom.updateChatroom,params:[{type:"Property",name:"chatroom"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"},{type:"Property",name:"antispamTag"}]},updateMyChatroomMemberInfo:{sid:i.chatroom.id,cid:i.chatroom.updateMyChatroomMemberInfo,params:[{type:"Property",name:"chatroomMember"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"},{type:"bool",name:"needSave"},{type:"Property",name:"antispamTag"}]},getChatroomMembersInfo:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMembersInfo,params:[{type:"StrArray",name:"accounts"}]},kickChatroomMember:{sid:i.chatroom.id,cid:i.chatroom.kickChatroomMember,params:[{type:"string",name:"account"},{type:"string",name:"custom"}]},updateChatroomMemberTempMute:{sid:i.chatroom.id,cid:i.chatroom.updateChatroomMemberTempMute,params:[{type:"String",name:"account"},{type:"long",name:"duration"},{type:"bool",name:"needNotify"},{type:"String",name:"custom"}]},queueOffer:{sid:i.chatroom.id,cid:i.chatroom.queueOffer,params:[{type:"string",name:"elementKey"},{type:"string",name:"elementValue"},{type:"bool",name:"transient"},{type:"string",name:"elementAccount"}]},queuePoll:{sid:i.chatroom.id,cid:i.chatroom.queuePoll,params:[{type:"string",name:"elementKey"}]},queueList:{sid:i.chatroom.id,cid:i.chatroom.queueList},peak:{sid:i.chatroom.id,cid:i.chatroom.peak},queueDrop:{sid:i.chatroom.id,cid:i.chatroom.queueDrop},queueInit:{sid:i.chatroom.id,cid:i.chatroom.queueInit,params:[{type:"int",name:"limit"}]},queueChange:{sid:i.chatroom.id,cid:i.chatroom.queueChange,params:[{type:"StrStrMap",name:"elementMap"},{type:"bool",name:"needNotify"},{type:"string",name:"notifyExt"}]},updateTagMembersTempMute:{sid:i.chatroom.id,cid:i.chatroom.updateTagMembersTempMute,params:[{type:"Property",name:"tagMuteReq"}]},updateCoordinate:{sid:i.chatroom.id,cid:i.chatroom.updateCoordinate,params:[{type:"Property",name:"coordinateReq"}]},getChatroomMemberCountByTag:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMemberCountByTag,params:[{type:"String",name:"tag"}]},getChatroomMembersByTag:{sid:i.chatroom.id,cid:i.chatroom.getChatroomMembersByTag,params:[{type:"Property",name:"tagMemberReq"}]},updateChatroomTags:{sid:i.chatroom.id,cid:i.chatroom.updateChatroomTags,params:[{type:"Property",name:"chatRoomTagsUpdateTag"}]},notifyCdnInfo:{sid:i.chatroom.id,cid:i.chatroom.notifyCdnInfo},syncRobot:{sid:i.user.id,cid:i.user.syncRobot,params:[{type:"long",name:"timetag"}]}}),s=r.merge({},o.packetConfig,{"4_10":{service:"notify"},"4_11":{service:"notify"},"3_16":{service:"chatroom",cmd:"syncRobot",response:[{type:"PropertyArray",name:"robots",entity:"robot"}]},"13_2":{service:"chatroom",cmd:"login",response:[{type:"Property",name:"chatroom"},{type:"Property",name:"chatroomMember"},{type:"Property",name:"chatroomCdnInfo"}]},"13_3":{service:"chatroom",cmd:"kicked",response:[{type:"Number",name:"reason"},{type:"String",name:"custom"}]},"13_4":{service:"chatroom",cmd:"logout"},"13_6":{service:"chatroom",cmd:"sendMsg",response:[{type:"Property",name:"msg"}]},"13_7":{service:"chatroom",cmd:"msg",response:[{type:"Property",name:"msg"}]},"13_8":{service:"chatroom",cmd:"getChatroomMembers",response:[{type:"PropertyArray",name:"members",entity:"chatroomMember"}]},"13_9":{service:"chatroom",cmd:"getHistoryMsgs",response:[{type:"PropertyArray",name:"msgs",entity:"msg"}]},"13_11":{service:"chatroom",cmd:"markChatroomMember",response:[{type:"Property",name:"chatroomMember"}]},"13_12":{service:"chatroom",cmd:"closeChatroom"},"13_13":{service:"chatroom",cmd:"getChatroom",response:[{type:"Property",name:"chatroom"}]},"13_14":{service:"chatroom",cmd:"updateChatroom"},"13_15":{service:"chatroom",cmd:"updateMyChatroomMemberInfo"},"13_16":{service:"chatroom",cmd:"getChatroomMembersInfo",response:[{type:"PropertyArray",name:"members",entity:"chatroomMember"}]},"13_17":{service:"chatroom",cmd:"kickChatroomMember"},"13_19":{service:"chatroom",cmd:"updateChatroomMemberTempMute"},"13_20":{service:"chatroom",cmd:"queueOffer"},"13_21":{service:"chatroom",cmd:"queuePoll",response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},"13_22":{service:"chatroom",cmd:"queueList",response:[{type:"KVArray",name:"queueList"}]},"13_23":{service:"chatroom",cmd:"peak",response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},"13_24":{service:"chatroom",cmd:"queueDrop"},"13_25":{service:"chatroom",cmd:"queueInit"},"13_26":{service:"chatroom",cmd:"queueChange",response:[{type:"StrArray",name:"elementKeyArray"}]},"13_30":{service:"chatroom",cmd:"updateTagMembersTempMute"},"13_33":{service:"chatroom",cmd:"updateCoordinate"},"13_31":{service:"chatroom",cmd:"getChatroomMembersByTag",response:[{type:"PropertyArray",name:"members",entity:"chatroomMember"}]},"13_32":{service:"chatroom",cmd:"getChatroomMemberCountByTag",response:[{type:"long",name:"count"}]},"13_34":{service:"chatroom",cmd:"updateChatroomTags"},"13_99":{service:"chatroom",cmd:"notifyCdnInfo",response:[{type:"Property",name:"chatroomCdnInfo"}]}});e.exports={idMap:i,cmdConfig:a,packetConfig:s}},function(e,t){e.exports={imLogin:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appKey:18,account:19,browser:24,session:26,deviceInfo:32,sdkType:41,userAgent:42,isReactNative:112,authType:115,loginExt:116,token:1e3,customTag:38},nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},audioToText:{url:2},imageOp:{type:0,stripmeta:1,typeType:2,blurRadius:3,blurSigma:4,qualityQuality:5,cropX:6,cropY:7,cropWidth:8,cropHeight:9,rotateAngle:10,pixelPixel:11,thumbnailMode:12,thumbnailWidth:13,thumbnailHeight:14,thumbnailAxisX:15,thumbnailAxisY:16,thumbnailCenterX:17,thumbnailCenterY:18,thumbnailEnlarge:19,thumbnailToStatic:20,watermarkType:21,watermarkGravity:22,watermarkDissolve:23,watermarkDx:24,watermarkDy:25,watermarkImage:26,watermarkText:27,watermarkFont:28,watermarkFontSize:29,watermarkFontColor:30,interlace:31},robot:{account:4,nick:5,avatar:6,intro:7,config:8,valid:9,createTime:10,updateTime:11,custid:12,botid:13,bindTime:14},clientAntispam:{version:1,md5:2,nosurl:3,thesaurus:4},fileQuickTransfer:{md5:1,url:2,size:3,threshold:4},transToken:{name:1,type:2,transType:3,size:4,extra:5,body:6},transInfo:{docId:1,name:2,prefix:3,size:4,type:5,state:6,transType:7,transSize:8,pageCount:9,picInfo:10,extra:11,flag:12},nosFileUrlTag:{safeUrl:0,originUrl:1},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3},fileListParam:{fromDocId:1,limit:2},avSignalTag:{type:1,channelName:2,channelId:3,channelCreateTime:4,channelExpireTime:5,creator:6,ext:7,channelInValid:8,from:10,to:11,requestId:12,needPush:13,pushTitle:14,pushContent:15,pushPayload:16,needBadge:17,members:18,attach:19,attachExt:20,isSave:21,msgid:22,uid:23,time:24},login:{appKey:1,account:2,deviceId:3,chatroomId:5,appLogin:8,chatroomNick:20,chatroomAvatar:21,chatroomCustom:22,chatroomEnterCustom:23,session:26,isAnonymous:38,tags:39,notifyTargetTags:40,loginAuthType:41,loginExt:42,loc_x:43,loc_y:44,loc_z:45,distance:46,antiSpamBusinessId:47},chatroom:{id:1,name:3,announcement:4,broadcastUrl:5,custom:12,createTime:14,updateTime:15,queuelevel:16,creator:100,onlineMemberNum:101,mute:102},msg:{idClient:1,type:2,attach:3,custom:4,resend:5,userUpdateTime:6,fromNick:7,fromAvatar:8,fromCustom:9,yidunEnable:10,antiSpamContent:11,skipHistory:12,body:13,antiSpamBusinessId:14,clientAntiSpam:15,antiSpamUsingYidun:16,time:20,from:21,chatroomId:22,fromClientType:23,highPriority:25,callbackExt:27,subType:28,yidunAntiCheating:29,env:30,notifyTargetTags:31,yidunAntiSpamExt:32,yidunAntiSpamRes:33,loc_x:34,loc_y:35,loc_z:36,toAccids:37},chatroomMember:{chatroomId:1,account:2,type:3,level:4,nick:5,avatar:6,custom:7,online:8,guest:9,enterTime:10,blacked:12,gaged:13,valid:14,updateTime:15,tempMuted:16,tempMuteDuration:17},antispamTag:{antiSpamBusinessId:1},chatroomCdnInfo:{enable:1,cdnUrls:2,timestamp:3,interval:4,decryptType:5,decryptKey:6,timeout:7},tagMemberReq:{tag:1,time:2,limit:3},tagMuteReq:{tag:1,duration:2,needNotify:3,custom:4,notifyTargetTags:5},coordinateReq:{x:1,y:2,z:3,distance:4},chatRoomTagsUpdateTag:{tags:1,notifyTargetTags:2,needNotify:3,ext:4}}},function(e,t){e.exports={imLogin:{3:"clientType",4:"os",6:"sdkVersion",8:"appLogin",9:"protocolVersion",10:"pushTokenName",11:"pushToken",13:"deviceId",18:"appKey",19:"account",24:"browser",26:"session",32:"deviceInfo",38:"customTag",41:"sdkType",42:"userAgent",112:"isReactNative",1e3:"token"},nosToken:{1:"objectName",2:"token",3:"bucket",4:"expireTime",7:"expireSec",8:"tag",9:"shortUrl"},audioToText:{2:"url"},imageOp:{0:"type",1:"stripmeta",2:"typeType",3:"blurRadius",4:"blurSigma",5:"qualityQuality",6:"cropX",7:"cropY",8:"cropWidth",9:"cropHeight",10:"rotateAngle",11:"pixelPixel",12:"thumbnailMode",13:"thumbnailWidth",14:"thumbnailHeight",15:"thumbnailAxisX",16:"thumbnailAxisY",17:"thumbnailCenterX",18:"thumbnailCenterY",19:"thumbnailEnlarge",20:"thumbnailToStatic",21:"watermarkType",22:"watermarkGravity",23:"watermarkDissolve",24:"watermarkDx",25:"watermarkDy",26:"watermarkImage",27:"watermarkText",28:"watermarkFont",29:"watermarkFontSize",30:"watermarkFontColor",31:"interlace"},robot:{4:"account",5:"nick",6:"avatar",7:"intro",8:"config",9:"valid",10:"createTime",11:"updateTime",12:"custid",13:"botid",14:"bindTime",_6_safe:"_avatar_safe"},clientAntispam:{1:"version",2:"md5",3:"nosurl",4:"thesaurus"},fileQuickTransfer:{1:"md5",2:"url",3:"size",4:"threshold",_2_safe:"_url_safe"},transToken:{1:"name",2:"type",3:"transType",4:"size",5:"extra",6:"body"},transInfo:{1:"docId",2:"name",3:"prefix",4:"size",5:"type",6:"state",7:"transType",8:"transSize",9:"pageCount",10:"picInfo",11:"extra",12:"flag"},nosFileUrlTag:{0:"safeUrl",1:"originUrl"},nosAccessTokenTag:{0:"token",1:"url",2:"userAgent",3:"ext"},fileListParam:{1:"fromDocId",2:"limit"},avSignalTag:{1:"type",2:"channelName",3:"channelId",4:"channelCreateTime",5:"channelExpireTime",6:"creator",7:"ext",8:"channelInValid",10:"from",11:"to",12:"requestId",13:"needPush",14:"pushTitle",15:"pushContent",16:"pushPayload",17:"needBadge",18:"members",19:"attach",20:"attachExt",21:"isSave",22:"msgid",23:"uid",24:"time"},login:{1:"appKey",2:"account",3:"deviceId",5:"chatroomId",8:"appLogin",20:"chatroomNick",21:"chatroomAvatar",22:"chatroomCustom",23:"chatroomEnterCustom",26:"session",38:"isAnonymous",39:"tags",40:"notifyTargetTags",41:"loginAuthType",42:"loginExt",43:"loc_x",44:"loc_y",45:"loc_z",46:"distance",47:"antiSpamBusinessId",_21_safe:"_chatroomAvatar_safe"},chatroom:{1:"id",3:"name",4:"announcement",5:"broadcastUrl",12:"custom",14:"createTime",15:"updateTime",16:"queuelevel",100:"creator",101:"onlineMemberNum",102:"mute"},msg:{1:"idClient",2:"type",3:"attach",4:"custom",5:"resend",6:"userUpdateTime",7:"fromNick",8:"fromAvatar",9:"fromCustom",10:"yidunEnable",11:"antiSpamContent",12:"skipHistory",13:"body",14:"antiSpamBusinessId",15:"clientAntiSpam",16:"antiSpamUsingYidun",20:"time",21:"from",22:"chatroomId",23:"fromClientType",25:"highPriority",27:"callbackExt",28:"subType",29:"yidunAntiCheating",30:"env",31:"notifyTargetTags",32:"yidunAntiSpamExt",33:"yidunAntiSpamRes",34:"loc_x",35:"loc_y",36:"loc_z",37:"toAccids",_8_safe:"_fromAvatar_safe"},chatroomMember:{1:"chatroomId",2:"account",3:"type",4:"level",5:"nick",6:"avatar",7:"custom",8:"online",9:"guest",10:"enterTime",12:"blacked",13:"gaged",14:"valid",15:"updateTime",16:"tempMuted",17:"tempMuteDuration",_6_safe:"_avatar_safe"},antispamTag:{antiSpamBusinessId:1},chatroomCdnInfo:{1:"enable",2:"cdnUrls",3:"timestamp",4:"interval",5:"decryptType",6:"decryptKey",7:"timeout"},nosConfigTag:{1:"bucket",2:"cdnDomain",3:"expire",4:"objectNamePrefix"}}},,,,,,,function(e,t,n){"use strict";var r=n(479),o=n(480),i=n(481),a=n(482);function s(e,t,n){var r=e;return o(t)?(n=t,"string"==typeof e&&(r={uri:e})):r=a(t,{uri:e}),r.callback=n,r}function c(e,t,n){return u(t=s(e,t,n))}function u(e){if(void 0===e.callback)throw new Error("callback argument missing");var t=!1,n=function(n,r,o){t||(t=!0,e.callback(n,r,o))};function r(){var e=void 0;if(e=l.response?l.response:l.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(l),y)try{e=JSON.parse(e)}catch(e){}return e}function o(e){return clearTimeout(p),e instanceof Error||(e=new Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,n(e,v)}function a(){if(!u){var t;clearTimeout(p),t=e.useXDR&&void 0===l.status?200:1223===l.status?204:l.status;var o=v,a=null;return 0!==t?(o={body:r(),statusCode:t,method:f,headers:{},url:m,rawRequest:l},l.getAllResponseHeaders&&(o.headers=i(l.getAllResponseHeaders()))):a=new Error("Internal XMLHttpRequest Error"),n(a,o,o.body)}}var s,u,l=e.xhr||null;l||(l=e.cors||e.useXDR?new c.XDomainRequest:new c.XMLHttpRequest);var p,m=l.url=e.uri||e.url,f=l.method=e.method||"GET",d=e.body||e.data,g=l.headers=e.headers||{},h=!!e.sync,y=!1,v={body:void 0,headers:{},statusCode:0,method:f,url:m,rawRequest:l};if("json"in e&&!1!==e.json&&(y=!0,g.accept||g.Accept||(g.Accept="application/json"),"GET"!==f&&"HEAD"!==f&&(g["content-type"]||g["Content-Type"]||(g["Content-Type"]="application/json"),d=JSON.stringify(!0===e.json?d:e.json))),l.onreadystatechange=function(){4===l.readyState&&setTimeout(a,0)},l.onload=a,l.onerror=o,l.onprogress=function(){},l.onabort=function(){u=!0},l.ontimeout=o,l.open(f,m,!h,e.username,e.password),h||(l.withCredentials=!!e.withCredentials),!h&&e.timeout>0&&(p=setTimeout((function(){if(!u){u=!0,l.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",o(e)}}),e.timeout)),l.setRequestHeader)for(s in g)g.hasOwnProperty(s)&&l.setRequestHeader(s,g[s]);else if(e.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(e.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in e&&(l.responseType=e.responseType),"beforeSend"in e&&"function"==typeof e.beforeSend&&e.beforeSend(l),l.send(d||null),l}e.exports=c,e.exports.default=c,c.XMLHttpRequest=r.XMLHttpRequest||function(){},c.XDomainRequest="withCredentials"in new c.XMLHttpRequest?c.XMLHttpRequest:r.XDomainRequest,function(e,t){for(var n=0;n<e.length;n++)t(e[n])}(["get","put","post","patch","head","delete"],(function(e){c["delete"===e?"del":e]=function(t,n,r){return(n=s(t,n,r)).method=e.toUpperCase(),u(n)}}))},function(e,t,n){(function(t){var n;n="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},e.exports=n}).call(this,n(49))},function(e,t){e.exports=function(e){if(!e)return!1;var t=n.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)};var n=Object.prototype.toString},function(e,t){var n=function(e){return e.replace(/^\s+|\s+$/g,"")};e.exports=function(e){if(!e)return{};for(var t,r={},o=n(e).split("\n"),i=0;i<o.length;i++){var a=o[i],s=a.indexOf(":"),c=n(a.slice(0,s)).toLowerCase(),u=n(a.slice(s+1));void 0===r[c]?r[c]=u:(t=r[c],"[object Array]"===Object.prototype.toString.call(t)?r[c].push(u):r[c]=[r[c],u])}return r}},function(e,t){e.exports=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var o in r)n.call(r,o)&&(e[o]=r[o])}return e};var n=Object.prototype.hasOwnProperty},function(e,t,n){var r=n(28),o=n(94),i=n(10),a=n(12);function s(e){var t=this,n=e.url||null;t.level={debug:0,log:1,info:2,warn:3,error:4}[e.level]||0,t.logCache=[],t.logNum=1,t.timeInterval=5e3,window.onerror=function(e,n,r,o,i){t.error(i)},o((function(){t.logCache.length>0&&n&&t.postLogs(n,t.logCache)}),t.timeInterval)}s.prototype.debug=function(){var e;this.level>0||(console.debug.apply(this,arguments),this.cacheLogs.apply(this,i(e=["[degbug]"]).call(e,arguments)))},s.prototype.log=function(){var e;this.level>1||(console.log.apply(this,arguments),this.cacheLogs.apply(this,i(e=["[log]"]).call(e,arguments)))},s.prototype.info=function(){var e;this.level>2||(console.info.apply(this,arguments),this.cacheLogs.apply(this,i(e=["[info]"]).call(e,arguments)))},s.prototype.warn=function(){var e;this.level>3||(console.warn.apply(this,arguments),this.cacheLogs.apply(this,i(e=["[warn]"]).call(e,arguments)))},s.prototype.error=function(){var e;this.level>4||(console.error.apply(this,arguments),this.cacheLogs.apply(this,i(e=["[error]"]).call(e,arguments)))},s.prototype.cacheLogs=function(e,t){for(var n=[],o=0;o<t.length;o++){var i=t[o];"object"===r(i)?n.push(a(i)):n.push(i)}var s=this.logNum+++" "+e+" "+n.join("; ");this.logCache.push(s.replace("%c",""))},s.prototype.postLogs=function(e,t){var n=this,r=new XMLHttpRequest;r.onreadystatechange=function(){4===r.readyState&&(200===r.status?(console.info("LoggerPlugin::日志上报完成"),n.logCache=[],n.timeInterval=5e3):n.timeInterval+=5e3)},r.open("POST",e),r.setRequestHeader("Content-Type","plain/text;charset=utf-8"),r.timeout=360,r.send(t.join("\n"))},e.exports=s},function(e,t,n){var r=n(4),o=n(41);e.exports=function(e){var t,n,i,a;e.db&&(o.db=e.db),e.rnfs&&(o.rnfs=e.rnfs,o.rnfs.size||(o.rnfs.size=1048576),o.rnfs.nimPromise=(t=o.rnfs,n=t.size/2-256,a=0,i=o.rnfs.DocumentDirectoryPath+"/nimlog_"+a+".log",t.exists(i).then((function(e){return e?t.stat(i):r.reject(0)})).then((function(e){return e&&e.size>n?r.reject(1):r.reject(0)})).catch((function(e){return"number"==typeof e&&(t.nimIndex=e),r.resolve()}))))}},function(e,t,n){var r=n(51),o=n(0),i=o.notundef,a=o.undef;function s(e){i(e.name)&&(this.name=""+e.name),i(e.announcement)&&(this.announcement=""+e.announcement),i(e.broadcastUrl)&&(this.broadcastUrl=""+e.broadcastUrl),i(e.custom)&&(this.custom=""+e.custom),i(e.queuelevel)&&(this.queuelevel=r(e.queuelevel))}s.reverse=function(e){var t=o.copy(e);return a(t.announcement)&&(t.announcement=""),a(t.broadcastUrl)&&(t.broadcastUrl=""),a(t.custom)&&(t.custom=""),i(t.createTime)&&(t.createTime=+t.createTime),i(t.updateTime)&&(t.updateTime=+t.updateTime),i(t.onlineMemberNum)&&(t.onlineMemberNum=+t.onlineMemberNum),i(t.mute)&&(t.mute="1"===t.mute),t},e.exports=s},function(e,t,n){var r,o,i;e.exports=(i=n(66),o=(r=i).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)o.push(r.charAt(a>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,n=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var i=0;i<n.length;i++)r[n.charCodeAt(i)]=i}var a=n.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return function(e,t,n){for(var r=[],i=0,a=0;a<t;a++)if(a%4){var s=n[e.charCodeAt(a-1)]<<a%4*2,c=n[e.charCodeAt(a)]>>>6-a%4*2,u=s|c;r[i>>>2]|=u<<24-i%4*8,i++}return o.create(r,i)}(e,t,r)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},i.enc.Base64)},function(e,t,n){var r,o,i,a,s,c,u,l;e.exports=(l=n(66),n(594),n(595),o=(r=l).lib,i=o.Base,a=o.WordArray,s=r.algo,c=s.MD5,u=s.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:c,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n,r=this.cfg,o=r.hasher.create(),i=a.create(),s=i.words,c=r.keySize,u=r.iterations;s.length<c;){n&&o.update(n),n=o.update(e).finalize(t),o.reset();for(var l=1;l<u;l++)n=o.finalize(n),o.reset();i.concat(n)}return i.sigBytes=4*c,i}}),r.EvpKDF=function(e,t,n){return u.create(n).compute(e,t)},l.EvpKDF)},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){n(206);var r=n(155);n(219)(r),e.exports=r},function(e,t,n){var r=n(17);n(146).fn.refreshSocketUrl=function(){var e;this.socketUrlsBackup=this.socketUrlsBackup||[],this.socketUrls=r(e=this.socketUrlsBackup).call(e,0),this.logger.info("link::refreshSocketUrl"),this.connectToUrl(this.getNextSocketUrl())}},function(e,t,n){var r=n(12),o=n(146).fn,i=n(41),a=n(0);o.assembleLogin=function(){var e=this.options;this.sdkSession=this.genSessionKey();var t={appKey:e.appKey,account:e.account,deviceId:i.deviceId,chatroomId:e.chatroomId,session:this.sdkSession,appLogin:this.autoconnect?1:0},n=!0;if(void 0===e.loc_x||void 0===e.loc_y||void 0===e.loc_z?n=!1:("number"!=typeof e.loc_x&&(e.loc_x=0),"number"!=typeof e.loc_y&&(e.loc_y=0),"number"!=typeof e.loc_z&&(e.loc_z=0)),e.authType=e.loginAuthType,t=a.merge(t,a.filterObj(e,n?"chatroomNick chatroomAvatar chatroomCustom chatroomEnterCustom isAnonymous notifyTargetTags loginAuthType loginExt loc_x loc_y loc_z distance antiSpamBusinessId":"chatroomNick chatroomAvatar chatroomCustom chatroomEnterCustom isAnonymous notifyTargetTags loginAuthType loginExt distance antiSpamBusinessId")),e.tags&&e.tags.length>0)try{t.tags=r(e.tags)}catch(t){this.logger.error("assembleLogin::format tags error",e.tags,t)}return{type:1,login:t,imLogin:this.assembleIMLogin()}},o.afterLogin=function(e){var t=e.chatroom;this.sendCmd("getNosCdnHost",{}),this.chatroom=t,this.notifyLogin()},o.kickedReasons=["","chatroomClosed","managerKick","samePlatformKick","silentlyKick","blacked"],o.kickedMessages=["","聊天室关闭了","被房主或者管理员踢出","不允许同一个帐号在多个地方同时登录","悄悄被踢","被拉黑了"]},function(e,t,n){var r=n(5),o=n(25),i=n(77),a=n(48),s=n(51),c=n(17),u=n(30),l=n(94),p=n(3),m=n(9),f=n(146).fn,d=n(0),g=n(55),h=n(591),y=n(486),v=n(596),b=n(597),T=n(598);f.completeMsg=function(e){e.chatroomId=this.chatroom&&this.chatroom.id,e.from=this.options.account,e.fromClientType="Web",e.time||(e.time=+new Date)},f.onMsg=function(e){var t,n=r(t=this.message).call(t,e.content.msg);this.checkMsgUnique(n)?(this.msgBuffer.push(n),this.msgFlushTimer||this.startMsgFlushTimer()):this.logger.warn("onMsg::msg::repeat",n.idClient)},f.startMsgFlushTimer=function(){var e=this,t=e.options;e.msgFlushTimer=o((function(){var n,r=i(n=e.msgBuffer).call(n,0,t.msgBufferSize);e.options.onmsgs(r),e.msgBuffer.length?e.startMsgFlushTimer():delete e.msgFlushTimer}),t.msgBufferInterval)},f.checkMsgUnique=d.genCheckUniqueFunc("idClient"),f.onSendMsg=function(e){var t,n=e.obj.msg;e.error?n.status="fail":(n=e.content.msg).status="success",a(n,e.obj.msg),n=r(t=this.message).call(t,n),this.checkMsgUnique(n),e.obj=n},f.onHistoryMsgs=function(e){e.error||(e.obj||(e.obj={}),e.obj.msgs=this.message.reverseMsgs(e.content.msgs))},f.onCdnMsgInfo=function(e){if(e){e.cdnUrls&&(e.cdnUrls=e.cdnUrls.split("|")),e.interval&&(e.interval=1e3*s(e.interval)),e.timeout&&(e.timeout=+e.timeout),e.enable&&(e.enable=+e.enable),e.timestamp&&(e.timestamp=+e.timestamp,this.correctCdnTime(e.timestamp));var t=!this.cdnInfo.enable&&e.enable;a(this.cdnInfo,e),t&&this.queryCdnMsgs()}},f.correctCdnTime=function(e){e&&"number"==typeof e&&(this.cdnInfo||(this.cdnInfo={}),this.cdnInfo.timestamp=e,this.cdnInfo.localTime=+new Date,this.cdnInfo.lastNow=null,this.logger.info("correctCdnTime: timestamp, localTime",e,this.cdnInfo.localTime,new Date(e),new Date(this.cdnInfo.localTime)))},f.queryCdnMsgs=function(e){var t,n=this;if(this.cdnInfo&&this.cdnInfo.enable&&this.isConnected()){this.queryCdnTimer=o((function(){return n.queryCdnMsgs()}),this.cdnInfo.interval);var r=+new Date;if(r=r-this.cdnInfo.localTime+this.cdnInfo.timestamp,r-=r%this.cdnInfo.interval,this.cdnInfo.lastNow!==r&&this.cdnInfo.cdnUrls&&this.cdnInfo.cdnUrls.length){this.cdnInfo.cdnUrls.push(this.cdnInfo.cdnUrls.shift());var i=this,a=this.cdnInfo.decryptKey,s=this.cdnInfo.timeout||Math.ceil(this.cdnInfo.interval/2),u=c(t=this.cdnInfo.cdnUrls).call(t,0);e=e||0,function t(){if(e>=3)return void i.finalFail++;if(!u.length)return;var n=u.shift().replace("#time",r),o=n.split("/")[2],c=+new Date;g(n,{timeout:s,onload:function(e){if(i.cdnInfo.lastNow>r)i.logger.warn("queryCdnMsgs::doQuery cdnInfo.lastNow > now, will throw data ",i.cdnInfo.lastNow,r,e);else{i.cdnInfo.lastNow=r,i.afterQueryCdn(o,+new Date-c,!0);try{e=JSON.parse(e)}catch(e){i.logger.warn("queryCdnMsgs::doQuery::onload parse res error: ",e)}if(e&&e.data){e.ptm&&(i.cdnInfo.timeout=e.ptm),e.pis&&(i.cdnInfo.interval=1e3*e.pis);var t,n,s=!0===e.e?(t=e.data,n=a,h.decrypt(t,y.parse(n),{mode:v,padding:b}).toString(T)):e.data;i.onCdnMsgsSmoothly(s,r,1e3*e.c)}}},onerror:function(n){if(e++,i.afterQueryCdn(o,+new Date-c,!1),404===n.status){var r;try{var a=JSON.parse(n.result);404===a.code&&a.timestamp&&(r=a.timestamp)}catch(e){i.logger.warn("queryCdnMsgs::doQuery::parse 404 result error",e,n)}!r&&n.date&&(r=+new Date(n.date)),r&&(i.correctCdnTime(r),clearTimeout(i.queryCdnTimer),i.queryCdnMsgs(e))}else t()}})}()}}},f.onCdnMsgsSmoothly=function(e,t,n){try{e=JSON.parse(e)}catch(e){this.logger.warn("onCdnMsgsSmoothly::JSON.parse error",e)}if(this.logger.info("receive cdn msgs "+e.length+" and request time is "+new Date(t)),u(e)&&e.length){var r=this,o=this.options.msgBufferInterval,a=Math.ceil(e.length*o/n);s(),clearInterval(r.cdnSmoothTimer),r.cdnSmoothTimer=l((function(){return s()}),o)}function s(){if(r.isConnected()){var t=i(e).call(e,0,a);p(t).call(t,(function(e){var t=r.parser.syncUnserialize(e,"msg");r.onMsg({content:{msg:t}})})),e.length||clearInterval(r.cdnSmoothTimer)}}},f.resetCdnData=function(){this.cdnData={},this.finalFail=0,this.cdnDataTime=+new Date},f.initCdnData=function(){this.resetCdnData(),d.isFunction(this.options.onCdnRequestData)?(this.options.cdnRequestDataInterval=this.options.cdnRequestDataInterval||3e4,this.afterQueryCdn=function(e,t,n){var r,o=this;this.cdnData[e]||(this.cdnData[e]={success:0,fail:0,sr:0,fr:0,smr:0,fmr:0}),n?(this.cdnData[e].success++,this.cdnData[e].sr+=t,t>this.cdnData[e].smr&&(this.cdnData[e].smr=t)):(this.cdnData[e].fail++,this.cdnData[e].fr+=t,t>this.cdnData[e].fmr&&(this.cdnData[e].fmr=t)),+new Date-this.cdnDataTime<this.options.cdnRequestDataInterval||(p(r=m(this.cdnData)).call(r,(function(e){o.cdnData[e].sr=o.cdnData[e].sr/o.cdnData[e].success||0,o.cdnData[e].fr=o.cdnData[e].fr/o.cdnData[e].fail||0})),this.options.onCdnRequestData(a({},this.cdnData),this.finalFail),this.resetCdnData())}):this.afterQueryCdn=function(){}}},function(e,t,n){var r;e.exports=(r=n(66),n(486),n(593),n(487),n(221),function(){var e=r,t=e.lib.BlockCipher,n=e.algo,o=[],i=[],a=[],s=[],c=[],u=[],l=[],p=[],m=[],f=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var n=0,r=0;for(t=0;t<256;t++){var d=r^r<<1^r<<2^r<<3^r<<4;d=d>>>8^255&d^99,o[n]=d,i[d]=n;var g=e[n],h=e[g],y=e[h],v=257*e[d]^16843008*d;a[n]=v<<24|v>>>8,s[n]=v<<16|v>>>16,c[n]=v<<8|v>>>24,u[n]=v,v=16843009*y^65537*h^257*g^16843008*n,l[d]=v<<24|v>>>8,p[d]=v<<16|v>>>16,m[d]=v<<8|v>>>24,f[d]=v,n?(n=g^e[e[e[y^g]]],r^=e[e[r]]):n=r=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],g=n.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,n=e.sigBytes/4,r=4*((this._nRounds=n+6)+1),i=this._keySchedule=[],a=0;a<r;a++)a<n?i[a]=t[a]:(u=i[a-1],a%n?n>6&&a%n==4&&(u=o[u>>>24]<<24|o[u>>>16&255]<<16|o[u>>>8&255]<<8|o[255&u]):(u=o[(u=u<<8|u>>>24)>>>24]<<24|o[u>>>16&255]<<16|o[u>>>8&255]<<8|o[255&u],u^=d[a/n|0]<<24),i[a]=i[a-n]^u);for(var s=this._invKeySchedule=[],c=0;c<r;c++){if(a=r-c,c%4)var u=i[a];else u=i[a-4];s[c]=c<4||a<=4?u:l[o[u>>>24]]^p[o[u>>>16&255]]^m[o[u>>>8&255]]^f[o[255&u]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,c,u,o)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,l,p,m,f,i),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,o,i,a,s){for(var c=this._nRounds,u=e[t]^n[0],l=e[t+1]^n[1],p=e[t+2]^n[2],m=e[t+3]^n[3],f=4,d=1;d<c;d++){var g=r[u>>>24]^o[l>>>16&255]^i[p>>>8&255]^a[255&m]^n[f++],h=r[l>>>24]^o[p>>>16&255]^i[m>>>8&255]^a[255&u]^n[f++],y=r[p>>>24]^o[m>>>16&255]^i[u>>>8&255]^a[255&l]^n[f++],v=r[m>>>24]^o[u>>>16&255]^i[l>>>8&255]^a[255&p]^n[f++];u=g,l=h,p=y,m=v}g=(s[u>>>24]<<24|s[l>>>16&255]<<16|s[p>>>8&255]<<8|s[255&m])^n[f++],h=(s[l>>>24]<<24|s[p>>>16&255]<<16|s[m>>>8&255]<<8|s[255&u])^n[f++],y=(s[p>>>24]<<24|s[m>>>16&255]<<16|s[u>>>8&255]<<8|s[255&l])^n[f++],v=(s[m>>>24]<<24|s[u>>>16&255]<<16|s[l>>>8&255]<<8|s[255&p])^n[f++],e[t]=g,e[t+1]=h,e[t+2]=y,e[t+3]=v},keySize:8});e.AES=t._createHelper(g)}(),r.AES)},function(e,t){},function(e,t,n){var r;e.exports=(r=n(66),function(e){var t=r,n=t.lib,o=n.WordArray,i=n.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,o=e[r];e[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,a=e[t+0],c=e[t+1],f=e[t+2],d=e[t+3],g=e[t+4],h=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],T=e[t+9],S=e[t+10],x=e[t+11],k=e[t+12],M=e[t+13],w=e[t+14],_=e[t+15],C=i[0],P=i[1],I=i[2],A=i[3];C=u(C,P,I,A,a,7,s[0]),A=u(A,C,P,I,c,12,s[1]),I=u(I,A,C,P,f,17,s[2]),P=u(P,I,A,C,d,22,s[3]),C=u(C,P,I,A,g,7,s[4]),A=u(A,C,P,I,h,12,s[5]),I=u(I,A,C,P,y,17,s[6]),P=u(P,I,A,C,v,22,s[7]),C=u(C,P,I,A,b,7,s[8]),A=u(A,C,P,I,T,12,s[9]),I=u(I,A,C,P,S,17,s[10]),P=u(P,I,A,C,x,22,s[11]),C=u(C,P,I,A,k,7,s[12]),A=u(A,C,P,I,M,12,s[13]),I=u(I,A,C,P,w,17,s[14]),C=l(C,P=u(P,I,A,C,_,22,s[15]),I,A,c,5,s[16]),A=l(A,C,P,I,y,9,s[17]),I=l(I,A,C,P,x,14,s[18]),P=l(P,I,A,C,a,20,s[19]),C=l(C,P,I,A,h,5,s[20]),A=l(A,C,P,I,S,9,s[21]),I=l(I,A,C,P,_,14,s[22]),P=l(P,I,A,C,g,20,s[23]),C=l(C,P,I,A,T,5,s[24]),A=l(A,C,P,I,w,9,s[25]),I=l(I,A,C,P,d,14,s[26]),P=l(P,I,A,C,b,20,s[27]),C=l(C,P,I,A,M,5,s[28]),A=l(A,C,P,I,f,9,s[29]),I=l(I,A,C,P,v,14,s[30]),C=p(C,P=l(P,I,A,C,k,20,s[31]),I,A,h,4,s[32]),A=p(A,C,P,I,b,11,s[33]),I=p(I,A,C,P,x,16,s[34]),P=p(P,I,A,C,w,23,s[35]),C=p(C,P,I,A,c,4,s[36]),A=p(A,C,P,I,g,11,s[37]),I=p(I,A,C,P,v,16,s[38]),P=p(P,I,A,C,S,23,s[39]),C=p(C,P,I,A,M,4,s[40]),A=p(A,C,P,I,a,11,s[41]),I=p(I,A,C,P,d,16,s[42]),P=p(P,I,A,C,y,23,s[43]),C=p(C,P,I,A,T,4,s[44]),A=p(A,C,P,I,k,11,s[45]),I=p(I,A,C,P,_,16,s[46]),C=m(C,P=p(P,I,A,C,f,23,s[47]),I,A,a,6,s[48]),A=m(A,C,P,I,v,10,s[49]),I=m(I,A,C,P,w,15,s[50]),P=m(P,I,A,C,h,21,s[51]),C=m(C,P,I,A,k,6,s[52]),A=m(A,C,P,I,d,10,s[53]),I=m(I,A,C,P,S,15,s[54]),P=m(P,I,A,C,c,21,s[55]),C=m(C,P,I,A,b,6,s[56]),A=m(A,C,P,I,_,10,s[57]),I=m(I,A,C,P,y,15,s[58]),P=m(P,I,A,C,M,21,s[59]),C=m(C,P,I,A,g,6,s[60]),A=m(A,C,P,I,x,10,s[61]),I=m(I,A,C,P,f,15,s[62]),P=m(P,I,A,C,T,21,s[63]),i[0]=i[0]+C|0,i[1]=i[1]+P|0,i[2]=i[2]+I|0,i[3]=i[3]+A|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var i=e.floor(r/4294967296),a=r;n[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+o+a;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+o+a;return(s<<i|s>>>32-i)+t}function p(e,t,n,r,o,i,a){var s=e+(t^n^r)+o+a;return(s<<i|s>>>32-i)+t}function m(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+o+a;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(c),t.HmacMD5=i._createHmacHelper(c)}(Math),r.MD5)},function(e,t,n){var r,o,i,a,s,c,u,l;e.exports=(l=n(66),o=(r=l).lib,i=o.WordArray,a=o.Hasher,s=r.algo,c=[],u=s.SHA1=a.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],a=n[3],s=n[4],u=0;u<80;u++){if(u<16)c[u]=0|e[t+u];else{var l=c[u-3]^c[u-8]^c[u-14]^c[u-16];c[u]=l<<1|l>>>31}var p=(r<<5|r>>>27)+s+c[u];p+=u<20?1518500249+(o&i|~o&a):u<40?1859775393+(o^i^a):u<60?(o&i|o&a|i&a)-1894007588:(o^i^a)-899497514,s=a,a=i,i=o<<30|o>>>2,o=r,r=p}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(r+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),r.SHA1=a._createHelper(u),r.HmacSHA1=a._createHmacHelper(u),l.SHA1)},function(e,t,n){var r,o,i,a;e.exports=(r=n(66),i=(o=r).lib.Base,a=o.enc.Utf8,void(o.algo.HMAC=i.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),i=this._iKey=t.clone(),s=o.words,c=i.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;o.sigBytes=i.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))},function(e,t,n){var r,o;e.exports=(o=n(66),n(221),o.mode.ECB=((r=o.lib.BlockCipherMode.extend()).Encryptor=r.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),r.Decryptor=r.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),r),o.mode.ECB)},function(e,t,n){var r;e.exports=(r=n(66),n(221),r.pad.Pkcs7)},function(e,t,n){var r;e.exports=(r=n(66),r.enc.Utf8)},function(e,t,n){var r=n(5),o=n(146).fn,i=n(220);o.onChatroomMembersInfo=o.onChatroomMembers=function(e){e.error||(e.obj.members=i.reverseMembers(e.content.members))},o.onGetChatroomMemberCountByTag=function(e){e.error||(e.obj=e.content)},o.onMarkChatroomMember=function(e){e.error||(e.obj.member=r(i).call(i,e.content.chatroomMember))},o.onSyncRobot=function(e){!e.error&&this.options.onrobots?this.options.onrobots(null,e.content):this.ontions.onrobots(e.error,{})}},function(e,t,n){var r=n(5),o=n(8),i=n(0),a=function(e){this.account=e.account},s=a.prototype,c=s.Message=n(91),u=s.TextMessage=n(601),l=s.FileMessage=n(156),p=s.GeoMessage=n(605),m=s.NotificationMessage=n(606),f=s.CustomMessage=n(607),d=s.TipMessage=n(608),g=s.RobotMessage=n(609);s.validTypes=c.validTypes,s.reverse=function(e){var t;switch(c.getType(e)){case"text":t=r(u).call(u,e);break;case"image":case"audio":case"video":case"file":t=r(l).call(l,e);break;case"geo":t=r(p).call(p,e);break;case"notification":t=r(m).call(m,e);break;case"custom":t=r(f).call(f,e);break;case"tip":t=r(d).call(d,e);break;case"robot":t=r(g).call(g,e);break;default:t=r(c).call(c,e)}return c.setExtra(t,this.account),t},s.reverseMsgs=function(e,t){var n,a,s=this;return o(e).call(e,(function(e){return e=r(s).call(s,e),t&&((n=t.modifyObj)&&(e=i.merge(e,n)),a=t.mapper,i.isFunction(a)&&(e=a(e))),e}))},e.exports=a},function(e,t,n){var r=n(14),o=n(5),i=n(91),a=n(0);function s(e){a.verifyOptions(e,"text","msg::TextMessage"),e.type="text",i.call(this,e),this.attach=e.text,this.body=""}s.prototype=r(i.prototype),s.reverse=function(e){var t=o(i).call(i,e);return t.text=e.attach,t},e.exports=s},function(e,t,n){var r=n(14),o=n(0),i=n(156);function a(){}a.prototype=r(i.prototype),a.verifyFile=function(e,t){o.verifyOptions(e,"w h",!0,"file.",t)},e.exports=a},function(e,t,n){var r=n(14),o=n(156),i=n(0);function a(){}a.prototype=r(o.prototype),a.verifyFile=function(e,t){i.verifyOptions(e,"dur",!0,"file.",t)},e.exports=a},function(e,t,n){var r=n(14),o=n(156),i=n(0);function a(){}a.prototype=r(o.prototype),a.verifyFile=function(e,t){i.verifyOptions(e,"dur w h",!0,"file.",t)},e.exports=a},function(e,t,n){var r=n(12),o=n(14),i=n(5),a=n(91),s=n(0);function c(e){e.type="geo",s.verifyOptions(e,"geo","msg::GeoMessage"),s.verifyOptions(e.geo,"lng lat title",!0,"geo.","msg::GeoMessage"),s.verifyParamType("geo.lng",e.geo.lng,"number","msg::GeoMessage"),s.verifyParamType("geo.lat",e.geo.lat,"number","msg::GeoMessage"),s.verifyParamType("geo.title",e.geo.title,"string","msg::GeoMessage"),a.call(this,e),this.attach=r(e.geo)}c.prototype=o(a.prototype),c.reverse=function(e){var t=i(a).call(a,e);return e.attach=e.attach?""+e.attach:"",t.geo=e.attach?JSON.parse(e.attach):{},t},e.exports=c},function(e,t,n){var r=n(14),o=n(5),i=n(51),a=n(0).notundef,s=n(91),c={301:"memberEnter",302:"memberExit",303:"blackMember",304:"unblackMember",305:"gagMember",306:"ungagMember",307:"addManager",308:"removeManager",309:"addCommon",310:"removeCommon",311:"closeChatroom",312:"updateChatroom",313:"kickMember",314:"addTempMute",315:"removeTempMute",316:"updateMemberInfo",317:"updateQueue",318:"muteRoom",319:"unmuteRoom",320:"batchUpdateQueue",321:"addTempMuteTag",322:"removeTempMuteTag",324:"batchQueueOffer",323:"deleteChatroomMsg",325:"updateChatroomTags"};function u(){}u.prototype=r(s.prototype),u.reverse=function(e){var t=o(s).call(s,e);if(e.attach=e.attach?""+e.attach:"",e.attach){var n=JSON.parse(e.attach);if(t.attach={type:c[n.id]},a(n.data)){var r=n.data;if(a(r.operator)&&(t.attach.from=r.operator),a(r.opeNick)&&(t.attach.fromNick=r.opeNick),a(r.target)&&(t.attach.to=r.target),a(r.tarNick)&&(t.attach.toNick=r.tarNick),a(r.muteDuration)&&(t.attach.duration=i(r.muteDuration,10)),"memberEnter"===t.attach.type&&(a(r.muted)?t.attach.gaged=1==+r.muted:t.attach.gaged=!1,a(r.tempMuted)?t.attach.tempMuted=1==+r.tempMuted:t.attach.tempMuted=!1,a(r.muteTtl)?t.attach.tempMuteDuration=+r.muteTtl:t.attach.tempMuteDuration=0),"deleteChatroomMsg"===t.attach.type&&(t.attach.msgId=r.msgId,t.attach.msgTime=r.msgTime),a(r.ext)&&(t.attach.custom=r.ext),a(r.queueChange)){var u=JSON.parse(r.queueChange);switch(u._e){case"OFFER":t.attach.queueChange={type:"OFFER",elementKey:u.key,elementValue:u.content};break;case"POLL":t.attach.queueChange={type:"POLL",elementKey:u.key,elementValue:u.content};break;case"DROP":t.attach.queueChange={type:"DROP"};break;case"PARTCLEAR":case"BATCH_UPDATE":t.attach.queueChange={type:u._e,elementKv:u.kvObject};break;case"BATCH_OFFER":t.attach.queueChange={type:u._e,elements:u.elements}}}}}else t.attach={};return t},e.exports=u},function(e,t,n){var r=n(12),o=n(14),i=n(5),a=n(91),s=n(0);function c(e){s.verifyOptions(e,"content","msg::CustomMessage"),e.type="custom",a.call(this,e),"string"!=typeof e.content&&(e.content=r(e.content)),this.attach=e.content}c.prototype=o(a.prototype),c.reverse=function(e){var t=i(a).call(a,e);return t.content=e.attach,t},e.exports=c},function(e,t,n){var r=n(14),o=n(5),i=n(91),a=n(0);function s(e){a.verifyOptions(e,"tip","msg::TipMessage"),e.type="tip",i.call(this,e),this.attach=e.tip}s.prototype=r(i.prototype),s.reverse=function(e){var t=o(i).call(i,e);return t.tip=e.attach,t},e.exports=s},function(e,t,n){var r=n(12),o=n(14),i=n(5),a=n(8),s=n(91),c=n(0),u={welcome:"00",text:"01",link:"03"},l={"01":"text","02":"image","03":"answer",11:"template"};function p(e){c.verifyOptions(e,"content","msg::RobotMessage");var t=e.content;switch(t.type){case"welcome":c.undef(e.body)&&(this.body="欢迎消息");break;case"text":c.verifyOptions(t,"content","msg::RobotMessage"),c.undef(e.body)&&(this.body=t.content);break;case"link":c.verifyOptions(t,"target","msg::RobotMessage")}t.type&&(t.type=u[t.type]),t={param:t,robotAccid:e.robotAccid},this.attach=r(t),e.type="robot",s.call(this,e)}p.prototype=o(s.prototype),p.reverse=function(e){var t=i(s).call(s,e);if("robot"===t.type){var n=JSON.parse(e.attach);if(n.param&&(n.param.type=l[n.param.type]||"unknown"),n.robotMsg){var r=(n=c.merge(n,n.robotMsg)).message;"bot"===n.flag?n.message=a(r).call(r,(function(e){return e.type=l[e.type]||"unknown",e})):n.flag,delete n.robotMsg}t.content=n}return t},e.exports=p},function(e,t,n){var r=n(5),o=n(30),i=n(8),a=n(19),s=n(0),c=s.undef,u=n(155).fn;u.beforeSendMsg=function(e){e.cmd="sendMsg"};var l={text:0,image:1,audio:2,video:3,geo:4,notification:5,file:6,tip:10,robot:11,custom:100};u.getHistoryMsgs=function(e){if(s.verifyOptions(e),c(e.timetag)?e.timetag=0:s.verifyParamType("timetag",e.timetag,"number","msg::getHistoryMsgs"),c(e.limit)?e.limit=100:s.verifyParamType("limit",e.limit,"number","msg::getHistoryMsgs"),c(r(e))?e.reverse=!1:s.verifyParamType("reverse",r(e),"boolean","msg::getHistoryMsgs"),c(e.msgTypes))e.msgTypes=[];else if(o(e.msgTypes)){var t,n;e.msgTypes=i(t=e.msgTypes).call(t,(function(e){return l[e]})),e.msgTypes=a(n=e.msgTypes).call(n,(function(e){return"number"==typeof e}))}else"number"==typeof l[e.msgTypes]?e.msgTypes=[l[e.msgTypes]]:e.msgTypes=[];this.processCallback(e),this.sendCmd("getHistoryMsgs",e,(function(t,n,r){o(r)&&(r=i(r).call(r,(function(e){return l[e.type]&&(e.type=l[e.type]),e}))),e.callback(t,n,r)}))}},function(e,t,n){var r=n(0),o=r.undef,i=r.verifyOptions,a=r.verifyParamType,s=n(220),c=n(155).fn;c.updateMyChatroomMemberInfo=function(e){i(e,"member needNotify","member::updateMyChatroomMemberInfo"),a("needNotify",e.needNotify,"boolean","member::updateMyChatroomMemberInfo"),e.needSave=e.needSave||!1,a("needSave",e.needSave,"boolean","member::updateMyChatroomMemberInfo"),this.processCustom(e),this.processCallback(e),e.chatroomMember=new s(e.member),e.antispamTag={antiSpamBusinessId:e.antiSpamBusinessId},this.sendCmd("updateMyChatroomMemberInfo",e)},c.getChatroomMembers=function(e){i(e,"guest","member::getChatroomMembers"),a("guest",e.guest,"boolean","member::getChatroomMembers"),o(e.time)?e.time=0:a("time",e.time,"number","member::getChatroomMembers"),o(e.limit)?e.limit=100:a("limit",e.limit,"number","member::getChatroomMembers"),this.processCallback(e),e.guest?e.type=!1===e.desc?3:1:e.type=e.onlyOnline?2:0,this.sendCmd("getChatroomMembers",e)},c.getChatroomMembersByTag=function(e){i(e,"tag","getChatroomMembersByTag::rag"),o(e.time)?e.time=0:a("time",e.time,"number","getChatroomMembersByTag::time"),o(e.limit)?e.limit=100:a("limit",e.limit,"number","getChatroomMembersByTag::limit"),this.processCallback(e),this.sendCmd("getChatroomMembersByTag",{tagMemberReq:r.filterObj(e,"tag limit time"),callback:e.callback})},c.getChatroomMemberCountByTag=function(e){i(e,"tag","getChatroomMemberCountByTag::rag"),this.processCallback(e),this.sendCmd("getChatroomMemberCountByTag",e)},c.getChatroomMembersInfo=function(e){i(e,"accounts","member::getChatroomMembersInfo"),a("accounts",e.accounts,"array","member::getChatroomMembersInfo"),this.processCallback(e),this.sendCmd("getChatroomMembersInfo",e)},c.markChatroomIdentity=function(e){i(e,"identity","member::markChatroomIdentity"),e.type={manager:1,common:2,black:-1,mute:-2}[e.identity],delete e.identity,isNaN(e.type)?i(e,"identity",'member::markChatroomIdentity. The valid value of the identity is "manager" or "common" or "black" or "mute".'):this.markChatroomMember(e)},c.markChatroomManager=function(e){e.type=1,this.markChatroomMember(e)},c.markChatroomCommonMember=function(e){e.type=2,this.markChatroomMember(e)},c.markChatroomBlacklist=function(e){e.type=-1,this.markChatroomMember(e)},c.markChatroomGaglist=function(e){e.type=-2,this.markChatroomMember(e)},c.markChatroomMember=function(e){i(e,"account type isAdd","member::markChatroomMember"),a("isAdd",e.isAdd,"boolean","member::markChatroomMember"),o(e.level)?e.level=0:a("level",e.level,"number","member::markChatroomMember");this.processCustom(e),this.processCallback(e),this.sendCmd("markChatroomMember",e)},c.kickChatroomMember=function(e){i(e,"account","member::kickChatroomMember"),this.processCustom(e),this.processCallback(e),this.sendCmd("kickChatroomMember",e)},c.updateChatroomMemberTempMute=function(e){i(e,"account duration needNotify","member::updateChatroomMemberTempMute"),a("duration",e.duration,"number","member::updateChatroomMemberTempMute"),a("needNotify",e.needNotify,"boolean","member::updateChatroomMemberTempMute"),this.processCustom(e),this.processCallback(e),this.sendCmd("updateChatroomMemberTempMute",e)},c.updateTagMembersTempMute=function(e){i(e,"tag duration needNotify","member::updateTagMembersTempMute"),a("duration",e.duration,"number","member::updateTagMembersTempMute"),a("needNotify",e.needNotify,"boolean","member::updateTagMembersTempMute"),this.processCustom(e),this.processCallback(e);var t=r.filterObj(e,"tag duration custom notifyTargetTags");t.needNotify=e.needNotify?1:0,this.sendCmd("updateTagMembersTempMute",{tagMuteReq:t,callback:e.callback})},c.updateCoordinate=r.throttle((function(e){var t=!0;void 0===e.x||void 0===e.y||void 0===e.z?t=!1:("number"!=typeof e.x&&(e.x=0),"number"!=typeof e.y&&(e.y=0),"number"!=typeof e.z&&(e.z=0)),this.processCallback(e);var n=r.filterObj(e,t?"x y z distance":"distance");this.sendCmd("updateCoordinate",{coordinateReq:n,callback:e.callback})}),300,{},(function(e){e&&e.done&&e.done({code:416,chatroomId:this.options.chatroomId})})),c.getRobotList=function(e){this.logger.warn("this api will be abandon");o(e.timetag)&&(e.timetag=0),this.processCallback(e),this.sendCmd("syncRobot",e)}},function(e,t,n){var r=n(155).fn,o=n(0);r.queueOffer=function(e){o.verifyOptions(e,"elementKey elementValue","msg::queueOffer");var t={elementKey:e.elementKey,elementValue:e.elementValue,transient:!!e.transient};e.elementAccount&&(t.elementAccount=e.elementAccount),this.processCallback(e),this.sendCmd("queueOffer",t,e.callback)},r.queuePoll=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.elementKey=e.elementKey||"",this.processCallback(e),this.sendCmd("queuePoll",e,e.callback)},r.queueList=function(e){this.processCallback(e),this.sendCmd("queueList",e,e.callback)},r.peak=function(e){this.processCallback(e),this.sendCmd("peak",e,e.callback)},r.queueDrop=function(e){this.processCallback(e),this.sendCmd("queueDrop",e,e.callback)},r.queueChange=function(e){o.verifyOptions(e,"elementMap","msg::queueOffer"),e.needNotify?(e.needNotify=!0,o.verifyOptions(e,"notifyExt","msg::queueOffer")):e.needNotify=!1,this.processCallback(e),this.sendCmd("queueChange",e,e.callback)}},,function(e,t,n){e.exports=n(587)}])}));