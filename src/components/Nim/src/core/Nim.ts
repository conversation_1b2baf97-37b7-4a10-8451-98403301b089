// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import * as SDK from "../library/NIM_Web_NIM_v9.0.1.js";
import merge from "lodash.merge";
import { computed, ComputedRef, reactive } from "vue";
import { messageDistributionController } from "../controllers";
import { PubSub } from "../utils/PubSub";
import { NimMember } from "../types/member";
import { NimEvent, NimInitOptions, NimSendMeesageMethod } from "../types/nim";
import {
  Message,
  MessagePayload,
  MessageScene,
  ResendMessagePayload,
  TextMessagePayload,
} from "../types/message";

export default class Nim {
  /** Nim 实例 */
  private nimInstance: SDK;

  /** 发布/订阅实例 */
  private pubsubInstance = new PubSub<NimEvent>();

  /** 当前群id */
  private currentTeamId: string | null;

  /** 当前发送方头像 */
  private currentFormAvatar: string | null;

  /** 消息记录列表 */
  private messageRecords = reactive<Record<string, Message>>({});

  /** 成员列表 */
  private members = reactive<Record<string, NimMember>>({});

  /** 渲染消息列表 */
  get renderMessages() {
    return computed(() =>
      Object.values(this.messageRecords).sort((a, b) => a.time - b.time)
    );
  }

  /** 渲染成员列表 */
  get renderMembers(): ComputedRef<NimMember[]> {
    console.log(this.members, this.nimInstance, "this.members");
    return computed(() =>
      Object.values(this.members).sort((a, b) => a.time - b.time)
    );
  }

  /**
   * @description 初始化
   * @param {NimInitOptions} options
   */
  init(options: NimInitOptions) {
    this.currentTeamId = options.teamId;
    this.currentFormAvatar = options.formAvatar;

    const mergerOptions = merge(options, {
      onconnect: this.onConnect.bind(this),
      ondisconnect: this.onDisconnect.bind(this),
      onwillreconnect: this.onWillReconnect.bind(this),
      onerror: this.onError.bind(this),
      onmsg: this.onMsg.bind(this),
    });
    console.log(mergerOptions, "SDK");

    this.nimInstance = SDK.getInstance(mergerOptions);
  }

  /**
   * @description 添加成员
   * @param {NimMember} member
   */
  addMember(uid: string, member: NimMember) {
    this.members[uid] = member;
  }
  /**
   * @description 解散群
   * @param ""
   */
  dismissTeam(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      let res: any = API.netEaseYunXinApi.deleteTeam.request({
        bodyParams: {
          Id: parseInt(this.currentTeamId as string),
        },
      });
      resolve(res);
    });
  }
  /**
   * @description 删除成员
   * @param {string} uid
   */
  delMember(uid: string) {
    delete this.members[uid];
  }

  /**
   * @description 发送文本消息
   * @param {TextMessagePayload} payload
   */
  sendText(payload: TextMessagePayload) {
    return this.sendMeesage(NimSendMeesageMethod.SEND_TEXT, payload);
  }

  /**
   * @description 消息重发
   * @param {ResendMessagePayload} payload
   */
  resendMessage(payload: ResendMessagePayload) {
    return this.sendMeesage(NimSendMeesageMethod.RESEND_MSG, payload as any);
  }

  /**
   * @description 监听事件
   * @param {NimEvent} event
   * @param {function} handler
   */
  on<T extends NimEvent>(event: T, handler: (...any: any[]) => void): void {
    this.pubsubInstance.on(event, handler);
  }

  /**
   * @description 取消事件监听
   * @param {NimEvent} event
   * @param {function} handler
   */
  off<T extends NimEvent>(event: T, handler: (...any: any[]) => void) {
    this.pubsubInstance.off(event, handler);
  }

  /**
   * @description 发送消息
   * @param {NimSendMeesageMethod} sendMeesageMethod
   * @param {MessagePayload} payload
   * @param {boolean} isDistribution
   */
  private sendMeesage(
    sendMeesageMethod: NimSendMeesageMethod,
    payload: MessagePayload,
    isDistribution = true
  ) {
    if (!this.nimInstance) {
      return;
    }

    const { done } = payload;
    // 默认消息体
    const defPayload = {
      to: this.currentTeamId,
      scene: MessageScene.TEAM,
      custom: { formAvatar: this.currentFormAvatar },
    };
    // 覆盖的消息体
    const coverPayload = {
      done: (error: any, message: Message) => {
        !error
          ? console.log("消息发送成功", message)
          : console.log("消息发送失败1", error);
        // 有回调则调用
        done && done(error, message);
        // 是否需要消息分发
        isDistribution &&
          messageDistributionController({
            messageRecords: this.messageRecords,
            message,
          });
      },
    };
    // 合并消息体
    const mergePayload = merge(defPayload, payload, coverPayload);
    // 发送消息
    const message = this.nimInstance[sendMeesageMethod](mergePayload);
    console.log("消息发送中", message);
    // 是否需要消息分发
    isDistribution &&
      messageDistributionController({
        messageRecords: this.messageRecords,
        message,
      });
    // 发布发送消息事件
    this.pubsubInstance.emit(NimEvent.SEND_MSG, message);
  }

  /**
   * @description 退出登录
   */
  logout() {
    this.nimInstance?.logout();
    this.messageRecords = reactive({});
    this.members = reactive({});
    this.currentTeamId = null;
    this.currentFormAvatar = null;
    this.nimInstance = null;
  }

  /**
   * @description 连接成功
   * @param {any} event
   */
  private onConnect(event: any) {
    console.log("连接成功", event);
    // 发布连接成功事件
    this.pubsubInstance.emit(NimEvent.CONNECT, event);
  }

  /**
   * @description 重新连接
   * @param {any} error
   */
  private onWillReconnect(error: any) {
    console.log("重新连接", error);
    // 发布重新连接事件
    this.pubsubInstance.emit(NimEvent.WILLRECONNECT, error);
  }

  /**
   * @description 断开连接
   * @param {any} error
   */
  private onDisconnect(error: any) {
    console.log("断开连接", error);
    // 发布断开连接事件
    this.pubsubInstance.emit(NimEvent.DISCONNECT, error);
  }

  /**
   * @description 连接错误
   * @param {any} error
   */
  private onError(error: any) {
    console.log("连接错误", error);
    // 发布连接错误事件
    this.pubsubInstance.emit(NimEvent.ERROR, error);
  }

  /**
   * @description 接收消息
   * @param {Message} message
   */
  private onMsg(message: Message) {
    const isDispose = this.currentTeamId === message.target;
    console.log(
      `接收消息${!isDispose && "（该消息不属于当前群组，直接丢弃）"}`,
      message
    );

    if (isDispose) {
      // 消息分发
      messageDistributionController({
        messageRecords: this.messageRecords,
        message,
      });
      // 发布接收消息事件
      this.pubsubInstance.emit(NimEvent.MSG, message);
    }
  }
}
