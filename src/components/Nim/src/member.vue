<template>
  <div>
    <div class="member-admin">
      <div class="member">
        <div class="member-left">
          <div class="member-icon">
            <iconpark-icon name="ziyuan14"></iconpark-icon>
          </div>
          <div class="member-name">成员管理</div>
        </div>
        <van-icon @click="memberShowClick" color="#8C8C8C" name="arrow-left" />
      </div>
      <div class="member-search">
        <van-search v-model="searchValue" :clearable="false" class="input-search" @update:model-value="searchClick">
          <template #left-icon>
            <div>
              <iconpark-icon class="iconpark" name="weibiaoti-1huaban1fuben7"></iconpark-icon>
            </div>
          </template>
        </van-search>
      </div>
      <div v-for="(item, index) in memberList" :key="index" class="member-bottom">
        <div class="member-bottom-left">
          <van-image class="member-image" :src="(item.avatar)" :show-loading="false" :show-error="false" />
          <div>
            <div class="member-bottom-name">{{ item.nickname }}</div>
            <div v-if="userInfo && userInfo.RealName === item.nickname" class="member-bottom-phone">(主持人，我)</div>
          </div>
        </div>
        <!-- <div class="member-bottom-right" @click="show = true">邀请加入</div> -->
        <div v-if="userInfo && userInfo.RealName !== item.nickname" class="member-bottom-right"
          @click="removeConference(item)"></div>
        <div v-else class="member-bottom-right" @click="onInvite">复制链接</div>
      </div>
    </div>
    <van-popup v-model:show="show" position="bottom" :style="{ height: '20%' }">
      <div class="popup-name">邀请加入</div>
      <van-grid class="home-grid" :border="false">
        <van-grid-item>
          <div class="iconpark-class">
            <van-icon class="home-grid-image1" name="wechat" />
          </div>

          <div class="home-grid-name">分享到微信</div>
        </van-grid-item>
        <van-grid-item>
          <div class="iconpark-class">
            <iconpark-icon class="home-grid-image" name="weibiaoti-1huaban1fuben4"></iconpark-icon>
          </div>
          <div class="home-grid-name">分享到企业</div>
        </van-grid-item>
        <van-grid-item @click="onInvite">
          <div class="iconpark-class">
            <iconpark-icon class="home-grid-image" name="weibiaoti-1huaban1fuben30"></iconpark-icon>
          </div>
          <div class="home-grid-name">复制链接</div>
        </van-grid-item>
        <van-grid-item>
          <div class="iconpark-class">
            <iconpark-icon class="home-grid-image" name="weibiaoti-1huaban1fuben2"></iconpark-icon>
          </div>
          <div class="home-grid-name">发送通知</div>
        </van-grid-item>
      </van-grid>
    </van-popup>
  </div>
</template>

<script lang="ts" setup>
import clipboard from 'vue-clipboard3'
import { PropType, ref } from "vue";
import { useNim } from './hooks/useNim'
import { useNertc } from '@/components/Nertc/src/hooks/useNertc'
import { NimMember } from './types/member';
import { getLocalStorage, LocalStorageName } from '@/utils/storage';
const nim = useNim()
const { toClipboard } = clipboard()
const emits = defineEmits(['update:memberShow', 'invitation'])
const userInfo = getLocalStorage(LocalStorageName.user);
//弹出层
const show = ref(false)
const nertc = useNertc()
const searchValue = ref('')
watch(
  () => searchValue.value,
  (val) => {
    if (!val) {
      memberList.value = nim.renderMembers.value
    }
  }
)
//成员列表List
const memberList = ref<NimMember[]>([])
memberList.value = nim.renderMembers.value
//搜索
const searchClick = (val: string) => {
  //模糊搜索inputValues
  memberList.value = nim.renderMembers.value.filter((item) => {
    return item.nickname.indexOf(val) !== -1
  })
}
//关闭成员列表
const memberShowClick = () => {
  emits('update:memberShow', false)
}
// 复制邀请链接
const onInvite = () => {
  emits('invitation', toClipboard)
}

//移除会议
const removeConference = (item: NimMember) => {
  // nim.delMember(item.uid)
  // nertc.deleteMember(item.uid)
}

</script>

<style lang="scss" scoped>
.member-admin {
  margin: 0 14px;
}

.member {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 9px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
}

.member-left {
  display: flex;
  align-items: center;
}

.member-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.member-name {
  margin-left: 5px;
  margin-top: 2px;
}

.member-search {
  box-sizing: border-box;
  padding: 7px 14px;
  height: 46px;
  background: #E6ECFB;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
}

.input-search {
  height: 100%
}

.iconpark {
  margin-top: 7px;
}

.input-search {
  width: 100%;
  padding: 0;
  line-height: 32px;
  display: inline-block;
  font-size: 14px;
  border-radius: 16px;
  overflow: hidden;
  // margin-left: 14px;
}

.member-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  height: 52px;
  background: #FFFFFF;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border-bottom: 1px solid #F1F1F1;
}

.member-image {
  margin-right: 6px;
  width: 32px;
  height: 32px;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #E6ECFB;
}

.member-bottom-left {
  display: flex;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #343840;
}

.member-bottom-phone {
  margin-top: 2px;
  color: #8B96A6;
}

.member-bottom-right {
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #650FAA;
}

.popup-name {
  margin: 18px 0 0 12px;
  font-size: 14px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
}

.home-grid-name {
  margin-top: 5px;
  font-size: 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #494949;
}

.iconpark-class {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #E6ECFB;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 20px;
  color: #8B96A6;
}

.home-grid-image {
  color: #8B96A6;
}

.home-grid-image1 {
  color: #00C800;
}
</style>
