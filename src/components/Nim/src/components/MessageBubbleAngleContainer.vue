<!--
 * @description 消息气泡角标容器
-->

<script setup lang="ts">
  import { useMessageContext } from './MessageProvider.vue'

  const { message } = useMessageContext()
</script>

<template>
  <div class="message-bubble-angle-container" :class="message.flow">
    <slot v-bind="$attrs"></slot>
  </div>
</template>

<style lang="scss" scoped>
  .message-bubble-angle-container {
    position: relative;
    border-radius: 4px;

    &:before,
    &:after {
      display: block;
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      top: 12px;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
    }

    &.in {
      background: #e6e3e7;
      &:before {
        left: -8px;
        border-right: 8px solid #e6e3e7;
      }
    }
    &.out {
      background: #F0E7F7;
      &:after {
        right: -8px;
        border-left: 8px solid #F0E7F7;
      }
    }
}
</style>
