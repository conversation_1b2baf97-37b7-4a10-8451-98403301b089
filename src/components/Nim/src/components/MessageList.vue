<!--
 * @description 消息列表
-->

<script setup lang="ts">
import MessageProvider from "./MessageProvider.vue";
import MessageBubble from "./MessageBubble.vue";
import { Message } from "../types/message";
import { NimEvent } from "../types/nim";
import { useNim } from "../hooks/useNim";
import { nextTick, onMounted, onUnmounted, ref } from "vue";

const props = defineProps<{
  messageList: Message[];
}>();

const nim = useNim();
const elScrollbarRef = ref();

// 消息列表内容器元素
const messageListInnerRef = ref();

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    const outerH = elScrollbarRef.value.offsetHeight;
    const innerH = messageListInnerRef.value.offsetHeight;
    const scrollTop = Math.abs(outerH - innerH);
    elScrollbarRef.value.scrollTop = scrollTop;
  });
};

// 处理接受到消息
const onMsgHandler = () => {
  const outerH = elScrollbarRef.value.offsetHeight;
  const scrollTop = elScrollbarRef.value.childNodes[0].scrollTop;
  const innerH = messageListInnerRef.value.offsetHeight;
  // 如果滚动条距离底部为可视区域的一半则自动滚动到底部
  if (innerH - (scrollTop + outerH) <= outerH / 2) {
    scrollToBottom();
  }
};

// 处理发送消息
const onSendMsgHandler = () => {
  scrollToBottom();
};

// 监听接收到消息
nim.on(NimEvent.MSG, onMsgHandler);
// 监听发送消息
nim.on(NimEvent.SEND_MSG, onSendMsgHandler);

onMounted(scrollToBottom);

onUnmounted(() => {
  nim.off(NimEvent.MSG, onMsgHandler);
  nim.off(NimEvent.SEND_MSG, onSendMsgHandler);
});

// const messageHeight = ref('442px')
</script>

<template>
  <div ref="elScrollbarRef" class="message-list">
    <div ref="messageListInnerRef" class="message-list-inner">
      <MessageProvider
        v-for="(message, index) in props.messageList"
        :key="message.idClient"
        :message="message"
        :prev-message="props.messageList[index - 1]"
      >
        <MessageBubble />
      </MessageProvider>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .message-list-inner {
    padding: 6px 0;
  }
}
</style>
