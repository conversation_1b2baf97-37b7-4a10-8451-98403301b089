<!--
 * @description 消息列表
-->

<script setup lang="ts">
import MessageProvider from "./MessageProvider.vue";
import MessageBubble from "./MessageBubble.vue";
import { Message } from "../types/message";
import { NimEvent } from "../types/nim";
import { useNim } from "../hooks/useNim";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  messageList: Message[];
}>();

const nim = useNim();
const elScrollbarRef = ref();

// 消息列表内容器元素
const messageListInnerRef = ref();

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (elScrollbarRef.value) {
      // 方法1：设置scrollTop为scrollHeight
      elScrollbarRef.value.scrollTop = elScrollbarRef.value.scrollHeight;

      // 方法2：使用scrollIntoView作为备选（如果方法1不工作）
      setTimeout(() => {
        if (messageListInnerRef.value) {
          const lastMessage = messageListInnerRef.value.lastElementChild;
          if (lastMessage) {
            lastMessage.scrollIntoView({ behavior: "smooth", block: "end" });
          }
        }
      }, 50);
    }
  });
};

// 检查是否应该自动滚动到底部
const shouldAutoScroll = () => {
  if (!elScrollbarRef.value) return true;

  const scrollTop = elScrollbarRef.value.scrollTop;
  const scrollHeight = elScrollbarRef.value.scrollHeight;
  const clientHeight = elScrollbarRef.value.clientHeight;

  // 如果用户滚动到接近底部（距离底部小于50px），则自动滚动
  return scrollHeight - scrollTop - clientHeight < 50;
};

// 处理接受到消息
const onMsgHandler = () => {
  // 接收到新消息时，如果用户在底部附近，则自动滚动到底部
  // 使用setTimeout确保DOM更新完成
  setTimeout(() => {
    if (shouldAutoScroll()) {
      scrollToBottom();
    }
  }, 10);
};

// 处理发送消息
const onSendMsgHandler = () => {
  // 发送消息时总是滚动到底部
  setTimeout(() => {
    scrollToBottom();
  }, 10);
};

// 监听接收到消息
nim.on(NimEvent.MSG, onMsgHandler);
// 监听发送消息
nim.on(NimEvent.SEND_MSG, onSendMsgHandler);

// 监听消息列表变化，当有新消息时检查是否需要滚动
watch(
  () => props.messageList.length,
  () => {
    // 当消息列表长度变化时，检查是否需要自动滚动
    setTimeout(() => {
      if (shouldAutoScroll()) {
        scrollToBottom();
      }
    }, 10);
  }
);

onMounted(scrollToBottom);

onUnmounted(() => {
  nim.off(NimEvent.MSG, onMsgHandler);
  nim.off(NimEvent.SEND_MSG, onSendMsgHandler);
});

// const messageHeight = ref('442px')
</script>

<template>
  <div ref="elScrollbarRef" class="message-list">
    <div ref="messageListInnerRef" class="message-list-inner">
      <MessageProvider
        v-for="(message, index) in props.messageList"
        :key="message.idClient"
        :message="message"
        :prev-message="props.messageList[index - 1]"
      >
        <MessageBubble />
      </MessageProvider>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .message-list-inner {
    padding: 6px 0;
  }
}
</style>
