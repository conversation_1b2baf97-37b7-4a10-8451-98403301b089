<!--
 * @description 消息列表
-->

<script setup lang="ts">
import MessageProvider from "./MessageProvider.vue";
import MessageBubble from "./MessageBubble.vue";
import { Message } from "../types/message";
import { NimEvent } from "../types/nim";
import { useNim } from "../hooks/useNim";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

const props = defineProps<{
  messageList: Message[];
}>();

const nim = useNim();
const elScrollbarRef = ref();

// 消息列表内容器元素
const messageListInnerRef = ref();

// 滚动到底部
const scrollToBottom = () => {
  console.log("开始执行滚动到底部");
  nextTick(() => {
    if (elScrollbarRef.value) {
      const scrollHeight = elScrollbarRef.value.scrollHeight;
      const clientHeight = elScrollbarRef.value.clientHeight;
      console.log(
        `滚动信息: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}`
      );

      // 方法1：强制设置scrollTop为最大值
      const maxScrollTop = scrollHeight - clientHeight;
      elScrollbarRef.value.scrollTop = maxScrollTop;
      console.log(`设置scrollTop为: ${maxScrollTop}`);

      // 方法2：再次确保滚动到底部
      setTimeout(() => {
        if (elScrollbarRef.value) {
          elScrollbarRef.value.scrollTop = elScrollbarRef.value.scrollHeight;
          console.log(
            `再次设置scrollTop为: ${elScrollbarRef.value.scrollHeight}`
          );
        }
      }, 10);

      // 方法3：使用scrollIntoView作为最终备选
      setTimeout(() => {
        if (messageListInnerRef.value) {
          const lastMessage = messageListInnerRef.value.lastElementChild;
          if (lastMessage) {
            console.log("使用scrollIntoView滚动到最后一条消息");
            lastMessage.scrollIntoView({ behavior: "auto", block: "end" });
          }
        }
      }, 20);
    } else {
      console.log("elScrollbarRef.value 为空，无法滚动");
    }
  });
};

// 处理接受到消息
const onMsgHandler = (message: Message) => {
  console.log("收到新消息，准备滚动到底部:", message);
  // 接收到新消息时，总是滚动到底部
  // 使用setTimeout确保DOM更新完成
  setTimeout(() => {
    console.log("执行滚动到底部");
    scrollToBottom();
  }, 100); // 增加延迟时间确保DOM完全更新
};

// 处理发送消息
const onSendMsgHandler = () => {
  console.log("发送消息，准备滚动到底部");
  // 发送消息时总是滚动到底部
  setTimeout(() => {
    console.log("发送消息后执行滚动到底部");
    scrollToBottom();
  }, 100); // 增加延迟时间确保DOM完全更新
};

// 监听接收到消息
nim.on(NimEvent.MSG, onMsgHandler);
// 监听发送消息
nim.on(NimEvent.SEND_MSG, onSendMsgHandler);

// 监听消息列表变化，当有新消息时自动滚动到底部
watch(
  () => props.messageList.length,
  (newLength, oldLength) => {
    console.log(`消息列表长度变化: ${oldLength} -> ${newLength}`);
    // 当消息列表长度变化时，总是滚动到底部
    setTimeout(() => {
      console.log("消息列表变化后执行滚动到底部");
      scrollToBottom();
    }, 150); // 更长的延迟确保所有DOM更新完成
  }
);

onMounted(scrollToBottom);

onUnmounted(() => {
  nim.off(NimEvent.MSG, onMsgHandler);
  nim.off(NimEvent.SEND_MSG, onSendMsgHandler);
});

// const messageHeight = ref('442px')
</script>

<template>
  <div ref="elScrollbarRef" class="message-list">
    <div ref="messageListInnerRef" class="message-list-inner">
      <MessageProvider
        v-for="(message, index) in props.messageList"
        :key="message.idClient"
        :message="message"
        :prev-message="props.messageList[index - 1]"
      >
        <MessageBubble />
      </MessageProvider>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-list {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }

  .message-list-inner {
    padding: 6px 0;
  }
}
</style>
