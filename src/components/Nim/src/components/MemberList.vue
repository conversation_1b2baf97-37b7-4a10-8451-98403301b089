<!--
 * @description 成员列表
-->

<script setup lang="ts">
  import { NimMember } from '../types/member'

  const props = defineProps<{
    memberList: NimMember[],
  }>()
</script>

<template>
  <ElScrollbar class="member-list">
    <div class="member-list-inner">
      <ElCard v-for="member in props.memberList" :key="member.uid" class="member-list-item" shadow="never">
        <ElAvatar shape="square" :size="42" :src="member.avatar" />
        <div class="member-list-nickname">{{ member.nickname }}</div>
      </ElCard>
    </div>
  </ElScrollbar>
</template>

<style lang="scss" scoped>
  .member-list {
    .member-list-inner {
      padding: 12px;
    }

    .member-list-item {
      :deep(.el-card__body) {
        display: flex;
        align-items: center;
        padding: 12px !important;
      }
    }

    .member-list-item + .member-list-item {
      margin-top: 12px;
    }

    .member-list-nickname {
      padding: 0 12px;
      color: #636363;
    }
  }
</style>
