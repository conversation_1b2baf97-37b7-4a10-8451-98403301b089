<!--
 * @description 发送时间控制消息
-->

<script setup lang="ts">
  import { computed } from 'vue'
  import { useMessageContext } from './MessageProvider.vue'

  const { message } = useMessageContext()

  // 消息发送时间
  const messageSendTime = computed(() => {
    const date = new Date(message.value.time)
    return `${date.getHours()}:${date.getMinutes()}`
  })
</script>

<template>
  <div class="message-send-time-controller">{{ messageSendTime }}</div>
</template>

<style lang="scss" scoped>
  .message-send-time-controller {
    text-align: center;
    color: #636363;
    font-size: 12px;
  }
</style>
