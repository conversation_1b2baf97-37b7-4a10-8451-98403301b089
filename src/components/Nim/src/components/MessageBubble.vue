<!--
 * @description 消息气泡
-->

<script setup lang="ts">
import MessageNickname from './MessageNickname.vue'
import MessageSendTimeController from './MessageSendTimeController.vue'
import MessageSendStatus from './MessageSendStatus.vue'
import MessageTextBubble from './MessageTextBubble.vue'
import { computed } from 'vue'
import { useMessageContext } from './MessageProvider.vue'
import { MessageFlow, MessageType } from '../types/message'

const { message, prevMessage } = useMessageContext()

// 是否显示发送时间
const isShowSendTime = computed(() => {
  if (!prevMessage.value) {
    // 如果上一条消息为空直接显示发送时间
    return true
  } else {
    // 两条消息的时间如果相隔5分钟，则显示发送时间
    const time = message.value.time
    const prevTime = prevMessage?.value.time
    const intervalTime = 1000 * 60 * 5
    const jetLag = time - prevTime
    return jetLag > intervalTime
  }
})

// 是否是收到的消息
const isIn = computed(() => message.value.flow === MessageFlow.IN)

// 发送方头像
const formAvatar = computed(() => JSON.parse(message.value.custom).formAvatar)

// 样式排序
const styleOrders = computed(() => {
  const order = [0, 1].reduce((order: string[], v) => {
    order.push(`order: ${v}`)
    return order
  }, [])

  !isIn.value && order.reverse()

  return order
})

</script>

<template>
  <div class="message-bubble-tip">
    <!-- 发送时间控制消息 -->
    <MessageSendTimeController v-if="isShowSendTime" />
  </div>
  <div class="message-bubble" :class="message.flow">
    <van-image class="vanimageClass" :style="styleOrders[0]" fit="cover" :src="formAvatar" :show-loading="false"
      :show-error="false" />
    <div :style="styleOrders[1]" style="flex: 1">
      <!-- 消息昵称 -->
      <MessageNickname v-if="isIn" />
      <div class="message-bubble-container">
        <div class="message-bubble-content" :style="styleOrders[0]">
          <!-- 文本消息气泡 -->
          <MessageTextBubble v-if="message.type === MessageType.TEXT" />
        </div>
        <div :style="styleOrders[1]">
          <!-- 消息发送状态 -->
          <MessageSendStatus />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.message-bubble {
  display: flex;
  padding: 14px;

  .message-bubble-container {
    display: flex;
  }

  .message-bubble-inner {
    width: 100%;
    display: flex;
  }

  .message-bubble-content {
    word-wrap: break-word;
    max-width: 280px;
    padding: 0 12px;
  }

  &.in {
    .message-bubble-container {
      justify-content: flex-start;
    }
  }

  &.out {
    .message-bubble-container {
      justify-content: flex-end;
    }
  }
}

.vanimageClass {
  width: 35px;
  height: 35px;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  border: 1px solid #E2E2E2;
}
</style>
