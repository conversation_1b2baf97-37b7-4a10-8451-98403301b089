<!--
 * @description 发送消息框
-->

<script setup lang="ts">
import { useNim } from '../hooks/useNim'
import { onMounted, ref } from 'vue'

const nim = useNim()

// 文本
const text = ref('')
const emit = defineEmits(["update:activePage"])
//点击功能按钮
const functionClickShow = ref(true);
function onSendMessageHandler() {
  functionClickShow.value = !functionClickShow.value;
  if (functionClickShow.value) {
    emit("update:activePage", false);
  } else {
    emit("update:activePage", true);
  }

}
//发送按钮是否显示
const sendBtnShow = ref(false);
const sendClick = () => {
  nim.sendText({ text: text.value.trim() })
  text.value = "";
  sendBtnShow.value = false;
}
watch(
  () => text.value,
  (val) => {
    if (val.trim() === "") {
      sendBtnShow.value = false;
    } else {
      sendBtnShow.value = true;
    }
  }
)
// 发送消息
onMounted(() => {
  // 监听enter事件（调用登录）
  document.onkeydown = (e: any) => {
    e = window.event || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      nim.sendText({ text: text.value.trim() })
      text.value = "";
      sendBtnShow.value = false;
    }
  };
})

</script>

<template>
  <div class="char-form">
    <div class="char-form-text">
      <textarea class="textarea" v-model="text"></textarea>
    </div>
    <div v-if="sendBtnShow" class="sendClass" @click="sendClick">
      发送
    </div>
    <div v-else class="char-from-function" @click="onSendMessageHandler">
      <van-icon class="home-o-icon" name="home-o" />
    </div>

  </div>
</template>

<style lang="scss" scoped>
.char-form {
  box-sizing: border-box;
  margin: 0 14px 0 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  background: #E6ECFB;
  border-radius: 0px 0px 4px 4px;
  opacity: 1;
  padding: 6px 10px;

}

.char-form-text {
  display: flex;
  width: 284px;
  align-items: center;
}

.textarea {
  width: 100%;
  height: 100%;
  overflow: auto;
  border: none;
  background-color: white;
  resize: none;
  font-size: 14px;
}

.char-from-function {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #FFFFFF;
  opacity: 1;
  border-radius: 32px;
}

.home-o-icon {
  font-size: 16px;
  color: #8B96A6;
  transform: rotate(180deg);
}

.sendClass {
  margin-left: 8px;
  width: 68px;
  height: 32px;
  background: #650FAA;
  border-radius: 16px 16px 16px 16px;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
}
</style>
