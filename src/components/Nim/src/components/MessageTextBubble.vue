<!--
 * @description 文本消息气泡
-->

<script setup lang="ts">
  import MessageBubbleAngleContainer from './MessageBubbleAngleContainer.vue'
  import { useMessageContext } from './MessageProvider.vue'

  const { message } = useMessageContext()
</script>

<template>
  <MessageBubbleAngleContainer>
    <div class="message-text-bubble">{{ message.text }}</div>
  </MessageBubbleAngleContainer>
</template>

<style lang="scss" scoped>
  .message-text-bubble {
    padding: 9px 12px;
    color: #636363;
    font-size: 14px;
  }
</style>
