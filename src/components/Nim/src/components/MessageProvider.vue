<!--
 * @description 消息提供者
-->

<script lang="ts">
  import { Message } from '../types/message'
  import { useContext } from '../hooks/useContext'
  import { defineComponent, Ref, toRefs } from 'vue'

  const [MessageProvider, useMessageContext] = useContext<{
    prevMessage: Ref<Message>
    message: Ref<Message>
  }>()

  export { useMessageContext }

  export default defineComponent({
    name: "MessageProvider"
  })
</script>

<script setup lang="ts">
  const props = defineProps<{
    message: Message,
    prevMessage: Message
  }>()

  MessageProvider(toRefs(props))
</script>

<template>
  <slot v-bind="$attrs"></slot>
</template>

<style lang="scss" scoped></style>
