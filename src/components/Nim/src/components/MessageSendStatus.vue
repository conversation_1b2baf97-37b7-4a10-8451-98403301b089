<!--
 * @description 消息发送状态
-->

<script setup lang="ts">
  import { MessageSendStatus } from '../types/message'
  import { useNim } from '../hooks/useNim'
  import { useMessageContext } from './MessageProvider.vue'

  const nim = useNim()
  const { message } = useMessageContext()

  // 重发消息
  const onResendMessage = () => {
    if (message.value) {
      nim.resendMessage({ msg: message.value })
    }
  }
</script>

<template>
  <div class="message-send-status">
    <!-- <ElTooltip v-if="message.status === MessageSendStatus.SENDING" content="消息发送中" placement="left">
      <ElIcon class="is-loading">
        <Loading />
      </ElIcon>
    </ElTooltip>
    <ElTooltip
      v-else-if="message.status === MessageSendStatus.FAIL"
      content="消息发送失败，点击重新发送"
      placement="left"
    >
      <ElIcon class="message-send-fail" color="red" @click="onResendMessage">
        <WarningFilled />
      </ElIcon>
    </ElTooltip> -->
  </div>
</template>

<style lang="scss" scoped>
  .message-send-status {
    align-items: center;
    display: flex;
    height: 36px;

    .message-send-fail {
      cursor: pointer;
    }
  }
</style>
