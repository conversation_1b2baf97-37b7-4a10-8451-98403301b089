<!--
 * @description 消息昵称
-->
<script setup lang="ts">
  import { useMessageContext } from './MessageProvider.vue'

  const { message } = useMessageContext()
</script>

<template>
  <div class="message-bubble-nick">{{ message.fromNick }}</div>
</template>

<style lang="scss" scoped>
  .message-bubble-nick {
    font-size: 12px;
    color: #636363;
    margin-bottom: 3px;
    padding: 0 12px;
  }
</style>
