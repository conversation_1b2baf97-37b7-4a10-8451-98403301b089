import { UnwrapNestedRefs } from "vue";
import { Message } from "./message";

/** Nim 初始化配置 */
export interface NimInitOptions {
  /** 在云信管理后台查看应用的 appKey */
  appKey: string;
  /** 帐号, 应用内唯一 */
  account: string;
  /** 帐号的 token, 用于建立连接 */
  token: string;
  /** 群id */
  teamId: string;
  /** 发送方头像 */
  formAvatar: string;
}

/** Nim 事件 */
export const enum NimEvent {
  /** 连接成功 */
  CONNECT = "onconnect",
  /** 断开连接 */
  DISCONNECT = "ondisconnect",
  /** 重新连接 */
  WILLRECONNECT = "onwillreconnect",
  /** 连接错误 */
  ERROR = "onerror",
  /** 接收消息 */
  MSG = "onmsg",
  /** 发送消息 */
  SEND_MSG = "sendMsg",
  /** 聊天窗口激活 */
  CHAT_WINDOW_ACTIVATED = "chatWindowActivated",
  /** 聊天窗口失活 */
  CHAT_WINDOW_DEACTIVATED = "chatWindowDeactivated",
}

/**
 * Nim 发送消息方法名（更多方法名请自行查看）
 * @see https://doc.yunxin.163.com/docs/TM5MzM5Njk/jg0NTA4NjE?platformId=60179#%E5%8F%91%E9%80%81%E6%B6%88%E6%81%AF
 */
export const enum NimSendMeesageMethod {
  /** 发送文本消息 */
  SEND_TEXT = "sendText",
  /** 重发消息 */
  RESEND_MSG = "resendMsg",
}

/** 控制器数据 */
export interface ControllerData {
  /** 消息记录列表 */
  messageRecords: UnwrapNestedRefs<Record<string, Message>>;
  /** 消息 */
  message: Message;
}
