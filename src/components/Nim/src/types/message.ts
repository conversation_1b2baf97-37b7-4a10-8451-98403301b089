/** 消息类型 */
export const enum MessageType {
  /** 文本 */
  TEXT = 'text',
  /** 图片 */
  IMAGE = 'image',
  /** 音频 */
  AUDIO = 'audio',
  /** 视频 */
  VIDEO = 'video',
  /** 文件 */
  FILE = 'file',
  /** 位置 */
  GEO = 'geo',
  /** 自定义 */
  CUSTOM = 'custom',
  /** 提醒 */
  TIP = 'tip',
  /** 通知 */
  NOTIFICATION = 'notification'
}

/** 消息发送状态 */
export const enum MessageSendStatus {
  /** 发送中 */
  SENDING = 'sending',
  /** 发送成功 */
  SUCCESS = 'success',
  /** 发送失败 */
  FAIL = 'fail'
}

/** 客户端类型 */
export const enum ClientType {
  /** 安卓 */
  ANDROID = 'Android',
  /** 苹果 */
  IOS = 'iOS',
  /** Windows 桌面 */
  PC = 'PC',
  /** 游览器 */
  WEB = 'Web',
  /**  AC 桌面 */
  MAC = 'Mac'
}

/** 消息流向 */
export const enum MessageFlow {
  /** 表示此消息是收到的消息 */
  IN = 'in',
  /** 表示此消息是发出的消息 */
  OUT = 'out'
}

/** 消息场景 */
export const enum MessageScene {
  /** 点对点消息 */
  P2P = 'p2p',
  /** 群消息 */
  TEAM = 'team',
  /** 超大群群消息 */
  SUPER_TEAM = 'superTeam'
}

/**
 * 消息体（更多字段请自行查看）
 * @see https://doc.yunxin.163.com/docs/TM5MzM5Njk/jg0NTA4NjE?platformId=60179
 */
export interface Message extends Record<string, any> {
  /** 消息场景 */
  scene: MessageScene
  /** 消息发送方, 帐号或群id */
  from: string
  /** 消息发送方的昵称 */
  fromNick: string
  /** 消息接收方, 帐号或群id */
  to: string
  /** 时间戳 */
  time: number
  /** 消息类型 */
  type: MessageType
  /** 消息所属的会话对象的ID */
  sessionId: string
  /** 聊天对象, 账号或者群id */
  target: string
  /** 消息流向 */
  flow: MessageFlow
  /** 消息发送状态 */
  status: MessageSendStatus
  /** SDK生成的消息id, 在发送消息之后会返回给开发者, 开发者可以在发送消息的结果回调里面根据这个ID来判断相应消息的发送状态, 到底是发送成功了还是发送失败了, 然后根据此状态来更新页面的UI。如果发送失败, 那么可以重新发送此消息 */
  idClient: string
  /** 是否是重发的消息 */
  resend: boolean
  /** 扩展字段 */
  custom: string
}

/** 文本消息 */
export interface TextMessage extends Message {
  /** 文本消息的文本内容 */
  text: string
}

/** 消息体 */
export interface MessagePayload {
  /** 消息场景 */
  scene?: MessageScene
  /** 消息接收方, 帐号或群id */
  to?: string
  /** 发送结果回调 */
  done?: (error: any, message: Message) => void
  /** 自定义扩展 */
  custom?: Record<string, any>
}

/** 文本消息体 */
export interface TextMessagePayload extends MessagePayload {
  /** 文本消息内容 */
  text: string
}

/** 重发消息体 */
export interface ResendMessagePayload extends Pick<MessagePayload, 'done'> {
  /** 重发的消息 */
  msg: Message
}
