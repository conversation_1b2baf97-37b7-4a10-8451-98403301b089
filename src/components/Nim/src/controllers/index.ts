import { MessageType } from '../types/message'
import { ControllerData } from '../types/nim'
import { textMessageHandler } from './text'

/**
 * @description 消息分发控制器
 * @param {ControllerData} data
 */
export function messageDistributionController(data: ControllerData) {
  const controllers: Partial<Record<MessageType, (...args: any[]) => void>> = {
    [MessageType.TEXT]: textMessageHandler
  }

  controllers[data.message.type]?.(data)
}
