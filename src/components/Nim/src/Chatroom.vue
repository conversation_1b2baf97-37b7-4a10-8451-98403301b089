<!--
 * @description 聊天室
-->

<script setup lang="ts">
import MessageList from './components/MessageList.vue'
import { useNim } from './hooks/useNim'
import { ref } from 'vue'


const nim = useNim()



const charRoomRef = ref();



</script>

<template>
  <div class="chatroom" ref="charRoomRef">
    <div class="tab-item">
      <van-icon name="smile-comment" />
      <span>聊天</span>
    </div>
    <!-- 消息列表 -->
    <MessageList :message-list="nim.renderMessages.value" />
  </div>
</template>

<style lang="scss" scoped>
.chatroom {
  margin: 0 14px 0 14px;
  border-radius: 4px 4px 0 0;
  height: 60%;
  background-color: #fff;

  .chatroom-tab-icon {
    display: inline-block;
    margin-right: 9px;
    font-size: 18px;
  }
}


.tab-item {
  padding: 0 7px 0 9px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
  border-bottom: 1px solid #E6ECFB;
}
</style>
