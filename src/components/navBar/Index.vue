//顶部导航
<template>
  <div class="demo">
    <van-nav-bar
      :title="props.title"
      left-text=""
      :left-arrow="props.leftAarrow"
      :fixed="true"
      :placeholder="true"
      :safe-area-inset-top="true"
      @click-left="onClickLeft"
      :border="props.border"
    />
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  leftAarrow: {
    type: Boolean,
    default: true,
  },
  border: {
    type: Boolean,
    default: true,
  },
});
const router = useRouter();
const onClickLeft = () => {
  if (window.history.state.back) {
    history.back();
  } else {
    // 没有上一页则返回到首页
    router.replace({ path: "/" });
  }
};
</script>
<style lang="scss" scoped>
.demo {
  :deep(.van-nav-bar .van-icon) {
    color: #000;
  }
}

:deep(.van-nav-bar--fixed) {
  z-index: 99;
}
</style>
