<template>
  <div class="nav-tab-container">
    <van-tabs class="nav-tabs" v-model:active="activeName" :ellipsis="false" :border="false" line-width="20px"
      title-active-color="#650faa" title-inactive-color="#4D545F" @click-tab="onChange">
      <van-tab v-for="[v, k] in Enum2Map(TableTab)" :key="v" :name="v" :title="k">
      </van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import { TableTab, Enum2Map } from "@/types/enum";
import useStore from "@/store";
// const props = defineProps({
//   selectedIdx: {
//     type: Number,
//     default: 1,
//   },
// });
const emit = defineEmits(["tabClick"]);
const { selectColor } = useStore();
const activeName = ref(selectColor.selectNav);
const onChange = () => {
  emit("tabClick", activeName.value);
};
</script>

<style lang="scss" scoped>
:deep(.van-tabs__wrap) {
  width: 100%;
}

.nav-tab-container {
  width: 100%;
  background-color: white;
}

:deep(.van-tabs__line) {
  background: #650faa;
  border-radius: 3px;
  border-width: 20px;
  height: 4px;
}

:deep(.van-tabs__nav.van-tabs__nav--line.van-tabs__nav--complete) {
  z-index: 2;
}
</style>
