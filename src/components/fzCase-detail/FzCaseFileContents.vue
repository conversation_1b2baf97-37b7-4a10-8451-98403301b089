<template>
  <div class="case-info-content">
    <div class="form-title">
      <div></div>
      <div>附件信息</div>
    </div>
    <div v-if="props.fileList.length > 0" class="form-content">
      <div
        v-for="item in props.fileList"
        :key="item.Id"
        class="form-content-title"
        @click="showFile(item)"
      >
        <div>
          <div class="g-ellipsis">{{ item.ImageName }}</div>
          <div class="form-size g-ellipsis">
            {{ item.TypeName }}&nbsp;|&nbsp;{{ GetFileSize(item.ImageSize) }}
          </div>
        </div>
        <div
          class="form-content-btn"
          :style="item.Status === 2 ? 'color:#beb833' : ''"
        >
          {{ item.StatusName }}
        </div>
      </div>
    </div>
    <van-empty v-else description="暂无附件信息" />
  </div>
</template>

<script lang="ts" setup>
import { Toast, ImagePreview } from "vant";
import { GetFileSize } from "@/utils/means";
import { PropType } from "vue";
import useStore from "@/store";
const router = useRouter();
const props = defineProps({
  fileList: {
    type: Array as PropType<Array<defs.AnnexDto>>,
    default: () => [],
  },
});
const { tokens } = useStore();
//附件预览
const showFile = (item: defs.AnnexDto) => {
  if (tokens.token) {
    if (item.Status === 1) {
      return Toast.fail("附件未上传");
    }
    if (item.AnnexUrl) {
      if (item.ImageName?.indexOf("pdf") != -1) {
        router.push({
          path: "/previeWaccessorys",
          query: {
            url: item.AnnexUrl,
          },
        });
      }
      //图片预览
      const imageExtensions = /\.(jpg|jpeg|png)$/i;
      if (item.ImageName && imageExtensions.test(item.ImageName)) {
        ImagePreview([item.AnnexUrl]);
      } else if (item.ImageName) {
        Toast.fail("不支持此格式，请在PC端查看!");
      }
    } else {
      Toast.fail("附件链接为空");
    }
  } else {
    if (item.AnnexUrl) {
      router.push({
        path: "/shareReport",
        query: {
          url: item.AnnexUrl,
        },
      });
    } else {
      Toast.fail("附件链接为空");
    }
  }
};
</script>
<style lang="scss" scoped>
.form-content {
  margin: 0 14px;
}

.form-content-title {
  box-sizing: border-box;
  height: 61px;
  border-bottom: 1px solid #e6ecfb;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.form-content div:last-child {
  border-bottom: none;
}

.form-content-title div:nth-child(1) {
  width: 80%;
}

.form-size {
  margin-top: 5px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
}
.form-content-btn {
  width: 60px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  font-size: 12px;
  color: #4d545f;
}
</style>
