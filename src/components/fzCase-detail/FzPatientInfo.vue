<template>
  <div class="container">
    <div class="case-card-info">
      <van-row>
        <div class="case-card-title g-ellipsis">
          原病理号：{{ props.dataDetails?.PathologyNumber }}
        </div>
      </van-row>
      <van-row class="case-card-content">
        <van-col span="4" class="case-card-image">
          <van-image
            class="ase-card-images"
            :src="
              zjsection.sectionList[zjsection.sectionList.length - 1]
                ?.SliceThumbnail || DefaultSection
            "
            fit="contain"
            @click="
              showImagePreview(
                zjsection.sectionList[zjsection.sectionList.length - 1]
                  ?.SliceThumbnail as string
              )
            "
          >
            <template v-slot:error
              ><van-image :src="DefaultSection" /> </template
          ></van-image>
        </van-col>
        <van-col span="14" class="case-card-text">
          <span class="case-card-name"
            >{{ props.dataDetails?.Name ? props.dataDetails?.Name + " |" : "" }}
            {{
              props.dataDetails?.SexName
                ? props.dataDetails?.SexName + " |"
                : ""
            }}
            {{ props.dataDetails?.Age }}</span
          >
          <div class="case-card-detail g-ellipsis">
            <span class="case-card-message g-ellipsis">
              {{ props.dataDetails?.CaseTypeName }} |
              {{ SampleLocationName(props.dataDetails?.SampleLocationName) }} |
              {{ props.dataDetails?.Inspection }}
            </span>
            <div class="case-card-time">
              {{ props.dataDetails?.SubscribeTime }}
            </div>
          </div>
        </van-col>
        <van-col span="6" class="case-card-btn">
          <div
            v-if="props.reportTemplateShow"
            class="case-review-btn"
            @click="gotoInfo()"
          >
            报告
          </div>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ImagePreview, Toast } from "vant";
import useStore from "@/store";
import DefaultSection from "@/assets/images/default-section.png";
import { SampleLocationName } from "@/utils/utils";
const router = useRouter();
const route = useRoute();
const props = defineProps({
  dataDetails: {
    type: Object,
    default: {},
  },
  //是否有报告
  reportTemplateShow: {
    type: Boolean,
    default: false,
  },
  shareUrl: {
    type: String,
    default: "",
  },
});
const { tokens, zjsection } = useStore();
const showImagePreview = (imgUrl: string) => {
  if (!imgUrl) return;
  ImagePreview({
    images: [imgUrl as string],
  });
};
//查看报告
const gotoInfo = () => {
  if (!tokens.token) {
    router.push({
      path: "/shareReport",
      query: {
        url: props.shareUrl,
      },
    });
  } else {
    router.push({
      path: "/report",
      query: {
        caseId: route.query.id,
        reportShow: 1,
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.container {
  margin: 14px 14px 0 14px;
}

.case-card-info {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px 12px;
  // height: 96px;
  background: #6510aa;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to top, #dfb7ff, #6510aa);
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to top, #dfb7ff, #6510aa);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.case-card-title {
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
}

.case-card-content {
  margin-top: 9px;
}

.ase-card-images {
  width: 51px;
  height: 51px;
}

.case-card-text {
  margin-left: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

.case-card-name {
  font-size: 14px;
}

.case-card-detail {
  margin-top: 5px;
}

.case-card-time {
  margin-top: 2px;
}

.case-card-btn {
  flex: auto;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.case-review-btn {
  width: 61px;
  height: 32px;
  background: #fff;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 32px;
  font-size: 12px;
  font-weight: 400;
  color: #650faa;
}

.case-card-icon {
  position: absolute;
  right: 12px;
  top: 12px;
  font-size: 20px;
  color: #ffffff;
}

.case-card-icon1 {
  color: #f9c55a;
}

.case-card-message {
  overflow: hidden;
}
</style>
