<template>
  <div class="case-info-content">
    <div class="form-title">
      <div></div>
      <div>病例信息</div>
    </div>
    <div class="case-info-details">
      <div class="form-content">
        <div class="form-content-title">原病理号</div>
        <div class="form-content-value">
          {{ props.data?.PathologyNumber || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">会诊号</div>
        <div class="form-content-value">{{ props.data?.BarCode || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">姓名</div>
        <div class="form-content-value">{{ props.data?.Name || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">性别</div>
        <div class="form-content-value">{{ props.data?.SexName || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">月经时间</div>
        <div class="form-content-value">
          {{ props.data?.MenstruationDate || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">婚否</div>
        <div class="form-content-value">
          {{ props.data?.MaritalStatusName || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">年龄</div>
        <div class="form-content-value">{{ props.data?.Age || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">职业</div>
        <div class="form-content-value">{{ props.data?.Job || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">民族</div>
        <div class="form-content-value">
          {{ props.data?.EthnicName || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">病人电话</div>
        <div class="form-content-value">{{ props.data?.Phone || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">身份证号</div>
        <div class="form-content-value">
          {{ props.data?.IdNumber || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">采样日期</div>
        <div class="form-content-value">
          {{ props.data?.SamplingTime || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">就诊号</div>
        <div class="form-content-value">
          {{ props.data?.VisitingNumber || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">病房</div>
        <div class="form-content-value">
          {{ props.data?.InpatientWard || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">住院号</div>
        <div class="form-content-value">
          {{ props.data?.HospitalizedId || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">床号</div>
        <div class="form-content-value">
          {{ props.data?.BedNumber || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">送检医院</div>
        <div class="form-content-value">
          {{ props.data?.Inspection || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">送检科室</div>
        <div class="form-content-value">
          {{ props.data?.InspectionDept || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">取材部位</div>
        <div class="form-content-value">
          {{
            SampleLocationName(props.data?.SampleLocationName as string) || "-"
          }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">亚专科</div>
        <div class="form-content-value">
          {{ props.data?.SubProfessionalName || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">预约时间</div>
        <div class="form-content-value">
          {{ props.data?.SubscribeTime || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">送检项目</div>
        <div class="form-content-value">
          {{ props.data?.CheckItemsName || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">医师姓名</div>
        <div class="form-content-value">
          {{ props.data?.DoctorName || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">医师电话</div>
        <div class="form-content-value">
          {{ props.data?.DoctorPhone || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">临床资料</div>
        <div class="form-content-value">
          {{ props.data?.ClinicalData || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">大体所见</div>
        <div class="form-content-value">{{ props.data?.Thus || "-" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">临床诊断</div>
        <div class="form-content-value">
          {{ props.data?.ClinicalDiagnosis || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">医嘱项目</div>
        <div class="form-content-value">
          {{ props.data?.Immunohistochemical || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">原病理诊断</div>
        <div class="form-content-value">
          {{ props.data?.DiagnosisContent || "-" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">备注</div>
        <div class="form-content-value">
          {{ props.data?.Remark || "-" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from "vue";
import { SampleLocationName } from "@/utils/utils";
const route = useRoute();
const props = defineProps({
  data: {
    type: Object as PropType<defs.CaseRenderingDto>,
    require: true,
  },
});
</script>
<style lang="scss" scoped>
.case-info-content {
  padding-bottom: 12px;
  border-radius: 4px 4px 4px 4px;
}

.form-title {
  border-bottom: none;
}

.case-info-details {
  padding: 0 14px;
}

.form-content {
  box-sizing: content-box;
  display: flex;
  align-items: center;
  line-height: 20px;
  border: 1px solid #e6ecfb;
  border-bottom: none;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}

.case-info-details > div:last-child {
  border-bottom: 1px solid #e6ecfb;
}

.form-content-title {
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: 7px;
  width: 84px;
}

.form-content-value {
  padding: 8px 0;
  display: flex;
  align-items: center;
  width: 236px;
  padding-left: 7px;
  font-weight: 400;
  border-left: 1px solid #e6ecfb;
  word-break: break-all; //英文
  white-space: pre-wrap; //中文
}

.van-cell {
  padding: 0;
}
</style>
