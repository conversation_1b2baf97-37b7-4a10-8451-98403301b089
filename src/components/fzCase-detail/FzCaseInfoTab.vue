<template>
  <div class="nav-tab-info-container">
    <van-tabs class="nav-tabs" v-model:active="indexName" :ellipsis="false" :border="false" line-width="40"
      @click-tab="onChange" title-active-color="#650FAA">
      <van-tab class="nav-item" title="病例信息" name="case"></van-tab>
      <van-tab class="nav-item" title="切片信息" name="slice"></van-tab>
      <van-tab class="nav-item" title="附件信息" name="file"></van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts" setup>
import useStore from "@/store";
const emits = defineEmits(["navClick"]);
const props = defineProps({
  activeName: {
    type: String,
    default: "case",
  },
});
const indexName = ref(props.activeName);
watch(
  () => props.activeName,
  (n) => {
    indexName.value = n;
  }
);

const onChange = (el: any) => {
  emits("navClick", indexName.value);
};
</script>

<style lang="scss" scoped>
.nav-tab-info-container {
  :deep(.van-tabs__line) {
    background-color: #650faa;
  }

  margin: 14px 14px 12px 14px;
}

:deep(.van-tabs--line .van-tabs__wrap) {
  border-radius: 4px 4px 4px 4px;
}

.nav-tab-container {
  width: 100%;
  height: 3rem;
}

.nav-bar-title {
  height: auto;
  width: 100%;
  font-weight: bold;
}
</style>
