<template>
  <div class="case-bottom-container">
    <template v-if="selectColor.selectNav === 1">
      <div
        class="case-diagnose"
        style="background-color: #ff7272"
        @click="sendBack"
      >
        退回病例
      </div>

      <div class="case-diagnose case-bottom-left" @click="allocation(1)">
        分配
      </div>
      <div
        class="case-diagnose case-bottom-left"
        style="background-color: #58bad1"
        @click="gotoLog()"
      >
        操作日志
      </div>
    </template>
    <template v-if="selectColor.selectNav === 2">
      <div
        class="case-video"
        style="background-color: #58bad1"
        @click="gotoLog()"
      >
        操作日志
      </div>
      <div
        v-if="![5, 6, 9, 13].includes(Number(status)) || overtime === '1'"
        class="case-diagnose case-bottom-left"
        @click="allocation(2)"
      >
        重新分配
      </div>
    </template>
    <template v-if="selectColor.selectNav === 3">
      <div
        v-if="status !== '10'"
        class="case-diagnose"
        style="background-color: #ff7272"
        @click="sendBack"
      >
        退回站点
      </div>
      <div
        class="case-diagnose case-bottom-left"
        style="background-color: #ffaf0c"
        @click="checkCause(1)"
      >
        查看原因
      </div>
      <div
        class="case-diagnose case-bottom-left"
        style="background-color: #58bad1"
        @click="gotoLog()"
      >
        操作日志
      </div>
      <div
        v-if="status !== '10'"
        class="case-diagnose case-bottom-left"
        @click="allocation(2)"
      >
        重新分配
      </div>
    </template>
    <template v-if="selectColor.selectNav === 4">
      <div
        v-if="status !== '10'"
        class="case-diagnose"
        style="background-color: #ff7272"
        @click="sendBack"
      >
        退回站点
      </div>
      <div
        v-if="status !== '10'"
        class="case-diagnose case-bottom-left"
        @click="withdraw"
      >
        确认撤回
      </div>
      <div
        class="case-diagnose case-bottom-left"
        style="background-color: #ffaf0c"
        @click="checkCause(2)"
      >
        查看原因
      </div>
      <div
        class="case-diagnose case-bottom-left"
        style="background-color: #58bad1"
        @click="gotoLog()"
      >
        操作日志
      </div>
    </template>
    <!-- 撤回 --start-- -->
    <CaseOverlay
      v-model:show="reviewerShow"
      overlayTitle="确定要撤回当前病例吗？"
      @submit="submit"
    />
    <!-- 撤回 --end-- -->
  </div>
</template>
<script lang="ts" setup>
import { Toast } from "vant";
import useStore from "@/store";
import { Aes } from "@/utils/aesEncrypt";
const { selectColor } = useStore();
const props = defineProps({
  pathologyNumber: {
    type: String,
  },
});
const route = useRoute();
const router = useRouter();
const { id, status, category, siteId, overtime } = route.query;
const reviewerShow = ref(false);
// 退回病例
const sendBack = () => {
  router.push({
    path: "/sendBack",
    query: {
      id: id,
    },
  });
};
// 分配
const allocation = (type: number) => {
  if (type === 1) {
    router.push({
      path: "/allocation",
      query: {
        id: id,
        siteId: siteId,
      },
    });
  } else {
    router.push({
      path: "/allocation",
      query: {
        id: id,
        siteId: siteId,
        anew: 1,
      },
    });
  }
};
// 操作日志
const gotoLog = () => {
  router.push({
    path: "/operationLog",
    query: {
      id: id,
    },
  });
};
// 查看原因
const checkCause = (type: number) => {
  if (type === 1) {
    router.push({
      path: "/checkCause",
      query: {
        id: id,
      },
    });
  } else {
    router.push({
      path: "/checkCause",
      query: {
        id: id,
        type: 2,
      },
    });
  }
};
// 撤回
const withdraw = () => {
  reviewerShow.value = true;
};
// 撤回提交
const submit = async () => {
  await API.triageFunctionApi.putAgreeWithdraw.request({
    bodyParams: {
      Id: Number(id),
      IsFrozen: false,
    },
  });
  router.go(-1);
};
</script>

<style lang="scss" scoped>
//底部区域

.case-bottom-container {
  box-sizing: border-box;
  display: flex;
  padding: 13px 14px 0 14px;
  position: fixed;
  bottom: 0;
  z-index: 100;
  width: 100%;
  height: 88px;
  background: #fff;
  opacity: 1;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
}

.case-bottom-left {
  margin-left: 10px;
}

.case-video {
  flex: 1;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #58bad1;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-diagnose {
  flex: 1.5;
  // width: 76px;
  height: 32px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  background-color: #650faa;
  text-align: center;
  line-height: 32px;
  border-radius: 16px;
}

.case-center {
  margin-top: 9px;
  width: 1px;
  height: 40px;
  background-color: #e6ecfb;
}

// .case-videos {
//   // flex: none;
//   // width: 100px;
// }
.case-videos-right {
  margin-right: 10px;
}
</style>
