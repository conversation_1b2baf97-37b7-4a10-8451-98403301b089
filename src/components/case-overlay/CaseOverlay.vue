<template>
  <van-overlay :show="props.show">
    <div class="wrapper">
      <div class="block">
        <div class="overlay-title">{{ props.overlayTitle }}</div>
        <div class="overlay-btn">
          <div class="overlay-btn-left" @click="cancel">否</div>
          <div class="overlay-btn-right" @click="submit">是</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script lang="ts" setup>
import { Toast } from "vant";
const props = defineProps({
  overlayTitle: {
    type: String,
    default: "是否重新复核诊断？",
  },
  show: {
    type: Boolean,
    default: false,
  },
});
//防抖

const timeout: any = ref(null);
function debounce(doSomeThing: () => void, time: number = 2000): () => void {
  return () => {
    if (timeout.value) {
      clearTimeout(timeout.value);
    }
    timeout.value = setTimeout(() => {
      doSomeThing();
    }, time);
  };
}
const emits = defineEmits(["update:show", "submit", "cancels"]);
const submit = () => {
  debounce(() => {
    emits("submit");
    emits("update:show", false);
  }, 200)();
};

//取消弹窗
const cancel = () => {
  emits("update:show", false);
  emits("cancels");
};
</script>

<style lang="scss" scoped>
.van-overlay {
  z-index: 100;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  width: 320px;
  height: 168px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.overlay-title {
  display: flex;
  height: 113px;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 16px;
  font-weight: 500;
  color: #343840;
}

.overlay-btn {
  display: flex;
  height: 54px;
}

.overlay-btn-left {
  height: 100%;
  flex: 1;
  text-align: center;
  line-height: 54px;
  font-size: 16px;
  font-weight: 500;
  color: #4d545f;
}

.overlay-btn-right {
  height: 100%;
  flex: 1;
  text-align: center;
  line-height: 54px;
  font-size: 16px;
  font-weight: 500;
  color: #650faa;
}
</style>
