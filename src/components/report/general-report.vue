<template>
  <div class="report-box">
    <div class="reportHeader">
      <span>
        <!-- <img src="../../../public/logo.png" alt=""> -->
      </span>
      <h2>{{ reportData?.SiteName }}</h2>
      <h4>{{ reportData?.Name }}</h4>
    </div>
    <div class="reportBody">
      <div class="bodyTextOne">
        <span>送检医院：{{ reportData?.HospitalName }}</span>
        <span>原病理号：{{ reportData?.PathologyNumber }}</span>
      </div>
      <el-divider></el-divider>
      <div class="bodyTextTwo">
        <div>
          <span>姓名：</span>
          {{ reportData?.PatientName }}
        </div>
        <div>
          <span>性别：</span>
          {{ reportData?.SexName }}
        </div>
        <div>
          <span>年龄：</span>
          {{ reportData?.PatientAge }}
        </div>
        <div>
          <span>会诊号：</span>
          {{ reportData?.BarCode }}
        </div>
        <div>
          <span>患者电话：</span>
          {{ reportData?.PatientPhone }}
        </div>
        <div>
          <span>门/住院号：</span>
          {{ reportData?.HospitalizedId }}
        </div>
        <div>
          <span>科别：</span>
          {{ reportData?.InspectionDept }}
        </div>
        <div>
          <span>床号：</span>
          {{ reportData?.BedNumber }}
        </div>
        <div>
          <span>送检组织及部位：</span>
          {{ SampleLocationName(reportData?.SampleLocationName as string) }}
        </div>
        <div>
          <span>送检医生：</span>
          {{ reportData?.CaseDoctorName }}
        </div>
        <div>
          <span>联系电话：</span>
          {{ reportData?.DoctorPhone }}
        </div>
        <div>
          <span>预约时间：</span>
          {{ reportData?.SubscribeTime?.substring(0, 10) }}
        </div>
        <div>
          <span>上传时间：</span>
          {{ reportData?.CaseCreateTime?.substring(0, 10) }}
        </div>
      </div>
      <el-divider></el-divider>
      <div class="bodyTextThree">
        <div>
          <div class="item-list">
            <span>临床诊断：</span>
            {{ reportData?.ClinicalDiagnosis }}
          </div>
          <div class="item-list">
            <span>大体描述：</span>
            {{ reportData?.Thus }}
          </div>
          <div class="item-list">
            <span>图像所示：</span>
            <div class="img" v-if="images">
              <base-image
                v-for="(item, index) in images"
                :key="index"
                :src="item.Url"
              ></base-image>
            </div>
          </div>
          <div class="item-list">
            <span>图像描述：</span>
            <!-- {{ tick.Images[0].Remark }} -->
            {{ imageDescription }}
          </div>
          <div class="item-list">
            <span>诊断意见：</span>
            <!-- {{ reviewOpinion }} -->
            {{ diagnosisOpinion }}
          </div>
        </div>
      </div>
    </div>
    <div class="reportFooter">
      <div class="autograph">
        <div>
          <span>报告医师:</span>
          <base-image :src="reportData?.DoctorSignatureUrl"></base-image>
        </div>
        <div>
          <span>复核医师:</span>
          <template v-if="reportData?.ReviewDoctorSignature">
            <base-image
              :src="reportData?.ReviewDoctorSignatureUrl"
            ></base-image>
          </template>
        </div>
        <div>
          <span>报告时间:{{ reportData?.CreateTime }}</span>
        </div>
      </div>
      <p>
        本结果只对条码来样负责，如有疑问请在报告日期一周内提出，谢谢配合！
        电话：{{ reportData?.OrganizePhone }}
      </p>
    </div>
  </div>
</template>
<script lang="ts">
/**
 * 通用报告
 */
import { computed, defineComponent, PropType } from "vue";
import { SampleLocationName } from "@/utils/utils";
export default defineComponent({
  name: "GeneralReport",
  props: {
    reportData: {
      type: Object as any,
      required: true,
    },
  },
  setup(props) {
    const reportJsonData = computed(() => {
      if (props.reportData?.ReportJsonData) {
        return JSON.parse(props.reportData.ReportJsonData);
      } else {
        return {};
      }
    });
    //图像描述
    const imageDescription = computed(
      () => reportJsonData.value.ImageDescription
    );
    //诊断意见
    const diagnosisOpinion = computed(
      () => reportJsonData.value.DiagnosisOpinion
    );
    //screenshots
    const images = computed(() => reportJsonData.value.Images);
    return {
      imageDescription,
      diagnosisOpinion,
      images,
      SampleLocationName,
    };
  },
});
</script>
<style scoped lang="scss">
.report-box {
  padding: 22px 22px 16px;
  // max-height: 800px !important;
  box-sizing: border-box;
  overflow: hidden;
  overflow: auto;
  border: 1px solid #eaeaea;

  .reportHeader {
    text-align: center;
    margin-top: 2px;
    padding-bottom: 6px;
    border-bottom: 2px solid #636363;

    img {
      width: 313px;
      height: 128px;
      float: left;

      div {
        margin: 0 30px;
      }
    }

    h2 {
      font-size: 22px;
      color: red;
    }

    h4 {
      font-size: 22px;
      font-weight: bold;
      color: #636363;
      padding: 0;
      margin: 7px 0 0 0;
    }

    p {
      font-size: 12px;
      padding: 0;
      margin: 0;
    }
  }

  .reportBody {
    color: red;
    font-size: 14px;

    .bodyTextOne {
      display: flex;
      padding: 0 16px;

      span {
        padding: 8px;
        font-size: 14px;
        flex: 1;
      }
    }

    .bodyTextTwo {
      padding: 0 16px;

      div {
        padding: 8px;
        font-size: 14px;
        display: inline-table;
        width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        span {
          font-weight: bold;
          color: #636363;
        }
      }

      .time {
        width: 250px;
      }
    }

    .bodyTextThree {
      padding: 0 16px;

      // height: 510px;
      .checkbox div:nth-child(2) {
        div:nth-child(6) {
          float: left;
        }

        div:nth-child(8) {
          float: left;
        }

        .el-checkbox:last-of-type {
          width: 200px;
        }
      }

      .marginLeft {
        margin-left: 20px;
      }

      .checkbox div:first-child div:first-child {
        margin: 0;
      }

      .item-list {
        margin-top: 13px;
        height: 71px;

        span {
          font-weight: bold;
          color: #636363;
        }

        .img {
          div {
            width: 130px;
            height: 60px;
          }
        }
      }

      .opinion {
        float: left;
        margin-left: 33px;

        .opinionSpan {
          font-weight: bold;

          span {
            font-weight: 400;
          }
        }
      }
    }
  }
}

:deep(.el-divider) {
  background-color: #636363 !important;
  padding: 0;
  margin: 0;
}

.reportFooter {
  margin-top: 8px;

  .autograph {
    display: flex;

    div {
      flex: 1;

      span {
        font-weight: bold;
        float: left;
        margin-top: 10px;
      }

      div {
        width: 60px;
        height: 36px;
      }
    }
  }

  p {
    padding: 0;
    margin: 5px 0;
  }
}
</style>
