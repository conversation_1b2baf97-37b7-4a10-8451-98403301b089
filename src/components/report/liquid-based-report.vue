<template>
  <div class="report-box">
    <div class="reportHeader">
      <h2>{{ reportData.SiteName }}</h2>
      <h4>{{ reportData.Name }}</h4>
    </div>
    <div class="reportBody">
      <div class="bodyTextOne">
        <span>送检医院：{{ reportData.HospitalName }}</span>
        <span style="text-align: right"
          >原病理号：{{ reportData.PathologyNumber }}</span
        >
      </div>
      <div class="bodyTextTwo">
        <div>
          <span>姓名：</span>
          {{ reportData.PatientName }}
        </div>
        <div>
          <span>性别：</span>
          {{ reportData.SexName }}
        </div>
        <div>
          <span>年龄：</span>
          {{ reportData.PatientAge }}
        </div>
        <div>
          <span>会诊号：</span>
          {{ reportData.BarCode }}
        </div>
        <div>
          <span>患者电话：</span>
          {{ reportData.PatientPhone }}
        </div>
        <div>
          <span>门/住院号：</span>
          {{ reportData.HospitalizedId }}
        </div>
        <div>
          <span>科别：</span>
          {{ reportData.InspectionDept }}
        </div>
        <div>
          <span>床号：</span>
          {{ reportData.BedNumber }}
        </div>
        <div>
          <span>送检组织及部位：</span>
          {{ SampleLocationName(reportData?.SampleLocationName as string) }}
        </div>
        <div>
          <span>送检医生：</span>
          {{ reportData.CaseDoctorName }}
        </div>
        <div>
          <span>联系电话：</span>
          {{ reportData.DoctorPhone }}
        </div>
        <div>
          <span>预约时间：</span>
          {{ reportData.SubscribeTime }}
        </div>
      </div>
      <div class="form-content-box">
        <div>
          <div class="item-list">
            <span class="label span-title">临床诊断：</span>
            {{ reportData.ClinicalDiagnosis }}
          </div>
          <div class="item-list">
            <div class="form-content">
              <div class="title">样本满意评估</div>
              <div>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.TbsStandardDiagnosis.LaboratoryResults
                      .Satisfaction
                  "
                  label="满意"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.TbsStandardDiagnosis.LaboratoryResults
                      .NeckTubeCells
                  "
                  label="预管细胞"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.TbsStandardDiagnosis.LaboratoryResults
                      .BiochemicalCell
                  "
                  label="化生细胞"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.TbsStandardDiagnosis.LaboratoryResults
                      .NotSatisfied
                  "
                  label="不满意(细胞量<10%)"
                ></el-checkbox>
              </div>
              <div class="title title1">鳞状上皮细胞分析：</div>
              <div class="ml-2">
                <el-checkbox
                  v-model="liquidBasedJsonData.EpithelialCells.Epithelium"
                  label="未见上皮内病变或恶性病变"
                ></el-checkbox>
              </div>
              <div class="title ml-1">微生物：</div>
              <div class="ml-2">
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.Microorganism
                      .Trichomonas
                  "
                  label="阴道滴虫"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.Microorganism.Flour
                  "
                  label="真菌，形态上符合念珠菌病"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.Microorganism
                      .BacteriaChange
                  "
                  label="菌群变化，提示细菌性阴道病"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.Microorganism
                      .Actinobacteria
                  "
                  label="细菌，形态符合放线菌属"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.Microorganism
                      .CellChanged
                  "
                  label="细胞变化符合单纯孢疹病毒感染"
                ></el-checkbox>
              </div>
              <div class="title ml-1">非典型鳞状细胞：</div>
              <div class="ml-2">
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.AtypicalSquamousCell
                      .Parent
                  "
                  label="非典型鳞状细胞："
                ></el-checkbox>
              </div>
              <div class="ml-3">
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.AtypicalSquamousCell
                      .AscUs
                  "
                  label="不能明确意义（ASC-US）"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.AtypicalSquamousCell
                      .AscH
                  "
                  label="不能除外高度鳞状上皮内病变（ASC-H）"
                ></el-checkbox>
              </div>
              <div class="title ml-1">反应性细胞改变：</div>
              <div class="ml-2">
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells.Parent
                  "
                  label="反应性细胞改变："
                ></el-checkbox>
              </div>
              <div class="ml-3">
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells
                      .Inflammatory
                  "
                  label="炎性（包括典型修复）"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells.Shrinking
                  "
                  label="萎缩"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells
                      .IntrauterineDevice
                  "
                  label="宫内节育器（IUD）&nbsp;&nbsp;&nbsp;&nbsp;"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells
                      .Radiotherapy
                  "
                  label="放疗"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.ReactiveCells.Other
                  "
                  label="其他"
                ></el-checkbox>
              </div>
              <div class="ml-2">
                <el-checkbox
                  v-model="liquidBasedJsonData.EpithelialCells.LowParent"
                  label="低度鳞状上皮内病变（LHIL）"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.PromptHpvInfection
                  "
                  label="提示HPV感染"
                ></el-checkbox>
                <el-checkbox
                  v-model="liquidBasedJsonData.EpithelialCells.HighParent"
                  label="高度鳞状上皮内病变（HSIL）"
                ></el-checkbox>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.EpithelialCells.SquamousCellCarcinoma
                  "
                  label="鳞状细胞癌"
                ></el-checkbox>
              </div>
              <div class="title title1">腺上皮细胞分析：</div>
              <div class="flex">
                <div class="left">
                  <div class="title ml-1">非典型腺细胞（宫颈管）：</div>
                  <div class="ml-1">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .AtypicalGlandularCells.Parent
                      "
                      label="非典型腺细胞（宫颈管）"
                    ></el-checkbox>
                  </div>
                  <div class="ml-2">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .AtypicalGlandularCells.NonSpecificChanges
                      "
                      label="非特异性改变"
                    ></el-checkbox>
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .AtypicalGlandularCells.NeoplasticChanges
                      "
                      label="倾向于肿瘤性病变"
                    ></el-checkbox>
                  </div>
                </div>
                <div class="center">
                  <div class="title ml-1">非典型腺细胞（宫内膜）：</div>
                  <div class="ml-1">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .EndometrialAtypicalGlandCells.Parent
                      "
                      label="非典型腺细胞（宫内膜）："
                    ></el-checkbox>
                  </div>
                  <div class="ml-2">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .EndometrialAtypicalGlandCells.NonSpecificChanges
                      "
                      label="非特异性改变"
                    ></el-checkbox>
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .EndometrialAtypicalGlandCells.NeoplasticChanges
                      "
                      label="倾向于肿瘤性病变"
                    ></el-checkbox>
                  </div>
                  <div class="ml-3">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .UnknownAtypicalGlandCells
                      "
                      label="非典型腺细胞（来源不明）"
                    ></el-checkbox>
                  </div>
                </div>
                <div class="right">
                  <div class="title ml-1">腺癌：</div>
                  <div class="ml-1">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .Adenocarcinoma.Parent
                      "
                      label="腺癌"
                    ></el-checkbox>
                  </div>
                  <div class="ml-2">
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .Adenocarcinoma.Cervical
                      "
                      label="宫颈"
                    ></el-checkbox>
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .Adenocarcinoma.Endometrium
                      "
                      label="宫内膜"
                      :style="{ 'margin-right': 0 }"
                    ></el-checkbox>
                    <el-checkbox
                      v-model="
                        liquidBasedJsonData.GlandularEpithelialCellAnalysis
                          .Adenocarcinoma.Other
                      "
                      label="其他"
                    ></el-checkbox>
                  </div>
                </div>
              </div>

              <div>
                <el-checkbox
                  v-model="
                    liquidBasedJsonData.GlandularEpithelialCellAnalysis
                      .OtherMalignantTumors
                  "
                  label="其他恶性肿瘤"
                ></el-checkbox>
              </div>
            </div>
          </div>
          <div class="flex">
            <div class="item-list" style="flex: 1">
              <span class="span-title">镜下所见：</span>
              <div class="img">
                <base-image
                  :src="images[0].Url"
                  v-if="images && images.length && images[0].Url"
                ></base-image>
              </div>
            </div>
            <div class="item-list" style="flex: 2">
              <span class="span-title">诊断意见：</span>
              {{ diagnosisOpinion }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="reportFooter">
      <div class="autograph">
        <div>
          <span>报告医师:</span>
          <base-image :src="reportData.DoctorSignatureUrl"></base-image>
        </div>
        <div>
          <span>复核医师:</span>
          <template v-if="reportData.ReviewDoctorSignature">
            <base-image :src="reportData.ReviewDoctorSignatureUrl"></base-image>
          </template>
        </div>
        <div>
          <span>报告时间:{{ reportData.CreateTime }}</span>
        </div>
      </div>
      <p>
        本结果只对条码来样负责，如有疑问请在报告日期一周内提出，谢谢配合！
        电话：{{ reportData.OrganizePhone }}
      </p>
    </div>
  </div>
</template>
<script lang="ts">
/**
 * 通用报告
 */
import { computed, defineComponent, PropType } from "vue";
import { SampleLocationName } from "@/utils/utils";
class LiquidBasedJsonData {
  TbsStandardDiagnosis: {
    LaboratoryResults: {
      //样本满意评估
      Satisfaction: boolean; //满意
      NeckTubeCells: boolean; //颈管细胞
      BiochemicalCell: boolean; //生化细胞
      NotSatisfied: boolean; //不满意（细胞量<10%)
    };
  } = {
    LaboratoryResults: {
      Satisfaction: false,
      NeckTubeCells: false,
      BiochemicalCell: false,
      NotSatisfied: false,
    },
  };
  EpithelialCells: {
    //鳞状上皮细胞
    Epithelium: boolean; //未见上皮内病变或恶行病变
    Microorganism: {
      //微生物
      Trichomonas: boolean; //阴道滴虫
      Flour: boolean; //真菌，形态上符合念珠菌属
      BacteriaChange: boolean; //菌群变化, 提示细菌性阴道病
      Actinobacteria: boolean; //细菌，形态符合放线菌属
      CellChanged: boolean; //细胞变化符合单纯疱疹病毒感染
    };
    ReactiveCells: {
      //反应性细胞改变
      Inflammatory: boolean; //炎性（包括典型修复）
      Shrinking: boolean; //萎缩
      IntrauterineDevice: boolean; //宫内节育器
      Radiotherapy: boolean; //放疗
      Other: boolean; //其它
      Parent: boolean; //反应性细胞改变
    };
    AtypicalSquamousCell: {
      //非典型鳞状细胞
      AscUs: boolean; //不能明确意义（ASC - US）
      AscH: boolean; //不除外高度鳞状上皮肉病变(ASC - H)
      Parent: boolean; //非典型鳞状细胞
    };
    PromptHpvInfection: boolean; //提示HPV感染
    LowParent: boolean; //低度鳞状上皮肉病变(LSIL)
    SquamousCellCarcinoma: boolean; //鳞状细胞癌
    HighParent: boolean; //高度鳞状上皮肉病变(HSIL)
  } = {
    Epithelium: false,
    Microorganism: {
      Trichomonas: false,
      Flour: false,
      BacteriaChange: false,
      Actinobacteria: false,
      CellChanged: false,
    },
    ReactiveCells: {
      Inflammatory: false,
      Shrinking: false,
      IntrauterineDevice: false,
      Radiotherapy: false,
      Other: false,
      Parent: false,
    },
    AtypicalSquamousCell: {
      AscUs: false,
      AscH: false,
      Parent: false,
    },
    PromptHpvInfection: false,
    LowParent: false,
    SquamousCellCarcinoma: false,
    HighParent: false,
  };
  DiagnosisOpinion: {
    OriginalPathologyNumber: string; //原单位病理号
    Remark: string;
  } = {
    OriginalPathologyNumber: "",
    Remark: "",
  };
  GlandularEpithelialCellAnalysis: {
    //腺上皮细胞
    AtypicalGlandularCells: {
      //非典型腺细胞（宫颈管）
      NonSpecificChanges: boolean; //非特异性改变
      NeoplasticChanges: boolean; //倾向于肿瘤性病变
      Parent: boolean; //非典型腺细胞(宫颈管)
    };
    EndometrialAtypicalGlandCells: {
      //非典型腺细胞（宫内膜）：
      NonSpecificChanges: boolean; //非特异性改变
      NeoplasticChanges: boolean; //倾向于肿瘤性病变
      Parent: boolean; //非典型腺细胞(宫内膜)
    };
    UnknownAtypicalGlandCells: boolean; //非典型腺细胞（来源不明）
    Adenocarcinoma: {
      //腺癌
      Cervical: boolean; //宫颈
      Endometrium: boolean; //宫内膜
      Other: boolean; //其它
      Parent: boolean; //腺癌
    };
    OtherMalignantTumors: boolean; //其它恶性肿瘤
  } = {
    AtypicalGlandularCells: {
      NonSpecificChanges: false,
      NeoplasticChanges: false,
      Parent: false,
    },
    EndometrialAtypicalGlandCells: {
      NonSpecificChanges: false,
      NeoplasticChanges: false,
      Parent: false,
    },
    UnknownAtypicalGlandCells: false,
    Adenocarcinoma: {
      Cervical: false,
      Endometrium: false,
      Other: false,
      Parent: false,
    },
    OtherMalignantTumors: false, //其它恶性肿瘤
  };
  Images: any[] = [];
  Quality: number = 1; //切片质量
}
export default defineComponent({
  name: "LiquidBasedReport",
  props: {
    reportData: {
      type: Object as any,
      required: true,
    },
  },
  setup(props) {
    const reportJsonData = computed(() => {
      return props.reportData.ReportJsonData
        ? JSON.parse(props.reportData.ReportJsonData)
        : {};
    });
    //图像描述
    // const imageDescription = computed(
    //     () => reportJsonData.value.ImageDescription
    // );
    //诊断意见
    const diagnosisOpinion = computed(
      () => reportJsonData.value.DiagnosisOpinion?.Remark
    );
    //screenshots
    const images = computed(() => reportJsonData.value.Images);
    const liquidBasedJsonData = computed(() =>
      reportJsonData.value ? reportJsonData.value : new LiquidBasedJsonData()
    );

    return {
      // imageDescription,
      diagnosisOpinion,
      images,
      liquidBasedJsonData,
      SampleLocationName,
    };
  },
});
</script>
<style scoped lang="scss">
.report-box {
  width: 700px;
  padding: 22px 22px 16px;
  // max-height: 800px !important;
  box-sizing: border-box;
  overflow: hidden;
  overflow: auto;
  // border: 1px solid #636363;
  .reportHeader {
    text-align: center;
    margin-top: 2px;
    padding-bottom: 6px;
    border-bottom: 2px solid #636363;
    img {
      width: 358px;
      height: 58px;
      div {
        margin: 0 30px;
      }
    }
    h2 {
      font-size: 22px;
      color: red;
    }
    h4 {
      font-size: 22px;
      font-weight: bold;
      color: #636363;
      padding: 0;
      margin: 7px 0 0 0;
    }
    p {
      font-size: 12px;
      padding: 0;
      margin: 0;
    }
  }
  .reportBody {
    color: red;
    font-size: 14px;
    .bodyTextOne {
      display: flex;
      padding: 0 16px;
      span {
        padding: 8px;
        font-size: 14px;
        flex: 1;
      }
    }
    .bodyTextTwo {
      padding: 3px 16px;
      border-top: #636363 solid 1px;
      border-bottom: #636363 solid 1px;

      div {
        padding: 3px 8px;
        font-size: 14px;
        display: inline-table;
        width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        span {
          font-weight: bold;
          color: #636363;
        }
      }
      .time {
        width: 250px;
      }
    }

    .form-content-box {
      .span-title {
        font-size: 14px;
        font-weight: bold;
        padding-top: 10px;
        display: inline-block;
      }
      .title {
        font-size: 14px;
        font-weight: bold;
        padding-top: 10px;
      }
      .title1 {
        font-size: 15px;
        font-weight: bold;
        padding-top: 10px;
      }
      .ml-1 {
        margin-left: 8px;
      }
      .ml-2 {
        margin-left: 24px;
        label {
          height: 25px;
        }
      }
      .ml-3 {
        margin-left: 60px;
      }
      .flex {
        display: flex;
      }
      :deep(.el-checkbox__label) {
        padding-left: 4px;
      }
      :deep(.el-image),
      .img {
        width: 153px;
        height: 98px;
      }
    }
  }
}
.reportFooter {
  margin-top: 8px;
  .autograph {
    display: flex;
    div {
      flex: 1;
      span {
        font-weight: bold;
        float: left;
        margin-top: 10px;
      }
      div {
        width: 60px;
        height: 36px;
      }
    }
  }
  p {
    padding: 0;
    margin: 5px 0;
  }
}
</style>
