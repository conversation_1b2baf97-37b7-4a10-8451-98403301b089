<template>
  <div>
    <div class="radio">
      <div>{{ props.name }}</div>
      <div v-for="(item, index) in props.list" :key="item.Id" class="radio-class" @click="radioClick(item.value)">
        <div :class="['circle', item.checked ? 'circle1' : '']">
          <div :class="[item.checked ? 'circles' : '']"></div>
        </div>
        <div>{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from "vue";
const props = defineProps({
  name: {
    type: String,
    default: "病例质量评估：",
  },
  list: {
    type: Array as PropType<any>,
    default: () => [],
  },
});
const emits = defineEmits(["radiosClick"]);

//是否选中
const radioClick = (value: number) => {
  props.list.map((item: any) => {
    if (item.value === value) {
      item.checked = true;
    } else {
      item.checked = false;
    }
  });
  emits("radiosClick", Number(value));
};

</script>
<style lang="scss" scoped>
.van-radio__icon--checked .van-icon {
  background-color: #650faa;
  border-color: #650faa;
}

.radio {
  display: flex;
  align-items: center;
  margin: 0 14px;
  margin-bottom: 12px;
  padding: 6px 0 6px 12px;
  background-color: #fff;
  font-size: 14px;
  font-weight: bold;
  color: #636363;
  border-radius: 4px;
}

.radio-class {
  display: flex;
  align-items: center;
  margin-right: 10px;
  font-size: 14px;
  font-weight: 400;
  color: #636363;
}

.circle {
  margin-right: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 14px;
  height: 14px;
  border: 1px solid #cbcbcb;
  border-radius: 14px;
}

.circle1 {
  border: 1px solid #650faa;
}

.circles {
  width: 7px;
  height: 7px;
  border-radius: 7px;
  background-color: #650faa;
}
</style>
