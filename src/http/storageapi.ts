import axios from "axios";
// axios服务
const _service = axios.create({
  baseURL: process.env.VUE_APP_STORAGEAPI,
  timeout: 5000,
  withCredentials: false,
});

export class StorageApiServer {
  /**
   * 获取对象签名地址
   * @param params 对象名称
   */
  static async SignerObjectUrlAsync(param: string) {
    const _url = `/s3token/presigner?BucketName=${
      process.env.VUE_APP_BUCKETNAME
    }&ObjectName=${param}&MethodType=1&TimeExpire=3000`;
    return _service
      .get(_url)
      .then((resp) => {
        const { data, status } = resp;
        return data;
      })
      .catch((err) => {
        console.log(`获取对象存储签名${err}`);
        return new Promise(err);
      });
  }
}
