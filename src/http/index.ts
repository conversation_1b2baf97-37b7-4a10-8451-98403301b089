// http/index.ts
import axios from "axios";
import useStore from "@/store";
import RcpWeb from "RcpWeb"; //项目配置
import { Toast } from "vant";
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
//创建axios的一个实例
const instance = axios.create({
  baseURL: RcpWeb.RemoteUrl, //接口统一域名
  timeout: 6000, //设置超时
  // headers: {
  //   "Content-Type": "application/json;charset=UTF-8;",
  // },
});

//请求拦截器
instance.interceptors.request.use(
  (config: any) => {
    // Toast.loading({
    //   message: "加载中...",
    //   forbidClick: true,
    //   loadingType: "spinner",
    // });
    config.baseURL = RcpWeb.RemoteUrl;
    const { tokens } = useStore();
    // 每次发送请求之前判断是否存在token，如果存在，则统一在http请求的header都加上token，不用每次请求都手动添加了
    const token = tokens.token;
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    } else {
    }
    return config;
  },
  (error) =>
    // 对请求错误做些什么
    Promise.reject(error)
);

//响应拦截器
instance.interceptors.response.use(
  (response) => {
    //响应成功
    // console.log("响应成功");
    return response.data;
  },
  (error) => {
    const { tokens } = useStore();
    console.log(error, error.response.data.code);
    //响应错误
    if (error.response && error.response.data.code === 401) {
      tokens.redirectLogin();
      return Promise.reject(error);
    }
    return Promise.reject(error);
  }
);
export default instance;
