import { RouteRecordRaw } from "vue-router";
//分诊端
const fzRouter: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/fzEnd/home/<USER>"),
  },
  {
    path: "/triageAdmin",
    name: "TriageAdmin",
    component: () => import("@/views/fzEnd/triageAdmin/Index.vue"),
  },
  {
    path: "/fzDetails", //分诊详情
    name: "FzDetails",
    component: () => import("@/views/fzEnd/triageAdmin/Details.vue"),
  },
  {
    path: "/sendBack", //退回
    name: "SendBack",
    component: () => import("@/views/fzEnd/triageAdmin/SendBack.vue"),
  },
  {
    path: "/operationLog", //操作日志
    name: "OperationLog",
    component: () => import("@/views/fzEnd/triageAdmin/operationLog.vue"),
  },
  {
    path: "/allocation", //分诊
    name: "Allocation",
    component: () => import("@/views/fzEnd/triageAdmin/allocation.vue"),
  },
  {
    path: "/checkCause", //查看原因
    name: "CheckCause",
    component: () => import("@/views/fzEnd/triageAdmin/checkCause.vue"),
  },
  {
    path: "/examineDetails", //切片预览
    name: "ExamineDetails",
    component: () => import("@/views/fzEnd/triageAdmin/ExamineDetails.vue"),
  },
  {
    path: "/myout", //我的外出
    name: "Myout",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/alterCode", //修改密码
    name: "AlterCode",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/feedback", //反馈
    name: "Feedback",
    component: () => import("@/views/zjEnd/regulates/Feedback.vue"),
  },

  {
    path: "/freezeReservation", //冰冻预约
    name: "FreezeReservation",
    component: () => import("@/views/fzEnd/freezeReservation/Index.vue"),
  },
  {
    path: "/freezeReservation/details", //冰冻预约
    name: "Detailss",
    component: () => import("@/views/fzEnd/freezeReservation/Details.vue"),
  },
  {
    path: "/frozenCause", //冰冻预约退回原因
    name: "FrozenCause",
    component: () => import("@/views/fzEnd/freezeReservation/frozenCause.vue"),
  },
  {
    path: "/withdraw", //冰冻预约撤回原因
    name: "Withdraw",
    component: () => import("@/views/fzEnd/freezeReservation/withdraw.vue"),
  },
  {
    path: "/checkTheCause", //冰冻预约退回查看原因
    name: "CheckTheCause",
    component: () =>
      import("@/views/fzEnd/freezeReservation/CheckTheCause.vue"),
  },
  {
    path: "/statistics", //数据统计
    name: "Statistics",
    component: () => import("@/views/fzEnd/statistics/Index.vue"),
  },
  {
    path: "/previeWaccessorys", //数据统计详情
    name: "PrevieWaccessorys",
    component: () => import("@/views/fzEnd/triageAdmin/PrevieWaccessorys.vue"),
  },
];

export default fzRouter;
