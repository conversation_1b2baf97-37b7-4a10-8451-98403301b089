import { Router, RouteRecordRaw } from "vue-router";
import useStore from "@/store";

// 白名单(不需要登录检测)
const whiteList = [
  "/login",
  "/forgetPassword",
  "/caseVideo",
  "/privacy",
  "/agreement",
  "/details",
  "/shareReport",
  "/shareImage",
  "/inspect",
];

export function CreateRouterGuards(router: Router) {
  router.beforeEach(async (to) => {
    const { tokens, routers } = useStore();
    // console.log("router", routers);

    if (!whiteList.includes(to.path)) {
      // 如果没有登录 跳转到登录页页面

      if (!tokens.token) {
        router.replace({
          path: "/login",
        });
        // window.uni.getEnv(function (res: { h5: any }) {
        //   if (res.h5) {
        //     router.replace({
        //       path: "/login",
        //     });
        //   } else {
        //     window.uni.reLaunch({
        //       url: "/pages/login/Login",
        //     });
        //   }
        // });
        return;
      }
    }

    //   // 如果路由没有初始化 则初始化路由
    if (routers.router.length === 0) {
      const list = (await routers.getAsyncRouter()) as RouteRecordRaw[];
      list.forEach((item) => {
        router.addRoute(item);
      });
      return to.fullPath;
    }
    return;
  });

  router.afterEach((to) => {});

  router.onError((error) => {
    console.log(error);
  });
}
