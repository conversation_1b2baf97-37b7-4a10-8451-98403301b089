import { RouteRecordRaw } from "vue-router";
//专家端
const zjRouter: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/myout", //我的
    name: "Myout",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/alterCode", //修改密码
    name: "AlterCode",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/reminder", //修改密码
    name: "Reminder",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
  {
    path: "/regulates", //规章制度
    name: "Regulates",
    component: () => import("@/views/zjEnd/regulates/Index.vue"),
  },
  {
    path: "/feedback", //反馈
    name: "Feedback",
    component: () => import("@/views/zjEnd/regulates/Feedback.vue"),
  },
  {
    path: "/report", //报告
    name: "Report",
    component: () => import("@/views/zjEnd/regulates/Report.vue"),
  },
  {
    path: "/examineDetails", //审核详情
    name: "ExamineDetails",
    component: () => import("@/views/zjEnd/regulates/ExamineDetails.vue"),
  },
  {
    path: "/invitationReview", //邀请复审
    name: "InvitationReview",
    component: () => import("@/views/zjEnd/regulates/InvitationReview.vue"),
  },

  {
    path: "/previeWaccessory", //预览附件
    name: "PrevieWaccessory",
    component: () => import("@/views/zjEnd/regulates/PrevieWaccessory.vue"),
  },
  {
    path: "/collect", //收藏
    name: "Collect",
    component: () => import("@/views/zjEnd/collect/Index.vue"),
  },
  {
    path: "/subscribe", //订阅
    name: "Subscribe",
    component: () => import("@/views/zjEnd/subscribe/Index.vue"),
  },
  {
    path: "/sendBack", //退回
    name: "SendBack",
    component: () => import("@/views/zjEnd/subscribe/SendBack.vue"),
  },
  {
    path: "/statistics", //统计
    name: "Statistics",
    component: () => import("@/views/zjEnd/statistics/Index.vue"),
  },
  {
    path: "/withdrawReason", //撤回理由
    name: "WithdrawReason",
    component: () => import("@/views/zjEnd/regulates/withdrawReason.vue"),
  },
];

export default zjRouter;
