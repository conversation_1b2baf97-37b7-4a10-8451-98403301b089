import { createRouter, createWeb<PERSON>ashHistory, RouteRecordRaw } from "vue-router";
import { App } from "vue";
import Login from "@/views/login/Index.vue";
import { CreateRouterGuards } from "@/router/guards";
import fzRouter from "./fzEnd";
import zjRouter from "./zjEnd";
export const BaseRoutes: Array<RouteRecordRaw> = [
  {
    path: "/login",
    name: "Login",
    component: Login,
  },
  {
    path: "/forgetPassword",
    name: "ForgetPassword",
    component: () => import("@/views/login/ForgetPassword.vue"),
  },
  {
    path: "/caseVideo",
    name: "CaseVideo",
    component: () => import("@/views/zjEnd/regulates/CaseVideo.vue"),
  },
  {
    path: "/privacy",
    name: "Privacy",
    component: () => import("@/views/privacy/Index.vue"),
  },
  {
    path: "/agreement",
    name: "Agreement",
    component: () => import("@/views/privacy/Agreement.vue"),
  },
  {
    path: "/details", //详情
    name: "Details",
    component: () => import("@/views/zjEnd/regulates/Details.vue"),
  },
  {
    path: "/shareReport", //分享报告
    name: "ShareReport",
    component: () => import("@/views/zjEnd/regulates/shareReport.vue"),
  },
  {
    path: "/shareImage", //分享报告
    name: "ShareImage",
    component: () => import("@/views/zjEnd/regulates/shareImage.vue"),
  },
  {
    path: "/inspect", //送检须知
    name: "inspect",
    component: () => import("@/views/zjEnd/regulates/Inspect.vue"),
  },
  {
    path: "/updateLog", //更新日志
    name: "UpdateLog",
    component: () => import("@/views/zjEnd/regulates/updateLog.vue"),
  },
  {
    path: "/aboutUs", //关于我们
    name: "AboutUs",
    component: () => import("@/views/zjEnd/home/<USER>"),
  },
];
export const PageNotFound: RouteRecordRaw = {
  path: "/:path(.*)",
  name: "404",
  component: () => import("@/views/errer/Index.vue"),
};

//专家router
export const zjRoutes: RouteRecordRaw[] = zjRouter;
//分诊router
export const fzRoutes: RouteRecordRaw[] = fzRouter;
const router = createRouter({
  history: createWebHashHistory(),
  routes: BaseRoutes,
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});
export function mountRouter(app: App) {
  app.use(router);
  CreateRouterGuards(router);
}
export default router;
