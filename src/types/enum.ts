/**
 * 枚举转Map
 * @param data 枚举
 * @returns
 */
export function Enum2Map(data: Record<string, string | number>) {
  const m = new Map<number, string>([]);

  for (const key in data) {
    if (Object.hasOwnProperty.call(data, key)) {
      const value = data[key];
      const _k = Number(key);
      if (!Number.isNaN(_k)) {
        m.set(_k, value.toString());
      }
    }
  }

  return m;
}

/** tab切换 */
export enum TableTab {
  待诊断 = 1,
  复核,
  已发布,
  受邀诊断,
  退回,
}
/**分诊tab切换 */
export enum TriageTab {
  待分诊 = 1,
  已分诊,
  被退回,
  待撤回,
}
/** 分诊冰冻预约tab切换 */
export enum TriageFrozenTab {
  待分诊 = 1,
  已预约,
  待撤回,
  已退回,
}

/**根据状态返回颜色 */
export function getStatusColor(status: "未超时" | "已超时" | "即将超时") {
  let color = "";
  switch (status) {
    case "未超时":
      color = "#4D545F";
      break;
    case "已超时":
      color = "#F53F3F";
      break;
    case "即将超时":
      color = "#F9C55A";
      break;
  }
  return color;
}

// 专家端当前状态
export enum ExpertStatus {
  noReply = 1, //未回复
  replied = 2, //已回复
  withdrawalReport = 5, //撤回报告中
  inDiagnosis = 6, //诊断中
  toBeDiagnosed = 7, //待诊断
  reviewReturn = 8, //复核退回
  toReview = 9, //复核
  underReview = 10, //被退回
  returned = 11, //退回中
  published = 13, //已发布
}

//获取专家端的状态类型
// 各种状态的颜色值
export enum StatusColor {
  pink = "#61BED3", //报告撤回中
  orange = "#F9C55A", //诊断中
  red = "#FF7272", //未回复、被退回
  lightPurple = "#DF67BB", //待诊断、待复核
  purple = "#8600EF", //复核中
  green = "#BEB833", //已诊断、已提交、已发布、已回复
  blue = "#58BAD1", //可编辑
}
export function GetExpertStatusAndColor(
  status: ExpertStatus,
  statusName: string
): { text: string; background: string } {
  let background = "";
  switch (status) {
    case ExpertStatus.noReply:
      background = StatusColor.red;
      break;
    case ExpertStatus.replied:
      background = StatusColor.green;
      break;
    case ExpertStatus.withdrawalReport:
      background = StatusColor.pink;
      break;
    case ExpertStatus.inDiagnosis:
      background = StatusColor.orange;
      break;
    case ExpertStatus.toBeDiagnosed:
      background = StatusColor.lightPurple;
      break;
    case ExpertStatus.reviewReturn:
      background = StatusColor.red;
      break;
    case ExpertStatus.toReview:
      background = StatusColor.purple;
      break;
    case ExpertStatus.underReview:
      background = StatusColor.red;
      break;
    case ExpertStatus.returned:
      background = StatusColor.orange;
      break;
    case ExpertStatus.published:
      background = StatusColor.green;
      break;
    default:
      background = StatusColor.blue;
      break;
  }
  if (isStatusTimeout(statusName)) {
    background = StatusColor.red;
  }
  return {
    text: statusName,
    background,
  };
}
function isStatusTimeout(name: string) {
  if (name && name.indexOf("超时") !== -1) {
    return true;
  }
}
