declare module "RcpWeb" {
  declare const RemoteUrl: string; //测试接口地址
  declare const WebSocketUrl: string; //WebSocket插件连接地址
  declare const WebSocketProxyUrl: string; //WebSocket代理插件连接地址
  declare const decodeSdpcCoreJsPath: string; //切片解码库地址
  declare const decodeSdpcCoreWasmPath: string; //切片解码库地址
  declare const sharePath: string; ////分享地址
  declare const Bucket: string;
  declare const HomeTitle: string;
  declare const BackEndUrl: string; //后端解码接口地址
  declare const IsBackEnd: boolean; //是否后端解码
  declare const readingStore: string; //后台切片获取需要的地址
  declare const plugStorageUploaUrl: string; //后台切片获取需要的地址
  declare const IsEvaluate: Boolean; //是否开启切片评价
}
