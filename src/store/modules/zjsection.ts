import { defineStore } from "pinia";
import { Sdpc, SdpcWs } from "sdpc-web-ts";
import { cloneDeep } from "lodash";
import RcpWeb from "RcpWeb"; //项目配置
export default defineStore("zjsection", {
  state: () => ({
    sectionList: [] as Array<defs.CaseSliceDto>,
    /**选中的切片 */
    selectSection: {} as defs.CaseSliceDto,
    /**切片详细信息 */
    sectionDetail: {} as any,
    /**版本弹窗是否打开 */
    versionsShow: false,
    /**病例数据 */
    fillingDetails: {} as defs.WriteDiagnosticsDto,
  }),
  actions: {
    // 获取切片信息
    async getSectionAsync(params: API.userCommonApi.getSliceList.Params) {
      params.SliceType = 0;
      const sdpcws = ref();
      const res = await API.userCommonApi.getSliceList.request({ params });
      this.sectionList = res.Data as Array<defs.CaseSliceDto>;
      const list = await this.getSliceFileListAsync(params);
      this.sectionList = this.sectionList.concat(list || []);
      this.sectionList.forEach(async (item) => {
        if (
          item.Status === 2 &&
          item.SliceUrl &&
          !item.SliceMacroImageUrl &&
          item.FileType !== "dcm"
        ) {
          const sdpc = new Sdpc(
            item.SliceUrl as string,
            RcpWeb.decodeSdpcCoreJsPath,
            RcpWeb.decodeSdpcCoreWasmPath
          );
          const url1 = (await sdpc.getMacroPathologicalImageUrl()) as string;
          if (url1) {
            item.SliceMacroImageUrl = url1;
          }
        }
        if (
          (item.DiskPath != "web" && !item.SliceUrl && item.Status === 1) ||
          item.FileType === "dcm"
        ) {
          const res = await API.accountApi.getGetpresigned.request({
            params: {
              Str: item.Thumbnail,
            },
          });
          item.SliceThumbnail = res.Data?.Str as any;
          const res1 = await API.accountApi.getGetpresigned.request({
            params: {
              Str: item.MacroImageUrl,
            },
          });
          item.SliceMacroImageUrl = res1.Data?.Str as any;
          const res2 = await API.accountApi.getGetpresigned.request({
            params: {
              Str: item.Label,
            },
          });
          item.SliceLabel = res2.Data?.Str as any;
        }
      });

      setTimeout(() => {
        if (sdpcws.value) {
          sdpcws.value.wss.close();
        }
      }, 5000);
    },
    /**获取切片文件列表 */
    async getSliceFileListAsync(params: API.userCommonApi.getSliceList.Params) {
      params.SliceType = 1;
      const res = await API.userCommonApi.getSliceList.request({ params });
      return res.Data as Array<defs.CaseSliceDto>;
    },
    //清除切片信息
    clearSection() {
      this.sectionList = [];
      this.selectSectionFn({});
      this.sectionDetailFn({});
      this.setFillingDetails({});
    },
    //选中的切片
    selectSectionFn(params: defs.CaseSliceDto) {
      this.selectSection = params;
    },
    //切片详细信息
    sectionDetailFn(params: any) {
      this.sectionDetail = params;
    },
    /**
     * 切片质量评价
     * @param val
     */
    casequalityEvaluation(val: number) {
      API.caseUploadsliceApi.postEvaluationUpdate.request({
        bodyParams: {
          CaseUploadsliceId: this.selectSection.Id,
          Evaluation: val,
        },
      });
    },
    /**切片质量评价,不合格多选*/
    casequalityEvaluationFalse(list: string[]) {
      API.caseUploadsliceApi.postSliceDescriptionUpdate.request({
        bodyParams: {
          CaseUploadsliceId: this.selectSection.Id,
          SliceDescription: list.length > 0 ? list.toString() : "",
        },
      });
    },
    /**设置病例数据 */
    setFillingDetails(data: defs.WriteDiagnosticsDto) {
      this.fillingDetails = data;
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: "li_mobile_zjsection",
        storage: localStorage,
        paths: ["versionsShow"],
      },
    ],
  },
});
