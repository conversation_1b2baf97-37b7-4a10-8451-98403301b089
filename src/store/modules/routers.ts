import router, { zjRoutes, fzRoutes, BaseRoutes, PageNotFound } from "@/router";
import { RouteRecordRaw } from "vue-router";
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
import { Toast } from "vant";
interface IState {
  router: RouteRecordRaw[];
  asyncRouter: RouteRecordRaw[];
}

export default defineStore("routers", {
  state: (): IState => {
    return {
      router: [],
      asyncRouter: [],
    };
  },
  actions: {
    getAsyncRouter() {
      return new Promise((resolve) => {
        const userInfo = getLocalStorage(LocalStorageName.user);

        if (userInfo?.RoleId === 2) {
          this.asyncRouter = [...zjRoutes, PageNotFound];
        } else if (userInfo?.RoleId === 3) {
          this.asyncRouter = [...fzRoutes, PageNotFound];
        } else {
          this.asyncRouter = [PageNotFound]; // 处理其他角色或未定义角色的情况
        }

        this.router = BaseRoutes.concat(this.asyncRouter);
        resolve(this.asyncRouter);
      });
    },
  },
});
