import { Aes } from "@/utils/aesEncrypt";

export default defineStore("oss", {
  state: () => ({
    config: <defs.OssConfigDto | null>null,
    refreshTime: <Date | null>null,
  }),
  getters: {
    isExpired: (state: any) => {
      //是否已过期
      if (state.refreshTime) {
        return new Date().getTime() - state.refreshTime.getTime() < 360000
          ? false
          : true;
      } else {
        return true;
      }
    },
  },
  actions: {
    //设置oss配置
    setOssConfig(config: defs.OssConfigDto) {
      this.config = config;
    },
    //获取oss配置
    async getOssConfig(): Promise<defs.OssConfigDto | null | undefined> {
      if (!this.refreshTime) {
        this.refreshTime = new Date();
      }
      if (!this.isExpired && this.config) {
        return this.config;
      } else {
        try {
          const result = await API.accountApi.getOss.request({});
          if (result && result.Data) {
            result.Data.AccessKeyId = Aes.Decrypt(
              result.Data.AccessKeyId as string
            );
            result.Data.AccessKeySecret = Aes.Decrypt(
              result.Data.AccessKeySecret as string
            );
            // result.Data.Endpoint = Aes.Decrypt(result.Data.Endpoint as string);
            result.Data.Token = Aes.Decrypt(result.Data.Token as string);

            this.refreshTime = new Date();
            this.setOssConfig(result.Data);
          }
          return result.Data;
        } catch (error) {
          return null;
        }
      }
    },
  },
});
