import { Aes } from "@/utils/aesEncrypt";

export default defineStore("h3c", {
  state: () => ({
    config: <defs.OssConfigDto | null>null,
    refreshTime: <Date | null>null,
  }),
  getters: {
    isExpired: (state: any) => {
      //是否已过期
      if (state.refreshTime) {
        return new Date().getTime() - state.refreshTime.getTime() < 360000
          ? false
          : true;
      } else {
        return true;
      }
    },
  },
  actions: {
    //设置oss配置
    setH3cConfig(config: defs.H3cConfigDto) {
      this.config = config;
    },
    //获取h3c配置
    async getH3cConfig(): Promise<defs.H3cConfigDto | null | undefined> {
      if (!this.refreshTime) {
        this.refreshTime = new Date();
      }
      if (!this.isExpired && this.config) {
        return this.config;
      } else {
        try {
          // const result = ({Data:1});
          const result = await API.accountApi.getH3c.request({});

          if (result && result.Data) {
            result.Data.AccessKeyId = Aes.Decrypt(
              result.Data.AccessKeyId as string
            );

            result.Data.AccessKeySecret = Aes.Decrypt(
              result.Data.AccessKeySecret as string
            );

            this.setH3cConfig(result.Data);
          }
          // console.log(result.Data);
          return result.Data;
        } catch (error) {
          return null;
        }
      }
    },
  },
});
