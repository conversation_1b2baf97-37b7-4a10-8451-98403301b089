import { defineStore } from "pinia";
export default defineStore("selectColor", {
  state: () => ({
    //选中颜色
    selectColor: "#FF2020",

    //选中导航栏
    selectNav: 1,
    //冰冻预约选中导航栏
    selectNav1: 1,

    //画线宽度
    strokeWidth: 1,
    //批量分配id数组
    batchIdList: [],
    /**是否主视频播放 */
    isMainVideoPlay: false,
  }),
  actions: {
    // 选中颜色
    selectColorFn(params: string) {
      this.selectColor = params;
    },
    //选中导航栏
    selectNavFn(params: number) {
      this.selectNav = params;
    },
    //冰冻预约选中导航栏
    selectNav1Fn(params: number) {
      this.selectNav1 = params;
    },
    //画线宽度
    strokeWidthFn(params: number) {
      this.strokeWidth = params;
    },
    //批量分配id数组
    batchIdListFn(params: any) {
      this.batchIdList = params;
    },
    /**是否主视频播放 */
    isMainVideoPlayFn(params: boolean) {
      this.isMainVideoPlay = params;
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: "li_mobile_selectColor",
        storage: localStorage,
        paths: ["selectNav", "selectNav1"],
      },
    ],
  },
});
