import useStore from "@/store";
import { H3c<PERSON><PERSON><PERSON> } from "@/utils/h3c";
import { defineStore } from "pinia";
export default defineStore("label", {
  state: () => ({
    /**标注信息 */
    markLabels: [] as any[],
  }),
  actions: {
    /**获取标注接口 */
    async getLabelList(id: number) {
      this.markLabels = [];
      const res: any =
        await API.pathologicalSectionMarkApi.getPathologicalSectionMark.request(
          {
            params: {
              id: id,
            },
          }
        );
      if (Object.prototype.toString.call(res) == "[object Object]") {
        this.markLabels = res.GroupModel?.Labels;
      }
    },
    /**保存或更新标注 */
    async saveOrUpdateLabel(data: object) {
      const { zjsection } = useStore();
      await H3cHelper.SimpleUploadThumbnailDirectory(
        `${zjsection.selectSection.Id}.json`,
        JSON.stringify(data)
      );
      await API.pathologicalSectionMarkApi.postPathologicalSectionMark.request({
        bodyParams: {
          Marks: `${zjsection.selectSection.Id}.json`,
          PathologicalSectionId: zjsection.selectSection.Id as number,
        },
      });
    },
    /**删除标注 */
    deleteLabel() {
      this.markLabels = [];
    },
  },
});
