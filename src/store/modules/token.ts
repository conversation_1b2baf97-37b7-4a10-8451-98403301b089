/**
 * vuex里的token模块
 */
import LocalstorageHelper from "@/utils/localStorageHelper";
import { clearLocalStorage } from "@/utils/storage";
import { defineStore } from "pinia";
import router from "@/router";
export default defineStore("token", {
  state: () => ({
    token: LocalstorageHelper.getToken() || "",
  }),
  // getters: {
  //   //获取token
  //   getToken(): any {
  //     return this.token || LocalstorageHelper.getToken();
  //   }
  // },
  actions: {
    setToken(token: string) {
      this.token = token;
      LocalstorageHelper.setToken(token);
    },
    //清除token
    clearToken() {
      this.token = LocalstorageHelper.getToken() || "";
      LocalstorageHelper.clearToken(LocalstorageHelper.keys.token);
    },
    redirectLogin() {
      console.log(router.currentRoute.value, "router");

      clearLocalStorage();
      setTimeout(() => {
        if (router.currentRoute.value.path === "/caseVideo") {
          router.push({
            path: "/login",
            query: {
              redirect: router.currentRoute.value.fullPath,
            },
          });
        } else {
          router.push({
            path: "/login",
          });
        }
      }, 200);
    },
  },
});
