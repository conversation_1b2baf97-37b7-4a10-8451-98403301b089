import useTokenStore from "./modules/token";
import useRoutersStore from "./modules/routers";
import useZjsectionStore from "./modules/zjsection";
import useSaveCaseStore from "./modules/saveCase";
import useSelectColorStore from "./modules/selectColor";
import useH3cStore from "./modules/h3c";
import useOssStore from "./modules/oss";
import useLableStore from "./modules/label";
export default function useStore() {
  return {
    tokens: useTokenStore(),
    routers: useRoutersStore(),
    zjsection: useZjsectionStore(),
    saveCase: useSaveCaseStore(),
    selectColor: useSelectColorStore(),
    h3c: useH3cStore(),
    oss: useOssStore(),
    label: useLableStore(),
  };
}
