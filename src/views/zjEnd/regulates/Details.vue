<template>
  <div class="container">
    <!--顶部导航 start -->
    <NavBar :title="titles" :leftAarrow="tokens.token ? true : false" />
    <!--顶部导航 end -->
    <!--引入头部病人信息组件-->
    <PatientInfo
      :dataDetails="caseInfo"
      v-model:collectShow="collectShow"
      :reportTemplateShow="reportTemplateShow"
      :shareUrl="shareUrl"
      v-if="[1, 2, 3, 4, 5, 7, 8].includes(Number(category))"
    />
    <!--切换按钮-->
    <CaseInfoTab
      :activeName="activeTab"
      @nav-click="getChild"
      v-if="[1, 2, 3, 4, 5, 7, 8].includes(Number(category))"
    />
    <!--详情展示-->
    <!--病例信息-->
    <CaseInfoContent
      ref="infoComponent"
      :data="caseInfo"
      :appointment="appointment"
      :firstReview="firstReview"
      @messageClick="messageClick"
    />
    <!--切片信息-->
    <CaseSliceContent
      v-if="category !== '6'"
      ref="sliceinfoComponent"
      :sliceEvaluation="true"
    />
    <!--附件信息-->
    <CaseFileContent ref="fileinfoComponent" :fileList="fileList" />
    <!-- 邀请意见-->
    <div ref="otherinfoComponent" v-if="!tokens.token"></div>
    <CaseOpinion
      ref="otherinfoComponent"
      :list="list"
      v-if="[1, 2, 3, 4, 5, 7, 8].includes(Number(category)) && tokens.token"
    />

    <!-- 占位 -->
    <div class="occupied"></div>
    <!--详情底部按钮-->
    <CaseInfoBottomBtn
      :pathologyNumber="pathologyNumber"
      @confirmReplys="messageShow = true"
      @republishClick="republishClick"
      v-if="
        [1, 2, 3, 4].includes(Number(category)) ||
        (category === '6' && status === '7' && tokens.token)
      "
    />
    <van-popup v-model:show="show" round :style="{ width: '80%' }">
      <ReviewOpinion :firstReview="firstReview" />
    </van-popup>

    <van-popup v-model:show="messageShow" round :style="{ width: '80%' }">
      <!-- 回复意见 start -->
      <CaseReplyLate
        v-if="category === '4' && tokens.token && status === '1'"
        @messageLate="messageLate"
      />
      <!-- 回复意见 end -->
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import PatientInfo from "@/components/case-detail/PatientInfo.vue";
import CaseInfoTab from "@/components/case-detail/CaseInfoTab.vue";
import CaseInfoContent from "@/components/case-detail/CaseInfoContent.vue";
import CaseSliceContent from "@/components/case-detail/CaseSliceContent.vue";
import CaseFileContent from "@/components/case-detail/CaseFileContent.vue";
import CaseOpinion from "@/components/case-detail/CaseOpinion.vue";
import CaseInfoBottomBtn from "@/components/case-detail/CaseInfoBottomBtn.vue";
import CaseReplyLate from "@/components/case-detail/CaseReplyLate.vue";
import ReviewOpinion from "./components/ReviewOpinion.vue";
import { SliceDto } from "@/api/zjEnd/regulates.types";
import { GetCaseinfor } from "@/api/zjEnd/regulates";
import InvitingOpinions from "@/components/case-detail/InvitingOpinions.vue";
import leaveWord from "@/components/case-detail/leaveWord.vue";
import MedicalHistory from "@/components/case-detail/MedicalHistory.vue";
import DoctorAdvice from "@/components/case-detail/DoctorAdvice.vue";
import { Ref } from "vue";
import useStore from "@/store";
import { Toast } from "vant";
import { Sdpc, SdpcWs } from "sdpc-web-ts";
import RcpWeb from "RcpWeb"; //项目配置
const { zjsection, tokens } = useStore();

const route = useRoute();
const router = useRouter();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
const show = ref(false);
//回复弹窗
const messageShow = ref(false);
//navbar名称
//分享报告url
const shareUrl = ref("");
const { id, category, status } = route.query;
const titles = computed(() => {
  if (Number(category) === 2) {
    return "复核";
  } else if (Number(category) === 4) {
    return "回复";
  } else {
    return "查看";
  }
});
const list = reactive([
  {
    title: "邀请意见",
    id: 1,
    isActive: true,
    component: markRaw(InvitingOpinions),
  },
  {
    title: "留言",
    id: 2,
    isActive: false,
    component: markRaw(leaveWord),
  },
  {
    title: "病史",
    id: 3,
    isActive: false,
    component: markRaw(MedicalHistory),
  },
  {
    title: "医嘱",
    id: 4,
    isActive: false,
    component: markRaw(DoctorAdvice),
  },
]);
const reportTemplateShow = computed(() => {
  if (["9", "13"].includes(status as string)) {
    return true;
  } else {
    return false;
  }
});

onMounted(async () => {
  if (tokens.token) {
    if (id) {
      params.Id = Number(id);
      await zjsection.getSectionAsync(params);
      if (category === "6") {
        //预约信息
        const res = await API.userCommonApi.getFrozenRendering.request({
          params: {
            Id: Number(id),
          },
        });
        appointment.value = res.Data;
      } else {
        const res = await API.userCommonApi.getCasePendering.request({
          params: {
            Id: Number(id),
          },
        });
        caseInfo.value = res.Data;
        pathologyNumber.value = res.Data?.PathologyNumber;
        // if (zjsection.sectionList.length === 0) {
        //   await getSliceList(params);
        // }
        // sectionList.value = zjsection.sectionList as SliceDto[];
        // console.log(zjsection.sectionList);

        await collectShows(Number(id));
      }
      //获取附件信息
      await getFileList(Number(id));
      if (category === "2") {
        await getFirstReview(Number(id));
      }
    }
  } else {
    const sdpcws = ref();
    const res = await GetCaseinfor(id as string);
    caseInfo.value = res.data.WriteDiagnosticsDto;
    sectionList.value = res.data.SliceList.Data;
    fileList.value = res.data.AnnexList.Data;
    sectionList.value.forEach(async (item) => {
      if (item.Status === 2) {
        const sdpc = new Sdpc(
          item.SliceUrl as string,
          RcpWeb.decodeSdpcCoreJsPath,
          RcpWeb.decodeSdpcCoreWasmPath
        );
        const url1 = (await sdpc.getMacroPathologicalImageUrl()) as string;
        if (url1) {
          item.SliceMacroImageUrl = url1;
        }
      }
      if (item.DiskPath != "web" && !item.SliceUrl && item.Status === 1) {
        sdpcws.value = new SdpcWs(true);
        sdpcws.value.open(
          {
            uploadId: item.CreateUser,
            DiskPath: item.DiskPath,
            expertsId: 214539692229888,
          },
          true,
          RcpWeb.WebSocketProxyUrl
        );
        let timer = 0;
        timer = setInterval(async () => {
          if (sdpcws.value.wss.readyState === 1) {
            clearInterval(timer);
            console.log("连接成功");

            const sdpc = new Sdpc(
              sdpcws.value,
              RcpWeb.decodeSdpcCoreJsPath,
              RcpWeb.decodeSdpcCoreWasmPath
            );
            const url1 = (await sdpc.getMacroPathologicalImageUrl()) as string;
            if (url1) {
              item.SliceMacroImageUrl = url1;
            }
            const url2 = (await sdpc.getThumbImageUrl()) as string;
            if (url2) {
              item.SliceThumbnail = url2;
            }
            const url3 = (await sdpc.getMacroLabelImageUrl()) as string;
            if (url3) {
              item.SliceLabel = url3;
            }
          }
        }, 1000);
      }
    });
    setTimeout(() => {
      if (sdpcws.value) {
        sdpcws.value.wss.close();
      }
    }, 5000);
    zjsection.sectionList = res.data.SliceList.Data;
    // if (res.data.Report) {
    //   reportTemplateShow.value = true;
    // } else {
    //   reportTemplateShow.value = false;
    // }
    shareUrl.value = res.data.Report;
  }
});
//获取切片列表
const getSliceList = async (params: API.userCommonApi.getSliceList.Params) => {
  await zjsection.getSectionAsync(params);
  setTimeout(() => {
    sectionList.value = zjsection.sectionList as SliceDto[];
  }, 2000);
  if (zjsection.sectionList.length > 0) {
    thumbnailUrl.value = zjsection.sectionList[0].SliceThumbnail;
  }
};
//预约数据
const appointment: Ref<defs.FrozenInformationDto | undefined> = ref();
//病例信息data
const caseInfo: Ref<any> = ref(undefined);
const pathologyNumber: Ref<string | undefined> = ref(undefined);
//切片信息data
const sectionList: Ref<SliceDto[]> = ref([]);
const thumbnailUrl: Ref<string | undefined> = ref("");
//--切换按钮--
const activeTab = ref("case");
type CaseSliceContentCtx = InstanceType<typeof CaseSliceContent>;
const sliceinfoComponent: Ref<null | CaseSliceContentCtx> = ref(null);
type CaseFileContentCtx = InstanceType<typeof CaseFileContent>;
const fileinfoComponent = ref<null | CaseFileContentCtx>(null);
type CaseOpinionCtx = InstanceType<typeof CaseOpinion>;
const otherinfoComponent = ref<null | CaseOpinionCtx>(null);
type infoComponentCtx = InstanceType<typeof CaseInfoContent>;
const infoComponent = ref<null | infoComponentCtx>(null);
const getChild = (tab: string) => {
  let el = null;
  console.log(infoComponent.value?.$el);

  switch (tab) {
    case "case":
      el = infoComponent.value?.$el;
      break;
    case "slice":
      el = sliceinfoComponent.value?.$el;
      break;
    case "file":
      el = fileinfoComponent.value?.$el;
      break;
    case "other":
      el = otherinfoComponent.value?.$el;
      break;
    default:
      return;
  }
  if (el) {
    el.scrollIntoView();
  }
  activeTab.value = tab;
};

//是否收藏
const collectShow = ref(false);
const collectShows = async (id: number) => {
  const res = await API.expertFunctionApi.getCollect.request({
    params: {
      Id: id,
    },
  });
  collectShow.value = res.Data?.IsFalse as boolean;
};

//确认回复
const message = ref("");
const messageLate = (res: string) => {
  message.value = res;
};

//获取附件信息
const fileList: Ref<Array<defs.AnnexDto> | undefined> = ref();
const getFileList = async (id: number) => {
  const res = await API.userCommonApi.getAnnexList.request({
    params: {
      Id: id,
      PageIndex: 1,
      PageSize: 20,
    },
  });
  fileList.value = res.Data;
};

//获取初审意见
const firstReview: Ref<defs.ReviewOpinionDto | undefined> = ref();
const getFirstReview = async (id: number) => {
  const res = await API.expertReviewApi.getReviewOpinion.request({
    params: {
      Id: id,
    },
  });
  firstReview.value = res.Data;
};
//复核意见
const reviewerMessage = ref();
const messageClick = (res: string) => {
  reviewerMessage.value = res;
};
//重新发布

const republishClick = async () => {
  show.value = true;
};
</script>

<style lang="scss" scoped>
.image-box {
  width: 500px;
  height: 500px;
}
</style>
