<template>
  <div class="review">
    <div class="review-title">复核</div>
    <div class="review-content">初审意见：{{ props.firstReview?.Opinion }}</div>
    <div class="review-title" style="margin-top: 24px">复核意见</div>
    <div class="review-content-center">
      <van-field
        v-model="reviewerMessage"
        rows="4"
        autosize12
        show-word-limit
        type="textarea"
        :maxlength="300"
        placeholder="请输入"
      />
    </div>
    <div style="display: flex; justify-content: center">
      <van-button class="case-diagnose" color="#650FAA" @click="republishClick"
        >重新生成报告</van-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import { Toast } from "vant";
import { PropType } from "vue";
import useStore from "@/store";
const props = defineProps({
  //初审意见
  firstReview: {
    type: Object as PropType<defs.ReviewOpinionDto>,
    default: {},
  },
});
const router = useRouter();
const route = useRoute();
const { id } = route.query;
const { saveCase } = useStore();
const reviewerMessage = ref("");

const republishClick = async () => {
  if (!reviewerMessage.value) {
    return Toast.fail("请输入复核意见");
  }
  await API.expertReviewApi.postUpdateReviewOpinion.request({
    bodyParams: {
      Id: Number(id),
      Str: reviewerMessage.value,
    },
  });
  const resDatails = await API.expertFunctionApi.getDiagnosis.request({
    params: {
      Id: Number(id),
    },
  });
  const reportParam: any = new defs.CaseReportReqModel();
  reportParam.Opinion = reviewerMessage.value as string; //诊断意见
  reportParam.JsonData = resDatails?.Data?.JsonData || "";
  reportParam.IsReview = true;
  reportParam.Id = Number(id);
  saveCase.saveCase(reportParam);
  // await API.expertFunctionApi.putRelease.request({
  //   bodyParams: reportParam,
  // });

  router.push({
    path: "/report",
    query: {
      caseId: Number(id),
      recheck: 1,
    },
  });
};
</script>
<style lang="scss" scoped>
.review {
  padding: 16px;
}
.review-title {
  margin-bottom: 12px;
  font-size: 17px;
  font-weight: bold;
  line-height: 19.89px;
  color: #000000;
}
.review-content {
  padding: 6px 9px;
  height: 60px;
  font-size: 12px;
  font-weight: normal;
  line-height: 14px;
  color: #8b96a6;
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  overflow-y: auto;
}
.review-content-center {
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  overflow: hidden;
}
:deep(.van-cell) {
  padding: 9px;
}
.case-diagnose {
  margin-top: 14px;
  width: 128px;
  height: 32px;
  border-radius: 16px;
}
</style>
