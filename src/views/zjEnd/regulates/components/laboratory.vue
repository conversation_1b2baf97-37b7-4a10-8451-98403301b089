<template>
  <div class="laboratory">
    <div class="title">实验室分析结果</div>
    <div class="title title1">TBS标准诊断</div>
    <div class="tbs">
      <based-liquid :flag="false" body="TBS标准诊断："></based-liquid>
      <based-liquid
        body="满意"
        :flags="true"
        v-model:checkAll="Satisfied.TBS_Satisfied"
      >
      </based-liquid>
      &nbsp;
      <based-liquid
        body="颈管细胞"
        :flags="true"
        v-model:checkAll="Satisfied.TBS_EndocervicalCells"
      >
      </based-liquid
      >&nbsp;
      <based-liquid
        body="化生细胞"
        :flags="true"
        v-model:checkAll="Satisfied.TBS_MetaplasticCells"
      >
      </based-liquid>
    </div>
    <div class="tbs">
      <based-liquid
        :flag="false"
        body="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
      >
      </based-liquid>
      <based-liquid
        body="不满意,（细胞量<10%）"
        :flags="true"
        v-model:checkAll="Satisfied.TBS_NotSatisfied"
      >
      </based-liquid>
    </div>

    <div class="title title1">鳞状上皮细胞分析：</div>
    <based-liquid
      class="weight"
      :flag="false"
      :body="BasedLiquids.microbial.title"
      :list="BasedLiquids.microbial.content"
      v-model:checkedCitiess="BasedLiquids.microbial.checkedCities"
    >
    </based-liquid>
    <br />
    <based-liquid
      class="weight"
      :body="BasedLiquids.ReactiveCells.title"
      :list="BasedLiquids.ReactiveCells.content"
      @sayHi="ReactiveCellsFathersay"
      @sayHis="ReactiveCellsFathersays"
      v-model:checkedCitiess="BasedLiquids.ReactiveCells.checkedCities"
      v-model:checkAll="ReactiveCells.SEC_RCC"
    ></based-liquid>
    <br />
    <based-liquid
      class="weight"
      :body="BasedLiquids.AtypicalSquamousCell.title"
      :list="BasedLiquids.AtypicalSquamousCell.content"
      @sayHi="AtypicalSquamousCellFathersay"
      @sayHis="AtypicalSquamousCellFathersays"
      v-model:checkedCitiess="BasedLiquids.AtypicalSquamousCell.checkedCities"
      v-model:checkAll="AtypicalSquamousCell.RCC_ASC"
    >
    </based-liquid>

    <div class="tbs">
      <based-liquid
        :flags="true"
        :body="BasedLiquids.LowParent.title"
        v-model:checkAll="LowParent.RCC_LHIL"
      ></based-liquid>
      <based-liquid
        :flags="true"
        :body="BasedLiquids.PromptHpvInfection.title"
        v-model:checkAll="PromptHpvInfection.RCC_HPV"
      >
      </based-liquid>
    </div>
    <div class="tbs">
      <based-liquid
        :flags="true"
        :body="BasedLiquids.HighParent.title"
        v-model:checkAll="HighParent.RCC_HSIL"
      ></based-liquid>
      <based-liquid
        :flags="true"
        :body="BasedLiquids.SquamousCellCarcinoma.title"
        v-model:checkAll="SquamousCellCarcinoma.RCC_SCC"
      >
      </based-liquid>
    </div>
    <div class="title title1">腺上皮细胞分析：</div>
    <based-liquid
      class="weight"
      :body="BasedLiquids.TheCervicalCanal.title"
      :list="BasedLiquids.TheCervicalCanal.content"
      @sayHi="AtypicalGlandularCellsFathersay"
      @sayHis="AtypicalGlandularCellsFathersays"
      v-model:checkedCitiess="BasedLiquids.TheCervicalCanal.checkedCities"
      v-model:checkAll="AtypicalGlandularCells.GEC_AGC_CervicalCanal"
    >
    </based-liquid>
    <br />
    <based-liquid
      class="weight"
      :body="BasedLiquids.endometrium.title"
      :list="BasedLiquids.endometrium.content"
      @sayHi="EndometrialAtypicalGlandCellsFathersay"
      @sayHis="EndometrialAtypicalGlandCellsFathersays"
      v-model:checkedCitiess="BasedLiquids.endometrium.checkedCities"
      v-model:checkAll="EndometrialAtypicalGlandCells.GEC_AGC_Endocervix"
    >
    </based-liquid>
    <br />
    <based-liquid
      :flags="true"
      :body="BasedLiquids.Parent.title"
      v-model:checkAll="UnknownAtypicalGlandCells.GEC_AGC_SourceUnknown"
    ></based-liquid>
    <br />
    <based-liquid
      class="weight"
      :body="BasedLiquids.adenocarcinoma.title"
      :list="BasedLiquids.adenocarcinoma.content"
      @sayHi="AdenocarcinomaFathersay"
      @sayHis="AdenocarcinomaFathersays"
      :checkedCitiess="BasedLiquids.adenocarcinoma.checkedCities"
      v-model:checkAll="Adenocarcinoma.GEC_Ade"
    ></based-liquid>
    <br />
    <based-liquid
      :flags="true"
      :body="BasedLiquids.otherMalignantTumors.title"
      v-model:checkAll="OtherMalignantTumors.GEC_OtherMalignantTumors"
    >
    </based-liquid>
  </div>
</template>
<script lang="ts" setup>
import BasedLiquid from "@/components/case-checkbox/CaseCheckBox.vue";

const props = defineProps({
  diagnosticRendering: {
    type: Object as any,
    default: () => ({}),
  },
});
let jsonstr = {
  ExpansionInformation: {},
  Header: null,
  BasicInformation: null,
  Bottom: null,
};
const form = ref();
const BasedLiquids = ref({
  NoLesionsSeen: {
    title: "未见上皮内病变或恶性病变",
    checkAll: false,
  },
  microbial: {
    title: "微生物",
    content: [
      "阴道滴虫",
      "真菌,形态上符合念珠菌属",
      "菌群变化, 提示细菌性阴道病", //
      "细菌,形态符合放线菌属", //
      "细胞变化符合单纯疱疹病毒感染", //
    ],
    checkedCities: [] as any,
  },
  ReactiveCells: {
    title: "反应性细胞改变",
    content: [
      "炎性（包括典型修复）",
      "萎缩", //
      "宫内节育器", //
      "放疗", //
      "其他", //
    ],
    checkedCities: [] as any,
    checkAll: false,
  },
  AtypicalSquamousCell: {
    title: "非典型鳞状细胞",
    content: [
      "炎性（包括典型修复）",
      "萎缩", //
    ],
    checkedCities: [] as any,
    checkAll: false,
  },
  LowParent: {
    title: "低度鳞状上皮内病变（LHIL）",
    checkAll: false,
  },
  PromptHpvInfection: {
    title: "提示HPV感染",
    checkAll: false,
  },
  HighParent: {
    title: "高度鳞状上皮内病变（HSIL）",
    checkAll: false,
  },
  SquamousCellCarcinoma: {
    title: "鳞状细胞癌",
    checkAll: false,
  },
  adenocarcinoma: {
    title: "腺癌",
    content: [
      "宫颈",
      "宫内膜", //
      "其他",
    ],
    checkedCities: [] as any,
    checkAll: false,
  },
  Parent: {
    title: "非典型腺细胞（来源不明）",
    checkAll: false,
  },
  TheCervicalCanal: {
    title: "非典型腺细胞（宫颈管）",
    content: ["非特异性改变", "倾向于肿瘤性病变"],
    checkedCities: [] as any,
    checkAll: false,
  },
  endometrium: {
    title: "非典型腺细胞（宫内膜）",
    content: ["非特异性改变", "倾向于肿瘤性病变"],
    checkedCities: [] as any,
    checkAll: false,
  },
  otherMalignantTumors: {
    title: "其他恶性肿瘤",
    checkAll: false,
  },
});

const Satisfied = ref({
  TBS_Satisfied: false,
  TBS_EndocervicalCells: false,
  TBS_MetaplasticCells: false,
  TBS_NotSatisfied: false,
});

let Microorganism: any = ref({
  SEC_Mic_HerpesSimplexVirus: false,
  SEC_Mic_TrichomonasVaginalis: false,
  SEC_Mic_Fungus: false,
  SEC_Mic_ChangesFlora: false,
  SEC_Mic_Bacteria: false,
});
let ReactiveCells: any = ref({
  SEC_RCC: false,
  SEC_RCC_Inflammatory: false,
  SEC_RCC_Shrink: false,
  SEC_RCC_IUD: false,
  SEC_RCC_Radiotherapy: false,
  SEC_RCC_Other: false,
});
let AtypicalSquamousCell: any = ref({
  RCC_ASC: false,
  RCC_ASC_US: false,
  RCC_ASC_H: false,
});
let LowParent: any = ref({
  RCC_LHIL: false,
});
let PromptHpvInfection: any = ref({
  RCC_HPV: false,
});
let HighParent: any = ref({
  RCC_HSIL: false,
});
let SquamousCellCarcinoma: any = ref({
  RCC_SCC: false,
});
let AtypicalGlandularCells: any = ref({
  GEC_AGC_CervicalCanal: false,
  GEC_AGC_CervicalCanal_Nonspecific: false,
  GEC_AGC_CervicalCanal_Specificity: false,
});
let EndometrialAtypicalGlandCells: any = ref({
  GEC_AGC_Endocervix: false,
  GEC_AGC_Endocervix_Nonspecific: false,
  GEC_AGC_Endocervix_Specificity: false,
});
let UnknownAtypicalGlandCells: any = ref({
  GEC_AGC_SourceUnknown: false,
});
let Adenocarcinoma: any = ref({
  GEC_Ade: false,
  GEC_Ade_Cervix: false,
  GEC_Ade_Endocervix: false,
  GEC_Ade_Other: false,
});
let OtherMalignantTumors: any = ref({
  GEC_OtherMalignantTumors: false,
});

watch(
  () => props.diagnosticRendering,
  (n) => {
    if (n) {
      form.value = n.JsonData ? JSON.parse(n.JsonData) : null;
      BasedLiquids.value.microbial.checkedCities = [];
      BasedLiquids.value.ReactiveCells.checkedCities = [];
      if (form.value.ExpansionInformation) {
        ExpansionInformation();
      }
    }
  },
  { immediate: true }
);
function ExpansionInformation() {
  BasedLiquids.value.microbial.checkedCities = [];
  BasedLiquids.value.ReactiveCells.checkedCities = [];
  BasedLiquids.value.AtypicalSquamousCell.checkedCities = [];
  BasedLiquids.value.AtypicalSquamousCell.checkedCities = [];
  BasedLiquids.value.TheCervicalCanal.checkedCities = [];
  //标准诊断
  if (form.value.ExpansionInformation?.TBS_Satisfied) {
    Satisfied.value.TBS_Satisfied =
      form.value.ExpansionInformation.TBS_Satisfied;
  }
  if (form.value.ExpansionInformation?.TBS_EndocervicalCells) {
    Satisfied.value.TBS_EndocervicalCells =
      form.value.ExpansionInformation.TBS_EndocervicalCells;
  }
  if (form.value.ExpansionInformation?.TBS_MetaplasticCells) {
    Satisfied.value.TBS_MetaplasticCells =
      form.value.ExpansionInformation.TBS_MetaplasticCells;
  }
  if (form.value.ExpansionInformation?.TBS_NotSatisfied) {
    Satisfied.value.TBS_NotSatisfied =
      form.value.ExpansionInformation.TBS_NotSatisfied;
  }
  //未见上皮
  if (form.value.ExpansionInformation?.SEC_NoLesionsSeen) {
    BasedLiquids.value.NoLesionsSeen.checkAll =
      form.value.ExpansionInformation.SEC_NoLesionsSeen;
  }
  //微生物
  if (form.value.ExpansionInformation?.SEC_Mic_TrichomonasVaginalis) {
    BasedLiquids.value.microbial.checkedCities.push("阴道滴虫");
  }
  if (form.value.ExpansionInformation?.SEC_Mic_HerpesSimplexVirus) {
    BasedLiquids.value.microbial.checkedCities.push(
      "细胞变化符合单纯疱疹病毒感染"
    );
  }
  if (form.value.ExpansionInformation?.SEC_Mic_Fungus) {
    BasedLiquids.value.microbial.checkedCities.push("真菌,形态上符合念珠菌属");
  }
  if (form.value.ExpansionInformation?.SEC_Mic_Bacteria) {
    BasedLiquids.value.microbial.checkedCities.push("细菌,形态符合放线菌属");
  }
  if (form.value.ExpansionInformation?.SEC_Mic_ChangesFlora) {
    BasedLiquids.value.microbial.checkedCities.push(
      "菌群变化, 提示细菌性阴道病"
    );
  }
  //反应性细胞改变
  if (form.value.ExpansionInformation?.SEC_RCC) {
    BasedLiquids.value.ReactiveCells.checkAll =
      form.value.ExpansionInformation.SEC_RCC;
  }
  if (form.value.ExpansionInformation?.SEC_RCC_Inflammatory) {
    BasedLiquids.value.ReactiveCells.checkedCities.push("炎性（包括典型修复）");
  }
  if (form.value.ExpansionInformation?.SEC_RCC_Shrink) {
    BasedLiquids.value.ReactiveCells.checkedCities.push("萎缩");
  }
  if (form.value.ExpansionInformation?.SEC_RCC_IUD) {
    BasedLiquids.value.ReactiveCells.checkedCities.push("宫内节育器");
  }
  if (form.value.ExpansionInformation?.SEC_RCC_Radiotherapy) {
    BasedLiquids.value.ReactiveCells.checkedCities.push("放疗");
  }
  if (form.value.ExpansionInformation?.SEC_RCC_Other) {
    BasedLiquids.value.ReactiveCells.checkedCities.push("其他");
  }
  //非典型细胞
  if (form.value.ExpansionInformation?.RCC_ASC) {
    BasedLiquids.value.AtypicalSquamousCell.checkAll =
      form.value.ExpansionInformation.RCC_ASC;
  }
  if (form.value.ExpansionInformation?.RCC_ASC_US) {
    BasedLiquids.value.AtypicalSquamousCell.checkedCities.push(
      "炎性（包括典型修复）"
    );
  }
  if (form.value.ExpansionInformation?.RCC_ASC_H) {
    BasedLiquids.value.AtypicalSquamousCell.checkedCities.push("萎缩");
  }
  //非典型腺细宫颈
  if (form.value.ExpansionInformation?.GEC_AGC_CervicalCanal) {
    BasedLiquids.value.TheCervicalCanal.checkAll =
      form.value.ExpansionInformation.GEC_AGC_CervicalCanal;
  }
  if (form.value.ExpansionInformation?.GEC_AGC_CervicalCanal_Nonspecific) {
    BasedLiquids.value.TheCervicalCanal.checkedCities.push("非特异性改变");
  }
  if (form.value.ExpansionInformation?.GEC_AGC_CervicalCanal_Specificity) {
    BasedLiquids.value.TheCervicalCanal.checkedCities.push("倾向于肿瘤性病变");
  }
  //非典型腺细宫内
  if (form.value.ExpansionInformation?.GEC_AGC_Endocervix) {
    BasedLiquids.value.endometrium.checkAll =
      form.value.ExpansionInformation.GEC_AGC_Endocervix;
  }
  if (form.value.ExpansionInformation?.GEC_AGC_Endocervix_Nonspecific) {
    BasedLiquids.value.endometrium.checkedCities.push("非特异性改变");
  }
  if (form.value.ExpansionInformation?.GEC_AGC_Endocervix_Specificity) {
    BasedLiquids.value.endometrium.checkedCities.push("倾向于肿瘤性病变");
  }
  //腺癌GEC_Ade
  if (form.value.ExpansionInformation?.GEC_Ade) {
    BasedLiquids.value.adenocarcinoma.checkAll =
      form.value.ExpansionInformation.GEC_Ade;
  }
  if (form.value.ExpansionInformation?.GEC_Ade_Cervix) {
    BasedLiquids.value.adenocarcinoma.checkedCities.push("宫颈");
  }
  if (form.value.ExpansionInformation?.GEC_Ade_Endocervix) {
    BasedLiquids.value.adenocarcinoma.checkedCities.push("宫内膜");
  }
  if (form.value.ExpansionInformation?.GEC_Ade_Other) {
    BasedLiquids.value.adenocarcinoma.checkedCities.push("其他");
  }
  //单选 低度鳞状上皮内病变
  if (form.value.ExpansionInformation?.RCC_LHIL) {
    LowParent.value.RCC_LHIL = form.value.ExpansionInformation.RCC_LHIL;
  }
  //单选 高度鳞状上皮内病变
  if (form.value.ExpansionInformation?.RCC_HSIL) {
    HighParent.value.RCC_HSIL = form.value.ExpansionInformation.RCC_HSIL;
  }
  //单选 低度鳞状上皮内病变
  if (form.value.ExpansionInformation?.RCC_HPV) {
    PromptHpvInfection.value.RCC_HPV = form.value.ExpansionInformation.RCC_HPV;
  }
  //单选 低度鳞状上皮内病变
  if (form.value.ExpansionInformation?.RCC_SCC) {
    SquamousCellCarcinoma.value.RCC_SCC =
      form.value.ExpansionInformation.RCC_SCC;
  }
  //单选 非典型腺细胞：来源不明
  if (form.value.ExpansionInformation?.GEC_AGC_SourceUnknown) {
    UnknownAtypicalGlandCells.value.GEC_AGC_SourceUnknown =
      form.value.ExpansionInformation.GEC_AGC_SourceUnknown;
  }
  //单选 其他恶性肿瘤
  if (form.value.ExpansionInformation?.GEC_OtherMalignantTumors) {
    OtherMalignantTumors.value.GEC_OtherMalignantTumors =
      form.value.ExpansionInformation.GEC_OtherMalignantTumors;
  }
}
const ReactiveCellsFathersays = (val: boolean) => {
  if (val) {
    BasedLiquids.value.ReactiveCells.checkedCities = [
      "炎性（包括典型修复）",
      "萎缩", //
      "宫内节育器", //
      "放疗", //
      "其他", //
    ];
  } else {
    BasedLiquids.value.ReactiveCells.checkedCities = [];
  }
};
const ReactiveCellsFathersay = (info: any): void => {
  for (let key in ReactiveCells.value) {
    ReactiveCells.value[key] = false;
  }
  if (info.length === BasedLiquids.value.ReactiveCells.content.length) {
    ReactiveCells.value.SEC_RCC = true;
  } else {
    ReactiveCells.value.SEC_RCC = false;
  }
  info.forEach((t: any) => {
    if (t == "炎性（包括典型修复）") {
      ReactiveCells.value.SEC_RCC_Inflammatory = true;
    }
    if (t == "萎缩") {
      ReactiveCells.value.SEC_RCC_Shrink = true;
    }
    if (t == "放疗") {
      ReactiveCells.value.SEC_RCC_Radiotherapy = true;
    }
    if (t == "宫内节育器") {
      ReactiveCells.value.SEC_RCC_IUD = true;
    }
    if (t == "其他") {
      ReactiveCells.value.SEC_RCC_Other = true;
    }
  });
};
//非典型鳞状细胞
const AtypicalSquamousCellFathersays = (val: boolean) => {
  if (val) {
    BasedLiquids.value.AtypicalSquamousCell.checkedCities = [
      "炎性（包括典型修复）",
      "萎缩", //
    ];
  } else {
    BasedLiquids.value.AtypicalSquamousCell.checkedCities = [];
  }
};
const AtypicalSquamousCellFathersay = (info: any): void => {
  for (let key in AtypicalSquamousCell.value) {
    AtypicalSquamousCell.value[key] = false;
  }
  if (info.length == BasedLiquids.value.AtypicalSquamousCell.content.length) {
    AtypicalSquamousCell.value.RCC_ASC = true;
  } else {
    AtypicalSquamousCell.value.RCC_ASC = false;
  }
  info.forEach((t: any) => {
    if (t == "炎性（包括典型修复）") {
      AtypicalSquamousCell.value.RCC_ASC_US = true;
    }
    if (t == "萎缩") {
      AtypicalSquamousCell.value.RCC_ASC_H = true;
    }
  });
};
//非典型腺细胞（宫颈）
const AtypicalGlandularCellsFathersays = (val: boolean) => {
  if (val) {
    BasedLiquids.value.TheCervicalCanal.checkedCities = [
      "非特异性改变",
      "倾向于肿瘤性病变",
    ];
  } else {
    BasedLiquids.value.TheCervicalCanal.checkedCities = [];
  }
};
const AtypicalGlandularCellsFathersay = (info: any): void => {
  for (let key in AtypicalGlandularCells.value) {
    AtypicalGlandularCells.value[key] = false;
  }
  if (info.length == BasedLiquids.value.TheCervicalCanal.content.length) {
    AtypicalGlandularCells.value.GEC_AGC_CervicalCanal = true;
  } else {
    AtypicalGlandularCells.value.GEC_AGC_CervicalCanal = false;
  }
  info.forEach((t: any) => {
    if (t == "非特异性改变") {
      AtypicalGlandularCells.value.GEC_AGC_CervicalCanal_Nonspecific = true;
    }
    if (t == "倾向于肿瘤性病变") {
      AtypicalGlandularCells.value.GEC_AGC_CervicalCanal_Specificity = true;
    }
  });
};
//非典型腺细胞（宫内）
const EndometrialAtypicalGlandCellsFathersays = (val: boolean) => {
  if (val) {
    BasedLiquids.value.endometrium.checkedCities = [
      "非特异性改变",
      "倾向于肿瘤性病变",
    ];
  } else {
    BasedLiquids.value.endometrium.checkedCities = [];
  }
};
const EndometrialAtypicalGlandCellsFathersay = (info: any): void => {
  for (let key in EndometrialAtypicalGlandCells.value) {
    EndometrialAtypicalGlandCells.value[key] = false;
  }
  if (info.length == BasedLiquids.value.endometrium.content.length) {
    EndometrialAtypicalGlandCells.value.GEC_AGC_Endocervix = true;
  } else {
    EndometrialAtypicalGlandCells.value.GEC_AGC_Endocervix = false;
  }
  info.forEach((t: any) => {
    if (t == "非特异性改变") {
      EndometrialAtypicalGlandCells.value.GEC_AGC_Endocervix_Nonspecific = true;
    }
    if (t == "倾向于肿瘤性病变") {
      EndometrialAtypicalGlandCells.value.GEC_AGC_Endocervix_Specificity = true;
    }
  });
};
//腺癌
const AdenocarcinomaFathersays = (val: boolean) => {
  if (val) {
    BasedLiquids.value.adenocarcinoma.checkedCities = [
      "宫颈",
      "宫内膜",
      "其他",
    ];
  } else {
    BasedLiquids.value.adenocarcinoma.checkedCities = [];
  }
};
const AdenocarcinomaFathersay = (info: any): void => {
  for (let key in Adenocarcinoma.value) {
    Adenocarcinoma.value[key] = false;
  }
  if (info.length == BasedLiquids.value.adenocarcinoma.content.length) {
    Adenocarcinoma.value.GEC_Ade = true;
  } else {
    Adenocarcinoma.value.GEC_Ade = false;
  }
  info.forEach((t: any) => {
    if (t == "宫颈") {
      Adenocarcinoma.value.GEC_Ade_Cervix = true;
    }
    if (t == "宫内膜") {
      Adenocarcinoma.value.GEC_Ade_Endocervix = true;
    }
    if (t == "其他") {
      Adenocarcinoma.value.GEC_Ade_Other = true;
    }
  });
};

const drawinglist: any = ref();
const submit = async () => {
  if (BasedLiquids.value.microbial.checkedCities.length > 0) {
    Microorganism.value.SEC_Mic_HerpesSimplexVirus =
      BasedLiquids.value.microbial.checkedCities.includes(
        "细胞变化符合单纯疱疹病毒感染"
      );
    Microorganism.value.SEC_Mic_TrichomonasVaginalis =
      BasedLiquids.value.microbial.checkedCities.includes("阴道滴虫");
    Microorganism.value.SEC_Mic_Fungus =
      BasedLiquids.value.microbial.checkedCities.includes(
        "真菌,形态上符合念珠菌属"
      );
    Microorganism.value.SEC_Mic_ChangesFlora =
      BasedLiquids.value.microbial.checkedCities.includes(
        "菌群变化, 提示细菌性阴道病"
      );
    Microorganism.value.SEC_Mic_Bacteria =
      BasedLiquids.value.microbial.checkedCities.includes(
        "细菌,形态符合放线菌属"
      );
  }
  drawinglist.value = {
    ...Microorganism.value,
    ...ReactiveCells.value,
    ...AtypicalSquamousCell.value,
    ...LowParent.value,
    ...PromptHpvInfection.value,
    ...HighParent.value,
    ...SquamousCellCarcinoma.value,
    ...AtypicalGlandularCells.value,
    ...EndometrialAtypicalGlandCells.value,
    ...UnknownAtypicalGlandCells.value,
    ...Adenocarcinoma.value,
    ...OtherMalignantTumors.value,
    ...Satisfied.value,
  };
  jsonstr.ExpansionInformation = drawinglist.value;
  jsonstr.Header = form.value.Header;
  jsonstr.Bottom = form.value.Bottom;
  jsonstr.BasicInformation = form.value.BasicInformation;
  return jsonstr;
};
defineExpose({
  submit,
});
</script>
<style lang="scss" scoped>
.laboratory {
  box-sizing: border-box;
  margin: 12px 14px 12px 14px;
  padding: 7px 0 7px 7px;
  background-color: #fff;
}

.title {
  font-size: 14px;
  font-weight: bold;
  color: #636363;
  line-height: 24px;
}

.title1 {
  font-size: 14px;
  font-weight: bold;
  color: #636363;
  line-height: 24px;
}

.tbs {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  color: #636363;
}
</style>
