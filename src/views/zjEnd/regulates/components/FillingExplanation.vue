<template>
  <div class="case-info-content">
    <div class="form-content">
      <div class="form-content-title">送检医院</div>
      <div class="form-content-value">{{ props.data?.SiteName || "无" }}</div>
    </div>
    <div class="form-content">
      <div class="form-content-title">原病理号</div>
      <div class="form-content-value">
        {{ props.data?.PathologyNumber || "无" }}
      </div>
    </div>
    <div class="form-content">
      <div class="form-content-title">姓名</div>
      <div class="form-content-value">{{ props.data?.Name || "无" }}</div>
    </div>
    <div v-if="unfold" class="form-content unfoldClass" @click="unfoldClick">
      展开 &nbsp;<iconpark-icon class="component3" name="Component3">
      </iconpark-icon>
    </div>
    <template v-else>
      <div class="form-content">
        <div class="form-content-title">性别</div>
        <div class="form-content-value">{{ props.data?.Sex || "无" }}</div>
      </div>
      <div v-if="props.data?.Sex === '女'" class="form-content">
        <div class="form-content-title">月经时间</div>
        <div class="form-content-value">
          {{ props.data?.Menstruation || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">会诊号</div>
        <div class="form-content-value">{{ props.data?.BarCode || "无" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">患者电话</div>
        <div class="form-content-value">{{ props.data?.Phone || "无" }}</div>
      </div>
      <div class="form-content">
        <div class="form-content-title">门/住院号</div>
        <div class="form-content-value">
          {{ props.data?.HospitalizedId || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">科别</div>
        <div class="form-content-value">
          {{ props.data?.InspectionDept || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">床号</div>
        <div class="form-content-value">
          {{ props.data?.BedNumber || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">送检组织</div>
        <div class="form-content-value">
          {{ SampleLocationName(props.data?.SampleLocationName) || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">送检医生</div>
        <div class="form-content-value">
          {{ props.data?.DoctorName || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">联系电话</div>
        <div class="form-content-value">
          {{ props.data?.DoctorPhone || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">采样时间</div>
        <div class="form-content-value">
          {{ props.data?.InspectionDate || "无" }}
        </div>
      </div>
      <div class="form-content">
        <div class="form-content-title">预约时间</div>
        <div class="form-content-value">
          {{ props.data?.SubscribeTime || "无" }}
        </div>
      </div>
      <div class="form-content unfoldClass" @click="unfoldClick">
        收起 &nbsp;<iconpark-icon
          class="component3 component4"
          name="Component3"
        >
        </iconpark-icon>
      </div>
    </template>
    <div class="form-content">
      <div class="form-content-title">临床诊断</div>
      <div class="form-content-value">
        {{ props.data?.ClinicalDiagnosis || "无" }}
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from "vue";
import { SampleLocationName } from "@/utils/utils";

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    require: true,
  },
});

//是否展开
const unfold = ref(true);
//展开点击
const unfoldClick = () => {
  unfold.value = !unfold.value;
};
</script>
<style lang="scss" scoped>
.case-info-content {
  padding: 12px 14px;
  padding-bottom: 12px;
  border-radius: 4px 4px 4px 4px;
}

.form-content {
  display: flex;
  line-height: 40px;
  border: 1px solid #e6ecfb;
  border-top: none;
  font-size: 14px;
  font-weight: bold;
  color: #4d545f;
}

.form-content:nth-child(1) {
  border-top: 1px solid #e6ecfb;
}

.unfoldClass {
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
  text-align: center;
  justify-content: center;
}

.form-content-title {
  display: flex;
  align-items: center;
  padding-left: 7px;
  width: 84px;
  border-right: 1px solid #e6ecfb;
}

.form-content-value {
  display: flex;
  align-items: center;
  width: 236px;
  padding-left: 7px;
  font-weight: 400;
  word-break: break-all; //英文
  white-space: pre-wrap; //中文
}

.component3 {
  font-size: 12px;
}

.component4 {
  //旋转180度
  transform: rotate(180deg);
}
</style>
