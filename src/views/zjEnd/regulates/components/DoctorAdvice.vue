<template>
  <div class="doctor">
    <div v-if="issuedDoctor?.Id" class="doctor-top">
      <div>已发送</div>
      <div class="doctor-top-msg" style="height: 95px">
        <div style="overflow-y: auto; height: 95px">
          <div>已下发医嘱：{{ issuedDoctor.Content || "无" }}</div>
          <div>下发时间：{{ issuedDoctor.CreateTime }}</div>
          <div>下发专家：{{ issuedDoctor.CreateUserName }}</div>
          <div>医嘱状态：{{ issuedDoctorStatus }}</div>
        </div>
        <van-button
          v-if="issuedDoctor.Status !== 4"
          class="doctor-delete"
          @click="deleteClick"
          >删除</van-button
        >
      </div>
    </div>
    <div class="doctor-top">
      <div>医嘱</div>
      <div class="doctor-top-msg">{{ message }}</div>
    </div>
    <div class="doctor-center">
      <div style="display: flex; align-items: center">
        <div style="margin-right: 10px">医嘱类型</div>
        <van-field
          class="time-left"
          v-model="doctorType"
          :readonly="true"
          right-icon="arrow-down"
          placeholder="请选择"
          @click="show = true"
        />
      </div>
      <van-search
        v-model="search"
        placeholder="请搜索项目名称"
        @click-left-icon="searchClick"
        @search="searchClick"
      >
        <template #left-icon>
          <div>
            <iconpark-icon
              class="iconpark"
              name="weibiaoti-1huaban1fuben7"
            ></iconpark-icon>
          </div> </template
      ></van-search>
      <div class="doctor-list">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text=""
          @load="onLoad"
        >
          <van-checkbox-group
            v-model="checked"
            shape="square"
            ref="checkboxGroup"
          >
            <div
              v-for="item in doctorList"
              :key="item.Id"
              class="doctor-item"
              :style="item.active ? 'border: 1px solid #650FAA;' : ''"
            >
              <div style="display: flex; justify-content: space-between">
                <div class="doctor-item-left">
                  <span
                    :style="
                      item.active
                        ? 'background-color:#650FAA;color:#ffffff'
                        : ''
                    "
                    >{{ item.OrderType }}</span
                  >
                  <div>项目编号：{{ item.Code }}</div>
                  <div>项目名称：{{ item.Name }}</div>
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-right: 24px;
                    margin-top: 5px;
                  "
                >
                  <van-checkbox
                    :name="item.Id"
                    checked-color="#650FAA"
                    shape="square"
                    @click="checkboxClick(item)"
                  ></van-checkbox>
                </div>
              </div>
              <div
                v-if="item?.List && item.List.length > 0"
                class="doctor-bottom"
              >
                <div v-for="(i, index) in item.List" :key="index">
                  {{ i }}
                </div>
              </div>
            </div>
          </van-checkbox-group>
        </van-list>
      </div>
    </div>
    <div>
      <!-- 占位 -->
      <div class="occupied"></div>
      <div class="case-bottom">
        <div class="case-bottom-container">
          <div class="case-center" @click="diagnoseClick">发送</div>
        </div>
      </div>
    </div>
    <van-popup v-model:show="show" position="bottom" style="height: 320px">
      <div style="height: 320px">
        <van-picker
          title="医嘱类型"
          :columns="doctorTypeList"
          :columns-field-names="customFieldName"
          @confirm="onConfirm"
          @cancel="onCancel"
        />
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { AdvancedSearch, WhereParams } from "@/utils/query";

const route = useRoute();
const { caseId } = route.query;
const message = ref<string>("");
const doctorType = ref("");
const search = ref("");
const checked = ref<number[]>([]);
//医嘱列表总数
const total = ref(0);
const show = ref(false);
const params = reactive<API.medicalAdviceApi.getList.Params>({
  PageIndex: 1,
  PageSize: 5,
});
const loading = ref(false);
const finished = ref(false);

//医嘱列表
const doctorList = ref<defs.ExpertOrderTypeDto[]>([]);
//搜索医嘱
const searchClick = () => {
  const advancedSearch = new AdvancedSearch();

  advancedSearch.addParam(new WhereParams("Name", search.value, 1, 1));

  const advSearchValue = advancedSearch.getParams();
  params.AdvancedSearch = advSearchValue;
  params.PageIndex = 1;
  doctorList.value = [];
  getDoctorList();
};
//获取医嘱列表
const getDoctorList = async () => {
  const res = await API.medicalAdviceApi.getList.request({
    params: params,
  });

  //添加active属性
  res.Data?.forEach((item) => {
    item.active = false;
  });
  if (doctorList.value.length === 0) {
    doctorList.value = res.Data || [];
    total.value = res.Count || 0;
  } else {
    doctorList.value = doctorList.value.concat(res.Data || []);
    total.value = res.Count || 0;
  }
  params.PageIndex++;
};
const onLoad = async () => {
  await getDoctorList();
  // 加载状态结束
  loading.value = false;
  // 数据全部加载完成
  if (doctorList.value.length >= total.value) {
    finished.value = true;
  }
};
const diagnoseClick = async () => {
  await API.medicalAdviceApi.postCreate.request({
    bodyParams: {
      Id: Number(caseId),
      Str: message.value,
    },
  });
  setMoney();
  getIssuedDoctor();
};
const checkboxClick = (val: defs.ExpertOrderTypeDto) => {
  val.active = !val.active;
  const checkedData = doctorList.value.filter((item) => item.active);
  message.value = "";
  const list: string[] = [];
  checkedData.map((item) => {
    if (item.List && item.List.length > 0) {
      return list.push(
        item.OrderType +
          "：" +
          item.Name +
          "（" +
          item.List.join(",") +
          "）" +
          "；"
      );
    }
    return list.push(item.OrderType + "：" + item.Name + "；");
  });
  message.value = list.join("");
};
//已下发医嘱数据
const issuedDoctor = ref<defs.ExpertDoctorsAdviceDto>({});
//获取已下发的医嘱
const getIssuedDoctor = async () => {
  const res = await API.userCommonApi.getMedicaladvice.request({
    params: {
      Id: Number(caseId),
    },
  });
  issuedDoctor.value = res.Data as defs.ExpertDoctorsAdviceDto;
};

const issuedDoctorStatus = computed(() => {
  if (issuedDoctor.value.Status === 1) {
    return "待执行";
  } else {
    return "已删除";
  }
});
const customFieldName = {
  text: "Text",
};
//医嘱类型列表
const doctorTypeList = ref<ObjectMap<any, any>[]>([]);
//获取医嘱类型列表
const getDoctorTypeList = async () => {
  const res = await API.userCommonApi.getDropAdd.request({
    params: {
      code: "ORDER_TYPE",
    },
  });
  doctorTypeList.value = res.Data?.ORDER_TYPE as ObjectMap<any, any>[];
};
const onConfirm = async (val: { Text: string; Id: number | undefined }) => {
  finished.value = false;
  total.value = 0;
  doctorType.value = val.Text;
  show.value = false;
  params.PageIndex = 1;
  params.id = val.Id;
  doctorList.value = [];
};
const onCancel = () => {
  show.value = false;
};
//设置金额
const setMoney = async () => {
  const list = checked.value.map((item) => {
    return {
      Id: item,
    };
  });
  console.log(list);

  await API.orderAmountApi.putSetcharges.request({
    params: {
      caseId: Number(caseId),
    },
    bodyParams: list,
  });
};
//已选择默认渲染
const selectDefault = async () => {
  let result = await API.orderAmountApi.getModel.request({
    params: { Id: Number(caseId) },
  });
  if (result.Data?.JsonId) {
    let data = JSON.parse(result.Data.JsonId);
    checked.value = data.map((item: { Id: number }) => item.Id);
    const list: string[] = [];
    doctorList.value.forEach((item) => {
      if (data.some((i: { Id: number }) => i.Id === item.Id)) {
        item.active = true;
        if (item.List && item.List.length > 0) {
          return list.push(
            item.OrderType +
              "：" +
              item.Name +
              "（" +
              item.List.join(",") +
              "）" +
              "；"
          );
        }
        return list.push(item.OrderType + "：" + item.Name + "；");
      }
    });
    message.value = list.join("");
  }
};

//删除已下发医嘱
const deleteClick = async () => {
  await API.medicalAdviceApi.deleteDelete.request({
    bodyParams: {
      Id: Number(caseId),
    },
  });
  getIssuedDoctor();
};

onMounted(() => {
  getDoctorTypeList();
  getIssuedDoctor();
  setTimeout(() => {
    selectDefault();
  }, 1000);
});
</script>
<style lang="scss" scoped>
.doctor {
  padding: 12px 0;
}
.doctor-top {
  margin: 0 14px;
  padding: 10px 7px 10px 14px;
  background-color: #ffffff;
  border-radius: 4px;
  color: #343840;
}
.doctor-top-msg {
  position: relative;
  margin-top: 4px;
  padding: 12px 9px;
  height: 75px;
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  font-size: 12px;
  font-weight: normal;
  line-height: 14.04px;
  letter-spacing: 0px;
  color: #b4bfd0;

  & > div {
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0px;
    color: #343840;
  }
}
.doctor-delete {
  position: absolute;
  height: 32px;
  bottom: 5px;
  right: 10px;
  color: #fff;
  background: #ff7272;
  border-radius: 4px;
}
.doctor-center {
  margin: 0 14px;
  margin-top: 12px;
  padding: 14px 12px;
  background-color: #fff;
  border-radius: 4px;
  font-size: 14px;
  font-weight: normal;
  color: #343840;
}
.time-left {
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-content: space-around;
  width: 145px;
  height: 32px;
  background: #f3f6fd;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  line-height: 32px;
  color: #494949;
}
:deep(.van-search) {
  margin-top: 12px;
  width: 260px;
  height: 36px;
  border: 1px solid #e6ecfb;
  border-radius: 18px;
}
.doctor-list {
  margin-top: 22px;
  height: 300px;
  overflow-y: auto;
}

.case-bottom {
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 88px;
  background: #ffffff;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
  opacity: 1;
}

.case-bottom-container {
  padding: 13px 14px 0 14px;
  display: flex;
  justify-content: center;
}

.case-center {
  width: 212px;
  height: 32px;
  background: #650faa;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  color: #fff;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
.doctor-item {
  margin-bottom: 8px;
  padding-bottom: 10px;
  border: 1px solid #e6ecfb;
  border-radius: 4px;
  overflow: hidden;
}
.doctor-item-left {
  font-size: 14px;
  font-weight: normal;
  line-height: 16.38px;
  color: #343840;
  & > span {
    padding: 0;
    padding: 2px 8px;
    background: #e6ecfb;
  }
  & > div {
    margin-top: 5px;
    margin-left: 10px;
  }
}
.doctor-bottom {
  padding: 0 8px;
  display: flex;
  align-items: center;
  margin: 6px 8px;
  margin-bottom: 0;
  height: 40px;
  background: #f3f6fd;
  font-size: 12px;
  font-weight: normal;
  color: #343840;
  overflow-y: auto;
  & > div {
    padding: 3px 6px;
    margin-right: 12px;
    background-color: #fff;
    border: 1px solid #e6ecfb;
    border-radius: 4px;
  }
}
.iconpark {
  margin-top: 6px;
  font-size: 16px;
}
:deep(.van-search__content) {
  padding-left: 0;
}
</style>
