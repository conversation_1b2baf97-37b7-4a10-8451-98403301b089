<template>
  <div class="sliceEvaluation">
    <div class="sliceEvaluation-top">
      <div style="display: flex">
        <div>切片评价</div>
        <div class="sliceEvaluation-top-center">发布报告前请对切片做出评价</div>
      </div>
    </div>
    <div class="sliceEvaluation-center">
      <ImageBrowser ref="imageBrowser" slideUrl :showNavigators="false" />
      <div class="evaluation">
        <div
          v-if="sliceDescription"
          class="sliceEvaluation-top-right sliceEvaluation-top-rights"
        >
          已评价
        </div>
        <div v-else class="sliceEvaluation-top-right">未评价</div>
      </div>
    </div>
    <div class="theRanking">
      <span>{{ index + 1 }}</span
      ><span>/</span><span>{{ zjsection.sectionList.length }}</span>
    </div>
    <div class="theRanking-radio">
      <van-radio-group
        v-model="checked"
        icon-size="14px"
        direction="horizontal"
        checked-color="#650FAA"
        @click="evaluationChange"
      >
        <van-radio :name="1">优</van-radio>
        <van-radio :name="2">良</van-radio>
        <van-radio :name="3">合格</van-radio>
        <van-radio :name="4">不合格</van-radio>
      </van-radio-group>
      <div class="disqualification" v-if="disqualificationShow">
        <van-checkbox-group
          v-model="disqualificationChecked"
          icon-size="14px"
          checked-color="#650FAA"
          @click="disqualificationCheckedClick"
        >
          <van-checkbox shape="square" name="细胞或组织结构缺失"
            >细胞或组织结构缺失</van-checkbox
          >
          <van-checkbox shape="square" name="细胞或组织延展变形"
            >细胞或组织延展变形</van-checkbox
          >
          <van-checkbox shape="square" name="有杂质或污染物"
            >有杂质或污染物</van-checkbox
          >
          <van-checkbox shape="square" name="染色过度或不足"
            >染色过度或不足</van-checkbox
          >
          <van-checkbox shape="square" name="切片厚度不均匀"
            >切片厚度不均匀</van-checkbox
          >
          <van-checkbox shape="square" name="切片断裂或损坏"
            >切片断裂或损坏</van-checkbox
          >
        </van-checkbox-group>
      </div>
    </div>
    <div class="theRanking-radio theRanking-btn">
      <van-button
        class="submit"
        v-if="index > 0"
        round
        color="#f0e7f7"
        type="primary"
        style="margin-right: 10px"
        size="small"
        @click="upClick"
        ><span style="color: #650faa">上一张</span></van-button
      >
      <van-button
        class="submit"
        v-if="index + 1 < total"
        round
        color="#f0e7f7"
        type="primary"
        size="small"
        @click="nextClick"
        ><span style="color: #650faa">下一张</span></van-button
      >
      <van-button
        class="submit"
        v-else
        round
        color="#f0e7f7"
        type="primary"
        size="small"
        @click="submitEvaluation"
        ><span style="color: #650faa">提交评价</span></van-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import { useImageBrowser } from "@/hooks/useImageBrowser";
import { useSliceList } from "@/hooks/useSliceList";
import ImageBrowser from "@/components/image-viewer/components/imageBrowser/index.vue";
import { PropType } from "vue";
import useStore from "@/store";
import { Toast } from "vant";
const props = defineProps({
  data: {
    type: Object as PropType<defs.WriteDiagnosticsDto>,
    require: true,
  },
});
const emits = defineEmits(["submitEvaluation"]);
const { zjsection } = useStore();
const imageBrowser = ref<InstanceType<typeof ImageBrowser>>();
const checked = ref();
const disqualificationChecked = ref<string[]>([]);
const disqualificationShow = ref(false);
const { refreshViewers } = useSliceList(); //列表组件hooks
//当前是第几张切片
const index = ref(0);
//总共有几张切片
const total = ref(zjsection.sectionList.length);
const sliceDescription = computed(() => {
  return (
    (zjsection.selectSection?.Evaluation &&
      zjsection.selectSection?.Evaluation < 4) ||
    (zjsection.selectSection?.Evaluation === 4 &&
      zjsection.selectSection?.SliceDescription)
  );
});
//刷新切片
const refreshViewersClcik = (val: number) => {
  refreshViewers(
    zjsection.sectionList[val],
    useImageBrowser(imageBrowser as any),
    props.data?.PathologyNumber,
    props.data?.CaseTypeCode === "Frozen" ? true : false
  );
};
//提交评价
const submitEvaluation = () => {
  const list = zjsection.sectionList?.filter((item) => {
    return (
      item.Evaluation === null ||
      (item.Evaluation === 4 && !item.SliceDescription)
    );
  });
  if (list.length > 0) {
    Toast.fail("请评价切片质量");
  } else {
    emits("submitEvaluation", "news");
  }
};

//防抖

const timeout: any = ref(null);
function debounce(doSomeThing: () => void, time: number = 2000): () => void {
  return () => {
    if (timeout.value) {
      clearTimeout(timeout.value);
    }
    timeout.value = setTimeout(() => {
      doSomeThing();
    }, time);
  };
}

//上一张
const upClick = () => {
  debounce(async () => {
    if (index.value > 0) {
      disqualificationShow.value = false;
      disqualificationChecked.value = [];
      index.value = index.value - 1;
      zjsection.selectSectionFn(zjsection.sectionList[index.value]);
      checked.value = zjsection.selectSection.Evaluation;
      if (zjsection.selectSection.Evaluation === 4) {
        disqualificationShow.value = true;
        if (zjsection.selectSection.SliceDescription) {
          disqualificationChecked.value =
            zjsection.selectSection.SliceDescription?.split(",");
        }
      }
      refreshViewersClcik(index.value);
    }
  }, 1000)();
};
//下一张
const nextClick = () => {
  debounce(async () => {
    disqualificationShow.value = false;
    disqualificationChecked.value = [];
    index.value = index.value + 1;
    zjsection.selectSectionFn(zjsection.sectionList[index.value]);
    checked.value = zjsection.selectSection.Evaluation;
    if (zjsection.selectSection.Evaluation === 4) {
      disqualificationShow.value = true;
      if (zjsection.selectSection.SliceDescription) {
        disqualificationChecked.value =
          zjsection.selectSection.SliceDescription?.split(",");
      }
    }
    refreshViewersClcik(index.value);
  }, 1000)();
};
const evaluationChange = () => {
  zjsection.sectionList[index.value].Evaluation = checked.value;
  zjsection.selectSection.Evaluation = checked.value;
  if (
    zjsection.selectSection.Evaluation !== 4 &&
    zjsection.selectSection.SliceDescription
  ) {
    zjsection.casequalityEvaluationFalse([]);
    zjsection.selectSection.SliceDescription = "";
    zjsection.sectionList[index.value].SliceDescription = "";
  }
  if (checked.value === 4) {
    disqualificationShow.value = !disqualificationShow.value;
    if (zjsection.selectSection.SliceDescription) {
      disqualificationChecked.value =
        zjsection.selectSection.SliceDescription?.split(",");
    }
  } else {
    disqualificationShow.value = false;
    disqualificationChecked.value = [];
  }
  zjsection.casequalityEvaluation(checked.value);
};

const disqualificationCheckedClick = () => {
  zjsection.sectionList[index.value].SliceDescription =
    disqualificationChecked.value.toString();
  zjsection.selectSection.SliceDescription =
    disqualificationChecked.value.toString();
  zjsection.casequalityEvaluationFalse(disqualificationChecked.value);
};
onMounted(() => {
  if (zjsection.sectionList.length > 0) {
    zjsection.selectSectionFn(zjsection.sectionList[0]);

    checked.value = zjsection.selectSection?.Evaluation || 0;

    refreshViewersClcik(index.value);
  }
});
//页面卸载
onUnmounted(() => {
  console.log(imageBrowser.value);
});
</script>
<style lang="scss" scoped>
.sliceEvaluation {
  padding: 14px 17px 14px 17px;
  width: 324px;
  height: 540px;
  // background: #ffffff;
}
.sliceEvaluation-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7px;
  font-size: 17px;
  font-weight: 500;
  color: #000000;
}
.sliceEvaluation-top-center {
  margin-left: 2px;
  padding: 4px 4px 2px 4px;
  font-size: 10px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff4a4a;
  background-color: #ffe3e3;
  border-radius: 4px;
}
.sliceEvaluation-top-right {
  width: 56px;
  height: 24px;
  background: #fff5ea;
  border-radius: 4px 4px 0px 4px;
  text-align: center;
  line-height: 24px;
  font-size: 12px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #ff8743;
}
.sliceEvaluation-top-rights {
  background: #f1f8e7;
  color: #6ab10e;
}

.sliceEvaluation-center {
  position: relative;
  box-sizing: border-box;
  // padding-right: 17px;
  width: 100%;
  height: 382px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #f1f8e7;
}
.evaluation {
  position: absolute;
  top: 0;
  right: 0;
}
.theRanking {
  margin-top: 7px;
  box-sizing: border-box;
  // padding-right: 17px;
  text-align: center;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
}
.theRanking-radio {
  position: relative;
  margin-top: 25px;
  box-sizing: border-box;
  // padding-right: 17px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.disqualification {
  box-sizing: border-box;
  padding: 11px;
  position: absolute;
  top: -205px;
  right: 40px;
  // width: 190px;
  height: 194px;
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 6px 6px 6px 6px;
  //最下方插个三角形
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 195px;
    right: 50px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #fff;
    border-bottom: 9px solid transparent;
    z-index: 12;
  }
  &::before {
    content: "";
    display: block;
    position: absolute;
    top: 195px;
    right: 49px;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid #e5e5e5;
    border-bottom: 9px solid transparent;
    z-index: 10;
  }
}

.theRanking-btn {
  margin-top: 28px;
}
.submit {
  width: 116px;
  height: 32px;
  border-radius: 16px 16px 16px 16px;
  line-height: 32px;
  text-align: center;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
}
:deep(.van-checkbox) {
  margin-bottom: 10px;
}
.close {
  position: absolute;
}
</style>
