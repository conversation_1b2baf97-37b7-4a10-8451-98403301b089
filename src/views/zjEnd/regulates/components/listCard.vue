<template>
  <div class="container" v-if="props.dataList && props.dataList.length > 0">
    <van-swipe-cell
      v-for="(item, index) in props.dataList"
      :key="index"
      :disabled="disabledListCard"
    >
      <div class="case-card-info">
        <van-row>
          <div v-if="selectColor.selectNav === 6" class="case-card-title">
            预约编号 {{ item.Number }}
          </div>
          <div v-else class="case-card-title">
            <div class="g-ellipsis">原病理号：{{ item.PathologyNumber }}</div>
            <div
              v-if="selectColor.selectNav === 1"
              id="case-card"
              class="case-card-overtime"
              :style="{
                color: getStatusColor(isOvertime(item)),
                borderColor: getStatusColor(isOvertime(item)),
              }"
            >
              {{ isOvertime(item) }}
            </div>
          </div>
        </van-row>
        <div></div>
        <van-row class="case-card-content">
          <van-col span="4" class="case-card-image">
            <SliceImage :imgUrl="item.SliceThumbnail" />
          </van-col>
          <van-col span="15" class="case-card-text">
            <span class="case-card-name"
              >{{ item.Name ? item.Name + " |" : "" }}
              {{ item.SexName ? item.SexName + " |" : "" }} {{ item.Age }}</span
            >
            <div class="case-card-detail">
              <span class="case-card-message">
                <span
                  :style="
                    item.CaseTypeName === '冰冻' && selectColor.selectNav === 1
                      ? 'color:#1FBDD3'
                      : ''
                  "
                >
                  {{ item.CaseTypeName ? item.CaseTypeName : "" }}</span
                >
                <span>{{ item.CaseTypeName ? " |" : "" }}</span>
                {{ SampleLocationName(item.SampleLocationName as string) }} |
                {{ item.Inspection }}
              </span>
              <div class="case-card-time">{{ item.Time }}</div>
            </div>
          </van-col>
          <van-col span="3" class="case-card-btn">
            <van-button
              v-if="selectColor.selectNav === 1 && [7, 6].includes(item.Status)"
              class="case-review-btn"
              @click="gotoInfo(item, item.Status, 1)"
              color="#f0e7f7"
              :loading="gotoInfoLoading"
              >诊断</van-button
            >
            <van-button
              v-else-if="selectColor.selectNav === 2 && item.Status !== 13"
              class="case-review-btn"
              @click="gotoInfo(item, item.Status, 2)"
              color="#f0e7f7"
              :loading="gotoInfoLoading"
              >复核</van-button
            >
            <van-button
              v-else-if="item.Status === 1"
              class="case-review-btn"
              @click="gotoInfo(item, item.Status, 4)"
              color="#f0e7f7"
              :loading="gotoInfoLoading"
              >回复</van-button
            >
            <div
              v-else-if="item.Status === 4"
              class="case-review-btn"
              @click="withdrawReason(item)"
            >
              撤回理由
            </div>
            <van-button
              v-else
              class="case-review-btn"
              @click="gotoInfo(item, item.Status, selectColor.selectNav)"
              color="#f0e7f7"
              :loading="gotoInfoLoading"
              >查看</van-button
            >
          </van-col>
        </van-row>
        <div
          v-if="[1, 2, 3, 4, 5, 7].includes(selectColor.selectNav)"
          class="corner-mark"
          :style="[
            GetExpertStatusAndColor(item.Status || 0, item.StatusName || ''),
          ]"
        >
          {{ item.StatusName }}
        </div>
      </div>
      <template #right>
        <van-button
          text="取消收藏"
          class="delete-button"
          @click="cancelClcik(item.Id || 0)"
        />
      </template>
    </van-swipe-cell>
  </div>

  <div v-else>
    <van-empty description="暂无数据"> </van-empty>
  </div>
</template>

<script lang="ts" setup>
import SliceImage from "@/components/case-list/SliceImage.vue";
import { GetExpertStatusAndColor, getStatusColor } from "@/types/enum";
import useStore from "@/store";
import { SampleLocationName } from "@/utils/utils";
import { LocalStorageName, getLocalStorage } from "@/utils/storage";
import { render } from "vue";
const router = useRouter();
const props = defineProps({
  dataList: {
    type: Array as any,
    default: [],
  },
  //是否可滑动
  disabledListCard: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(["cancelCollect"]);
const { selectColor, zjsection } = useStore();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
const isOvertime = computed(
  () =>
    (item: {
      DisplayTime: string | number | Date;
      AboutToTimeOut: number;
      DiagnosticTimeOut: number;
    }) => {
      const now = new Date().valueOf(); //当前时间ha
      const created = new Date(item.DisplayTime).valueOf(); //创建时间
      const abouttime = item.AboutToTimeOut * 60 * 1000;
      const dianotime = item.DiagnosticTimeOut * 60 * 1000;
      if (created + dianotime > now) {
        if (created + abouttime < now) {
          //开始预警
          return "即将超时";
        } else {
          return "未超时";
        }
      } else {
        return "已超时";
      }
    }
);
//获取切片列表
// const getSliceList = async (params: API.userCommonApi.getSliceList.Params) => {
//   await zjsection.getSectionAsync(params);
// };
const gotoInfoLoading = ref(false);
//查看详情

const gotoInfo = async (
  caseInfo: defs.CaseDto,
  status: number,
  value: number
) => {
  gotoInfoLoading.value = true;
  console.log(status);
  if (value === 2 || status === 7) {
    await API.expertFunctionApi.postDiagnosis.request({
      bodyParams: { Id: Number(caseInfo.Id) },
    });
  }
  params.Id = caseInfo.Id as number;
  // setTimeout(async () => {
  //   await getSliceList(params);
  // }, 1000);
  await router.push({
    path: "/details",
    query: {
      id: caseInfo.Id && caseInfo.Id.toString(),
      status: status,
      category: value,
      siteId: caseInfo.SiteId || 0,
    },
  });
  gotoInfoLoading.value = false;
};
//取消收藏
const cancelClcik = (id: number) => {
  emits("cancelCollect", id);
};
//撤回理由
const withdrawReason = async (caseInfo: defs.CaseDto) => {
  await router.push({
    path: "/withdrawReason",
    query: {
      id: caseInfo.Id && caseInfo.Id.toString(),
    },
  });
};
</script>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  margin: 0 11px 0 14px;
}

.case-card-info {
  box-sizing: border-box;
  position: relative;
  padding: 10px 12px;
  margin-bottom: 12px;
  // height: 100px;
  background-color: #fff;
  border-radius: 4px;
}

.case-card-title {
  width: calc(100% - 40px);
  display: flex;
  align-items: center;
  margin-top: 2;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}
.case-card-overtime {
  margin-left: 4px;
  padding: 2px;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: normal;
  color: #f53f3f;
  border: 1px solid #f53f3f;
  border-radius: 2px;
}

.case-card-content {
  margin-top: 13px;
  align-items: flex-end;
}

// .case-card-image {
//   width: 51px;
//   height: 51px;
// }

.case-card-text {
  margin-left: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.case-card-name {
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.case-card-detail {
  margin-top: 5px;
}

.case-card-detail {
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.case-card-time {
  margin-top: 2px;
}

.case-card-btn {
  // line-height: 6rem;
  flex: auto;
}

.case-review-btn {
  width: 65px;
  height: 28px;
  background: #f0e7f7;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 28px;
  font-size: 12px;
  font-weight: 400;
  color: #650faa !important;
}

.corner-mark {
  position: absolute;
  padding: 0 6px;
  top: 0;
  right: 0;
  height: 24px;
  line-height: 24px;
  background: #df67bb;
  border-radius: 0px 4px 0px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

.delete-button {
  margin: auto;
  width: 66px;
  height: 66px;
  background: #f9c55a;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

:deep(.van-swipe-cell__right) {
  margin-top: 19px;
  right: -1px;
  transform: translate3d(100%, 0, 0);
}

.imageClass {
  height: 51px;
  width: 51px;
  border: 1px solid #e6ecfb;
}
:deep(.van-button--normal) {
  padding: 0;
}
</style>
