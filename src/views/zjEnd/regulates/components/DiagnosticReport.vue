<template>
  <div class="examine">
    <div class="examine-table">
      <van-tabs
        v-model:active="active"
        title-active-color="#650FAA"
        line-width="40"
        title-inactive-color="#4D545F"
        :ellipsis="false"
        :border="false"
        @click-tab="onChange"
      >
        <van-tab title="查看切片"></van-tab>
        <van-tab title="填写报告"></van-tab>
        <van-tab title="下医嘱"></van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
const active = ref(0);
const emit = defineEmits(["tabClick"]);
const onChange = () => {
  emit("tabClick", active.value);
};
</script>
<style lang="scss" scoped>
.examine {
  box-sizing: border-box;
  height: 100%;
  margin: 12px 14px 0 14px;
  background-color: #ffffff;
  :deep(.van-tabs__line) {
    background-color: #650faa;
  }
}
.examine-table {
  border-radius: 4px;
}
</style>
