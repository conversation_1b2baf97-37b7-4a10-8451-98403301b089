<template>
  <div style="background-color: #fff;height: 100%;">
    <!--顶部导航 start -->
    <NavBar title="报告" />
    <PdfViewerH5 ref="previewPdfh5" />
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import PdfViewerH5 from "@/components/pdfViewer/PdfViewerH5.vue"
import useStore from "@/store";
const router = useRouter();
const route = useRoute();
const { url } = route.query

//获取报告单
const previewPdfh5 = ref()
//获取报告
function getReport() {
  nextTick(() => {
    previewPdfh5.value.openPdf(url)
  })

}
onMounted(() => {
  getReport();
});
</script>
<style lang="scss" scoped>

</style>
