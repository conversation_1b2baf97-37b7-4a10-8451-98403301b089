<template>
  <div>
    <!--顶部导航 start -->
    <NavBar :title="titles" />
    <!--顶部导航 end -->

    <!-- 查看切片 start -->
    <ImageViewer ref="imageViewer" v-show="zjsection.sectionList.length > 0" :data="getFillingList"
      @changeClicks="changeClicks" />
    <!-- 查看切片 end -->

  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import ImageViewer from "@/components/image-viewer/ImageViewer.vue";
import useStore from "@/store";
import { Ref, inject } from "vue";
import { GetCaseinfor } from "@/api/zjEnd/regulates";

const { zjsection } = useStore();
const route = useRoute();
const { caseId, id, siteId } = route.query;
const imageViewer = ref()
const titles = computed(() => {
  if (id) {
    return "查看切片";
  } else {
    return "诊断";
  }
});
const changeClicks = () => {
  changeClick()
}
const changeClick = () => {
  imageViewer.value.refreshViewersClcik(getFillingList.value?.PathologyNumber, getFillingList.value?.CaseTypeCode === "Frozen" ? true : false)

}
//获取Filling数据
const getFillingList: Ref<any | undefined> = ref()
const getFilling = async () => {
  const res = await GetCaseinfor(caseId as string);
  getFillingList.value = res.data.WriteDiagnosticsDto;
}
onMounted(() => {
  getFilling();
});
</script>
<style scoped lang="scss">
.van-radio__icon--checked .van-icon {
  background-color: #650faa;
  border-color: #650faa;
}
</style>
