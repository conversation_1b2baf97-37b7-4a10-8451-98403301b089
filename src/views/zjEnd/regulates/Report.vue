<template>
  <div style="background-color: #fff; height: 100%">
    <!--顶部导航 start -->
    <NavBar title="报告" />
    <!--顶部导航 end -->

    <!-- 报告列表 start -->
    <!-- <LiquidBasedReport :reportData="reportData" v-if="reportData?.ModuleType == 'TCT'" />
            <GeneralReport :reportData="reportData" v-else /> -->
    <!-- <PdfViewer :pdfUrl="reportData?.Url " /> -->
    <!-- 报告列表 end -->
    <!-- <DocxViewer :url="reportData?.Url"/> -->
    <PdfViewerH5 ref="previewPdfh5" />
    <!-- 底部按钮 start -->
    <!-- <BottomBtn
      v-if="reportShow != '1'"
      rightName="确认发布"
      :btnShow="false"
      @rightBtn="show = true"
    /> -->
    <BottomBtn
      v-if="recheck === '1'"
      rightName="发布报告"
      :btnShow="false"
      @click="releaseReport"
    />
    <BottomBtn
      v-if="reportShow != '1'"
      leftName="邀请复核"
      rightName="发布报告"
      @leftBtn="submit"
      @rightBtn="releaseReport"
    />
    <!-- 底部按钮 end -->
    <!-- window.open("https://view.xdocin.com/view?src=" + encodeURIComponent("https://sqray-pathology-report.oss-cn-shenzhen.aliyuncs.com/345f3b80-b085-47b7-abff-4cbb950e8bae.docx?Expires=1666000474&OSSAccessKeyId=LTAI5tE49rrKU9iAxwDdzMcD&Signature=hKqYpFqPoXKoV78huoOoKpGlf%2FE%3D")); -->
    <!-- https://view.xdocin.com/view?src=https://sqray-pathology-report.oss-cn-shenzhen.aliyuncs.com/345f3b80-b085-47b7-abff-4cbb950e8bae.docx?Expires=1666000474&OSSAccessKeyId=LTAI5tE49rrKU9iAxwDdzMcD&Signature=hKqYpFqPoXKoV78huoOoKpGlf%2FE%3D -->
    <!-- 确认预约弹窗 start -->
    <!-- <CaseOverlay
      v-model:show="show"
      overlayTitle="是否邀请复核？"
      @cancels="releClcik"
      @submit="submit"
    /> -->
    <!-- 确认预约弹窗 end -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import BottomBtn from "@/components/bottomBtn/BottomBtn.vue";
import CaseOverlay from "@/components/case-overlay/CaseOverlay.vue";
import PdfViewerH5 from "@/components/pdfViewer/PdfViewerH5.vue";
import { Toast } from "vant";
import useStore from "@/store";
const router = useRouter();
const route = useRoute();
const show = ref(false);
const { saveCase } = useStore();
const { caseId, reportShow, recheck, siteId } = route.query;
const submit = () => {
  show.value = false;
  router.push({
    path: "/invitationReview",
    query: {
      caseId: caseId,
      siteId: siteId,
    },
  });
};

//获取报告单
const previewPdfh5 = ref();
//获取报告
async function getReport() {
  try {
    const result = await API.userCommonApi.getReport.request({
      params: {
        Id: Number(caseId),
      },
    });
    if (result.Data) {
      nextTick(() => {
        previewPdfh5.value.openPdf(result.Data);
      });
    }
  } catch (error: any) {
    Toast(error?.data?.message);
  }
}
const releClcik = async () => {
  //复核中的病例更改状态为诊断
  // await API.expertFunctionApi.postDiagnosis.request({
  //   bodyParams: {
  //     Id: Number(caseId),
  //   },
  // });
  releaseReport();
};
//发布报告
const releaseReport = async () => {
  const result = await API.expertFunctionApi.getDiagnosis.request({
    params: {
      Id: Number(caseId),
    },
  });
  if (result.Data) {
    const reportParam: any = new defs.CaseReportReqModel();
    reportParam.Opinion = result.Data?.Opinion as string; //诊断意见
    reportParam.JsonData = result.Data?.JsonData;
    reportParam.IsReview = true;
    reportParam.Id = Number(result.Data?.Id);
    await API.expertFunctionApi.putRelease.request({
      bodyParams: reportParam,
    });
    Toast.success("发布成功");
    router.go(-3);
    await API.expertShortMessageApi.putRelease.request({
      bodyParams: {
        Id: Number(caseId),
      },
    });
  }
};
onMounted(() => {
  getReport();
});
</script>
<style lang="scss" scoped></style>
