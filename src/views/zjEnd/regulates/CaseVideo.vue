<template>
  <div class="casevideo">
    <!--顶部导航 start -->
    <NavBar title="链接视频" :leftAarrow="false" />
    <!--顶部导航 end -->

    <NYunXin
      zoom
      @leaving="onLeaving"
      class="my-yunxin"
      @invitation="onInvitation"
    />
    <!-- <div class="video-bottoms"></div> -->
    <!-- <div class="video-bottom"></div> -->
  </div>
</template>
<script lang="ts">
import NavBar from "@/components/navBar/Index.vue";
import { Aes } from "@/utils/aesEncrypt";
import { defineComponent, onMounted, onUnmounted, ref, watch } from "vue";
import { useNim } from "@/components/Nim/src/hooks/useNim";
import { useNertc } from "@/components/Nertc/src/hooks/useNertc";
import { NertcEvent } from "@/components/Nertc/src/types/nertc";
import NYunXin from "@/components/YunXin/src/YunXin.vue";
import { Toast } from "vant";
import RcpWeb from "RcpWeb"; //项目配置
import useStore from "@/store";
export default defineComponent({
  components: {
    NYunXin,
    NavBar,
  },
  emits: ["leaving"],
  setup(props, { emit }) {
    const route = useRoute();
    const router = useRouter();
    const { caseId, teamId } = route.query;
    const nim = useNim();
    const { selectColor } = useStore();
    const nertc = useNertc();

    // 是否打开主音视频
    const isOpenMainVideo = ref(false);

    // 是否为手机
    const isPhone = navigator.userAgent.match(
      /(iPhone|iPod|Android|ios|iOS|iPad|Backerry|WebOS|Symbian|Windows Phone|Phone)/i
    );

    //用户信息
    const userInfo: any = ref({});

    onMounted(async () => {
      await init();
    });
    //获取用户信息
    const getUserInfoFun = async () => {
      try {
        const { token } = route.query;
        if (token) {
          const { tokens } = useStore();
          tokens.setToken(token as string);
        }
        const res = await API.accountApi.getInfo.request({});
        userInfo.value = res.Data;
        // 初始化
      } catch (error) {
        console.log(error, "获取用户信息");
      }
    };
    // 结束视频链接
    const onLeaving = async () => {
      if (nim.renderMembers.value.length <= 1) {
        try {
          let res = await nim.dismissTeam();
          if (res) {
            router.go(-1);
          }
        } catch (error) {
          router.go(-1);
          return false;
        }
      } else {
        router.go(-1);
      }

      // var userAgent = navigator.userAgent;
      // if (userAgent.indexOf("MSIE") > 0) {
      //   if (userAgent.indexOf("MSIE 6.0") > 0) {
      //     window.opener = null;
      //     window.close();
      //   } else {
      //     window.open("", "_top");
      //     window.top?.close();
      //   }
      // } else if (userAgent.indexOf("Firefox") != -1 || userAgent.indexOf("Chrome") != -1) {
      //   window.location.href = "about:blank "; //火狐默认状态非window.open的页面window.close是无效的
      //   //window.history.go(-2);
      // } else {
      //   window.opener = null;
      //   window.open("about:blank", "_self");
      //   window.close();
      // }
    };
    // 复制邀请链接
    const onInvitation = (toClipboard: (text: string) => Promise<unknown>) => {
      try {
        toClipboard(
          `${RcpWeb.sharePath}#/caseVideo?caseId=${caseId}&teamId=${teamId}`
        );
        Toast.success("复制成功");
      } catch (e) {
        Toast.fail("复制失败");
      }
    };

    // 初始化Nertc和IM
    const init = async () => {
      console.log(userInfo.value);
      const channelName = caseId;
      try {
        // await API.netEaseYunXinApi.getUserInfor.request({});//生成用户云信id
        const yunXinConfig = (await API.netEaseYunXinApi.getInfor.request({}))
          .Data;
        await getUserInfoFun();
        const yunXinNimUserInfo = (
          await API.netEaseYunXinApi.getUserInforId.request({
            params: { Id: userInfo.value.Id as number },
          })
        ).Data;
        const yunxinNertcToken = (
          await API.netEaseYunXinApi.getChannelToken.request({
            params: {
              UId: userInfo.value.Id as number,
              /** 房间名称 */
              ChannelName: Aes.Encrypt(channelName as string),
              /** 过期时间 */
              ExpireAt: 1000,
            },
          })
        ).Data;
        await API.netEaseYunXinApi.postPullCaseTeam.request({
          bodyParams: {
            Id: Number(caseId),
            Str: userInfo.value.Id?.toString(),
          },
        });

        // await API.netEaseYunXinApi.postPullUrlTeam.request({
        //   bodyParams: {
        //     tId: teamId as string,
        //     userId: userInfoId ? userInfoId as string : userInfo.value.Id as string
        //   }
        // })

        console.log(
          yunXinConfig,
          yunXinNimUserInfo,
          yunxinNertcToken,
          "云信配置"
        );

        if (yunXinConfig && yunXinNimUserInfo) {
          // 初始化IM
          nim.init({
            appKey: Aes.Decrypt(yunXinConfig.AppKey as string),
            account: yunXinNimUserInfo.NeteaseImaccId as string,
            token: yunXinNimUserInfo.NeteaseImtoken as string,
            teamId: teamId as string,
            formAvatar: "",
          });
          console.log(userInfo.value.Id, channelName, "++++");

          // 初始化Nertc
          nertc.init({
            appkey: Aes.Decrypt(yunXinConfig.AppKey as string),
            account: userInfo.value.Id + "",
            token: yunxinNertcToken as string,
            channelName: channelName as string,
          });
        }
      } catch (e) {
        console.log(e);

        console.log("初始化失败");
      }
    };

    // 处理加入房间
    const onJoinChannelHandler = async (evt: any) => {
      try {
        const time = Date.now();
        const res: any = await API.accountApi.getInfoId.request({
          params: { Id: evt.uid },
        });
        console.log(userInfo, evt, "用户信息");

        const member = {
          uid: res.Data.Id?.toString() as string,
          nickname: res.Data.RealName as string,
          avatar: res.Data.HeadPic as string,
          time,
        };
        // 更新音视频成员
        nertc.updateMember(member.uid, member);
        // 添加聊天室成员
        nim.addMember(member.uid, member);
      } catch (e) {
        console.log("添加成员失败");
      }
    };

    // 处理离开房间事件
    const onPeerLeaveHandler = (evt: any) => {
      // 删除聊天室成员
      nim.delMember(evt.uid);
    };

    // 处理主音视频切换
    const onCutMainVideoHandler = () => {
      isOpenMainVideo.value = true;
    };

    // 处理主音视频关闭
    const onCloseMainVideoHandler = () => {
      isOpenMainVideo.value = false;
    };

    // 监听加入房间
    nertc.on(NertcEvent.JOIN_CHANNEL, onJoinChannelHandler);
    // 监听离开房间
    nertc.on(NertcEvent.PEER_LEAVE, onPeerLeaveHandler);
    // 监听主音视频切换
    nertc.on(NertcEvent.OPEN_MAIN_VIDEO, onCutMainVideoHandler);
    // 监听主音视频关闭
    nertc.on(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);

    // onMounted(init)

    onUnmounted(() => {
      nertc.off(NertcEvent.JOIN_CHANNEL, onJoinChannelHandler);
      nertc.off(NertcEvent.PEER_LEAVE, onPeerLeaveHandler);
      nertc.off(NertcEvent.OPEN_MAIN_VIDEO, onCutMainVideoHandler);
      nertc.off(NertcEvent.CLOSE_MAIN_VIDEO, onCloseMainVideoHandler);
      // 离开房间
      nertc.leaving();
      // 退出登录
      nim.logout();
      selectColor.isMainVideoPlayFn(false);
    });
    return {
      onLeaving,
      init,
      onInvitation,
    };
  },
});
</script>
<style scoped lang="scss">
.video-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 35px;
  // background: #F9F9F9;
  // border-radius: 0px 0px 0px 0px;
  opacity: 1;
}

.video-bottoms {
  width: 100%;
  height: 35px;
}
</style>
