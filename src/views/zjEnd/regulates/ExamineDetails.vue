<template>
  <div>
    <!--顶部导航 start -->
    <NavBar :title="titles" />
    <!--顶部导航 end -->

    <!-- tab切换 start -->
    <DiagnosticReport v-if="!id" @tabClick="tabClick" />
    <!-- tab切换 end -->

    <!-- 查看切片 start -->
    <ImageViewer
      ref="imageViewer"
      v-if="tabClicks === 0 && zjsection.sectionList.length > 0 && !id"
      @screenshot="setScreenshot"
      @changeClicks="changeClicks"
      :imageEditorss="!id"
    />
    <!-- 查看切片 end -->

    <!-- 查看切片 start -->
    <ImageViewerTop
      ref="imageViewer"
      v-if="zjsection.sectionList.length > 0 && id"
      :data="getFillingList"
      @changeClicks="changeClicks"
      :imageEditorss="false"
      :disqualification="false"
    />
    <!-- 查看切片 end -->

    <!-- 填写报告 start -->
    <div v-show="tabClicks === 1">
      <FillingExplanation :data="getFillingList" />

      <div v-if="getFillingList?.ReportType === 'TCT'">
        <Laboratory :diagnosticRendering="getFillingList" ref="laboratory" />
        <UploadReport
          ref="uploadReport"
          :TCT="true"
          uploaderName="镜下所见"
          :ExpansionInformation="jsonDatas?.ExpansionInformation?.NR_ObjectName"
        />
        <InputMessage
          backTitle="诊断意见"
          backPlaceholder="请输入诊断意见"
          :messages="jsonDatas?.ExpansionInformation?.DiagnosticInformation"
          @messageClick="opinionClick"
        />
      </div>

      <div v-else>
        <InputMessage
          backTitle="大体描述"
          :disabled="true"
          :messages="getFillingList?.Thus"
          backPlaceholder=""
          @messageClick="generalClick"
        />

        <!-- 上传报告附图 start -->
        <UploadReport
          ref="uploadReport"
          :ExpansionInformation="jsonDatas?.ExpansionInformation?.NR_ObjectName"
        />
        <!-- 上传报告附图 end -->
      </div>

      <!-- 病例质量评估 start -->
      <!-- <CaseRadio :list="assessList" @radiosClick="assessListClick" /> -->
      <!-- 病例质量评估 end -->

      <!-- 阴阳性判断：start -->
      <!-- <CaseRadio name="阴阳性判断：" :list="judgeList" @radiosClick="judgeListClick" /> -->
      <!-- 阴阳性判断：end -->

      <div v-if="getFillingList?.ReportType !== 'TCT'">
        <InputMessage
          backTitle="图像描述"
          :messages="jsonDatas?.ExpansionInformation?.ImageDescription"
          backPlaceholder="请输入图像描述"
          :maxlength="500"
          @messageClick="photoClick"
        />
        <InputMessage
          backTitle="诊断意见"
          :messages="jsonDatas?.ExpansionInformation?.DiagnosticInformation"
          backPlaceholder="请输入诊断意见"
          :maxlength="300"
          @messageClick="opinionClick"
        />
      </div>
      <div v-else>
        <InputMessage
          backTitle="备注"
          backPlaceholder="请输入备注"
          :messages="jsonDatas?.ExpansionInformation?.reportRemark"
          @messageClick="remarkClick"
        />
      </div>

      <BottomBtn @leftBtn="rightBtn('save')" @rightBtn="rightBtn('news')" />
    </div>

    <!-- 切片评价 start -->
    <van-popup v-model:show="evaluateShow" closeable close-icon="close" round>
      <SliceEvaluation
        :data="getFillingList"
        :assessList="assessList"
        @submitEvaluation="submitEvaluation"
      />
    </van-popup>
    <!-- 切片评价 end -->

    <!-- 下医嘱 start -->
    <DoctorAdvice v-if="tabClicks === 2" />
    <!-- 下医嘱 end -->

    <!-- <van-loading /> -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import ImageViewer from "@/components/image-viewer/ImageViewer.vue";
import ImageViewerTop from "@/components/image-viewer/ImageViewerTop.vue";
import DiagnosticReport from "./components/DiagnosticReport.vue";
import FillingExplanation from "./components/FillingExplanation.vue";
import InputMessage from "@/components/inputMessage/InputMessage.vue";
import CaseRadio from "@/components/caseRadio/CaseRadio.vue";
import BottomBtn from "@/components/bottomBtn/BottomBtn.vue";
import UploadReport from "@/components/uploadReport/UploadReport.vue";
import Laboratory from "./components/laboratory.vue";
import SliceEvaluation from "./components/sliceEvaluation.vue";
import DoctorAdvice from "./components/DoctorAdvice.vue";
import { Toast } from "vant";
import useStore from "@/store";
import { Ref, inject } from "vue";
import router from "@/router";
import useScreenshot from "@/hooks/useScreenshot";
import { cloneDeep } from "lodash";
import RcpWeb from "RcpWeb"; //项目配置

const { zjsection, saveCase, label } = useStore();
const route = useRoute();
const { caseId, id, siteId } = route.query;
const imageViewer = ref();
//tab切换
const tabClicks = ref(0);
const tabClick = (index: number) => {
  tabClicks.value = index;
};
const titles = computed(() => {
  if (id) {
    return "查看切片";
  } else {
    return "诊断";
  }
});
//切片评价弹窗
const evaluateShow = ref(false);

const { addSliceScreenshot, initscreenshots, addScreenshot } = useScreenshot(); //截图hooks
//大体描述
const generalDescription = ref("");
const generalClick = (value: string) => {
  generalDescription.value = value;
};
//图像描述
const imageDescription = ref("");
const photoClick = (value: string) => {
  imageDescription.value = value;
};
//诊断意见
const opinion = ref("");
const opinionClick = (value: string) => {
  opinion.value = value;
};

//备注
const remark = ref("");
const remarkClick = (value: string) => {
  remark.value = value;
};

const uploadReport = ref();
const laboratory = ref();
//液基详情
const getFillingDetails = ref();
function isBase64(str: string) {
  if (str.indexOf("data:") != -1 && str.indexOf("base64") != -1) {
    return true;
  } else {
    return false;
  }
}

const submitEvaluation = async (rightBtnRes: string) => {
  evaluateShow.value = false;
  await uploadReport.value.handleUpload();

  if (getFillingList.value?.ReportType === "TCT") {
    if (
      !opinion.value &&
      !jsonDatas.value?.ExpansionInformation?.DiagnosisOpinion
    ) {
      Toast.fail("请填写诊断意见");
      return;
    }
    // if (!remark.value && !jsonDatas.value?.ExpansionInformation?.reportRemark) {
    //   Toast.fail('请填写备注信息')
    //   return
    // }
    getFillingDetails.value = await laboratory.value?.submit();
    console.log(await laboratory.value?.submit());

    // ((getFillingDetails.value.ExpansionInformation.GeneralDescription =
    //   getFillingList.value.Thus),
    (getFillingDetails.value.ExpansionInformation.DiagnosticInformation =
      opinion.value || jsonDatas.value.ExpansionInformation.DiagnosisOpinion),
      //备注字段
      (getFillingDetails.value.ExpansionInformation.reportRemark =
        remark.value || jsonDatas.value?.ExpansionInformation?.reportRemark);
    getFillingDetails.value.ExpansionInformation.GEC_ImageOne = uploadReport
      .value?.imageList[0]
      ? uploadReport.value.imageList[0].ImageUrl
      : null;
    getFillingDetails.value.ExpansionInformation.ImageDescriptionOne =
      uploadReport.value?.imageList[0].Remark;

    getFillingDetails.value.ExpansionInformation.NR_ObjectName = uploadReport
      .value?.imageList[0]
      ? uploadReport.value?.imageList[0].ImageUrl
      : "";
    getFillingDetails.value.BasicInformation.SamplingTime =
      getFillingList.value?.SamplingTime;
    getFillingDetails.value.ExpansionInformation.OriginalPathologicalDiagnosis =
      getFillingList.value?.DiagnosisContent; //原病理信息
    if (getFillingDetails.value.BasicInformation?.SpecimenTypeName) {
      try {
        getFillingDetails.value.BasicInformation.SpecimenTypeName = JSON.parse(
          getFillingDetails.value.BasicInformation.SpecimenTypeName
        ).join("，");
      } catch (error) {
        getFillingDetails.value.BasicInformation.SpecimenTypeName =
          getFillingDetails.value.BasicInformation.SpecimenTypeName;
      }
    }
    console.log(getFillingDetails.value, uploadReport.value);
    const input = new defs.CaseReportReqModel();
    input.Opinion = opinion.value; //诊断意见
    (input as any).IsReview = false;
    input.Id = Number(caseId);
    input.Quality = quality.value;
    input.YinPositive = YinPositive.value;
    input.JsonData = JSON.stringify(getFillingDetails.value);
    saveCase.saveCase(cloneDeep(input));
    await API.expertFunctionApi.postBuildReport.request({
      bodyParams: input,
    });
  } else {
    // if (!generalDescription.value && !jsonDatas.value?.ExpansionInformation?.GeneralDescription) {
    //   Toast.fail('请填写大体描述')
    //   return
    // }
    // if (
    //   !imageDescription.value &&
    //   !jsonDatas.value?.ExpansionInformation?.ImageDescription
    // ) {
    //   Toast.fail("请填写图像描述");
    //   return;
    // }
    if (
      !opinion.value &&
      !jsonDatas.value?.ExpansionInformation?.DiagnosticInformation
    ) {
      Toast.fail("请填写诊断意见");
      return;
    }
    const input: any = new defs.CaseReportReqModel();
    input.Opinion = opinion.value; // 诊断意见

    // 获取并解析 JSON 数据
    const jsonData = getFillingList.value?.JsonData
      ? JSON.parse(getFillingList.value.JsonData)
      : {};

    const getAttachedDrawing = (index: number, key: string) => {
      return uploadReport.value?.imageList[index]?.ImageUrl || null;
    };

    const getAttachedDrawingRemark = (index: number) => {
      return uploadReport.value?.imageList[index]?.Remark || "";
    };

    // 更新扩展信息
    jsonData.ExpansionInformation = {
      ImageDescription:
        imageDescription.value ||
        jsonDatas.value?.ExpansionInformation?.ImageDescription ||
        "", // 图像描述
      DiagnosticInformation:
        opinion.value ||
        jsonDatas.value?.ExpansionInformation?.DiagnosticInformation, // 诊断意见
      AttachedDrawingOne: getAttachedDrawing(0, "AttachedDrawingOnes"),
      AttachedDrawingTwo: getAttachedDrawing(1, "AttachedDrawingTwos"),
      AttachedDrawingThree: getAttachedDrawing(2, "AttachedDrawingThrees"),
      AttachedDrawingFour: getAttachedDrawing(3, "AttachedDrawingFours"),
      ImageDescriptionOne: getAttachedDrawingRemark(0),
      ImageDescriptionTwo: getAttachedDrawingRemark(1),
      ImageDescriptionThree: getAttachedDrawingRemark(2),
      ImageDescriptionFour: getAttachedDrawingRemark(3),
      OriginalPathologicalDiagnosis: getFillingList.value?.DiagnosisContent, // 原病理信息
      GeneralDescription: getFillingList.value?.Thus,
      NR_ObjectName: [], // 初始化附图数组
    };

    // 填充附图
    const addAttachedDrawing = (index: number, key: string) => {
      const imageUrl = getAttachedDrawing(index, key);
      if (imageUrl) {
        jsonData.ExpansionInformation.NR_ObjectName.push({
          ImageUrl: imageUrl,
          Remark: getAttachedDrawingRemark(index),
        });
      }
    };
    addAttachedDrawing(0, "AttachedDrawingOnes");
    addAttachedDrawing(1, "AttachedDrawingTwos");
    addAttachedDrawing(2, "AttachedDrawingThrees");
    addAttachedDrawing(3, "AttachedDrawingFours");

    // 更新其他信息
    jsonData.Header = jsonDatas.value?.Header;
    jsonData.Bottom = jsonDatas.value?.Bottom;
    jsonData.BasicInformation = {
      ...jsonDatas.value?.BasicInformation,
      SamplingTime: getFillingList.value?.SamplingTime,
    };

    // 格式化 SpecimenTypeName
    if (jsonData.BasicInformation?.SpecimenTypeName) {
      try {
        jsonData.BasicInformation.SpecimenTypeName = JSON.parse(
          jsonData.BasicInformation.SpecimenTypeName
        ).join("，");
      } catch (error) {
        jsonData.BasicInformation.SpecimenTypeName =
          jsonData.BasicInformation.SpecimenTypeName;
      }
    }
    console.log(jsonData, "2222");

    // 更新 input
    input.IsReview = false;
    input.JsonData = JSON.stringify(jsonData);
    input.Id = Number(caseId);
    input.Quality = quality.value;
    input.YinPositive = YinPositive.value;

    // 保存案例
    saveCase.saveCase(cloneDeep(input));
    try {
      await API.expertFunctionApi.postBuildReport.request({
        bodyParams: input,
      });
    } catch (error: any) {
      Toast.fail(error.data.message);
      console.log(error);
    }
  }
  if (rightBtnRes === "save") {
    Toast.success("保存成功");
  } else {
    try {
      await API.userCommonApi.getReport.request({
        params: {
          Id: Number(caseId),
        },
      });
      router.push({
        path: "/report",
        query: {
          caseId: caseId,
          siteId: siteId,
        },
      });
    } catch (error: any) {
      return Toast(error?.data?.message);
    }
  }
};
//发布病例
const rightBtn = async (rightBtnRes: string) => {
  const list = zjsection.sectionList?.filter((item) => {
    return (
      item.Evaluation === null ||
      (item.Evaluation === 4 && !item.SliceDescription)
    );
  });
  console.log(list, "1212");

  if (list.length > 0 && rightBtnRes !== "save" && RcpWeb.IsEvaluate) {
    evaluateShow.value = true;
  } else {
    submitEvaluation(rightBtnRes);
  }
};
//病例质量评估
const quality = ref(1);
const assessList = ref([]);
const getDropAdd = async () => {
  const res = await API.userCommonApi.getDropAdd.request({
    params: {
      code: "SLICE_QUALITY",
    },
  });
  assessList.value = res.Data?.SLICE_QUALITY;
};
const assessListClick = (value: number) => {
  quality.value = value;
};

//阴阳性判断
const YinPositive = ref(1);
const judgeList = ref([
  {
    name: "阴性",
    value: 1,
    checked: true,
  },
  {
    name: "阳性",
    value: 2,
    checked: false,
  },
]);
const judgeListClick = (value: number) => {
  YinPositive.value = value;
};

//获取Filling数据
const getFillingList: Ref<defs.WriteDiagnosticsDto | undefined> = ref();
const jsonDatas = ref();
const getFilling = async () => {
  const res = await API.expertFunctionApi.getDiagnosis.request({
    params: {
      Id: Number(caseId),
    },
  });
  getFillingList.value = res.Data;
  zjsection.setFillingDetails(getFillingList.value || {});
  changeClick();

  assessList.value &&
    assessList.value.forEach((item: any) => {
      quality.value = Number(res.Data?.Quality);
      if (Number(item.value) === Number(res.Data?.Quality)) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
  judgeList.value &&
    judgeList.value.forEach((item: any) => {
      YinPositive.value = Number(res.Data?.YinPositive);
      if (Number(item.value) === Number(res.Data?.YinPositive)) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
  console.log(JSON.parse(res.Data?.JsonData || "{}"));

  jsonDatas.value = JSON.parse(res.Data?.JsonData || "{}");
  if (
    res.Data?.ReportType !== "TCT" &&
    jsonDatas.value?.ExpansionInformation?.NR_ObjectName
  ) {
    const descriptionFields = [
      jsonDatas.value?.ExpansionInformation?.ImageDescriptionOne,
      jsonDatas.value?.ExpansionInformation?.ImageDescriptionTwo,
      jsonDatas.value?.ExpansionInformation?.ImageDescriptionThree,
      jsonDatas.value?.ExpansionInformation?.ImageDescriptionFour,
    ];

    jsonDatas.value?.ExpansionInformation?.NR_ObjectName?.forEach(
      (item: { Remark: any }, index: number) => {
        if (index < descriptionFields.length) {
          item.Remark = descriptionFields[index];
        }
      }
    );
  } else {
    if (jsonDatas.value?.ExpansionInformation?.NR_ObjectName)
      jsonDatas.value.ExpansionInformation.NR_ObjectName = [
        {
          ImageUrl: jsonDatas.value?.ExpansionInformation.NR_ObjectName,
          Remark: jsonDatas.value?.ExpansionInformation?.ImageDescriptionOne,
        },
      ];
  }
};
const changeClicks = () => {
  changeClick();
};
const changeClick = () => {
  imageViewer.value?.refreshViewersClcik(
    getFillingList.value?.PathologyNumber,
    getFillingList.value?.CaseTypeCode === "Frozen" ? true : false
  );
  label.getLabelList(zjsection.selectSection.Id as number);
};
watch(
  () => tabClicks.value,
  (val) => {
    if (val === 0 && zjsection.sectionList.length > 0) {
      nextTick(() => {
        changeClick();
      });
    }
  }
);
//截图

const setScreenshot = async (url: string) => {
  console.log(url);

  await addSliceScreenshot(
    url,
    zjsection.selectSection.FileName as string,
    getFillingList.value?.ReportType as string
  );
};
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
onMounted(async () => {
  if (zjsection.sectionList.length <= 0) {
    params.Id = Number(caseId);
    await zjsection.getSectionAsync(params);
  }
  getFilling();
});
//页面卸载
onBeforeUnmount(async () => {
  // await API.caseLockingApi.postDelete.request({
  //   bodyParams: {
  //     Id: Number(caseId),
  //   },
  // });
});
</script>
<style scoped lang="scss">
.van-radio__icon--checked .van-icon {
  background-color: #650faa;
  border-color: #650faa;
}
</style>
