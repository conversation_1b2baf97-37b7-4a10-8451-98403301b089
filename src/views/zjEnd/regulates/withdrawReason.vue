<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="撤回理由" />
    <!--顶部导航 end -->
    <div class="feedBack">
      <div class="feedBack-title">撤回理由</div>
      <van-cell-group inset>
        <van-field
          v-model="message"
          rows="5"
          label=""
          type="textarea"
          maxlength="100"
          show-word-limit
          autosize
          :autofocus="true"
          :disabled="true"
          placeholder="请输入退回理由"
        />
      </van-cell-group>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
const route = useRoute();
const { id } = route.query;
const message = ref("");
//获取撤回理由
const getWithdrawReason = async () => {
  const res = await API.userCommonApi.getWithdrawal.request({
    params: {
      Id: Number(id),
    },
  });
  message.value = res.Data as string;
};
onMounted(() => {
  getWithdrawReason();
});
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 11px 16px 14px;
  // height: 172px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}
</style>
