<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="预览附件" />
    <!--顶部导航 end -->
    <PdfViewerH5 ref="previewPdfh5" />
    <!-- <PdfViewer :pdfUrl="(url as string)" /> -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue"
// import PdfViewer from "@/components/pdfViewer/PdfViewer.vue"
import PdfViewerH5 from "@/components/pdfViewer/PdfViewerH5.vue"
const route = useRoute()
const { url } = route.query
const previewPdfh5 = ref()
onMounted(() => {
  nextTick(() => {
    previewPdfh5.value.openPdf(url)
    console.log(previewPdfh5.value);
  })
});
</script>

<style lang="scss" scoped>

</style>
