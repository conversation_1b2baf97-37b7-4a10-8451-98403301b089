<template>
  <div class="collect">
    <!--顶部导航 start -->
    <NavBar title="病例收藏" />
    <!--顶部导航 end -->

    <!-- 搜索 start -->
    <CaseSearch
      @onSearchHandle="executeSearch"
      placeholder="请搜索原病理号/患者姓名"
      :hospitalList="hospitalList"
      @hospitalListClick="hospitalListClick"
    />
    <!-- 搜索 end -->
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :immediate-check="false"
      @load="onLoadNext"
    >
      <!--列表 start -->
      <listCard
        :dataList="dataList"
        :disabledListCard="false"
        @cancelCollect="cancelCollect"
      >
      </listCard>
      <!--列表 end -->
    </van-list>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseSearch from "@/components/case-list/CaseSearch.vue";
import listCard from "@/views/zjEnd/regulates/components/listCard.vue";
import { cloneDeep } from "lodash";
import { Ref } from "vue";
import useStore from "@/store";
import { Toast } from "vant";

/**
 * 搜索
 */
const { selectColor, zjsection } = useStore();
const executeSearch = (value: any) => {
  dataList.value = [];
  query.EndTime = "";
  query.StartingTime = "";
  query.AdvancedSearch = value.AdvancedSearch;
  if (value.EndTime) {
    query.EndTime = value.EndTime;
  }
  if (value.StartingTime) {
    query.StartingTime = value.StartingTime;
  }
  query.PageSize = 10;
  query.PageIndex = 1;
  GetCaseCollectList();
};
/**
 * 列表
 */
const query: API.expertCaseManagementApi.getEnshrineList.Params = reactive({
  PageIndex: 1,
  PageSize: 10,
});
const dataList: Ref<Array<defs.CaseDto> | undefined> = ref();
const GetCaseCollectList = async () => {
  const res = await API.expertCaseManagementApi.getEnshrineList.request({
    params: query,
  });
  total.value = res.Count || 0;
  dataList.value = dataList.value?.concat(res.Data || []) || res.Data;
};
//获取医院
const paramss = reactive({
  PageIndex: 1,
  PageSize: 50,
});
const hospitalList: Ref<Array<defs.SiteDropDto>> = ref([]);
const GetHospital = async () => {
  const res = await API.expertFunctionApi.getSite.request({
    params: paramss,
  });
  const ress = cloneDeep(res.Data);
  if (ress && ress.length > 0) {
    ress.forEach((item: any) => {
      item.checked = false;
    });
    hospitalList.value = ress;
  }
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};

//取消收藏
const cancelCollect = async (id: number) => {
  const res = await API.expertFunctionApi.putCancelenshrine.request({
    bodyParams: {
      Id: id,
    },
  });
  if (res.Code === 200) {
    Toast.success("取消成功");
    query.PageSize = 10;
    query.PageIndex = 1;
    dataList.value = [];
    GetCaseCollectList();
  }
};

//下拉加载
const finished = ref(false);
const loading = ref(false);
const refreshing = ref(false);
const total = ref(0);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        dataList.value = [];
        refreshing.value = false;
      }
      query.PageIndex++;
      await GetCaseCollectList();

      loading.value = false;

      if (dataList.value && dataList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
onMounted(() => {
  zjsection.clearSection();
  selectColor.selectNav = 7;
  GetCaseCollectList();
  GetHospital();
});
</script>
<style lang="scss" scoped>
.collect {
  height: 100%;
  background-color: #f3f6fd;
}
</style>
