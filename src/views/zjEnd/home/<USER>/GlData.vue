<template>
  <div class="home-data">
    <div v-if="props.islink" class="home-data-title">实时数据</div>
    <div class="home-data-center">
      <div style="display: flex">
        <div
          :class="[
            'cell-button1',
            today && props.islink ? 'cell-button' : '',
            today && props.islink === false ? 'cell-button2' : '',
          ]"
          @click="cellClick('今日')"
        >
          今日
        </div>
        <div
          :class="[
            'cell-button1',
            yesterday && props.islink ? 'cell-button' : '',
            yesterday && props.islink === false ? 'cell-button2' : '',
          ]"
          @click="cellClick('昨日')"
        >
          昨日
        </div>
        <div
          :class="[
            'cell-button1',
            timeRange && props.islink ? 'cell-button' : '',
            timeRange && props.islink === false ? 'cell-button2' : '',
          ]"
          @click="cellClick('近七日')"
        >
          近七日
        </div>
      </div>
      <div
        v-if="props.islink"
        class="home-data-center-right"
        @click="goToStatistics"
      >
        <div>查看更多</div>
        <van-icon name="arrow" size="16" />
      </div>
    </div>
    <div class="home-data-bottom">
      <div class="home-data-bottom-top">
        <div style="flex: 1" @click="dataSkip(1)">
          <div>待诊断病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.ToBeDiagnosed }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkip(2)">
          <div>待复核病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.ToBeReviewed }}
          </div>
        </div>
      </div>
      <div class="home-data-bottom-top home-data-bottom-top1">
        <div style="flex: 1" @click="dataSkip(3)">
          <div>已发布病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.Published }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkip(5)">
          <div>已退回病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.Returned }}
          </div>
        </div>
      </div>
      <div class="home-data-bottom-top home-data-bottom-top1">
        <div style="flex: 1" @click="dataSkip(4)">
          <div>受邀诊断</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.InvitedDiagnosis }}
          </div>
        </div>
        <div style="flex: 1" @click="router.push('/subscribe')">
          <div>冰冻预约</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.Frozen }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { GetTimeRange, TimeRangeType } from "@/utils/time";
import { PropType } from "vue";
import useStore from "@/store";
const router = useRouter();
const props = defineProps({
  islink: {
    type: Boolean,
    default: true,
  },
  homeData: {
    type: Object as PropType<defs.ExpertHomeCountDto>,
    default: () => {},
  },
});
const emits = defineEmits(["timeClick"]);
const { selectColor } = useStore();
const today = ref(true);
const yesterday = ref(false);
const timeRange = ref(false);
const cellClick = (value: TimeRangeType) => {
  if (value === "今日") {
    today.value = true;
    yesterday.value = false;
    timeRange.value = false;
  } else if (value === "昨日") {
    today.value = false;
    yesterday.value = true;
    timeRange.value = false;
  } else if (value === "近七日") {
    today.value = false;
    yesterday.value = false;
    timeRange.value = true;
  }
  const time = GetTimeRange(value);
  emits("timeClick", time);
};
const goToStatistics = () => {
  router.push("/regulates");
};

//跳转
const dataSkip = (value: number) => {
  selectColor.selectNavFn(value);
  router.push({
    path: "/regulates",
  });
};
</script>
<style lang="scss" scoped>
.home-data {
  padding: 0 20px;
  background-color: #f4f7fd;
  z-index: 99;
}

.home-data-title {
  margin-top: 18px;
  margin-bottom: 8px;
  font-size: 18px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
  line-height: 21px;
}

.home-data-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.home-data-center-right {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.cell-button1 {
  margin-right: 8px;
  padding: 0 17px;
  height: 28px;
  background: #fff;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  border: 1px solid #e6ecfb;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  color: #8b96a6;
}

.cell-button {
  background: #650faa;
  color: #fff;
  border: 1px solid #650faa;
}

.cell-button2 {
  color: #650faa;
  border: 1px solid #650faa;
}

.van-cell {
  padding: 0;
  background-color: #f4f7fd;
}

.home-data-bottom {
  box-sizing: border-box;
  margin-top: 8px;
  padding: 24px 17px;
  height: 232px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
  color: #8b96a6;
}

.home-data-bottom-top {
  display: flex;
}

.home-data-bottom-top1 {
  margin-top: 24px;
}

.home-data-bottom-value {
  margin-top: 5px;
  font-size: 24px;
  font-weight: bold;
  color: #4d545f;
}
</style>
