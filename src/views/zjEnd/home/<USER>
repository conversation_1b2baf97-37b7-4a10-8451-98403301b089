<template>
  <div class="myout">
    <!--顶部导航 start -->
    <NavBar title="我的" />
    <!--顶部导航 end -->
    <van-cell class="myout-cell" title="修改密码" is-link @click="alterCode" />

    <div class="loginOut" @click="loginShow = true">退出登录</div>

    <!-- 退出登录 start -->
    <CaseOverlay
      v-model:show="loginShow"
      :overlayTitle="`确认退出登录？`"
      @submit="submit"
    />
    <!-- 退出登录 end -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseOverlay from "@/components/case-overlay/CaseOverlay.vue";
import useStore from "@/store";
import { clearLocalStorage } from "@/utils/storage";
const router = useRouter();
const { tokens, routers } = useStore();
const loginShow = ref(false);

const submit = () => {
  routers.router = [];
  routers.asyncRouter = [];
  tokens.redirectLogin();
  // window.uni.getEnv(function (res: { h5: any }) {
  //   if (res.h5) {
  //     tokens.redirectLogin();
  //   } else {
  //     clearLocalStorage();
  //     window.uni.reLaunch({
  //       url: "/pages/login/Login",
  //     });
  //   }
  // });
};

//修改秘密
const alterCode = () => {
  router.push({ path: "/alterCode" });
};
</script>
<style lang="scss" scoped>
.myout {
  padding: 0 14px;
}

.myout-cell {
  height: 48px;
  margin-top: 14px;
  border-radius: 4px 4px 4px 4px;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #4d545f;
}

:deep(.van-cell__title) {
  display: flex;
  align-items: center;
}

:deep(.van-field__label) {
  margin: 0 !important;
}

.loginOut {
  margin-top: 14px;
  height: 48px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff7272;
  text-align: center;
  line-height: 48px;
}
</style>
