<template>
  <div class="myout">
    <!--顶部导航 start -->
    <NavBar title="修改密码" />
    <!--顶部导航 end -->

    <van-cell-group inset>
      <van-field
        v-model="rawPassword"
        type="password"
        placeholder="请填写原密码"
        label="原密码"
      />
      <van-field
        v-model="newPassword"
        type="password"
        placeholder="密码由8-20位英文字母、数字组成"
        label="新密码"
      />
      <van-field
        v-model="confirmPassword"
        type="password"
        placeholder="请再次输入新的密码"
        label="确认密码"
      />
    </van-cell-group>

    <div class="submit" @click="submit">提交</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Aes } from "@/utils/aesEncrypt";
import { Toast } from "vant";
import useStore from "@/store";
const { tokens } = useStore();
//原密码
const rawPassword = ref();
//新密码
const newPassword = ref();
//确认密码
const confirmPassword = ref();

//提交密码
const submit = async () => {
  if (!rawPassword.value) {
    Toast.fail("请输入原密码");
    return;
  }
  if (!newPassword.value) {
    Toast.fail("请输入新密码");
    return;
  }
  if (!confirmPassword.value) {
    Toast.fail("请再次输入新的密码");
    return;
  }
  if (newPassword.value != confirmPassword.value) {
    Toast.fail("两次输入的密码不一致");
    return;
  }
  if (newPassword.value.length < 8 || newPassword.value.length > 20) {
    Toast.fail("密码由8-20位英文字母、数字组成");
    return;
  }
  if (!/^[a-zA-Z0-9]+$/.test(newPassword.value)) {
    Toast.fail("密码由8-20位英文字母、数字组成");
    return;
  }
  const input = new defs.UpdatePassWordReqModel();
  input.OldPassWord = Aes.Encrypt(rawPassword.value as string);
  input.PassWord = Aes.Encrypt(newPassword.value as string);
  await API.accountApi.putPasswordUpdate.request({
    bodyParams: input,
  });
  Toast.success("修改成功");
  tokens.redirectLogin();
};
</script>
<style lang="scss" scoped>
.myout {
  padding: 0 14px;
}

:deep(.van-cell-group--inset) {
  margin: 0;
  margin-top: 14px;
  border-radius: 4px 4px 4px 4px;
}

.van-cell.van-field {
  display: flex;
  align-items: center;
  height: 48px;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #4d545f;
}

:deep(.van-cell.van-field) {
  margin-right: 0 !important;
}

.submit {
  margin: 0 auto;
  margin-top: 16px;
  width: 279px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  text-align: center;
  line-height: 36px;
  opacity: 1;
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
</style>
