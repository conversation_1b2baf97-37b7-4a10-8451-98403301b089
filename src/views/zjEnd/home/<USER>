<template>
  <div class="reminder">
    <!--顶部导航 start -->
    <NavBar title="提示" />
    <!--顶部导航 end -->
    <van-list
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <van-cell class="li-cell" v-for="item in messageList" :key="item.Id">
        <template #title>
          <div
            style="display: flex; align-items: center; word-break: break-all"
            @click="reminder(item)"
          >
            <div
              :style="[
                item.Status === 1 || item.MessageType !== 1
                  ? 'backgroundColor:#8B96A6'
                  : '',
              ]"
              class="dot"
            ></div>
            <div class="custom-title">{{ item.Message }}</div>
          </div>
        </template></van-cell
      >
    </van-list>

    <van-popup v-model:show="showBottom" round position="bottom">
      <div class="popup-title">超时原因</div>
      <van-radio-group
        v-model="checked"
        :icon-size="14"
        checked-color="#650faa"
        shape="dot"
      >
        <van-radio name="标本采集和送检延迟">标本采集和送检延迟</van-radio>
        <van-radio name="病理工作量过大">病理工作量过大</van-radio>
        <van-radio name="技术操作复杂">技术操作复杂</van-radio>
        <van-radio name="多学科会诊和讨论">多学科会诊和讨论</van-radio>
        <van-radio name="数据分析和诊断确认">数据分析和诊断确认</van-radio>
        <van-radio name="其他原因">其他原因</van-radio>
      </van-radio-group>
      <div class="phoneClass" v-if="checked === '其他原因'">
        <van-field
          v-model="message"
          rows="2"
          type="textarea"
          maxlength="100"
          autosize
          :autofocus="true"
          placeholder="请输入"
        />
      </div>
      <div>
        <!-- 占位 -->
        <div class="occupied"></div>
        <div class="case-bottom">
          <!-- 插槽 -->
          <div class="case-bottom-container">
            <div class="case-diagnose" @click="cancel">取消</div>

            <div class="case-center" @click="submit">提交</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import NavBar from "@/components/navBar/Index.vue";
import { RcpNotify } from "@/services/baseClass";
import { Toast } from "vant";
const loading = ref(false);
const finished = ref(false);
const showBottom = ref(false);
const checked = ref("标本采集和送检延迟");
const message = ref("");
const count = ref(0);
const onLoad = async () => {
  if (count.value > 0) {
    params.PageIndex++;
    await getMessageRemindAsync();
  } else {
    await getMessageRemindAsync();
  }
  // 加载状态结束
  loading.value = false;

  // 数据全部加载完成
  if (messageList.value.length >= count.value) {
    finished.value = true;
  }
};

const messageList = ref<RcpNotify[]>([]);
const params = reactive({
  PageSize: 10,
  PageIndex: 1,
});
const getMessageRemindAsync = async () => {
  const res = await API.frontPageApi.getNotify.request({ params });
  if (res) {
    count.value = res.Count as number;
    if (params.PageIndex === 1) {
      messageList.value = res.Data as RcpNotify[];
    } else {
      messageList.value = messageList.value.concat(res.Data as RcpNotify[]);
    }
  }
};
const details = ref<RcpNotify>();
const reminder = (val: RcpNotify) => {
  if (val.Status !== 1 && val.MessageType === 1) {
    details.value = val;
    showBottom.value = true;
  }
};

const submit = async () => {
  if (checked.value === "其他原因" && message.value === "") {
    Toast("请输入超时原因");
    return;
  }
  await API.caseTimeoutMessageApi.postPost.request({
    bodyParams: {
      Id: details.value?.CaseId as any,
      Str: checked.value === "其他原因" ? message.value : checked.value,
    },
  });
  await API.frontPageApi.postNotifyStatus.request({
    bodyParams: {
      Id: details.value?.Id as any,
    },
  });
  messageList.value = [];
  params.PageIndex = 1;
  loading.value = false;
  finished.value = false;
  cancel();
  await getMessageRemindAsync();
};
const cancel = () => {
  checked.value = "标本采集和送检延迟";
  message.value = "";
  details.value = undefined;
  showBottom.value = false;
};
</script>

<style lang="scss" scoped>
.reminder {
  padding: 14px 14px;
}
.dot {
  min-width: 8px;
  height: 8px;
  background: #ff0707;
  border-radius: 50%;
  margin-right: 8px;
}
.custom-title {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #636363;
  line-height: 19px;
}
:deep(.van-cell.li-cell) {
  padding-top: 16px;
  padding-bottom: 16px;
}
.popup-title {
  margin-top: 6px;
  margin-left: 14px;
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: bold;
  color: #343840;
}
:deep(.van-radio-group) {
  margin-top: 15px;
  margin-left: 14px;
  > .van-radio {
    margin-bottom: 20px;
  }
  > .van-radio:last-child {
    margin-bottom: 5px;
  }
}
:deep(.van-radio__label) {
  color: #4d545f;
}
.phoneClass {
  margin: 0 14px;
  // padding: 5px 5px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border: 1px solid #e6ecfb;
}
.case-bottom {
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 88px;
  background: #ffffff;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
  opacity: 1;
}
.case-bottom-container {
  padding: 13px 14px 0 14px;
  display: flex;
}

.case-diagnose {
  margin-right: 16px;
  width: 100px;
  height: 32px;
  background: #f8f8f8;
  color: #494949;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}

.case-center {
  flex: 1;
  height: 32px;
  background: #650faa;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  color: #fff;
  text-align: center;
  line-height: 32px;
  font-size: 14px;
}
:deep(.van-list) {
  border-radius: 4px;
  overflow: hidden;
}
</style>
