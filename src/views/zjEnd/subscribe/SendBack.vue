<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="退回" />

    <!--顶部导航 end -->
    <div class="feedBack">
      <div class="feedBack-title">退回站点</div>
      <div class="feedBack-list">
        <van-radio-group
          v-model="checked"
          :icon-size="14"
          checked-color="#650faa"
        >
          <van-radio
            v-for="item in reasonList"
            :key="item.Id"
            :name="item.Content"
            @click="changeRadio(item)"
            >{{ item.Content }}</van-radio
          >
        </van-radio-group>
      </div>
    </div>
    <div v-if="selectId === 2008079" class="feedBack">
      <div class="feedBack-title">退回理由</div>
      <van-cell-group inset>
        <van-field
          v-model="message"
          rows="5"
          label=""
          type="textarea"
          maxlength="100"
          show-word-limit
          autosize
          :autofocus="true"
          placeholder="请输入退回理由"
        />
      </van-cell-group>
    </div>
    <div class="feedBack-btn" @click="feedBackClick">提交</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Toast } from "vant";
import { Ref } from "vue";
const router = useRouter();
const route = useRoute();
const message = ref("");
const checked = ref("");
const { caseId, status, category, siteId } = route.query;
//当前选择id
const selectId = ref();
const changeRadio = (val: defs.ReasonForReturnDto) => {
  selectId.value = val.Id;
};
//提交成功
const feedBackClick = async () => {
  if (status === "7" && category === "6") {
    if (!message.value) return Toast.fail("请输入退回理由");
  } else {
    if (!checked.value) return Toast.fail("请选择退回类型");
    if (selectId.value === 2008079 && !message.value) {
      return Toast.fail("请输入退回理由");
    }
  }

  if (siteId) {
    await API.userCommonApi.putReturnbyexpert.request({
      params: {
        Id: Number(siteId),
      },
    });
  }

  await API.distributorExpertApi.putReturnSubmit.request({
    bodyParams: {
      Id: Number(caseId),
      IsFrozen: status === "7" && category === "6" ? true : false,
      Remark: message.value,
      ReturnType: checked.value,
    },
  });
  await API.expertShortMessageApi.putReturnCase.request({
    bodyParams: {
      Id: Number(caseId),
    },
  });

  router.go(-2);
  Toast.success("提交成功");
};
const reasonList: Ref<defs.ReasonForReturnDto[]> = ref([]);
//获取退回原因列表
const getReasonList = async () => {
  const res = await API.distributorExpertApi.getReasonReturnList.request({
    params: {
      PageIndex: 1,
      PageSize: 50,
    },
  });
  //把退回原因放到最后
  const reason = res.Data?.find((item: any) => item.Id === 2008079);
  if (reason) {
    res.Data?.splice(res.Data?.indexOf(reason), 1);
    res.Data?.push(reason);
  }
  reasonList.value = res.Data as defs.ReasonForReturnDto[];
};
onMounted(() => {
  getReasonList();
});
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 11px 16px 14px;
  // height: 172px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.feedBack-list {
  padding: 10px 18px;
}

.phoneClass {
  margin: 0 14px;
  padding: 5px 0;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
  border-bottom: 1px solid #e7edfb;
}

.van-cell {
  padding: 0 0 10px 0;
}

:deep(.van-radio) {
  margin-bottom: 10px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
}

.van-field__body {
  height: 100%;
}

.feedBack-btn {
  margin: 0 49px 0 46px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
