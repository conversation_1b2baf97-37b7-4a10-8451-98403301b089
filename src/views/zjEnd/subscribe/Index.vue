<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="冰冻预约" />
    <!--顶部导航 end -->

    <!-- 搜索 start -->
    <CaseSearch
      @onSearchHandle="executeSearch"
      placeholder="请搜索预约编号/患者姓名"
      :hospitalList="hospitalList"
      @hospitalListClick="hospitalListClick"
      :category="6"
      :inquireType="2"
    />
    <!-- 搜索 end -->
    <van-list
      v-model:loading="loading"
      :finished="finished"
      :immediate-check="false"
      @load="onLoadNext"
    >
      <!--列表 start -->
      <div class="container">
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="case-card-info"
        >
          <div class="case-card-title">预约编号 {{ item.Number }}</div>
          <div class="case-bottom">
            <div class="case-left">
              <div class="case-card-name">
                {{ item.Name ? item.Name + " |" : "" }}
                {{ item.SexName ? item.SexName + " |" : "" }} {{ item.Age }}
              </div>
              <div class="case-card-detail">
                <div class="case-card-message">
                  {{ item.CaseTypeName ? item.CaseTypeName + " |" : "" }}
                  {{ SampleLocationName(item.SampleLocationName as string) }} |
                  {{ item.Inspection }}
                </div>
                <div class="case-card-time">{{ item.Time }}</div>
              </div>
            </div>
            <div
              class="case-review-btn"
              @click="gotoInfo(item, item.Status as number, 6)"
            >
              查看
            </div>
          </div>
          <div
            class="corner-mark"
            :style="[
              item.Status === 10 ? 'background: #FF6767' : '',
              item.Status === 6 ? 'background: #BEB833' : '',
            ]"
          >
            {{ item.StatusName }}
          </div>
        </div>
      </div>

      <!--列表 end -->
    </van-list>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseSearch from "@/components/case-list/CaseSearch.vue";
import { SampleLocationName } from "@/utils/utils";
import { Ref } from "vue";
const router = useRouter();

/**
 * 搜索
 */
const executeSearch = (value: any) => {
  dataList.value = [];
  query.EndTime = "";
  query.StartingTime = "";
  query.AdvancedSearch = value.AdvancedSearch;
  if (value.EndTime) {
    query.EndTime = value.EndTime;
  }
  if (value.StartingTime) {
    query.StartingTime = value.StartingTime;
  }
  query.PageSize = 10;
  query.PageIndex = 1;
  GetSubscribeList();
};

/**
 * 列表
 */
const dataList: Ref<Array<defs.DistributorFrozenDto> | undefined> = ref();
const query: API.expertCaseManagementApi.getFrozenList.Params = reactive({
  PageIndex: 1,
  PageSize: 10,
});
const GetSubscribeList = async () => {
  const res = await API.expertCaseManagementApi.getFrozenList.request({
    params: query,
  });
  total.value = res.Count || 0;
  dataList.value = dataList.value?.concat(res.Data || []) || res.Data;
};
//获取医院
const params = reactive({
  PageIndex: 1,
  PageSize: 50,
});
const hospitalList: Ref<Array<defs.SiteDropDto>> = ref([]);
const GetHospital = async () => {
  const res = await API.expertFunctionApi.getSite.request({
    params,
  });
  if (res.Data && res.Data.length > 0) {
    res.Data.forEach((item: any) => {
      item.checked = false;
    });
    hospitalList.value = res.Data;
  }
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};
//下拉加载
const finished = ref(false);
const loading = ref(false);
const refreshing = ref(false);
const total = ref(0);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        dataList.value = [];
        refreshing.value = false;
      }
      query.PageIndex++;
      await GetSubscribeList();

      loading.value = false;

      if (dataList.value && dataList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};

//查看
const gotoInfo = (caseInfo: defs.CaseDto, status: number, value: number) => {
  router.push({
    path: "/details",
    query: {
      id: caseInfo.Id && caseInfo.Id.toString(),
      status: status,
      category: value,
    },
  });
};
onMounted(() => {
  GetSubscribeList();
  GetHospital();
});
</script>
<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  margin: 0 14px 0 14px;
}

.case-card-info {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 10px 12px;
  margin-bottom: 12px;
  // height: 96px;
  background-color: #fff;
  border-radius: 4px;
}

.case-card-title {
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.case-card-name {
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 400;
  line-height: 14px;
  color: #4d545f;
}

.case-card-message {
  margin: 5px 0;
}

.case-card-content {
  margin-top: 13px;
  align-items: flex-end;
}

.case-bottom {
  margin-top: 6px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.case-left {
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.case-review-btn {
  width: 56px;
  height: 28px;
  background: #f0e7f7;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 28px;
  font-size: 12px;
  font-weight: 400;
  color: #650faa;
}

.corner-mark {
  position: absolute;
  padding: 0 6px;
  top: 0;
  right: 0;
  height: 24px;
  line-height: 24px;
  background: #df67bb;
  border-radius: 0px 4px 0px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}
</style>
