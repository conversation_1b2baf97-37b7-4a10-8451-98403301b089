<template>
  <div class="statistics">
    <!--顶部导航 start -->
    <NavBar title="数据统计" />

    <!--顶部导航 end -->
    <!-- 搜索 start -->
    <div class="caseSearchClass">
      <CaseSearch
        @onSearchHandle="executeSearch"
        placeholder="请搜索原病理号/患者姓名"
        :hospitalList="hospitalList"
        @hospital-list-click="hospitalListClick"
      >
        <template #search>
          <!-- 实时数据 start -->
          <div class="navBarClass">
            <GlData
              :islink="false"
              :homeData="homeData"
              @timeClick="timeClick"
            />
          </div>
          <!-- 实时数据 end -->
        </template>
      </CaseSearch>
    </div>
    <!-- 搜索 end -->

    <van-list
      v-model:loading="loading"
      :finished="finished"
      :immediate-check="false"
      @load="onLoadNext"
    >
      <!--列表 start -->
      <listCard :dataList="statisticsList"></listCard>
      <!--列表 end -->
    </van-list>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseSearch from "@/components/case-list/CaseSearch.vue";
import listCard from "@/views/zjEnd/regulates/components/listCard.vue";
import GlData from "@/views/zjEnd/home/<USER>/GlData.vue";
import { Ref } from "vue";
import { GetTimeRange } from "@/utils/time";
import useStore from "@/store";
//时间选择
const timeList = ref<string[]>([]);
/**
 * 搜索
 */
const executeSearch = (value: any) => {
  console.log(value);

  statisticsList.value = [];
  queryStatistics.EndTime = timeList.value[1];
  queryStatistics.StartingTime = timeList.value[0];
  queryStatistics.AdvancedSearch = value.AdvancedSearch;
  if (value.EndTime) {
    queryStatistics.EndTime = value.EndTime;
  }
  if (value.StartingTime) {
    queryStatistics.StartingTime = value.StartingTime;
  }
  queryStatistics.PageSize = 10;
  queryStatistics.PageIndex = 1;
  getStatisticsAsync();
};
const { selectColor, zjsection } = useStore();
/**
 * 列表
 */
//获取医院
const params = reactive({
  PageIndex: 1,
  PageSize: 50,
});
const hospitalList: Ref<Array<defs.SiteDropDto>> = ref([]);
const GetHospital = async () => {
  const res = await API.expertFunctionApi.getSite.request({
    params,
  });
  if (res.Data && res.Data.length > 0) {
    res.Data.forEach((item: any) => {
      item.checked = false;
    });
    hospitalList.value = res.Data;
  }
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};
//获取列表数据
const queryStatistics: API.expertCaseManagementApi.getStatisticsList.Params =
  reactive({
    PageIndex: 1,
    PageSize: 10,
  });
const statisticsList: Ref<Array<defs.CaseDto> | undefined> = ref();
const getStatisticsAsync = async () => {
  const res = await API.expertCaseManagementApi.getStatisticsList.request({
    params: queryStatistics,
  });
  total.value = res.Count || 0;
  if (queryStatistics.PageIndex == 1) {
    statisticsList.value = res.Data || [];
  } else {
    statisticsList.value =
      statisticsList.value?.concat(res.Data || []) || res.Data;
  }
};

//下拉加载
const finished = ref(false);
const loading = ref(false);
const refreshing = ref(false);
const total = ref(0);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        statisticsList.value = [];
        refreshing.value = false;
      }
      queryStatistics.PageIndex++;
      await getStatisticsAsync();

      loading.value = false;

      if (statisticsList.value && statisticsList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
//获取首页数据大版
const homeData: Ref<defs.ExpertHomeCountDto | undefined> = ref();

const getHomeDataAsync = async (time: any) => {
  const res = await API.expertAppStatisticsApi.getExpertHomeCount.request({
    params: {
      StartingTime: time[0],
      EndTime: time[1],
    },
  });
  homeData.value = res.Data;
};

const timeClick = async (time: any) => {
  statisticsList.value = [];
  getHomeDataAsync(time);
  queryStatistics.StartingTime = time[0];
  queryStatistics.EndTime = time[1];
  timeList.value = time;
  getStatisticsAsync();
};
onMounted(() => {
  const time = GetTimeRange("今日");
  timeList.value = time;
  queryStatistics.StartingTime = time[0];
  queryStatistics.EndTime = time[1];
  GetHospital();
  getHomeDataAsync(time);
  getStatisticsAsync();
  zjsection.clearSection();
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #f3f6fd;
}

:deep(.home-data) {
  padding: 17px 14px 0 14px !important;
}
</style>
