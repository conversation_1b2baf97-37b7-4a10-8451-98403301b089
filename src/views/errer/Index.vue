<template>
  <div class="wrap">
    <h1>404</h1>
    <p>哎呀，您要查找的页面不存在。</p>
    <div>
      <van-button color="#ffa000" style="width: 300px" round @click="goHome">
        返 回
      </van-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter();
const goHome = () => {
  if (window.history.state.back) {
    history.back();
  } else {
    // 没有上一页则返回到首页
    router.replace({ path: "/" });
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  text-align: center;
  background-color: #ffc107;
  padding: 40px 0;
  min-height: calc(100vh);
  box-sizing: border-box;
  &::before {
    content: "";
    position: absolute;
    width: inherit;
    top: 0;
    bottom: 0;
    background-color: inherit;
    border: inherit;
  }
  h1 {
    font-size: 8em;
    color: #fff;
    margin-bottom: 0;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  }
  p {
    color: #000;
    margin: 50px 0;
    font-size: 1.55rem;
    font-family: cursive;
    font-weight: 600;
  }
}
</style>
