<template>
  <div class="home-tabar">
    <div class="home-tabar-top" @click="goMyout">
      <div>
        <van-image
          class="home-image"
          fit="cover"
          :src="userInfo.HeadPic || DefaultAvatar"
        >
          <template v-slot:error><van-image :src="DefaultAvatar" /> </template
        ></van-image>
      </div>
      <div class="home-name">
        <div>
          您好，<span class="home-names">{{ userInfo.RealName }}</span>
        </div>
        <div>欢迎登录，请开始一天的工作吧！</div>
      </div>
    </div>

    <van-grid class="home-grid" :border="false" :column-num="3">
      <van-grid-item @click="goHome">
        <van-image class="home-grid-image" :src="Pathology" />
        <!-- <iconpark-icon class="home-grid-image" name="zu672x"></iconpark-icon> -->
        <div class="home-grid-name">分诊管理</div>
      </van-grid-item>
      <van-grid-item @click="router.push('/FreezeReservation')">
        <van-image class="home-grid-image" :src="Subscribe" />
        <!-- <iconpark-icon class="home-grid-image" name="zu682x"></iconpark-icon> -->
        <div class="home-grid-name">冰冻预约</div>
      </van-grid-item>
      <van-grid-item @click="router.push('/statistics')">
        <van-image class="home-grid-image" :src="Regulates" />
        <!-- <iconpark-icon class="home-grid-image" name="zu692x"></iconpark-icon> -->
        <div class="home-grid-name">数据统计</div>
      </van-grid-item>
    </van-grid>
  </div>
</template>
<script lang="ts" setup>
import { LocalStorageName, setLocalStorage } from "@/utils/storage";
import useStore from "@/store";
import Pathology from "@/assets/images/pathology.png";
import Regulates from "@/assets/images/regulates.png";
import Subscribe from "@/assets/images/subscribe.png";
import DefaultAvatar from "@/assets/images/default-avatar.png";
const router = useRouter();
const goMyout = () => {
  router.push("/myout");
};
const userInfo: any = ref({});
onMounted(async () => {
  const res = await API.accountApi.getInfo.request({});
  userInfo.value = res.Data;
  setLocalStorage(LocalStorageName.user, [], res.Data);
});
const goHome = () => {
  router.push("/triageAdmin");
};
</script>
<style lang="scss" scoped>
.home-tabar {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 183px;
  padding: 19px 6px 10px 6px;
  background-color: #fff;
  box-shadow: 0px 0px 4px 1px rgba(0, 0, 0, 0.1);
  border-radius: 0px 0px 12px 12px;
}

.home-tabar-top {
  padding: 0 14px;
  display: flex;
  align-items: center;
}

.home-image {
  width: 47px;
  height: 47px;
  background: #e3e5e8;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  overflow: hidden;
}

.home-name {
  margin-left: 7px;
  font-size: 14px;

  font-weight: 400;
  color: #8b96a6;
  line-height: 20px;

  .home-names {
    font-size: 18px;
    font-family: PingFang SC-Bold, PingFang SC;
    font-weight: bold;
    color: #4d545f;
  }
}

// .home-grid {
//   margin-top: 20px;
// }
.home-grid-image {
  width: 44px;
  height: 44px;
}

.home-grid-name {
  margin-top: 6px;
  font-size: 14px;

  font-weight: 400;
  color: #4d545f;
}
</style>
