<template>
  <div class="home-data">
    <div v-if="props.islink" class="home-data-title">实时数据</div>
    <div class="home-data-center">
      <div style="display: flex">
        <div :class="[
          'cell-button1',
          today && props.islink ? 'cell-button' : '',
          today && props.islink === false ? 'cell-button2' : '',
        ]" @click="cellClick('分诊数据')">
          分诊数据
        </div>
        <div :class="[
          'cell-button1',
          timeRange && props.islink ? 'cell-button' : '',
          timeRange && props.islink === false ? 'cell-button2' : '',
        ]" @click="cellClick('预约数据')">
          预约数据
        </div>
      </div>
      <div v-if="props.islink" class="home-data-center-right" @click="goToStatistics">
        <div>查看更多</div>
        <van-icon name="arrow" />
      </div>
    </div>
    <div v-if="today" class="home-data-bottom">
      <div class="home-data-bottom-top">
        <div style="flex: 1" @click="dataSkip(1)">
          <div>待分诊病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETOBETRIAGED }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkip(2)">
          <div>已分诊病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETRIAGED }}
          </div>
        </div>
      </div>
      <div class="home-data-bottom-top home-data-bottom-top1">
        <div style="flex: 1" @click="dataSkip(3)">
          <div>被退回病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTERETURNED }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkip(4)">
          <div>待撤回病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETOBEWITHDRAWN }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="timeRange" class="home-data-bottom">
      <div class="home-data-bottom-top">
        <div style="flex: 1" @click="dataSkips(1)">
          <div>待分诊预约</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETOBETRIAGEDFROZEN }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkips(2)">
          <div>已预约病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETRIAGEDFROZEN }}
          </div>
        </div>
      </div>
      <div class="home-data-bottom-top home-data-bottom-top1">
        <div style="flex: 1" @click="dataSkips(3)">
          <div>待撤回病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTETOBEWITHDRAWNFROZEN }}
          </div>
        </div>
        <div style="flex: 1" @click="dataSkips(4)">
          <div>已退回病例</div>
          <div class="home-data-bottom-value">
            {{ props.homeData?.DISTRIBUTERETURNEDFROZEN }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from "vue";
import useStore from '@/store'
const router = useRouter();
const props = defineProps({
  islink: {
    type: Boolean,
    default: true,
  },
  homeData: {
    type: Object as PropType<any>,
    default: () => { },
  },
});
const { selectColor } = useStore()
const today = ref(true);
const timeRange = ref(false);
const cellClick = (value: string) => {
  if (value === '分诊数据') {
    today.value = true;
    timeRange.value = false;
  } else {
    today.value = false;
    timeRange.value = true;
  }

};
const goToStatistics = () => {
  if (today.value) {
    router.push("/triageAdmin");
  } else {
    router.push("/FreezeReservation");

  }


};

//跳转
const dataSkip = (value: number) => {
  selectColor.selectNavFn(value)
  router.push({
    path: "/triageAdmin",
  });

};
const dataSkips = (value: number) => {
  selectColor.selectNav1Fn(value)
  router.push({
    path: "/FreezeReservation",
  });
};
</script>
<style lang="scss" scoped>
.home-data {
  padding: 0 20px;
  background-color: #f4f7fd;
  z-index: 99;
}

.home-data-title {
  margin-top: 18px;
  margin-bottom: 8px;
  font-size: 18px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
  line-height: 21px;
}

.home-data-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.home-data-center-right {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.cell-button1 {
  margin-right: 8px;
  padding: 0 17px;
  height: 28px;
  background: #fff;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  border: 1px solid #e6ecfb;
  text-align: center;
  line-height: 28px;
  font-size: 14px;
  color: #8b96a6;
}

.cell-button {
  background: #650faa;
  color: #fff;
  border: 1px solid #650faa;
}

.cell-button2 {
  color: #650faa;
  border: 1px solid #650faa;
}

.van-cell {
  padding: 0;
  background-color: #f4f7fd;
}

.home-data-bottom {
  box-sizing: border-box;
  margin-top: 8px;
  padding: 24px 17px;
  height: 169px;
  background: #ffffff;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
  color: #8b96a6;
}

.home-data-bottom-top {
  display: flex;
}

.home-data-bottom-top1 {
  margin-top: 24px;
}

.home-data-bottom-value {
  margin-top: 5px;
  font-size: 24px;
  font-weight: bold;
  color: #4d545f;
}
</style>
