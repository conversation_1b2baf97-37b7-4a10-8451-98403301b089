<template>
  <div>
    <!--顶部导航 start -->
    <!-- <NavBar title="首页" :leftAarrow="false"></NavBar> -->
    <!--顶部导航 end -->
    <div class="home-title van-safe-area-top">
      {{ userInfo.JinFeng ? userInfo.RcpName : RcpWeb.HomeTitle }}
    </div>

    <!-- 用户名 start -->
    <GlTabbar />
    <!-- 用户名 end -->
    <div class="notice">
      <van-notice-bar
        left-icon="volume-o"
        background="#fff"
        :scrollable="false"
        color="#8B96A6"
      >
        <van-swipe
          vertical
          class="notice-swipe"
          :autoplay="5000"
          :show-indicators="false"
        >
          <van-swipe-item
            v-for="item in messageList"
            :key="item.Id"
            @click="informClick"
            >{{ item.Message }},<span class="dispose"
              >立即处理</span
            ></van-swipe-item
          >
        </van-swipe>
        <template #left-icon>
          <div class="infromClass">
            <iconpark-icon
              class="iconClass"
              name="weibiaoti-1huaban1fuben2"
            ></iconpark-icon>
            <div>&nbsp; 通知</div>
          </div>
        </template>
      </van-notice-bar>
    </div>
    <!-- 实时数据 start -->
    <GlData :homeData="homeData" />
    <!-- 实时数据 end -->

    <!-- 联系我们 start -->
    <Contactus />
    <!-- 联系我们 end -->

    <VersionsShow />
  </div>
</template>
<script lang="ts" setup>
import GlTabbar from "./components/GlTabbar.vue";
import GlData from "./components/GlData.vue";
import Contactus from "@/components/contactUs/Contactus.vue";
import { Ref } from "vue";
import RcpWeb from "RcpWeb"; //项目配置
import { RcpNotify } from "@/services/baseClass";
import useStore from "@/store";
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
const { selectColor } = useStore();
const router = useRouter();
const userInfo = getLocalStorage(LocalStorageName.user);
const informClick = () => {
  selectColor.selectNavFn(1);
  router.push("/triageAdmin");
};

//消息提醒
const messageList: Ref<RcpNotify[]> = ref() as Ref<RcpNotify[]>;

const getMessageRemindAsync = async () => {
  const params = {
    PageSize: 20,
    PageIndex: 1,
  };
  const res = await API.frontPageApi.getNotify.request({ params });
  if (res) {
    res.Data?.forEach((item) => {
      // 切割字符串后面加...
      item.Message =
        item.Message && item.Message.length > 12
          ? item.Message?.slice(0, 11) + "..."
          : item.Message;
    });
  }

  messageList.value = res.Data as RcpNotify[];
};

//获取首页数据大版
const homeData: Ref<any | undefined> = ref();

const getHomeDataAsync = async () => {
  const res = await API.frontPageApi.getCount.request({
    params: {
      CaseType: 3,
    },
  });
  homeData.value = res.Data;
};

onMounted(() => {
  getMessageRemindAsync();
  getHomeDataAsync();
});
</script>
<style lang="scss" scoped>
.home-title {
  height: 50px;
  font-size: 22px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #000000;
  background: #ffffff;
  padding-left: 21px;
  line-height: 50px;
}

.notice-swipe {
  height: 40px;
  line-height: 40px;
}

.notice {
  margin: 16px 20px;
  border-radius: 12px 12px 12px 12px;
}

.van-notice-bar {
  border-radius: 12px;
}

.dispose {
  color: #650faa;
  font-weight: bold;
}

.infromClass {
  display: flex;
  justify-content: center;
  margin-right: 5px;
  width: 69px;
  height: 26px;
  background: #f3f6fd;
  border-radius: 12px 12px 12px 12px;
  opacity: 1;
  line-height: 26px;
  vertical-align: middle;
}

:deep(.van-notice-bar) {
  padding: 0 10px;
}
</style>
