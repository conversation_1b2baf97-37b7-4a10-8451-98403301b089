<template>
  <div class="container">
    <!--顶部导航 start -->
    <NavBar title="病例信息" />
    <!--顶部导航 end -->
    <!--引入头部病人信息组件-->
    <FzPatientInfo :dataDetails="caseInfo" :shareUrl="shareUrl" />
    <!--切换按钮-->
    <FzCaseInfoTab :activeName="activeTab" @nav-click="getChild" />
    <!--详情展示-->
    <!--病例信息-->
    <FzCaseInfoContent ref="infoComponent" :data="caseInfo" />
    <!--切片信息-->
    <CaseSliceContent v-if="category !== '6'" ref="sliceinfoComponent" />
    <!--附件信息-->
    <FzCaseFileContents ref="fileinfoComponent" :fileList="fileList" />
    <CaseOpinion
      ref="otherinfoComponent"
      :list="list"
      v-if="[1, 2, 3, 4].includes(selectColor.selectNav) && tokens.token"
    />
    <!-- 占位 -->
    <div class="occupied"></div>
    <!--详情底部按钮-->
    <FzCaseInfoBottomBtn :pathologyNumber="pathologyNumber" />
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseSliceContent from "@/components/case-detail/CaseSliceContent.vue";
import CaseOpinion from "@/components/case-detail/CaseOpinion.vue";
import { SliceDto } from "@/api/zjEnd/regulates.types";
import leaveWord from "@/components/case-detail/leaveWord.vue";
import MedicalHistory from "@/components/case-detail/MedicalHistory.vue";
import DoctorAdvice from "@/components/case-detail/DoctorAdvice.vue";
import { Ref } from "vue";
import useStore from "@/store";
import { Toast } from "vant";
const { zjsection, saveCase, tokens, selectColor } = useStore();

const route = useRoute();
const router = useRouter();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
//navbar名称
//分享报告url
const shareUrl = ref("");
const { id, category, status } = route.query;
const list = reactive([
  {
    title: "留言",
    id: 2,
    isActive: true,
    component: markRaw(leaveWord),
  },
  {
    title: "病史",
    id: 3,
    isActive: false,
    component: markRaw(MedicalHistory),
  },
  {
    title: "医嘱",
    id: 4,
    isActive: false,
    component: markRaw(DoctorAdvice),
  },
]);
onMounted(async () => {
  if (id) {
    params.Id = Number(id);
    const res = await API.userCommonApi.getCasePendering.request({
      params: {
        Id: Number(id),
      },
    });
    caseInfo.value = res.Data;
    pathologyNumber.value = res.Data?.PathologyNumber;
    //获取报告单
    // await getReportTemplate(Number(id));

    //获取附件信息
    await getFileList(Number(id));
  }
});
//病例信息data
const caseInfo: Ref<any> = ref(undefined);
const pathologyNumber: Ref<string | undefined> = ref(undefined);
//切片信息data
const sectionList: Ref<SliceDto[]> = ref([]);
//--切换按钮--
const activeTab = ref("case");
const sliceinfoComponent: Ref<any> = ref(null);
const fileinfoComponent = ref(null);
const otherinfoComponent = ref(null);
const infoComponent = ref(null);
const getChild = (tab: string) => {
  let el = null;
  switch (tab) {
    case "case":
      el = (infoComponent.value as any).$el;
      break;
    case "slice":
      el = (sliceinfoComponent.value as any).$el;
      break;
    case "file":
      el = (fileinfoComponent.value as any).$el;
      break;
    default:
      return;
  }
  if (el) {
    el.scrollIntoView();
  }
  activeTab.value = tab;
};

//获取附件信息
const fileList: Ref<Array<defs.AnnexDto> | undefined> = ref();
const getFileList = async (id: number) => {
  const res = await API.userCommonApi.getAnnexList.request({
    params: {
      Id: id,
      PageIndex: 1,
      PageSize: 20,
    },
  });
  fileList.value = res.Data;
};

onMounted(async () => {
  params.Id = Number(id);
  await zjsection.getSectionAsync(params);
});
</script>

<style lang="scss" scoped>
.image-box {
  width: 500px;
  height: 500px;
}
</style>
