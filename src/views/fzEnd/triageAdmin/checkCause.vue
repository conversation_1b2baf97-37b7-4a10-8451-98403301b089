<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="查看原因" />
    <!--顶部导航 end -->
    <van-list
      v-if="type !== '2'"
      v-model:loading="loading"
      :finished="finished"
      :immediate-check="false"
      @load="onLoadNext"
    >
      <div class="operationLog">
        <div
          v-for="(item, index) in logList"
          :key="index"
          class="operationLogs"
        >
          <div>
            退回原因：{{ item.ReasonType
            }}{{ item.Remark ? "——" + item.Remark : "" }}
          </div>
          <div>操作人：{{ item.Name }}</div>
          <div>退回时间：{{ item.ApplyTime }}</div>
        </div>
      </div>
    </van-list>
    <div v-else class="operationLog">
      <div class="operationLogs">
        <div>撤回理由</div>
        <div class="operationLogs-text">{{ reasonList }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Ref } from "vue";
const route = useRoute();
const { id, type } = route.query;
const loading = ref(false);
const finished = ref(false);
const total = ref(0);
const searchRequest: Ref<API.allCommonApi.getLog.Params> = ref({
  PageIndex: 1,
  PageSize: 10,
  Id: Number(id),
});
const logList: Ref<defs.CaseReturnedDto[]> = ref([]);
//获取查看原因
const getLogList = async () => {
  const res = await API.userCommonApi.getReasonList.request({
    params: searchRequest.value,
  });
  total.value = res.Count as number;
  if (searchRequest.value.PageIndex === 1) {
    logList.value = res.Data as defs.CaseReturnedDto[];
  } else {
    if (res.Data && res.Data.length > 0) {
      res.Data.forEach((item: defs.CaseReturnedDto) => {
        logList.value?.push(item);
      });
    }
  }
};

const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (total.value > 0) {
        searchRequest.value.PageIndex++;
        await getLogList();
      }

      loading.value = false;

      if (logList.value && logList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
const reasonList: Ref<any> = ref();
//查看撤回理由
const getReason = async () => {
  const res = await API.triageFunctionApi.getWithdrawal.request({
    params: {
      Id: Number(id),
    },
  });
  reasonList.value = res.Data;
};
onMounted(() => {
  if (type === "2") {
    getReason();
  } else {
    getLogList();
  }
});
</script>
<style lang="scss" scoped>
.operationLog {
  box-sizing: border-box;
  margin: 0 11px 0 14px;
}

.operationLogs {
  margin-top: 12px;
  padding: 12px;
  background: #ffffff;
  border-radius: 4px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
  line-height: 14px;

  & > div {
    margin-bottom: 5px;
  }

  & > div:nth-child(1) {
    font-size: 14px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #343840;
    margin-bottom: 5px;
    line-height: 20px;
  }

  & > div:nth-child(3) {
    //溢出换行
    word-break: break-all;
  }
}

.operationLogs-text {
  box-sizing: border-box;
  padding: 10px;
  margin-top: 5px;
  height: 200px;
  border: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
