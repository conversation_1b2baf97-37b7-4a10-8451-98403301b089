<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="退回站点" />
    <!--顶部导航 end -->
    <div class="feedBack">
      <div class="feedBack-title">退回站点</div>
      <div class="feedBack-list">
        <van-radio-group
          v-model="checked"
          :icon-size="14"
          checked-color="#650faa"
        >
          <van-radio
            v-for="item in reasonList"
            :key="item.Id"
            :name="item.Content"
            @click="changeRadio(item)"
            >{{ item.Content }}</van-radio
          >
        </van-radio-group>
        <div class="phoneClass" v-if="selectId === 2008079">
          <van-field
            v-model="message"
            rows="4"
            label=""
            type="textarea"
            maxlength="100"
            autosize
            :autofocus="true"
            show-word-limit
            placeholder="请输入备注"
          />
        </div>
      </div>
    </div>
    <!-- <div class="feedBack">
      <div class="phoneClass">备注</div>
      <van-cell-group inset>
        <van-field
          v-model="message"
          rows="4"
          label=""
          type="textarea"
          maxlength="100"
          autosize
          :autofocus="true"
          show-word-limit
          placeholder="请输入备注"
        />
      </van-cell-group>
    </div> -->
    <div class="feedBack-btn" @click="feedBackClick">确认退回</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Toast } from "vant";
import { Ref } from "vue";
const route = useRoute();
const router = useRouter();
const { id, type } = route.query;
const message = ref("");
const checked = ref("");
//当前选择id
const selectId = ref();
const changeRadio = (val: defs.ReasonForReturnDto) => {
  selectId.value = val.Id;
};
//确认退回
const feedBackClick = async () => {
  if (!checked.value) return Toast.fail("请选择退回原因");
  if (selectId.value === 2008079 && !message.value)
    return Toast.fail("请输入备注");
  try {
    await API.distributorExpertApi.putReturnSubmit.request({
      bodyParams: {
        Id: Number(id),
        IsFrozen: type === "1" ? true : false,
        Remark: message.value,
        ReturnType: checked.value,
      },
    });
    Toast.success("退回成功");
    router.go(-2);
  } catch (error) {}
};
const reasonList: Ref<defs.ReasonForReturnDto[]> = ref([]);
//获取退回原因列表
const getReasonList = async () => {
  const res = await API.distributorExpertApi.getReasonReturnList.request({
    params: {
      PageIndex: 1,
      PageSize: 50,
    },
  });
  //把退回原因放到最后
  const reason = res.Data?.find((item: any) => item.Id === 2008079);
  if (reason) {
    res.Data?.splice(res.Data?.indexOf(reason), 1);
    res.Data?.push(reason);
  }
  reasonList.value = res.Data as defs.ReasonForReturnDto[];
};
onMounted(() => {
  getReasonList();
});
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 11px 16px 14px;
  // height: 172px;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.feedBack-list {
  padding: 10px 18px;
}

.phoneClass {
  // margin: 0 14px;
  padding: 5px 5px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
  border: 1px solid #e7edfb;
}

.van-cell {
  padding: 0 0 10px 0;
}

:deep(.van-radio) {
  margin-bottom: 10px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #4d545f;
}

.van-field__body {
  height: 100%;
}

.feedBack-btn {
  margin: 0 49px 0 46px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
