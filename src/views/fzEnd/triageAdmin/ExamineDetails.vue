<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="查看切片" />
    <!--顶部导航 end -->

    <!-- 查看切片 start -->
    <ImageViewerTop
      ref="imageViewer"
      v-if="zjsection.sectionList.length > 0"
      :data="getFillingList"
      @changeClicks="changeClicks"
      :imageEditorss="false"
      :disqualification="false"
    />
    <!-- 查看切片 end -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import ImageViewerTop from "@/components/image-viewer/ImageViewerTop.vue";
import useStore from "@/store";
import { Ref, inject } from "vue";

const { zjsection } = useStore();
const route = useRoute();
const { caseId, id, siteId } = route.query;
const imageViewer = ref();
const changeClicks = () => {
  changeClick();
};
const changeClick = () => {
  imageViewer.value?.refreshViewersClcik(
    getFillingList.value?.PathologyNumber,
    getFillingList.value?.CaseTypeCode === "Frozen" ? true : false
  );
};
//获取Filling数据
const getFillingList: Ref<any | undefined> = ref();
const getFilling = async () => {
  const res = await API.userCommonApi.getCasePendering.request({
    params: {
      Id: Number(caseId),
    },
  });
  getFillingList.value = res.Data;
};
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
onMounted(async () => {
  getFilling();
  params.Id = Number(caseId);
  await zjsection.getSectionAsync(params);
  changeClick();
});
</script>
<style scoped lang="scss">
.van-radio__icon--checked .van-icon {
  background-color: #650faa;
  border-color: #650faa;
}
</style>
