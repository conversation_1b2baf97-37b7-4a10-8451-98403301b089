<template>
  <div>
    <!--顶部导航 start -->
    <NavBar :title="title" />
    <!--顶部导航 end -->
    <van-overlay :show="showOverlay" z-index="20">
      <div
        style="
          position: fixed;
          top: 50%;
          left: 50%;
          z-index: 10;
          transform: translate(-50%, -50%);
        "
      >
        <van-loading size="24px" vertical color="#0094ff"
          >加载中...</van-loading
        >
      </div>
    </van-overlay>
    <!-- 搜索 start -->
    <CaseSearch
      :showtime="false"
      @onSearchHandle="executeSearch"
      :areaList="areaList"
      :inquireType="1"
      placeholder="请搜索专家姓名"
    />
    <!-- 搜索 end -->

    <div class="invitationReview">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :immediate-check="false"
        @load="onLoadNext"
      >
        <van-collapse
          v-for="(item, index) in expertList"
          :key="item.Id"
          class="collapseClass"
          v-model="activeName"
          accordion
        >
          <van-collapse-item :name="index">
            <template #title>
              <div class="invitationReview-left">
                <!-- <van-checkbox v-if="inviteType === '1'" v-model="item.checked" checked-color="#650FAA">
                                </van-checkbox> -->
                <van-radio-group v-model="checked" checked-color="#650FAA">
                  <van-radio :name="index"></van-radio>
                </van-radio-group>
                <van-image
                  class="vanImage"
                  :src="item.HeadPicUrl || DefaultAvatar"
                >
                  <template v-slot:error
                    ><van-image :src="DefaultAvatar" /> </template
                ></van-image>
                <div class="invitation">
                  <div>{{ item.ExpertName }}</div>
                  <div>
                    {{ item.ExpertleveName }} | {{ item.CityName }} |
                    {{ item.ExpertTypeName }}
                  </div>
                </div>
              </div>
              <div
                :class="[
                  'consultations',
                  Number(item.NumberOfSubmittedCases || 0) >=
                  Number(item.NumberOfCases || 0)
                    ? 'consultationss'
                    : '',
                ]"
              >
                会诊数量： {{ item.NumberOfSubmittedCases || 0 }}/{{
                  item.NumberOfCases || 0
                }}
              </div>
            </template>
            个人简介：{{ item.BriefIntroduction }}
          </van-collapse-item>
        </van-collapse>
      </van-list>
    </div>
    <BottomBtn
      rightName="确认分配"
      :btnShow="false"
      @rightBtn="reviewerRightBtn"
    />
    <!-- 底部按钮 end -->

    <!-- 确认复核弹窗 start -->
    <CaseOverlay
      v-model:show="reviewerShow"
      :overlayTitle="`是否将病例分配给
                                  “${name}”？`"
      @submit="submit"
    />
    <!-- 确认复核弹窗 end -->
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import CaseSearch from "@/components/case-list/CaseSearch.vue";
import BottomBtn from "@/components/bottomBtn/BottomBtn.vue";
import CaseOverlay from "@/components/case-overlay/CaseOverlay.vue";
import DefaultAvatar from "@/assets/images/default-avatar.png";
import { Ref } from "vue";
import { cloneDeep } from "lodash";
import useStore from "@/store";
import { Toast } from "vant";
const route = useRoute();
const router = useRouter();
const { selectColor } = useStore();
const { id, siteId, anew, batch, isFrozen } = route.query;
const activeName = ref(0);
const checked = ref();
const show = ref(false);
//确认复核弹窗
const reviewerShow = ref(false);
const name = ref("");
const showOverlay = ref(false);
//选中的值
const datailsList: Ref<any> = ref([]);
//获取专家列表
const params: API.allCommonApi.getExpertList.Params = reactive({
  PageIndex: 1,
  PageSize: 10,
  Id: Number(siteId),
});
const title = computed(() => {
  return anew === "1" ? "重新分配" : "分配";
});
const expertList: Ref<Array<any> | undefined> = ref([]);
const getExpertList = async () => {
  const res = await API.allCommonApi.getExpertList.request({
    params,
  });
  if (res.Data && res.Data.length > 0) {
    const data = cloneDeep(res.Data);
    data.forEach((item: any, index) => {
      item.checked = false;
      // index = index + 1
    });
    total.value = res.Count || 0;
    expertList.value = expertList.value?.concat(data || []) || data;
  }
  if (!anew && !batch) {
    const data = await API.allCommonApi.getExpertInfor.request({
      params: {
        Id: Number(id),
        IntentionalExpert: true,
      },
    });
    expertList.value?.map((item: any, index: any) => {
      if (item.Id === data.Data?.Id) {
        checked.value = index;
        item.checked = true;
        activeName.value = index;
      }
    });
  }
};

//邀请复核
const reviewerRightBtn = () => {
  datailsList.value =
    expertList.value &&
    expertList.value.filter((item: any, index: any) => index == checked.value);
  name.value = datailsList.value[0]?.ExpertName;
  console.log(datailsList.value);
  if (!datailsList.value[0]?.Id) {
    Toast.fail("请选择专家");
    return;
  }

  reviewerShow.value = true;
};
const submit = async () => {
  console.log(datailsList.value);
  const expert = datailsList.value[0];
  let sum = 0;
  try {
    const res = await API.allCommonApi.getExpertCasecount.request({
      params: {
        Id: expert.Id,
      },
    });
    sum = res.Data || 0;
    //batch === "1" 为批量分配
    if (
      expert.NumberOfCases === null ||
      expert.NumberOfCases === "0" ||
      (batch === "1" &&
        sum + selectColor.batchIdList.length >=
          Number(expert.NumberOfCases || 0)) ||
      (batch !== "1" && sum >= Number(expert.NumberOfCases || 0))
    ) {
      return Toast.fail(`${expert.ExpertName}专家会诊数量已满`);
    }

    showOverlay.value = true;

    if (batch === "1") {
      const idList = selectColor.batchIdList.map((item) => ({ Id: item }));

      await Promise.all([
        API.triageFunctionApi.putBulkAllocation.request({
          bodyParams: {
            ExpertId: expert.Id,
            ExpertName: expert.ExpertName,
            IsFrozen: false,
            Id: idList,
          },
        }),
        API.distributorShortMessageApi.putBulkAllocation.request({
          bodyParams: {
            Count: idList.length + "",
            ExpertId: expert.Id,
            Id: idList[0].Id,
          },
        }),
      ]);

      Toast.success("分配成功");
      selectColor.batchIdListFn([]);
      router.go(-1);
    } else if (anew === "1") {
      await API.universalApi.putRedistribute.request({
        bodyParams: {
          ExpertId: expert.Id,
          ExpertName: expert.ExpertName,
          Id: Number(id),
        },
      });
      router.go(-2);
    } else {
      const isFrozenMode = isFrozen === "1";
      const triageFunctionApiRequest = isFrozenMode
        ? API.triageFunctionApi.putDistribute
        : API.triageFunctionApi.putDistribute;

      await Promise.all([
        triageFunctionApiRequest.request({
          bodyParams: {
            ExpertId: expert.Id,
            ExpertName: expert.ExpertName,
            Id: Number(id),
            IsFrozen: isFrozenMode,
          },
        }),
        API.distributorShortMessageApi[
          isFrozenMode ? "putFrozenDistribution" : "putOrdinaryCaseAssignment"
        ].request({
          bodyParams: {
            ExpertId: expert.Id,
            Id: Number(id),
          },
        }),
      ]);

      Toast.success("分配成功");
      router.go(-2);
    }
  } catch (error) {
    // Handle errors here
  } finally {
    showOverlay.value = false;
  }
};

//定义areaList类型
interface AreaListType {
  province_list: {
    [key: number]: string;
  };
  city_list: {
    [key: number]: string;
  };
}
//获取省市数据
const areaList: AreaListType = reactive({
  province_list: {},
  city_list: {},
});
const getCityList = async () => {
  const res = await API.userCommonApi.getDropAdd.request({
    params: {
      code: "AREA",
    },
  });
  // const dataList:  = cloneDeep(res.Data?.AREA)
  res.Data?.AREA.map((item: { Code: number; Value: string; ChildList: [] }) => {
    //给areaList.province_list对象添加键值对
    areaList.province_list[item.Code] = item.Value;
    item.ChildList.map((item: { Code: number; Value: string }) => {
      //给areaList.city_list对象添加键值对
      areaList.city_list[item.Code] = item.Value;
    });
  });
};

/**
 * 加载更多
 */
const finished = ref(false);
const loading = ref(false);
const refreshing = ref(false);
const total = ref(0);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        expertList.value = [];
        refreshing.value = false;
      }
      if (total.value > 0) {
        params.PageIndex++;
        await getExpertList();
      }

      loading.value = false;

      if (expertList.value && expertList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
/**
 * 搜索
 */
const executeSearch = (value: any) => {
  expertList.value = [];
  params.AdvancedSearch = value.AdvancedSearch;
  params.PageSize = 10;
  params.PageIndex = 1;

  getExpertList();
};
onMounted(() => {
  getExpertList();
  getCityList();
});
</script>
<style lang="scss" scoped>
.invitationReview {
  margin: 0 14px 12px 14px;
}

.vanImage {
  margin-left: 15px;
  width: 37px;
  height: 37px;
  // border: 1px solid #e2e2e2;
}

.invitationReview-left {
  position: relative;
  display: flex;
  align-items: center;
  // margin-bottom: 10px;
}
.consultations {
  position: absolute;
  top: 15px;
  right: 14px;
  font-size: 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(101, 15, 170, 0.65);
  line-height: 12px;
}
.consultationss {
  color: #ff7272;
}
.invitation {
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #343840;
}

.invitation > div:nth-child(2) {
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.invitationReview-left[data-v-ee5c42d6] {
  display: flex;
  align-items: center;
  margin-bottom: -0.333333vw;
}

.collapseClass {
  margin-bottom: 12px;
  border-radius: 4px;
}

:deep(.van-cell.van-cell--clickable.van-collapse-item__title) {
  align-items: flex-end;
}

.specialistClass {
  display: flex;
  padding: 4px 12px;
  width: 100%;
  height: 32px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  overflow: auto;
}

.datailsListClass {
  margin-right: 9px;
}

.popup-title {
  margin: 6px 0 0 14px;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #343840;
}

.van-cell {
  padding: 5px;
}

.van-cell-group--inset {
  margin-top: 12px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  border: 1px solid #e6ecfb;
}

.popupBtn {
  margin: 41px 23px 20px 23px;
  height: 32px;
  background: #650faa;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  font-size: 14px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 32px;
}
</style>
