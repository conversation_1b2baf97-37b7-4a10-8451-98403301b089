<template>
  <div class="regulates">
    <!-- <van-sticky> -->
    <!--顶部导航 start -->
    <NavBar title="分诊管理"></NavBar>
    <!--顶部导航 end -->
    <!-- tabbar start -->
    <FzTabBars @tab-click="executeLoadData" />
    <!-- tabbar end -->
    <!-- </van-sticky> -->

    <!-- 搜索 start -->
    <FzSearch
      @onSearchHandle="executeSearch"
      placeholder="请搜索原病理号/患者姓名"
      :hospitalList="hospitalList"
      @hospital-list-click="hospitalListClick"
      :selectTimeShow="false"
    />
    <!-- 搜索 end -->

    <!--下拉刷新-->
    <van-pull-refresh
      v-model="isLoading"
      success-text="刷新成功"
      @refresh="onRefresh"
      style="min-height: 80vh"
    >
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :immediate-check="false"
        @load="onLoadNext"
      >
        <!--列表 start -->
        <listCard
          ref="listCards"
          :dataList="dataList"
          @cancelCollect="cancelCollect"
          :isShow="batchShow"
        ></listCard>
      </van-list>
      <!--列表 end -->
    </van-pull-refresh>
    <!-- 占位 -->
    <div
      v-if="selectColor.selectNav === 1 && dataList.length > 0"
      class="occupied"
    ></div>
    <!--详情底部按钮-->
    <div
      v-if="selectColor.selectNav === 1 && dataList.length > 0"
      class="case-bottom-container"
    >
      <div
        v-if="!batchShow"
        class="case-bottom-container-div"
        @click="batchShow = true"
      >
        批量分配
      </div>
      <div
        v-else
        class="case-bottom-container-div case-bottom-container-divs"
        @click="batchClick"
      >
        批量分配
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import FzTabBars from "@/components/TabBars/FzTabBars.vue";
import FzSearch from "@/components/fzSearch/index.vue";
import listCard from "./components/listCard.vue";
import { Ref } from "vue";
import { GetCaseListRequest } from "@/model/request/CaseApiRequest";
import { GetCaseListAsync } from "@/api/zjEnd/regulates";
import useStore from "@/store";
import { Toast } from "vant";
const { zjsection, selectColor } = useStore();
/**
 * 刷新
 */
const router = useRouter();
const finished = ref(false);
const isLoading = ref(false);
const listCards = ref();
const onRefresh = async () => {
  searchRequest.PageIndex = 1;
  total.value = 0;
  dataList.value = [];
  await getCaseList();
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);
};
/**
 * 搜索
 */
const executeSearch = (value: any) => {
  dataList.value = [];
  searchRequest.EndTime = "";
  searchRequest.StartingTime = "";
  searchRequest.Where = value.where;
  searchRequest.AdvancedSearch = value.AdvancedSearch;
  if (value.EndTime) {
    searchRequest.EndTime = value.EndTime;
  }
  if (value.StartingTime) {
    searchRequest.StartingTime = value.StartingTime;
  }
  searchRequest.PageSize = 10;
  searchRequest.PageIndex = 1;
  getCaseList();
};
//是否批量分配
const batchShow = ref(false);

//接口集合
const apiAddres: Ref<string> = ref("/api/distributor/triaged/list");
const getApiAddress = (): string => {
  const _idx = Number(selectColor.selectNav);
  switch (_idx) {
    case 1:
      apiAddres.value = "/api/distributor/triaged/list";
      break;
    case 2:
      apiAddres.value = "/api/distributor/triaged/triage/list";
      break;
    case 3:
      apiAddres.value = "/api/distributor/triaged/returned/list";
      break;
    case 4:
      apiAddres.value = "/api/distributor/triaged/withdraw/list";
      break;
  }
  return apiAddres.value;
};

const total = ref(0);
const currTotal = ref(0);
/**
 * 列表
 */
const dataList: Ref<defs.CaseDto[]> = ref([]);
const searchRequest: GetCaseListRequest = reactive({
  PageIndex: 1,
  PageSize: 10,
});
const getCaseList = async () => {
  const _apiUrl = getApiAddress();
  await GetCaseListAsync(searchRequest, _apiUrl as string)
    .then(async (res) => {
      total.value = res.Count;
      if (searchRequest.PageIndex === 1) {
        dataList.value = res.Data;
      } else {
        if (res.Data.length > 0) {
          res.Data.forEach((item: defs.CaseDto) => {
            dataList.value?.push(item);
          });
        }
      }
      if (dataList.value && dataList.value.length < total.value) {
        finished.value = false;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
/**
 * tabBar
 */
// const selectedIdx = ref(selectColor.selectNav);
const executeLoadData = async (values: number) => {
  total.value = 0;
  currTotal.value = 0;
  searchRequest.PageIndex = 1;
  searchRequest.AdvancedSearch = "";
  dataList.value = [];
  // selectedIdx.value = values;
  selectColor.selectNavFn(values);
  batchShow.value = false;
  await getCaseList();
};
/**
 * 加载更多
 */
const loading = ref(false);
const refreshing = ref(false);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        dataList.value = [];
        refreshing.value = false;
      }
      if (total.value > 0) {
        searchRequest.PageIndex++;
        await getCaseList();
      }

      loading.value = false;

      if (dataList.value && dataList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
const hospitalList: Ref<Array<any> | undefined> = ref();
//获取送检类型
const getCaseType = async () => {
  const res = await API.triageManagementApi.getDrop.request({
    params: {
      code: "CASE_TYPE",
    },
  });
  hospitalList.value = res.data.CASE_TYPE;
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};
//忽略
const cancelCollect = async (id: number) => {
  await API.triageFunctionApi.postNeglect.request({
    bodyParams: {
      Id: id,
    },
  });
  getCaseList();
};
//批量分配
const batchClick = async () => {
  console.log(listCards.value.checked);
  if (listCards.value.checked.length === 0) {
    Toast("请选择要分配的病例");
    return;
  }
  selectColor.batchIdListFn(listCards.value.checked);
  router.push({
    path: "/allocation",
    query: {
      siteId: dataList.value[0].SiteId,
      batch: 1,
    },
  });
};
onMounted(() => {
  zjsection.clearSection();
  getCaseList();
  getCaseType();
});
</script>

<style lang="scss" scoped>
.regulates {
  height: 100%;
  background-color: #f3f6fd;
}

.no-data-bottom {
  height: 4rem;
  line-height: 3rem;

  span {
    width: 100%;
    display: block;
    text-align: center;
    color: #8c8c8c;
  }
}

.case-bottom-container {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  padding: 13px 14px 0 14px;
  position: fixed;
  bottom: 0;
  z-index: 100;
  width: 100%;
  height: 88px;
  background: #fff;
  opacity: 1;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 0px 0px;
}

.case-bottom-container-div {
  width: 237px;
  height: 36px;
  background: #ffffff;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  border: 1px solid #650faa;
  text-align: center;
  line-height: 36px;
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #650faa;
}

.case-bottom-container-divs {
  background: #650faa;
  color: #ffffff;
}
</style>
