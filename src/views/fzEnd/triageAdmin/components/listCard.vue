<template>
  <div class="container" v-if="props.dataList && props.dataList.length > 0">
    <van-swipe-cell
      v-for="(item, index) in props.dataList"
      :key="index"
      :disabled="selectColor.selectNav !== 4"
    >
      <div v-if="!isShow" class="case-card-info">
        <van-row>
          <div class="case-card-title">
            原病理号：{{ item.PathologyNumber }}
          </div>
        </van-row>
        <div></div>
        <van-row class="case-card-content">
          <van-col span="4" class="case-card-image">
            <SliceImage :imgUrl="item.SliceThumbnail" />
          </van-col>
          <van-col span="15" class="case-card-text">
            <span class="case-card-name"
              >{{ item.Name ? item.Name + " |" : "" }}
              {{ item.SexName ? item.SexName + " |" : "" }} {{ item.Age }}</span
            >
            <div class="case-card-detail">
              <span class="case-card-message">
                {{ item.CaseTypeName ? item.CaseTypeName + " |" : "" }}
                {{
                  item.SampleLocationName
                    ? SampleLocationName(item.SampleLocationName) + " |"
                    : ""
                }}
                {{ item.Inspection }}
              </span>
              <div class="case-card-time">{{ item.Time }}</div>
            </div>
          </van-col>
          <van-col span="3" class="case-card-btn">
            <div
              v-if="selectColor.selectNav === 1"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              分配
            </div>
            <div
              v-if="
                selectColor.selectNav === 2 &&
                [5, 6, 9, 13].includes(item.Status) &&
                item.StatusName !== '诊断中(超时)'
              "
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              查看
            </div>
            <div
              v-if="
                selectColor.selectNav === 2 &&
                (![5, 6, 9, 13].includes(item.Status) ||
                  item.StatusName === '诊断中(超时)')
              "
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              重新分配
            </div>
            <div
              v-if="selectColor.selectNav === 3 && [10].includes(item.Status)"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              查看原因
            </div>
            <div
              v-if="selectColor.selectNav === 3 && [11].includes(item.Status)"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              退回站点
            </div>
            <div
              v-if="selectColor.selectNav === 4"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              确认撤回
            </div>
          </van-col>
        </van-row>
        <div
          class="corner-mark"
          :style="[
            GetExpertStatusAndColor(item.Status || 0, item.StatusName || ''),
          ]"
        >
          {{ item.StatusName }}
        </div>
      </div>
      <div v-else class="case-card-info">
        <div class="case-card-info-check">
          <div class="case-card-info-checks">
            <van-checkbox-group
              v-model="checked"
              icon-size="20px"
              checked-color="#650faa"
            >
              <van-checkbox :name="item.Id"></van-checkbox>
            </van-checkbox-group>
          </div>
          <div>
            <van-row>
              <div class="case-card-title">
                原病理号{{ item.PathologyNumber }}
              </div>
            </van-row>

            <van-row class="case-card-content">
              <van-col class="case-card-image">
                <SliceImage :imgUrl="item.SliceThumbnail" />
              </van-col>
              <van-col span="16" class="case-card-text">
                <span class="case-card-name"
                  >{{ item.Name ? item.Name + " |" : "" }}
                  {{ item.SexName ? item.SexName + " | " : "" }}
                  {{ item.Age }}</span
                >
                <div class="case-card-detail">
                  <span class="case-card-message">
                    {{ item.CaseTypeName ? item.CaseTypeName + " |" : "" }}
                    {{
                      item.SampleLocationName
                        ? SampleLocationName(item.SampleLocationName) + " |"
                        : ""
                    }}
                    {{ item.Inspection }}
                  </span>
                  <div class="case-card-time">{{ item.Time }}</div>
                </div>
              </van-col>
            </van-row>
          </div>
          <div class="case-card-btns">
            <div
              v-if="selectColor.selectNav === 1"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              分配
            </div>
            <div
              v-if="
                selectColor.selectNav === 2 && [5, 9, 13].includes(item.Status)
              "
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              查看
            </div>
            <div
              v-if="
                selectColor.selectNav === 2 && ![5, 9, 13].includes(item.Status)
              "
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              重新分配
            </div>
            <div
              v-if="selectColor.selectNav === 3 && [10].includes(item.Status)"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              查看原因
            </div>
            <div
              v-if="selectColor.selectNav === 3 && [11].includes(item.Status)"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              退回站点
            </div>
            <div
              v-if="selectColor.selectNav === 4"
              class="case-review-btn"
              @click="gotoInfo(item)"
            >
              确认撤回
            </div>
          </div>
        </div>

        <div
          class="corner-mark"
          :style="[
            GetExpertStatusAndColor(item.Status || 0, item.StatusName || ''),
          ]"
        >
          {{ item.StatusName }}
        </div>
      </div>
      <template #right>
        <van-button
          text="忽略"
          class="delete-button"
          @click="cancelClcik(item.Id || 0)"
        />
      </template>
    </van-swipe-cell>
  </div>

  <div v-else>
    <van-empty description="暂无数据"> </van-empty>
  </div>
</template>

<script lang="ts" setup>
import SliceImage from "@/components/case-list/SliceImage.vue";
import { GetExpertStatusAndColor } from "@/types/enum";
import useStore from "@/store";
import { SampleLocationName } from "@/utils/utils";
const router = useRouter();
const props = defineProps({
  dataList: {
    type: Array as any,
    default: [],
  },
  isShow: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(["cancelCollect"]);
const { selectColor, zjsection } = useStore();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
const reviewerShow = ref(false);
const checked = ref([]);
//获取切片列表
// const getSliceList = async (params: API.userCommonApi.getSliceList.Params) => {
//   await zjsection.getSectionAsync(params);
// };
//查看详情

const gotoInfo = async (caseInfo: defs.CaseDto) => {
  // params.Id = caseInfo.Id as number;
  // setTimeout(async () => {
  //   await getSliceList(params);
  // }, 1000);
  await router.push({
    path: "/fzDetails",
    query: {
      id: caseInfo.Id && caseInfo.Id.toString(),
      siteId: caseInfo.SiteId || 0,
      status: caseInfo.Status,
      overtime: caseInfo.StatusName === "诊断中(超时)" ? 1 : "",
    },
  });
};
//取消收藏
const cancelClcik = (id: number) => {
  emits("cancelCollect", id);
};
defineExpose({
  checked,
});
</script>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  margin: 0 11px 0 14px;
}

.case-card-info {
  box-sizing: border-box;
  position: relative;
  padding: 10px 12px;
  margin-bottom: 12px;
  // height: 100px;
  background-color: #fff;
  border-radius: 4px;
}

.case-card-title {
  margin-top: 2;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.case-card-content {
  margin-top: 13px;
  align-items: flex-end;
  flex-wrap: nowrap;
}

// .case-card-image {
//   width: 51px;
//   height: 51px;
// }

.case-card-text {
  margin-left: 6px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.case-card-name {
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.case-card-detail {
  margin-top: 5px;
}

.case-card-detail {
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.case-card-time {
  margin-top: 2px;
}

.case-card-btn {
  line-height: 6rem;
  flex: auto;
}

.case-card-btns {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.case-review-btn {
  width: 68px;
  height: 28px;
  background: #f0e7f7;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 28px;
  font-size: 12px;
  font-weight: 400;
  color: #650faa;
}

.corner-mark {
  position: absolute;
  padding: 0 6px;
  top: 0;
  right: 0;
  height: 24px;
  line-height: 24px;
  background: #df67bb;
  border-radius: 0px 4px 0px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

.delete-button {
  margin: auto;
  width: 66px;
  height: 66px;
  background: #650faa;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

:deep(.van-swipe-cell__right) {
  margin-top: 19px;
  right: -1px;
  transform: translate3d(100%, 0, 0);
}

.imageClass {
  height: 51px;
  width: 51px;
  border: 1px solid #e6ecfb;
}

.case-card-info-check {
  display: flex;
}

.case-card-info-checks {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
</style>
