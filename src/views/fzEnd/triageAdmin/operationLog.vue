<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="操作日志" />
    <!--顶部导航 end -->
    <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" @load="onLoadNext">
      <div class="operationLog">
        <div v-for="(item, index) in logList" :key="index" class="operationLogs">
          <div>操作人：{{ item.ApplyUserName }}</div>
          <div>操作账号：{{ item.Account }}&nbsp; 操作时间：{{ item.ApplyTime }}</div>
          <div>操作动作：{{ item.NodeName }}</div>
        </div>
      </div>
    </van-list>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Ref } from "vue";
const route = useRoute();
const { id } = route.query;
const loading = ref(false);
const finished = ref(false);
const total = ref(0);
const searchRequest: Ref<API.allCommonApi.getLog.Params> = ref({
  PageIndex: 1,
  PageSize: 10,
  Id: Number(id)
});
const logList: Ref<defs.OperationRecordDto[]> = ref([]);
//获取日志记录
const getLogList = async () => {
  const res = await API.allCommonApi.getLog.request({
    params: searchRequest.value
  });
  total.value = res.Count as number;
  if (searchRequest.value.PageIndex === 1) {
    logList.value = res.Data as defs.OperationRecordDto[];
  } else {
    if (res.Data && res.Data.length > 0) {
      res.Data.forEach((item: defs.OperationRecordDto) => {
        logList.value?.push(item);
      });
    }
  }
};

const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (total.value > 0) {
        searchRequest.value.PageIndex++;
        await getLogList();
      }

      loading.value = false;

      if (logList.value && logList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
}
onMounted(() => {
  getLogList();
});
</script>
<style lang="scss" scoped>
.operationLog {
  box-sizing: border-box;
  margin: 0 11px 0 14px;
}

.operationLogs {
  margin-top: 12px;
  padding: 12px;
  background: #FFFFFF;
  border-radius: 4px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8B96A6;
  line-height: 14px;

  &>div {
    margin-bottom: 5px;
  }

  &>div:nth-child(1) {
    font-size: 14px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #343840;
    margin-bottom: 5px;
  }
}
</style>
