<template>
  <div class="container" v-if="props.dataList && props.dataList.length > 0">
    <van-swipe-cell
      v-for="(item, index) in props.dataList"
      :key="index"
      :disabled="true"
    >
      <div class="case-card-info">
        <van-row>
          <div class="case-card-title">
            原病理号：{{ item.PathologyNumber }}
          </div>
        </van-row>
        <div></div>
        <van-row class="case-card-content">
          <van-col span="18" class="case-card-text">
            <span class="case-card-name"
              >{{ item.Name ? item.Name + " |" : "" }}
              {{ item.Sex ? item.Sex + " |" : "" }} {{ item.Age }}</span
            >
            <div class="case-card-detail">
              <span class="case-card-message">
                {{ item.CheckItems }}
              </span>
              <div class="case-card-time">{{ item.CreateTime }}</div>
            </div>
          </van-col>
        </van-row>
      </div>
      <template #right>
        <van-button
          text="忽略"
          class="delete-button"
          @click="cancelClcik(item.Id || 0)"
        />
      </template>
    </van-swipe-cell>
  </div>

  <div v-else>
    <van-empty description="暂无数据"> </van-empty>
  </div>
</template>

<script lang="ts" setup>
import SliceImage from "@/components/case-list/SliceImage.vue";
import { GetExpertStatusAndColor } from "@/types/enum";
import useStore from "@/store";
const router = useRouter();
const props = defineProps({
  dataList: {
    type: Array as any,
    default: [],
  },
});
const emits = defineEmits(["cancelCollect"]);
const { selectColor, zjsection } = useStore();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
const reviewerShow = ref(false);
//获取切片列表
const getSliceList = async (params: API.userCommonApi.getSliceList.Params) => {
  await zjsection.getSectionAsync(params);
};
//查看详情

const gotoInfo = async (caseInfo: defs.CaseDto) => {
  params.Id = caseInfo.Id as number;
  setTimeout(async () => {
    await getSliceList(params);
  }, 1000);
  await router.push({
    path: "/fzDetails",
    query: {
      id: caseInfo.Id && caseInfo.Id.toString(),
      siteId: caseInfo.SiteId || 0,
      status: caseInfo.Status,
    },
  });
};
//取消收藏
const cancelClcik = (id: number) => {
  emits("cancelCollect", id);
};
</script>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  margin: 0 11px 0 14px;
}

.case-card-info {
  box-sizing: border-box;
  position: relative;
  padding: 10px 12px;
  margin-bottom: 12px;
  height: 100px;
  background-color: #fff;
  border-radius: 4px;
}

.case-card-title {
  margin-top: 2;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.case-card-content {
  margin-top: 13px;
  align-items: flex-end;
}

// .case-card-image {
//   width: 51px;
//   height: 51px;
// }

.case-card-text {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.case-card-name {
  font-size: 14px;
  font-weight: 400;
  color: #4d545f;
}

.case-card-detail {
  margin-top: 5px;
}

.case-card-detail {
  font-size: 12px;
  font-weight: 400;
  color: #8b96a6;
}

.case-card-time {
  margin-top: 2px;
}

.case-card-btn {
  line-height: 6rem;
  flex: auto;
}

.case-review-btn {
  width: 68px;
  height: 28px;
  background: #f0e7f7;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  text-align: center;
  line-height: 28px;
  font-size: 12px;
  font-weight: 400;
  color: #650faa;
}

.corner-mark {
  position: absolute;
  padding: 0 6px;
  top: 0;
  right: 0;
  height: 24px;
  line-height: 24px;
  background: #df67bb;
  border-radius: 0px 4px 0px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

.delete-button {
  margin: auto;
  width: 66px;
  height: 66px;
  background: #650faa;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}

:deep(.van-swipe-cell__right) {
  margin-top: 19px;
  right: -1px;
  transform: translate3d(100%, 0, 0);
}

.imageClass {
  height: 51px;
  width: 51px;
  border: 1px solid #e6ecfb;
}
</style>
