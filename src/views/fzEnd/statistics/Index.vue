<template>
  <div class="statistics">
    <!--顶部导航 start -->
    <NavBar title="数据统计" />

    <!--顶部导航 end -->
    <!-- 搜索 start -->
    <div class="caseSearchClass">
      <FzSearch
        @onSearchHandle="executeSearch"
        placeholder="请搜索原病理号/患者姓名"
        :hospitalList="hospitalList"
        @hospital-list-click="hospitalListClick"
      >
        <template #search>
          <!-- 实时数据 start -->
          <div class="navBarClass">
            <GlData :islink="false" :homeData="homeData" />
          </div>
          <!-- 实时数据 end -->
        </template>
      </FzSearch>
    </div>
    <!-- 搜索 end -->

    <van-list
      v-model:loading="loading"
      :finished="finished"
      :immediate-check="false"
      @load="onLoadNext"
    >
      <!--列表 start -->
      <listCard :dataList="statisticsList"></listCard>
      <!--列表 end -->
    </van-list>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import FzSearch from "@/components/fzSearch/index.vue";
import listCard from "./components/listData.vue";
import GlData from "@/views/fzEnd/home/<USER>/GlData.vue";
import { Ref } from "vue";
import useStore from "@/store";

/**
 * 搜索
 */
const executeSearch = (value: any) => {
  statisticsList.value = [];
  queryStatistics.CompleteTime = "";
  queryStatistics.SubscribeTime = "";
  // queryStatistics.AdvancedSearch = value.AdvancedSearch;
  queryStatistics.Str = value.Str;
  queryStatistics.CaseType = value.CaseType;

  if (value.EndTime) {
    queryStatistics.CompleteTime = value.EndTime;
  }
  if (value.StartingTime) {
    queryStatistics.SubscribeTime = value.StartingTime;
  }
  queryStatistics.PageSize = 10;
  queryStatistics.PageIndex = 1;
  getStatisticsAsync();
};
/**
 * 列表
 */
const hospitalList: Ref<Array<defs.SiteDropDto>> = ref([]);
const GetHospital = async () => {
  const res = await API.triageManagementApi.getDrop.request({
    params: {
      code: "CASE_TYPE",
    },
  });
  hospitalList.value = res.data.CASE_TYPE;
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};
//获取列表数据
const queryStatistics: any = reactive({
  PageIndex: 1,
  PageSize: 10,
});
const statisticsList: Ref<defs.DistributionDto[]> = ref([]);
const getStatisticsAsync = async () => {
  const res = await API.statisticsManagerApi.getDistributionList.request({
    params: queryStatistics,
  });
  total.value = res.Count || 0;
  if (queryStatistics.PageIndex == 1) {
    statisticsList.value = res.Data || [];
  } else {
    statisticsList.value =
      statisticsList.value?.concat(res.Data || []) || res.Data;
  }
};

//下拉加载
const finished = ref(false);
const loading = ref(false);
const refreshing = ref(false);
const total = ref(0);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        statisticsList.value = [];
        refreshing.value = false;
      }
      queryStatistics.PageIndex++;
      await getStatisticsAsync();

      loading.value = false;

      if (statisticsList.value && statisticsList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
//获取首页数据大版
const homeData: Ref<any | undefined> = ref();

const getHomeDataAsync = async () => {
  const res = await API.frontPageApi.getCount.request({
    params: {
      CaseType: 3,
    },
  });
  homeData.value = res.Data;
};
onMounted(() => {
  GetHospital();
  getStatisticsAsync();
  getHomeDataAsync();
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #f3f6fd;
}

:deep(.home-data) {
  padding: 17px 14px 0 14px !important;
}
</style>
