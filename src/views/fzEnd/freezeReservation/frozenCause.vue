<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="退回" />
    <!--顶部导航 end -->
    <div class="feedBack">
      <div class="feedBack-title">请输入退回原因</div>
      <van-cell-group inset>
        <van-field
          v-model="message"
          rows="5"
          label=""
          type="textarea"
          maxlength="100"
          autosize
          :disabled="poops.disabled"
          :placeholder="poops.backPlaceholder"
        />
      </van-cell-group>
    </div>
    <div class="feedBack-btn" @click="feedBackClick">确认退回</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import { Toast } from "vant";
const poops = defineProps({
  backPlaceholder: {
    type: String,
    default: "请输入100字以内的退回原因",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const route = useRoute();
const router = useRouter();
const { id } = route.query;
const message = ref("");
const feedBackClick = async () => {
  if (!message.value) return Toast.fail("请输入退回原因");
  await API.distributorExpertApi.putReturnSubmit.request({
    bodyParams: {
      Id: Number(id),
      IsFrozen: true,
      Remark: message.value,
      ReturnType: "",
    },
  });
  router.go(-2);
};
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 14px 14px 14px;
  height: 100%;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.van-cell {
  padding: 0;
}

:deep(.van-field__control) {
  font-size: 12px;
  font-weight: 400;
  color: #b4bfd0;
}

.feedBack-btn {
  margin: 0 49px 0 46px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
