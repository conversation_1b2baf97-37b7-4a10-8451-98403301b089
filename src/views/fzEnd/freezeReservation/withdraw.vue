<template>
  <div>
    <!--顶部导航 start -->
    <NavBar title="撤回" />
    <!--顶部导航 end -->
    <div class="feedBack">

      <div class="feedBack-title">撤回理由</div>
      <van-cell-group inset>
        <van-field v-model="message" rows="5" label="" type="textarea" maxlength="100" autosize
          :disabled="poops.disabled" :placeholder="poops.backPlaceholder" />
      </van-cell-group>
    </div>
    <div class="feedBack-btn" @click="feedBackClick">确认撤回</div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
const poops = defineProps({
  backPlaceholder: {
    type: String,
    default: "请输入100字以内的撤回理由",
  },
  disabled: {
    type: Boolean,
    default: true,
  },
});
const route = useRoute();
const router = useRouter();
const { id } = route.query
const message = ref("");
const feedBackClick = async () => {
  await API.triageFunctionApi.putAgreeWithdraw.request({
    bodyParams: {
      Id: Number(id),
      IsFrozen: true
    },
  });
  router.go(-1)

};
//获取撤回理由
const getWithdrawReason = async () => {
  const res = await API.triageFunctionApi.getWithdrawal.request({
    params: {
      Id: Number(id),
    },
  });
  message.value = res.Data as string
}
onMounted(() => {
  getWithdrawReason()
})
</script>
<style lang="scss" scoped>
.feedBack {
  margin: 14px 14px 14px 14px;
  height: 100%;
  background: #ffffff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}

.feedBack-title {
  padding: 6px 14px;
  font-size: 14px;
  font-weight: bold;
  color: #343840;
}

.van-cell {
  padding: 0;
}

:deep(.van-field__control) {
  font-size: 12px;
  font-weight: 400;
  color: #b4bfd0;
}

.feedBack-btn {
  margin: 0 49px 0 46px;
  height: 36px;
  background: #650faa;
  border-radius: 36px 36px 36px 36px;
  opacity: 1;
  line-height: 36px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
}
</style>
