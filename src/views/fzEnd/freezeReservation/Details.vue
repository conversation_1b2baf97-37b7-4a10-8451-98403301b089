<template>
  <div class="container">
    <!--顶部导航 start -->
    <NavBar title="预约信息" />
    <!--顶部导航 end -->
    <!--引入头部病人信息组件-->
    <FzPatientInfos :dataDetails="caseInfo" :shareUrl="shareUrl" />
    <!--切换按钮-->
    <FzCaseInfoTabs :activeName="activeTab" @nav-click="getChild" />
    <!--详情展示-->
    <!--病例信息-->
    <FzCaseInfoContents ref="infoComponent" :data="caseInfo" />
    <!--附件信息-->
    <FzCaseFileContents ref="sliceinfoComponent" :fileList="fileList" />
    <!-- 专家信息 strat -->
    <ExpertFormation ref="fileinfoComponent" :expertInfo="expertInfo" />
    <!-- 专家信息 end -->
    <!-- 占位 -->
    <div v-if="selectColor.selectNav1 !== 2" class="occupied"></div>
    <!--详情底部按钮-->
    <FzCaseInfoBottomBtns v-if="selectColor.selectNav1 !== 2" />
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import ExpertFormation from "@/components/fzCase-details/ExpertFormation.vue";
import { Ref } from "vue";
import useStore from "@/store";
import { Toast } from "vant";
const { zjsection, saveCase, tokens, selectColor } = useStore();

const route = useRoute();
const router = useRouter();
const params: API.userCommonApi.getSliceList.Params = {
  Id: 0,
  PageIndex: 1,
  PageSize: 100,
};
//分享报告url
const shareUrl = ref("");
const { id, category, status } = route.query;
onMounted(async () => {
  if (id) {
    params.Id = Number(id);
    const res = await API.userCommonApi.getFrozenRendering.request({
      params: {
        Id: Number(id),
      },
    });
    caseInfo.value = res.Data;
    //获取附件信息
    await getFileList(Number(id));

    //获取专家信息
    await getExpertInfo();
  }
});
const expertInfo: Ref<defs.SiteExpertDto> = ref({});
// 获取专家信息
const getExpertInfo = async () => {
  const res = await API.allCommonApi.getExpertInfor.request({
    params: {
      Id: Number(id),
      IntentionalExpert: true,
    },
  });
  expertInfo.value = res.Data as defs.SiteExpertDto;
};

//病例信息data
const caseInfo: Ref<any> = ref(undefined);
//--切换按钮--
const activeTab = ref("case");
const sliceinfoComponent: Ref<any> = ref(null);
const fileinfoComponent = ref(null);
const infoComponent = ref(null);
const getChild = (tab: string) => {
  let el = null;
  switch (tab) {
    case "case":
      el = (infoComponent.value as any).$el;
      break;
    case "slice":
      el = (sliceinfoComponent.value as any).$el;
      break;
    case "file":
      el = (fileinfoComponent.value as any).$el;
      break;
    default:
      return;
  }
  if (el) {
    el.scrollIntoView();
  }
  activeTab.value = tab;
};

//获取附件信息
const fileList: Ref<Array<defs.AnnexDto> | undefined> = ref();
const getFileList = async (id: number) => {
  const res = await API.userCommonApi.getAnnexList.request({
    params: {
      Id: id,
      PageIndex: 1,
      PageSize: 20,
    },
  });
  fileList.value = res.Data;
};
</script>

<style lang="scss" scoped>
.image-box {
  width: 500px;
  height: 500px;
}
</style>
