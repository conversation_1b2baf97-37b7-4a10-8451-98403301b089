<template>
  <div class="regulates">
    <!-- <van-sticky> -->
    <!--顶部导航 start -->
    <NavBar title="冰冻预约"></NavBar>
    <!--顶部导航 end -->
    <!-- tabbar start -->
    <FreezeTabBars @tab-click="executeLoadData" />
    <!-- tabbar end -->
    <!-- </van-sticky> -->

    <!-- 搜索 start -->
    <FzSearch
      @onSearchHandle="executeSearch"
      placeholder="请搜索预约编号/患者姓名"
      :hospitalList="hospitalList"
      @hospital-list-click="hospitalListClick"
      hospitalListName="送检医院"
    />
    <!-- 搜索 end -->

    <!--下拉刷新-->
    <van-pull-refresh
      v-model="isLoading"
      success-text="刷新成功"
      @refresh="onRefresh"
      style="min-height: 80vh"
    >
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :immediate-check="false"
        @load="onLoadNext"
      >
        <!--列表 start -->
        <listCard
          :dataList="dataList"
          @cancelCollect="cancelCollect"
        ></listCard>
      </van-list>
      <!--列表 end -->
    </van-pull-refresh>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "@/components/navBar/Index.vue";
import FreezeTabBars from "@/components/TabBars/FreezeTabBars.vue";
import FzSearch from "@/components/fzSearch/frozenSearch.vue";
import listCard from "./components/listCard.vue";
import { Ref } from "vue";
import { GetCaseListRequest } from "@/model/request/CaseApiRequest";
import { GetCaseListAsync } from "@/api/zjEnd/regulates";
import useStore from "@/store";
const { zjsection, selectColor } = useStore();
/**
 * 刷新
 */
const finished = ref(false);
const isLoading = ref(false);
const onRefresh = async () => {
  searchRequest.PageIndex = 1;
  total.value = 0;
  dataList.value = [];
  await getCaseList();
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);
};
/**
 * 搜索
 */
const executeSearch = (value: any) => {
  dataList.value = [];
  searchRequest.Where = value.where;
  searchRequest.AdvancedSearch = value.AdvancedSearch;
  searchRequest.PageSize = 10;
  searchRequest.PageIndex = 1;
  getCaseList();
};
//接口集合
const apiAddres: Ref<string> = ref("/api/distributor/frozen/tobetriaged/list");
const getApiAddress = (): string => {
  const _idx = Number(selectColor.selectNav1);
  switch (_idx) {
    case 1:
      apiAddres.value = "/api/distributor/frozen/tobetriaged/list";
      break;
    case 2:
      apiAddres.value = "/api/distributor/frozen/scheduled/list";
      break;
    case 3:
      apiAddres.value = "/api/distributor/frozen/tobewithdrawn/list";
      break;
    case 4:
      apiAddres.value = "/api/distributor/frozen/returned/list";
      break;
  }
  return apiAddres.value;
};

const total = ref(0);
const currTotal = ref(0);
/**
 * 列表
 */
const dataList: Ref<Array<defs.CaseDto> | undefined> = ref();
const searchRequest: GetCaseListRequest = reactive({
  PageIndex: 1,
  PageSize: 10,
});
const getCaseList = async () => {
  const _apiUrl = getApiAddress();
  await GetCaseListAsync(searchRequest, _apiUrl as string)
    .then(async (res) => {
      total.value = res.Count;
      if (searchRequest.PageIndex === 1) {
        dataList.value = res.Data;
      } else {
        if (res.Data.length > 0) {
          res.Data.forEach((item: defs.CaseDto) => {
            dataList.value?.push(item);
          });
        }
      }
      if (dataList.value && dataList.value.length < total.value) {
        finished.value = false;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
/**
 * tabBar
 */
// const selectedIdx = ref(selectColor.selectNav);
const executeLoadData = async (values: number) => {
  total.value = 0;
  currTotal.value = 0;
  searchRequest.PageIndex = 1;
  searchRequest.AdvancedSearch = "";
  dataList.value = [];
  // selectedIdx.value = values;
  selectColor.selectNav1Fn(values);
  await getCaseList();
};
/**
 * 加载更多
 */
const loading = ref(false);
const refreshing = ref(false);
const onLoadNext = () => {
  nextTick(() => {
    setTimeout(async () => {
      if (refreshing.value) {
        dataList.value = [];
        refreshing.value = false;
      }
      if (total.value > 0) {
        searchRequest.PageIndex++;
        await getCaseList();
      }

      loading.value = false;

      if (dataList.value && dataList.value.length >= total.value) {
        finished.value = true;
      }
    }, 1000);
  });
};
const hospitalList: Ref<Array<any> | undefined> = ref();
//获取送检类型
const getCaseType = async () => {
  const res = await API.triageManagementApi.getDrop.request({
    params: {
      code: "DISTRIBUTOR_SITE",
    },
  });
  hospitalList.value = res.data.DISTRIBUTOR_SITE;
};
const hospitalListClick = () => {
  (hospitalList.value as any).forEach((item: any) => {
    item.checked = false;
  });
};
//忽略
const cancelCollect = async (id: number) => {
  await API.triageFunctionApi.postNeglect.request({
    bodyParams: {
      Id: Number(id),
    },
  });
  getCaseList();
};
onMounted(() => {
  zjsection.clearSection();
  getCaseList();
  getCaseType();
});
</script>

<style lang="scss" scoped>
.regulates {
  height: 100%;
  background-color: #f3f6fd;
}

.no-data-bottom {
  height: 4rem;
  line-height: 3rem;

  span {
    width: 100%;
    display: block;
    text-align: center;
    color: #8c8c8c;
  }
}
</style>
