<template>
  <div class="password">
    <div class="password-login">
      已有账号，马上<span style="color: #650faa" @click="router.push('/login')"
        >登录</span
      >
    </div>
    <div class="password-forget">找回密码</div>
    <div class="submitClass">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            class="login-field"
            v-model="Str"
            placeholder="请输入手机/邮箱"
            :rules="[
              { validator: validatorMessage, message: '请输入手机/邮箱' },
            ]"
          />
          <div class="login-code">
            <van-field
              class="login-field login-field1"
              v-model="Code"
              placeholder="请输入验证码"
              :rules="[{ required: true, message: '请输入验证码' }]"
            />
            <van-button
              class="login-send"
              :disabled="isDisposed"
              @click="getCode"
            >
              {{ isDisposed ? `${time}s后重新获取` : "发送验证码" }}
            </van-button>
          </div>
          <van-field
            class="login-field"
            v-model="PassWord"
            type="password"
            placeholder="请输入新的密码"
            autocomplete="new-passwordl"
            :rules="[{ required: true, message: '请输入新的密码' }]"
          />
          <van-field
            class="login-field"
            v-model="confirmPassword"
            type="password"
            placeholder="请再次输入新的密码"
            autocomplete="new-passwordl"
            :rules="[{ required: true, message: '请再次输入新的密码' }]"
          />
        </van-cell-group>
        <van-button
          class="login-btn"
          round
          block
          color="#650FAA"
          :loading="loading"
          loading-text="确认..."
          native-type="submit"
        >
          确认
        </van-button>
      </van-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Aes } from "@/utils/aesEncrypt";
import { Toast } from "vant";
import { Ref } from "vue";
const router = useRouter();
const loading = ref(false);
const Str = ref();
const Code = ref(); //验证码
const PassWord = ref();
const confirmPassword = ref();
const validatorMessage = (value: string) => {
  if (
    /^[1][3-9]\d{9}$/.test(value) ||
    /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test(value)
  ) {
    return true;
  } else {
    return false;
  }
};
//判断是手机号码还是邮箱
const isPhone: Ref<number | null> = ref(null);
const getCode = async () => {
  if (
    !/^[1][3-9]\d{9}$/.test(Str.value) &&
    !/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test(Str.value)
  ) {
    Toast.fail("请输入正确手机号");
    return;
  }
  if ((Str.value as string).indexOf("@") != -1) {
    isPhone.value = 1;
  } else {
    isPhone.value = 0;
  }
  isDisposed.value = true;
  try {
    const res = await API.accountApi.putCode.request({
      bodyParams: {
        PhoneOrMail: Str.value as string,
        SendType: isPhone.value,
      },
    });
    if (res.Code === 200) {
      Toast.success("获取成功");
      handleTimeChange();
    } else {
      Toast.fail(res.Message || "获取失败");
    }
  } catch (error) {
    Toast.fail("验证码获取失败");
    isDisposed.value = false;
  }
};
const handleTimeChange = () => {
  if (time.value <= 0) {
    isDisposed.value = false;
    time.value = 60;
  } else {
    setTimeout(() => {
      time.value--;
      handleTimeChange();
    }, 1000);
  }
};
const onSubmit = async () => {
  if (PassWord.value !== confirmPassword.value) {
    PassWord.value = "";
    confirmPassword.value = "";
    return Toast.fail("两次密码不一样");
  }
  const res = await API.accountApi.putUpdatePassword.request({
    bodyParams: {
      Code: Code.value,
      PassWord: Aes.Encrypt(PassWord.value),
      Str: Str.value,
    },
  });
  if (res.Code === 200) {
    Toast.success("成功");
    router.push("/login");
  } else {
    Code.value = "";
    Toast.fail(res.Message || "");
  }
};
const time = ref(60);
const isDisposed = ref(false);
</script>
<style lang="scss" scoped>
.password {
  box-sizing: border-box;
  height: 100%;
  padding: 48px 0 0 0;
  background-color: #fff;
}
.password-login {
  padding: 0 30px;
  font-size: 18px;
  font-weight: bold;
  color: #000000;
}
.password-forget {
  padding: 0 30px;
  margin-top: 10px;
  margin-bottom: 30px;
  font-size: 24px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #000000;
}
.submitClass{
  padding: 0 30px;
  background-color: #fff;
}
.login-btn {
  margin-top: 57px;
  width: 100%;
  height: 44px;
  opacity: 1;
}
.login-field {
  margin-top: 32px;
  width: 100%;
  background: #f4f7fd;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}
.login-field1 {
  margin-top: 0;
}
.van-cell-group--inset {
  margin: 0;
}
.login-code {
  margin-top: 32px;
  display: flex;
}
.login-send {
  margin-left: 10px;
  width: 210px;
  height: 44px;
  background: #650faa;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
  color: #f7f8fa;
}
</style>
