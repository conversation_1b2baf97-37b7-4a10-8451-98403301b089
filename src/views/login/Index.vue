<template>
  <div class="login">
    <div class="login-title">
      <div>欢迎登录</div>
      <div>数字病理远程会诊平台，</div>
    </div>
    <div class="login-form">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            class="login-field"
            v-model="account"
            name="用户名"
            label=""
            clearable
            placeholder="请输入账号"
            :rules="[{ required: true, message: '请填写账号' }]"
          />
          <van-field
            class="login-field"
            v-model="password"
            :type="passwords"
            name="密码"
            label=""
            placeholder="请输入密码"
            autocomplete="new-passwordl"
            @click-right-icon="clickRight"
            :rules="[{ required: true, message: '请填写密码' }]"
          >
            <template #right-icon>
              <div v-if="password">
                <van-icon v-if="passwordShow" name="closed-eye" />
                <van-icon style="color: #8b96a6" v-else name="eye-o" />
              </div>
            </template>
          </van-field>
        </van-cell-group>
        <div class="remember">
          <div>
            <van-checkbox
              class="forget"
              v-model="checked"
              checked-color="#650FAA"
              shape="square"
              >记住密码
            </van-checkbox>
          </div>
          <div @click="router.push('/forgetPassword')">忘记密码？</div>
        </div>
        <div class="remember"></div>

        <van-button
          class="login-btn"
          round
          block
          color="#650FAA"
          :loading="loading"
          loading-text="登录..."
          native-type="submit"
        >
          登录
        </van-button>
      </van-form>
    </div>
    <!-- <div class="versions">
      <div @click="router.push({ path: '/inspect', query: { type: 0 } })">
        关于我们
      </div>
      <div>发布版本号：V1.0</div>
      <div>完整版本号：v1.0.1.0329</div>
    </div> -->
    <!-- <div class="takePosition"></div>
    <div class="privacy">新用户登录即完成注册,且表示您已同意<span @click="router.push('/agreement')">《用户协议》</span>和<span
        @click="router.push('/privacy')">《隐私政策》</span></div> -->
  </div>
  <!-- <div v-else class="login loading">
    <van-loading type="spinner" color="#650FAA" />
  </div> -->
</template>
<script lang="ts" setup>
import { Aes } from "@/utils/aesEncrypt";
import { Toast } from "vant";
import {
  setLocalStorage,
  LocalStorageName,
  getLocalStorage,
} from "@/utils/storage";
import useStore from "@/store";
import RequestHelper from "@/utils/BaseRequest";
import { RouteRecordRaw } from "vue-router";
const userInfo = getLocalStorage(LocalStorageName.remember);
const router = useRouter();
const route = useRoute();
const { tokens, routers, zjsection, selectColor } = useStore();
const productShow = ref(false);
const loading = ref(false);
const account = ref("");
const password = ref("");

if (userInfo) {
  account.value = userInfo.account;
  password.value = Aes.Decrypt(userInfo.password);
}

//控制密码显示隐藏show
const passwordShow = ref(true);
//点击密码
const clickRight = () => {
  passwordShow.value = !passwordShow.value;
};
const passwords = computed(() => {
  return passwordShow.value ? "password" : "text";
});
//记住密码
const checked = ref(false);
const onSubmit = () => {
  loading.value = true;
  const data = {
    Account: account.value,
    Password: Aes.Encrypt(password.value),
  };
  API.accountApi.postLogin
    .request({
      bodyParams: data as any,
    })
    .then((res) => {
      if ([503, 506].includes(res.Code as any)) {
        Toast(res.Message);
      }
      if (res.Code === 200) {
        tokens.setToken(res.Data.Token);
        rememberPassword();
        getUserInfoFun();
      }
    })
    .catch((err) => {
      loading.value = false;
      Toast(err.data.message);
    })
    .finally(() => {});
};

//获取用户信息
const getUserInfoFun = async () => {
  try {
    const res = await API.accountApi.getInfo.request({});
    if ([1, 4].includes(res.Data?.RoleId as number)) {
      tokens.clearToken();
      loading.value = false;
      return Toast("不可登录");
    }
    setLocalStorage(LocalStorageName.user, [], res.Data);
    await routers.getAsyncRouter();
    const redirect = route.query.redirect;
    selectColor.selectNavFn(1);
    if (routers.asyncRouter.length > 1) {
      const list = (await routers.getAsyncRouter()) as RouteRecordRaw[];
      list.forEach((item: RouteRecordRaw) => {
        router.addRoute(item);
      });
      if (redirect) {
        router.push(redirect as string);
      } else {
        router.push("/");
      }

      // Toast.success("登录成功");
      loading.value = false;
    } else {
      if (redirect) {
        router.push(redirect as string);
      } else {
        router.push("/");
      }
      // Toast.success("登录成功");
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
    console.log(error, "获取用户信息");
  }
};
//记住密码
const rememberPassword = () => {
  if (checked.value) {
    setLocalStorage(LocalStorageName.remember, [], {
      account: account.value,
      password: Aes.Encrypt(password.value),
    });
  }
};
// //是否是小程序登录
// const isMiniProgram = ref(false);
// if (route.query.params) {
//   console.log("小程序登录");
//   isMiniProgram.value = true;
//   tokens.setToken(route.query.params + "");
//   getUserInfoFun();
// } else {
//   window.uni.getEnv(function (res: { h5: any }) {
//     if (res.h5) {
//       isMiniProgram.value = false;
//     } else {
//       isMiniProgram.value = true;
//       window.uni.reLaunch({
//         url: "/pages/login/Login",
//       });
//     }
//   });
// }

const version = navigator.userAgent.match(/OS\s([\d_]+)/);
const androidVersion = navigator.userAgent.match(/Android\s([\d.]+)/i) as any;
if (version) {
  const versions = version[1].split("_");
  if ((versions[0] as any) < 15) {
    zjsection.versionsShow = true;
  }
}
if (androidVersion) {
  const versions = androidVersion[1].split(".");
  if (versions[0] < 8) {
    zjsection.versionsShow = true;
  }
}
onMounted(() => {
  routers.asyncRouter = [];
  routers.router = [];
});
</script>
<style lang="scss" scoped>
.login {
  box-sizing: border-box;
  height: 100%;
  // height: calc(100vh);
  padding: 48px 0 0 0;
  background-color: #fff;
}

.login-title {
  padding: 0 32px;
  height: 68px;
  font-size: 24px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: 500;
  color: #000000;
  line-height: 30px;
}

.login-form {
  padding: 0 32px;
  margin-top: 40px;
  background-color: #fff;
}

.remember {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #636363;
}

.login-field {
  margin-top: 32px;
  width: 100%;
  background: #f4f7fd;
  border-radius: 8px 8px 8px 8px;
  opacity: 1;
}

.login-btn {
  margin-top: 57px;
  width: 100%;
  height: 44px;
  opacity: 1;
}

.van-cell-group--inset {
  margin: 0;
}

:deep(.van-checkbox__icon) {
  font-size: 16px !important;
}

.privacy {
  height: 60px;
  padding: 0 32px;
  padding-top: 10px;
  position: fixed;
  left: 0;
  bottom: 0;
  font-size: 16px;
  color: #636363;
  overflow: hidden;
  background-color: #fff;
  text-align: center;
}

span {
  color: #650faa;
}

.takePosition {
  height: 90px;
  width: 100%;
  background-color: #fff;
}

input:focus {
  border: 1px solid #650faa;
}
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
}
.productShow {
  width: 320px;
  box-sizing: border-box;
  padding: 24px 24px 0 24px;
  background-color: #fff;
}
.productShow-title {
  margin-bottom: 16px;
  font-size: 18px;
  font-family: PingFang SC-Bold, PingFang SC;
  font-weight: bold;
  color: #343840;
  line-height: 21px;
}
.productShow-content {
  font-size: 16px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #343840;
  line-height: 24px;
}
.productShow-btn {
  padding: 16px 0;
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  font-size: 16px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #8c8c8c;
  line-height: 19px;
  & > div {
    flex: 1;
    text-align: center;
    cursor: pointer;
  }
  & > div:nth-child(2) {
    color: #650faa;
  }
}
.versions {
  position: fixed;
  bottom: 30px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #8b96a6;
  line-height: 12px;
  & > div {
    margin-bottom: 4px;
  }
  & > div:nth-child(1) {
    font-size: 12px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #650faa;
    line-height: 14px;
  }
}
</style>
