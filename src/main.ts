import { createApp } from "vue";

import App from "./App.vue";
import { createPinia } from "pinia";
import piniaPluginPersist from "pinia-plugin-persist";
import "@/assets/css/index.scss";
import "@/assets/css/common.css";
import "normalize.css";
import "./services/"; //接口请求
import RequestHelper from "./utils/BaseRequest";
import { mountRouter } from "@/router";
import { Lazyload } from "vant";
import { Dialog } from "vant";
import Vant from "vant";
import "vant/lib/index.css";
import "vant/es/toast/style";

import mitt from "mitt";
const Mit = mitt();
declare module "vue" {
  export interface ComponentCustomProperties {
    $Bus: typeof Mit;
  }
}
RequestHelper.init();

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersist);
app.config.globalProperties.$Bus = Mit;
app.use(Vant);
app.use(Dialog);
app.use(Lazyload, {
  lazyComponent: true,
});
mountRouter(app);
app.use(pinia);
app.mount("#app");
