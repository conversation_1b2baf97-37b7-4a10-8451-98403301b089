/**
 * 专家端切片加载辅助方法
 */
import { nextTick, ref } from "vue";
import { getLocalStorage, LocalStorageName } from "@/utils/storage";
import RcpWeb from "RcpWeb"; //项目配置
export function useSliceList() {
  const userInfo = getLocalStorage(LocalStorageName.user);
  const sliceDto = ref(new defs.CaseSliceDto());
  let timer = ref();
  // 刷新切片
  async function refreshViewers(
    inputSliceDto: defs.CaseSliceDto,
    myImageBrowserRef: any,
    pathologyNumber: string | undefined,
    isFrozen: boolean | undefined
  ) {
    console.log(inputSliceDto, myImageBrowserRef, pathologyNumber, isFrozen);

    sliceDto.value = inputSliceDto;

    if (myImageBrowserRef) {
      if (inputSliceDto.Status != 1) {
        await myImageBrowserRef?.refreshViewer(
          RcpWeb.IsBackEnd
            ? (inputSliceDto.Url as string)
            : (inputSliceDto.SliceUrl as string)
        );
      } else {
        if (pathologyNumber && isFrozen) {
          console.log(inputSliceDto, myImageBrowserRef);
          //冰冻插件转发
          await FrozenLinkWs(
            inputSliceDto.CreateUser as string,
            inputSliceDto.DiskPath as string,
            userInfo.Account as string,
            myImageBrowserRef,
            isFrozen as boolean
          );
        }
      }
      clearTimeout(timer.value);
    }
  }
  function initSliceDto() {
    sliceDto.value = new defs.CaseSliceDto();
  }

  async function FrozenLinkWs(
    uploadId: string,
    diskPath: string,
    expertsId: string,
    myImageBrowserRef: any,
    isFrozen?: boolean
  ) {
    // console.log(uploadId, DiskPath, expertsId,isFrozen);
    let list = {
      uploadId,
      diskPath,
      expertsId,
    };
    let url = RcpWeb.WebSocketUrl;
    if (RcpWeb.WebSocketProxyUrl) {
      url = RcpWeb.WebSocketProxyUrl;
    }
    myImageBrowserRef.refreshViewer(list, isFrozen, url);
  }

  return {
    sliceDto,
    refreshViewers,
    initSliceDto,
  };
}
