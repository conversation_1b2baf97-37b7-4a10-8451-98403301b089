//切片截图，报告附图辅助方法
import { Oss<PERSON>elper } from "@/utils/oss";
import { H3c<PERSON>elper } from "@/utils/h3c";
import { provide, ref } from "vue";
// import { FastStorageOpload } from "@/components/backend-upload/oss-upload";
import useStore from "@/store";
export class ScreenShot {
  ImageUrl: string;
  Url: string;
  Remark?: string;
  Sort?: number;
}
export default function useScreenshot() {
  const { h3c, oss } = useStore();

  const screenshots = ref<ScreenShot[]>([]);

  // 镜下所见： 切片的集合数据
  provide("screenshots", screenshots.value);

  provide("updateScreenshot", updateScreenshot);

  provide("uploadScreenshot", uploadScreenshot);

  provide("deleteScreenshot", deleteScreenshot);

  provide("isBase64", isBase64);

  //添加切片截图
  async function addSliceScreenshot(
    url: string,
    sliceName: string,
    remark: string
  ) {
    const result = await H3c<PERSON>elper.GetSliceScreenshotDirectory(h3c, sliceName);
    if (remark === "TCT") {
      screenshots.value[0] = { ImageUrl: result, Url: url };
    } else {
      if (screenshots.value.length < 4) {
        screenshots.value.push({ ImageUrl: result, Url: url });
      }
    }
  }

  async function addScreenshot(screenShot: ScreenShot) {
    screenshots.value.push(screenShot);
  }

  function updateScreenshot(list: ScreenShot[]) {
    screenshots.value = list;
  }

  //上传截图
  async function uploadScreenshot(src: string, objectUrl: string) {
    try {
      const results = await API.accountApi.getH3c.request({});
      await H3cHelper.SimpleUploadThumbnailDirectory(
        objectUrl,
        OssHelper.dataURLtoBlob(src)
      );
      if (results.Data?.StorageName == "OSS") {
        await OssHelper.SimpleUploadThumbnailDirectory(
          oss,
          objectUrl,
          OssHelper.dataURLtoBlob(src)
        );
      }
    } catch (error) {}
  }

  //   function isBase64(str: string) {
  //     if (str === "" || str.trim() === "") {
  //       return false;
  //     }
  //     try {
  //       return btoa(atob(str)) == str;
  //     } catch (err) {
  //       return false;
  //     }
  //   }

  function isBase64(str: string) {
    if (str.indexOf("data:") != -1 && str.indexOf("base64") != -1) {
      return true;
    } else {
      return false;
    }
  }

  function initscreenshots() {
    screenshots.value.splice(0);
  }

  function deleteScreenshot(index: number) {
    screenshots.value.splice(index, 1);
  }
  return {
    screenshots,
    addScreenshot,
    addSliceScreenshot,
    uploadScreenshot,
    updateScreenshot,
    isBase64,
    initscreenshots,
    deleteScreenshot,
  };
}
