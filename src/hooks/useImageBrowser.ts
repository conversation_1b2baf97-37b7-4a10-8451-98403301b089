import { PicHead } from "sdpc-web-ts/dist/src/sdpc/picHead";
import { Sdpc, SliceSource } from "sdpc-web-ts/dist/src/sdpc/sdpc";
import { computed, ref, Ref, watch } from "vue";
import ImageBrowser from "@/components/image-viewer/components/imageBrowser/index.vue";
export function useImageBrowser(
  imageBrowser: Ref<InstanceType<typeof ImageBrowser>>
) {
  const slideUrl = ref<SliceSource>(); //hevc切片地址
  const sdpcWs = computed(() => {
    return imageBrowser.value ? (<any>imageBrowser.value).sdpcWs : null;
  });
  //openSeadragon查看器类
  const viewer = computed(() => {
    return imageBrowser.value
      ? ((<any>imageBrowser.value).viewer as OpenSeadragon.Viewer)
      : null;
  });
  //openSeadragon视图类
  const viewPort = computed(() => {
    return imageBrowser.value ? (<any>imageBrowser.value).viewPort : null;
  });

  //sdpc文件头
  const picHead: Ref<PicHead | null> = computed(() => {
    return imageBrowser.value ? (<any>imageBrowser.value).picHead : null;
  });
  //扫描倍率
  const rate: Ref<number> = computed(() => {
    return picHead.value ? picHead.value.rate : 1;
  });
  //3d系数
  const registration3D = computed(() => {
    return imageBrowser.value ? (<any>imageBrowser.value).registration3D : 1;
  });
  //解码库
  const sdpc: Ref<Sdpc> = computed(() => {
    return imageBrowser.value ? (<any>imageBrowser.value).sdpc : null;
  });
  const zoom = ref(0); //放大倍数
  let currZoom = zoom.value;
  //是否全屏
  const isFullscreen = ref(false);
  // let oWidth = document.body.clientWidth;
  // let oHeight = document.body.clientHeight;
  // console.log("oWidth", oWidth);
  const LoadingCompletedOrNot = computed(() => {
    return imageBrowser.value
      ? (<any>imageBrowser.value).LoadingCompletedOrNot
      : false;
  });
  watch(
    () => viewer.value,
    () => {
      if (viewer.value) {
        // let currZoom = zoom.value;
        // if (isFullscreen.value) {
        //   zoom.value = currZoom;
        //   return;
        // }
        let flog: boolean = true;

        viewer.value.addHandler("canvas-pinch", (ev) => {
          zoom.value =
            viewPort.value.getZoom() != ""
              ? Number(
                  (
                    viewPort.value.viewportToImageZoom(
                      viewPort.value.getZoom() / registration3D.value
                    ) * rate.value
                  ).toFixed(2)
                )
              : 0;
        });
        //监听缩放倍率判断是否窗口缩放进行操作
        viewer.value.addHandler("zoom", (ev) => {
          // console.log("zoom", ev, "缩放了");

          if (ev.immediately) {
            if (flog) {
              zoomai(ev);

              flog = false;
            }
          } else {
            zoomai(ev);
          }
        });
      }
    },
    {
      immediate: true,
    }
  );

  //刷新切片
  function refreshViewer(
    SliceUrl: SliceSource,
    isFrozen?: Boolean,
    url?: string
  ) {
    imageBrowser.value?.refreshViewer(SliceUrl, isFrozen, url);
  }
  //缩放计算倍率
  function zoomai(e: any) {
    zoom.value = e.zoom
      ? Number(
          (
            viewPort.value.viewportToImageZoom(e.zoom / registration3D.value) *
            rate.value
          ).toFixed(2)
        )
      : 0;
  }

  return {
    slideUrl, //切片地址
    viewer, //openSeadragon查看器类
    viewPort, //openSeadragon视图类
    picHead, //sdpc文件头信息
    rate, //扫描倍率
    registration3D, //3d系数
    sdpc, //解码库
    zoom, //放大倍数
    isFullscreen, //是否全屏
    refreshViewer, //刷新切换切片
    zoomai, //缩放计算倍率
    LoadingCompletedOrNot,
    sdpcWs,
  };
}
