//获取列表信息
import { axios } from "@/http/axios";
import { GetCaseListRequest } from "@/model/request/CaseApiRequest";
export function GetCaseListAsync(data: GetCaseListRequest, url: string) {
  return axios({
    method: "GET",
    url: url,
    data,
  });
}

//二维码病例数据
export function GetCaseinfor(id: any) {
  return axios({
    method: "GET",
    url: `/api/casedatawithqr/caseinfor?Id=${id}`,
  });
}

/**群聊用户列表 */
export function getTeamUserList(id: any) {
  return axios({
    method: "GET",
    url: `/api/neteaseyunxin/TeamUserList?Tid=${id}`,
  });
}
