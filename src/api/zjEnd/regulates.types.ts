// 病例管理
export interface GetSliceListResponse {
  Id: number;
  IsPrint?: number;
  PatientId: number;
  PathologyNumber?: string;
  Sex?: number;
  SexName?: string;
  Age: number;
  CaseTypeCode: string;
  CaseTypeName: string;
  CheckItemsCode: string;
  CheckItemsName: string;
  Inspection: string;
  SiteId: number;
  SampleLocationCode: string;
  SampleLocationName: string;
  TurnoverId: string;
  Name: string;
  Time: string;
  Status: number;
  StatusName: string;
  SliceThumbnail: string;
}

/*
 * 获取切片列表
 */
export interface CaseListRequest {
  id: string;
  pageIndex: number;
  pageSize: number;
}
/**
 * 病例信息
 */
export interface CaseRenderingDto {
  /** 年龄 */
  Age: string;

  /** 条形码 */
  BarCode: string;

  /** 床号 */
  BedNumber?: string;

  /** 病例类型 */
  CaseType: string;

  /** 病例类型名称 */
  CaseTypeName?: string;

  /** 送检项目 */
  CheckItems: string;

  /** 送检项目名称 */
  CheckItemsName?: string;

  /** 临床资料 */
  ClinicalData?: string;

  /** 临床诊断 */
  ClinicalDiagnosis?: string;

  /** 原病理诊断 */
  DiagnosisContent?: string;

  /** 医生名称 */
  DoctorName?: string;

  /** 医生电话 */
  DoctorPhone?: string;

  /** 民族 */
  EthnicId?: number;

  /** 民族 */
  EthnicName?: string;

  /** ExpertModel */
  ExpertModel?: any;

  /** 住院号 */
  HospitalizedId?: string;

  /** 病例id */
  Id?: number;

  /** 身份证号 */
  IdNumber?: string;

  /** 免疫组化 */
  Immunohistochemical?: string;

  /** 病房 */
  InpatientWard?: string;

  /** 送检单位 */
  Inspection: string;

  /** 送检科室 */
  InspectionDept?: string;

  /** 是否为疑难病 */
  IsRefractoryDisease?: number;

  /** 工作 */
  Job?: string;

  /** 婚姻状况 */
  MaritalStatus?: number;

  /** 婚姻状况名称 */
  MaritalStatusName?: string;

  /** 女性月经时间 */
  MenstruationDate?: string;

  /** 患者名称 */
  Name: string;

  /** 病理号 */
  PathologyNumber: string;

  /** 病人id */
  PatientId?: number;

  /** 联系电话 */
  Phone?: string;

  /** 备注信息 */
  Remark?: string;

  /** 取材部位 */
  SampleLocationCode: string;

  /** 取材部位 */
  SampleLocationName?: string;

  /** 采样日期 */
  SamplingTime: string;

  /** 性别 */
  Sex: number;

  /** 性别name */
  SexName?: string;

  /** 亚专科 */
  SubProfessional: string;

  /** 亚专科名称 */
  SubProfessionalName?: string;

  /** 预约日期 */
  SubscribeTime: string;

  /** 大体可见 */
  Thus?: string;

  /** 就诊号 */
  VisitingNumber?: string;
}

export interface SliceDto {
  value?: number | undefined;
  text?: string | undefined;
  /** 病例Id */
  CaseId?: number;

  /** 创建时间 */
  CreateTime?: string;

  /** 创建人 */
  CreateUser?: string;

  /** 创建人名称 */
  CreateUserName?: string;

  /** 磁盘路径 */
  DiskPath?: string;

  /** 染色 */
  Dyeing?: string;

  /** 染色名称 */
  DyeingName?: string;

  /** 切片名称 */
  FileName?: string;

  /** 切片大小 */
  FileSize?: number;

  /** 切片类型 */
  FileType?: string;

  /** 自增Id */
  Id: number;

  /** 标签 */
  Label?: string;

  /** 宏观图 */
  MacroImageUrl?: string;

  /** 磁盘路径 */
  SliceDiskPath?: string;

  /** 标签 */
  SliceLabel?: string;

  /** 宏观图 */
  SliceMacroImageUrl?: string;

  /** 缩略图 */
  SliceThumbnail?: string;

  /** 切片路径 */
  SliceUrl?: string;

  /** 状态 */
  Status?: number;

  /** 状态名称 */
  StatusName?: string;

  /** 缩略图 */
  Thumbnail?: string;

  /** 切片路径 */
  Url?: string;
}

/**确认预约*/
export interface ConfirmSubscribeRequest {
  /** 病例id */
  Id: number;
}

/**退回预约 */
export interface ReturnSubscribeRequest {
  Id: number;
  IsFrozen: boolean;
  Remark: string;
}
